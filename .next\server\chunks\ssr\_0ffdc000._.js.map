{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/lib/store/hr-store.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { subscribeWithSelector } from 'zustand/middleware'\nimport { Employee, Department, BulkOperation } from '@/lib/types'\n\ninterface HRState {\n  // Data\n  employees: Employee[]\n  departments: Department[]\n  freeBucket: Employee[]\n  \n  // UI State\n  selectedEmployees: string[]\n  isLoading: boolean\n  searchQuery: string\n  \n  // Actions\n  setEmployees: (employees: Employee[]) => void\n  setDepartments: (departments: Department[]) => void\n  addEmployee: (employee: Employee) => void\n  updateEmployee: (id: string, updates: Partial<Employee>) => void\n  removeEmployee: (id: string) => void\n  \n  // Selection\n  toggleEmployeeSelection: (id: string) => void\n  selectAllEmployees: (ids: string[]) => void\n  clearSelection: () => void\n  \n  // Bulk Operations\n  executeBulkOperation: (operation: BulkOperation) => Promise<void>\n  \n  // Search & Filter\n  setSearchQuery: (query: string) => void\n  getFilteredEmployees: () => Employee[]\n  \n  // Free Bucket\n  moveToFreeBucket: (employeeIds: string[]) => void\n  removeFromFreeBucket: (employeeIds: string[]) => void\n}\n\nexport const useHRStore = create<HRState>()(\n  subscribeWithSelector((set, get) => ({\n    // Initial state\n    employees: [],\n    departments: [],\n    freeBucket: [],\n    selectedEmployees: [],\n    isLoading: false,\n    searchQuery: '',\n\n    // Data actions\n    setEmployees: (employees) => set((state) => ({\n      employees,\n      freeBucket: employees.filter(emp => emp.departmentId === null)\n    })),\n    setDepartments: (departments) => set({ departments }),\n    \n    addEmployee: (employee) => set((state) => ({\n      employees: [...state.employees, employee]\n    })),\n    \n    updateEmployee: (id, updates) => set((state) => ({\n      employees: state.employees.map(emp => \n        emp.id === id ? { ...emp, ...updates } : emp\n      )\n    })),\n    \n    removeEmployee: (id) => set((state) => ({\n      employees: state.employees.filter(emp => emp.id !== id),\n      selectedEmployees: state.selectedEmployees.filter(empId => empId !== id)\n    })),\n\n    // Selection actions\n    toggleEmployeeSelection: (id) => set((state) => ({\n      selectedEmployees: state.selectedEmployees.includes(id)\n        ? state.selectedEmployees.filter(empId => empId !== id)\n        : [...state.selectedEmployees, id]\n    })),\n    \n    selectAllEmployees: (ids) => set({ selectedEmployees: ids }),\n    clearSelection: () => set({ selectedEmployees: [] }),\n\n    // Bulk operations\n    executeBulkOperation: async (operation) => {\n      const { employees, selectedEmployees } = get()\n      \n      switch (operation.type) {\n        case 'transfer':\n          set((state) => ({\n            employees: state.employees.map(emp =>\n              operation.employeeIds.includes(emp.id)\n                ? { ...emp, departmentId: operation.targetDepartmentId || null }\n                : emp\n            ),\n            freeBucket: state.employees\n              .map(emp =>\n                operation.employeeIds.includes(emp.id)\n                  ? { ...emp, departmentId: operation.targetDepartmentId || null }\n                  : emp\n              )\n              .filter(emp => emp.departmentId === null)\n          }))\n          break\n          \n        case 'status_update':\n          if (operation.newStatus) {\n            set((state) => ({\n              employees: state.employees.map(emp =>\n                operation.employeeIds.includes(emp.id)\n                  ? { ...emp, status: operation.newStatus! }\n                  : emp\n              )\n            }))\n          }\n          break\n          \n        case 'delete':\n          set((state) => ({\n            employees: state.employees.filter(emp => \n              !operation.employeeIds.includes(emp.id)\n            )\n          }))\n          break\n      }\n      \n      set({ selectedEmployees: [] })\n    },\n\n    // Search & filter\n    setSearchQuery: (query) => set({ searchQuery: query }),\n    \n    getFilteredEmployees: () => {\n      const { employees, searchQuery } = get()\n      if (!searchQuery) return employees\n      \n      return employees.filter(emp =>\n        emp.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        emp.email.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        emp.id.toLowerCase().includes(searchQuery.toLowerCase())\n      )\n    },\n\n    // Free bucket operations\n    moveToFreeBucket: (employeeIds) => set((state) => {\n      const movedEmployees = state.employees\n        .filter(emp => employeeIds.includes(emp.id))\n        .map(emp => ({ ...emp, departmentId: null }))\n      \n      return {\n        employees: state.employees.map(emp =>\n          employeeIds.includes(emp.id) ? { ...emp, departmentId: null } : emp\n        ),\n        freeBucket: [...state.freeBucket, ...movedEmployees]\n      }\n    }),\n    \n    removeFromFreeBucket: (employeeIds) => set((state) => ({\n      freeBucket: state.freeBucket.filter(emp => !employeeIds.includes(emp.id)),\n      employees: state.employees.filter(emp => !employeeIds.includes(emp.id))\n    }))\n  }))\n)"], "names": [], "mappings": ";;;AAAA;AACA;;;AAsCO,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,IAC7B,CAAA,GAAA,6IAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC,KAAK,MAAQ,CAAC;QACnC,gBAAgB;QAChB,WAAW,EAAE;QACb,aAAa,EAAE;QACf,YAAY,EAAE;QACd,mBAAmB,EAAE;QACrB,WAAW;QACX,aAAa;QAEb,eAAe;QACf,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C;oBACA,YAAY,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK;gBAC3D,CAAC;QACD,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QAEnD,aAAa,CAAC,WAAa,IAAI,CAAC,QAAU,CAAC;oBACzC,WAAW;2BAAI,MAAM,SAAS;wBAAE;qBAAS;gBAC3C,CAAC;QAED,gBAAgB,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC/C,WAAW,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,MAC7B,IAAI,EAAE,KAAK,KAAK;4BAAE,GAAG,GAAG;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAE7C,CAAC;QAED,gBAAgB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBACtC,WAAW,MAAM,SAAS,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;oBACpD,mBAAmB,MAAM,iBAAiB,CAAC,MAAM,CAAC,CAAA,QAAS,UAAU;gBACvE,CAAC;QAED,oBAAoB;QACpB,yBAAyB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAC/C,mBAAmB,MAAM,iBAAiB,CAAC,QAAQ,CAAC,MAChD,MAAM,iBAAiB,CAAC,MAAM,CAAC,CAAA,QAAS,UAAU,MAClD;2BAAI,MAAM,iBAAiB;wBAAE;qBAAG;gBACtC,CAAC;QAED,oBAAoB,CAAC,MAAQ,IAAI;gBAAE,mBAAmB;YAAI;QAC1D,gBAAgB,IAAM,IAAI;gBAAE,mBAAmB,EAAE;YAAC;QAElD,kBAAkB;QAClB,sBAAsB,OAAO;YAC3B,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAE,GAAG;YAEzC,OAAQ,UAAU,IAAI;gBACpB,KAAK;oBACH,IAAI,CAAC,QAAU,CAAC;4BACd,WAAW,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,MAC7B,UAAU,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,IACjC;oCAAE,GAAG,GAAG;oCAAE,cAAc,UAAU,kBAAkB,IAAI;gCAAK,IAC7D;4BAEN,YAAY,MAAM,SAAS,CACxB,GAAG,CAAC,CAAA,MACH,UAAU,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,IACjC;oCAAE,GAAG,GAAG;oCAAE,cAAc,UAAU,kBAAkB,IAAI;gCAAK,IAC7D,KAEL,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK;wBACxC,CAAC;oBACD;gBAEF,KAAK;oBACH,IAAI,UAAU,SAAS,EAAE;wBACvB,IAAI,CAAC,QAAU,CAAC;gCACd,WAAW,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,MAC7B,UAAU,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,IACjC;wCAAE,GAAG,GAAG;wCAAE,QAAQ,UAAU,SAAS;oCAAE,IACvC;4BAER,CAAC;oBACH;oBACA;gBAEF,KAAK;oBACH,IAAI,CAAC,QAAU,CAAC;4BACd,WAAW,MAAM,SAAS,CAAC,MAAM,CAAC,CAAA,MAChC,CAAC,UAAU,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE;wBAE1C,CAAC;oBACD;YACJ;YAEA,IAAI;gBAAE,mBAAmB,EAAE;YAAC;QAC9B;QAEA,kBAAkB;QAClB,gBAAgB,CAAC,QAAU,IAAI;gBAAE,aAAa;YAAM;QAEpD,sBAAsB;YACpB,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG;YACnC,IAAI,CAAC,aAAa,OAAO;YAEzB,OAAO,UAAU,MAAM,CAAC,CAAA,MACtB,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACvD,IAAI,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACxD,IAAI,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAEzD;QAEA,yBAAyB;QACzB,kBAAkB,CAAC,cAAgB,IAAI,CAAC;gBACtC,MAAM,iBAAiB,MAAM,SAAS,CACnC,MAAM,CAAC,CAAA,MAAO,YAAY,QAAQ,CAAC,IAAI,EAAE,GACzC,GAAG,CAAC,CAAA,MAAO,CAAC;wBAAE,GAAG,GAAG;wBAAE,cAAc;oBAAK,CAAC;gBAE7C,OAAO;oBACL,WAAW,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,MAC7B,YAAY,QAAQ,CAAC,IAAI,EAAE,IAAI;4BAAE,GAAG,GAAG;4BAAE,cAAc;wBAAK,IAAI;oBAElE,YAAY;2BAAI,MAAM,UAAU;2BAAK;qBAAe;gBACtD;YACF;QAEA,sBAAsB,CAAC,cAAgB,IAAI,CAAC,QAAU,CAAC;oBACrD,YAAY,MAAM,UAAU,CAAC,MAAM,CAAC,CAAA,MAAO,CAAC,YAAY,QAAQ,CAAC,IAAI,EAAE;oBACvE,WAAW,MAAM,SAAS,CAAC,MAAM,CAAC,CAAA,MAAO,CAAC,YAAY,QAAQ,CAAC,IAAI,EAAE;gBACvE,CAAC;IACH,CAAC", "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/progress.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,yBAAW,qMAAA,CAAA,aAAgB,CAG/B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/bulk/csv-import-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useRef } from \"react\"\nimport { Department, Employee, CSVImportResult } from \"@/lib/types\"\nimport { useHRStore } from \"@/lib/store/hr-store\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { generateEmployeeId } from \"@/lib/utils\"\nimport { \n  Upload, \n  FileText, \n  CheckCircle, \n  AlertCircle,\n  Download\n} from \"lucide-react\"\n\ninterface CSVImportSectionProps {\n  departments: Department[]\n}\n\nexport function CSVImportSection({ departments }: CSVImportSectionProps) {\n  const { employees, addEmployee } = useHRStore()\n  const [isUploading, setIsUploading] = useState(false)\n  const [uploadProgress, setUploadProgress] = useState(0)\n  const [importResult, setImportResult] = useState<CSVImportResult | null>(null)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  const handleFileSelect = () => {\n    fileInputRef.current?.click()\n  }\n\n  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (!file) return\n\n    if (!file.name.endsWith('.csv')) {\n      alert('Please select a CSV file')\n      return\n    }\n\n    setIsUploading(true)\n    setUploadProgress(0)\n    setImportResult(null)\n\n    try {\n      const text = await file.text()\n      const result = await processCSV(text)\n      setImportResult(result)\n    } catch (error) {\n      console.error('CSV import failed:', error)\n      alert('Failed to import CSV file')\n    } finally {\n      setIsUploading(false)\n      setUploadProgress(0)\n      // Reset file input\n      if (fileInputRef.current) {\n        fileInputRef.current.value = ''\n      }\n    }\n  }\n\n  const processCSV = async (csvText: string): Promise<CSVImportResult> => {\n    const lines = csvText.trim().split('\\n')\n    const headers = lines[0].split(',').map(h => h.trim().toLowerCase())\n    \n    // Validate required headers\n    const requiredHeaders = ['name', 'email']\n    const missingHeaders = requiredHeaders.filter(h => !headers.includes(h))\n    \n    if (missingHeaders.length > 0) {\n      throw new Error(`Missing required headers: ${missingHeaders.join(', ')}`)\n    }\n\n    const result: CSVImportResult = {\n      success: [],\n      errors: []\n    }\n\n    // Process each row\n    for (let i = 1; i < lines.length; i++) {\n      setUploadProgress((i / (lines.length - 1)) * 100)\n      \n      const values = lines[i].split(',').map(v => v.trim())\n      const rowData: Record<string, string> = {}\n      \n      headers.forEach((header, index) => {\n        rowData[header] = values[index] || ''\n      })\n\n      try {\n        // Validate required fields\n        if (!rowData.name || !rowData.email) {\n          throw new Error('Name and email are required')\n        }\n\n        // Check if email already exists\n        if (employees.some(emp => emp.email.toLowerCase() === rowData.email.toLowerCase())) {\n          throw new Error('Email already exists')\n        }\n\n        // Find department if specified\n        let departmentId: string | null = null\n        if (rowData.departmentcode) {\n          const department = departments.find(d => \n            d.code.toLowerCase() === rowData.departmentcode.toLowerCase()\n          )\n          if (!department) {\n            throw new Error(`Department with code '${rowData.departmentcode}' not found`)\n          }\n          departmentId = department.id\n        }\n\n        // Generate employee ID\n        const department = departments.find(d => d.id === departmentId)\n        const departmentCode = department?.code || 'GEN'\n        const existingIds = employees\n          .filter(emp => emp.id.startsWith(departmentCode))\n          .map(emp => parseInt(emp.id.split('-')[1]) || 0)\n        const nextSequence = Math.max(0, ...existingIds) + result.success.length + 1\n        const employeeId = generateEmployeeId(departmentCode, nextSequence)\n\n        // Validate status if provided\n        const validStatuses = ['ACTIVE', 'TRANSFERRED', 'PENDING_REMOVAL', 'ARCHIVED']\n        const status = rowData.status?.toUpperCase() || 'ACTIVE'\n        if (!validStatuses.includes(status)) {\n          throw new Error(`Invalid status '${rowData.status}'. Must be one of: ${validStatuses.join(', ')}`)\n        }\n\n        const newEmployee: Employee = {\n          id: employeeId,\n          name: rowData.name,\n          email: rowData.email,\n          departmentId,\n          status: status as Employee['status'],\n          hireDate: new Date(),\n          transferHistory: [],\n          createdAt: new Date(),\n          updatedAt: new Date(),\n        }\n\n        result.success.push(newEmployee)\n      } catch (error) {\n        result.errors.push({\n          row: i + 1,\n          data: rowData,\n          error: error instanceof Error ? error.message : 'Unknown error'\n        })\n      }\n\n      // Small delay to show progress\n      await new Promise(resolve => setTimeout(resolve, 10))\n    }\n\n    return result\n  }\n\n  const handleConfirmImport = () => {\n    if (!importResult) return\n\n    // Add all successful employees\n    importResult.success.forEach(employee => {\n      addEmployee(employee)\n    })\n\n    alert(`Successfully imported ${importResult.success.length} employees`)\n    setImportResult(null)\n  }\n\n  const downloadTemplate = () => {\n    const template = [\n      'name,email,departmentcode,status',\n      'John Doe,<EMAIL>,ENG,ACTIVE',\n      'Jane Smith,<EMAIL>,MKT,ACTIVE',\n      'Bob Johnson,<EMAIL>,,ACTIVE'\n    ].join('\\n')\n\n    const blob = new Blob([template], { type: 'text/csv' })\n    const url = URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = 'employee-import-template.csv'\n    a.click()\n    URL.revokeObjectURL(url)\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Upload Section */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Upload className=\"h-5 w-5\" />\n            Upload CSV File\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-4\">\n              <Button onClick={handleFileSelect} disabled={isUploading}>\n                <FileText className=\"h-4 w-4 mr-2\" />\n                Select CSV File\n              </Button>\n              \n              <Button variant=\"outline\" onClick={downloadTemplate}>\n                <Download className=\"h-4 w-4 mr-2\" />\n                Download Template\n              </Button>\n            </div>\n\n            {isUploading && (\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span>Processing CSV...</span>\n                  <span>{Math.round(uploadProgress)}%</span>\n                </div>\n                <Progress value={uploadProgress} />\n              </div>\n            )}\n\n            <input\n              ref={fileInputRef}\n              type=\"file\"\n              accept=\".csv\"\n              onChange={handleFileChange}\n              className=\"hidden\"\n            />\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Import Results */}\n      {importResult && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <CheckCircle className=\"h-5 w-5 text-green-600\" />\n              Import Results\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"flex gap-4\">\n                <Badge variant=\"default\" className=\"bg-green-100 text-green-800\">\n                  {importResult.success.length} Successful\n                </Badge>\n                {importResult.errors.length > 0 && (\n                  <Badge variant=\"destructive\">\n                    {importResult.errors.length} Errors\n                  </Badge>\n                )}\n              </div>\n\n              {importResult.errors.length > 0 && (\n                <div className=\"space-y-2\">\n                  <h4 className=\"font-medium text-red-800\">Errors:</h4>\n                  <div className=\"max-h-40 overflow-y-auto space-y-1\">\n                    {importResult.errors.map((error, index) => (\n                      <div key={index} className=\"text-sm p-2 bg-red-50 rounded border-l-4 border-red-400\">\n                        <div className=\"font-medium\">Row {error.row}:</div>\n                        <div className=\"text-red-700\">{error.error}</div>\n                        <div className=\"text-xs text-red-600 mt-1\">\n                          Data: {JSON.stringify(error.data)}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {importResult.success.length > 0 && (\n                <div className=\"flex gap-2\">\n                  <Button onClick={handleConfirmImport}>\n                    Import {importResult.success.length} Employees\n                  </Button>\n                  <Button variant=\"outline\" onClick={() => setImportResult(null)}>\n                    Cancel\n                  </Button>\n                </div>\n              )}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Instructions */}\n      <Card className=\"border-blue-200 bg-blue-50\">\n        <CardHeader>\n          <CardTitle className=\"text-blue-800\">CSV Format Instructions</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-2 text-sm text-blue-700\">\n            <p><strong>Required columns:</strong> name, email</p>\n            <p><strong>Optional columns:</strong> departmentcode, status</p>\n            <p><strong>Department codes:</strong> {departments.map(d => d.code).join(', ')}</p>\n            <p><strong>Valid statuses:</strong> ACTIVE, TRANSFERRED, PENDING_REMOVAL, ARCHIVED</p>\n            <p><strong>Note:</strong> If departmentcode is empty, employee will be added to the free bucket</p>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;;AAsBO,SAAS,iBAAiB,EAAE,WAAW,EAAyB;IACrE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,aAAU,AAAD;IAC5C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACzE,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB;QACvB,aAAa,OAAO,EAAE;IACxB;IAEA,MAAM,mBAAmB,OAAO;QAC9B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS;YAC/B,MAAM;YACN;QACF;QAEA,eAAe;QACf,kBAAkB;QAClB,gBAAgB;QAEhB,IAAI;YACF,MAAM,OAAO,MAAM,KAAK,IAAI;YAC5B,MAAM,SAAS,MAAM,WAAW;YAChC,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR,SAAU;YACR,eAAe;YACf,kBAAkB;YAClB,mBAAmB;YACnB,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;QACF;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC;QACnC,MAAM,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG,WAAW;QAEjE,4BAA4B;QAC5B,MAAM,kBAAkB;YAAC;YAAQ;SAAQ;QACzC,MAAM,iBAAiB,gBAAgB,MAAM,CAAC,CAAA,IAAK,CAAC,QAAQ,QAAQ,CAAC;QAErE,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,eAAe,IAAI,CAAC,OAAO;QAC1E;QAEA,MAAM,SAA0B;YAC9B,SAAS,EAAE;YACX,QAAQ,EAAE;QACZ;QAEA,mBAAmB;QACnB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,kBAAkB,AAAC,IAAI,CAAC,MAAM,MAAM,GAAG,CAAC,IAAK;YAE7C,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;YAClD,MAAM,UAAkC,CAAC;YAEzC,QAAQ,OAAO,CAAC,CAAC,QAAQ;gBACvB,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,IAAI;YACrC;YAEA,IAAI;gBACF,2BAA2B;gBAC3B,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,EAAE;oBACnC,MAAM,IAAI,MAAM;gBAClB;gBAEA,gCAAgC;gBAChC,IAAI,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,CAAC,WAAW,OAAO,QAAQ,KAAK,CAAC,WAAW,KAAK;oBAClF,MAAM,IAAI,MAAM;gBAClB;gBAEA,+BAA+B;gBAC/B,IAAI,eAA8B;gBAClC,IAAI,QAAQ,cAAc,EAAE;oBAC1B,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,IAClC,EAAE,IAAI,CAAC,WAAW,OAAO,QAAQ,cAAc,CAAC,WAAW;oBAE7D,IAAI,CAAC,YAAY;wBACf,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,QAAQ,cAAc,CAAC,WAAW,CAAC;oBAC9E;oBACA,eAAe,WAAW,EAAE;gBAC9B;gBAEA,uBAAuB;gBACvB,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAClD,MAAM,iBAAiB,YAAY,QAAQ;gBAC3C,MAAM,cAAc,UACjB,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,CAAC,UAAU,CAAC,iBAChC,GAAG,CAAC,CAAA,MAAO,SAAS,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK;gBAChD,MAAM,eAAe,KAAK,GAAG,CAAC,MAAM,eAAe,OAAO,OAAO,CAAC,MAAM,GAAG;gBAC3E,MAAM,aAAa,CAAA,GAAA,4GAAA,CAAA,qBAAkB,AAAD,EAAE,gBAAgB;gBAEtD,8BAA8B;gBAC9B,MAAM,gBAAgB;oBAAC;oBAAU;oBAAe;oBAAmB;iBAAW;gBAC9E,MAAM,SAAS,QAAQ,MAAM,EAAE,iBAAiB;gBAChD,IAAI,CAAC,cAAc,QAAQ,CAAC,SAAS;oBACnC,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,QAAQ,MAAM,CAAC,mBAAmB,EAAE,cAAc,IAAI,CAAC,OAAO;gBACnG;gBAEA,MAAM,cAAwB;oBAC5B,IAAI;oBACJ,MAAM,QAAQ,IAAI;oBAClB,OAAO,QAAQ,KAAK;oBACpB;oBACA,QAAQ;oBACR,UAAU,IAAI;oBACd,iBAAiB,EAAE;oBACnB,WAAW,IAAI;oBACf,WAAW,IAAI;gBACjB;gBAEA,OAAO,OAAO,CAAC,IAAI,CAAC;YACtB,EAAE,OAAO,OAAO;gBACd,OAAO,MAAM,CAAC,IAAI,CAAC;oBACjB,KAAK,IAAI;oBACT,MAAM;oBACN,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD;YACF;YAEA,+BAA+B;YAC/B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;QAEA,OAAO;IACT;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,cAAc;QAEnB,+BAA+B;QAC/B,aAAa,OAAO,CAAC,OAAO,CAAC,CAAA;YAC3B,YAAY;QACd;QAEA,MAAM,CAAC,sBAAsB,EAAE,aAAa,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC;QACtE,gBAAgB;IAClB;IAEA,MAAM,mBAAmB;QACvB,MAAM,WAAW;YACf;YACA;YACA;YACA;SACD,CAAC,IAAI,CAAC;QAEP,MAAM,OAAO,IAAI,KAAK;YAAC;SAAS,EAAE;YAAE,MAAM;QAAW;QACrD,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG;QACb,EAAE,KAAK;QACP,IAAI,eAAe,CAAC;IACtB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIlC,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,2HAAA,CAAA,SAAM;4CAAC,SAAS;4CAAkB,UAAU;;8DAC3C,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAIvC,8OAAC,2HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;;8DACjC,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;gCAKxC,6BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;;wDAAM,KAAK,KAAK,CAAC;wDAAgB;;;;;;;;;;;;;sDAEpC,8OAAC,6HAAA,CAAA,WAAQ;4CAAC,OAAO;;;;;;;;;;;;8CAIrB,8OAAC;oCACC,KAAK;oCACL,MAAK;oCACL,QAAO;oCACP,UAAU;oCACV,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAOjB,8BACC,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAA2B;;;;;;;;;;;;kCAItD,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;;gDAChC,aAAa,OAAO,CAAC,MAAM;gDAAC;;;;;;;wCAE9B,aAAa,MAAM,CAAC,MAAM,GAAG,mBAC5B,8OAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;;gDACZ,aAAa,MAAM,CAAC,MAAM;gDAAC;;;;;;;;;;;;;gCAKjC,aAAa,MAAM,CAAC,MAAM,GAAG,mBAC5B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDACZ,aAAa,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC/B,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAI,WAAU;;gEAAc;gEAAK,MAAM,GAAG;gEAAC;;;;;;;sEAC5C,8OAAC;4DAAI,WAAU;sEAAgB,MAAM,KAAK;;;;;;sEAC1C,8OAAC;4DAAI,WAAU;;gEAA4B;gEAClC,KAAK,SAAS,CAAC,MAAM,IAAI;;;;;;;;mDAJ1B;;;;;;;;;;;;;;;;gCAYjB,aAAa,OAAO,CAAC,MAAM,GAAG,mBAC7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,2HAAA,CAAA,SAAM;4CAAC,SAAS;;gDAAqB;gDAC5B,aAAa,OAAO,CAAC,MAAM;gDAAC;;;;;;;sDAEtC,8OAAC,2HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS,IAAM,gBAAgB;sDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW5E,8OAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;sCAAgB;;;;;;;;;;;kCAEvC,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAA0B;;;;;;;8CACrC,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAA0B;;;;;;;8CACrC,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAA0B;wCAAE,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC;;;;;;;8CACzE,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAAwB;;;;;;;8CACnC,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMrC", "debugId": null}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,qMAAA,CAAA,aAAgB,CAI5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 932, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/checkbox.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root> & {\n    indeterminate?: boolean\n  }\n>(({ className, indeterminate, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      {indeterminate ? (\n        <div className=\"h-2 w-2 bg-current rounded-sm\" />\n      ) : (\n        <Check className=\"h-4 w-4\" />\n      )}\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,yBAAW,qMAAA,CAAA,aAAgB,CAK/B,CAAC,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,OAAO,EAAE,oBACzC,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE;sBAEb,8BACC,8OAAC;gBAAI,WAAU;;;;;yEAEf,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;AAKzB,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 980, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/select.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;AAAA;AACA;;;;;;AAEA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,qMAAA,CAAA,aAAgB,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,qMAAA,CAAA,aAAgB,CAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,qMAAA,CAAA,aAAgB,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,qMAAA,CAAA,aAAgB,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1169, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/bulk/csv-export-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Employee, Department } from \"@/lib/types\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Label } from \"@/components/ui/label\"\nimport { Checkbox } from \"@/components/ui/checkbox\"\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\"\nimport { formatDate } from \"@/lib/utils\"\nimport { \n  Download, \n  FileText,\n  Filter\n} from \"lucide-react\"\n\ninterface CSVExportSectionProps {\n  employees: Employee[]\n  departments: Department[]\n}\n\nexport function CSVExportSection({ employees, departments }: CSVExportSectionProps) {\n  const [filters, setFilters] = useState({\n    department: 'all',\n    status: 'all',\n    includeTransferHistory: false\n  })\n  const [selectedColumns, setSelectedColumns] = useState({\n    id: true,\n    name: true,\n    email: true,\n    department: true,\n    status: true,\n    hireDate: true,\n    createdAt: false,\n    updatedAt: false\n  })\n\n  const availableColumns = [\n    { key: 'id', label: 'Employee ID', required: true },\n    { key: 'name', label: 'Name', required: true },\n    { key: 'email', label: 'Email', required: true },\n    { key: 'department', label: 'Department', required: false },\n    { key: 'status', label: 'Status', required: false },\n    { key: 'hireDate', label: 'Hire Date', required: false },\n    { key: 'createdAt', label: 'Created Date', required: false },\n    { key: 'updatedAt', label: 'Updated Date', required: false }\n  ]\n\n  const getFilteredEmployees = () => {\n    return employees.filter(employee => {\n      // Department filter\n      if (filters.department !== 'all') {\n        if (filters.department === 'unassigned') {\n          if (employee.departmentId !== null) return false\n        } else {\n          if (employee.departmentId !== filters.department) return false\n        }\n      }\n\n      // Status filter\n      if (filters.status !== 'all' && employee.status !== filters.status) {\n        return false\n      }\n\n      return true\n    })\n  }\n\n  const getDepartmentName = (departmentId: string | null) => {\n    if (!departmentId) return 'Unassigned'\n    const department = departments.find(dept => dept.id === departmentId)\n    return department?.name || 'Unknown'\n  }\n\n  const generateCSV = () => {\n    const filteredEmployees = getFilteredEmployees()\n    \n    if (filteredEmployees.length === 0) {\n      alert('No employees match the current filters')\n      return\n    }\n\n    // Generate headers\n    const headers: string[] = []\n    if (selectedColumns.id) headers.push('Employee ID')\n    if (selectedColumns.name) headers.push('Name')\n    if (selectedColumns.email) headers.push('Email')\n    if (selectedColumns.department) headers.push('Department')\n    if (selectedColumns.status) headers.push('Status')\n    if (selectedColumns.hireDate) headers.push('Hire Date')\n    if (selectedColumns.createdAt) headers.push('Created Date')\n    if (selectedColumns.updatedAt) headers.push('Updated Date')\n\n    // Generate rows\n    const rows = filteredEmployees.map(employee => {\n      const row: string[] = []\n      if (selectedColumns.id) row.push(employee.id)\n      if (selectedColumns.name) row.push(employee.name)\n      if (selectedColumns.email) row.push(employee.email)\n      if (selectedColumns.department) row.push(getDepartmentName(employee.departmentId))\n      if (selectedColumns.status) row.push(employee.status)\n      if (selectedColumns.hireDate) row.push(formatDate(employee.hireDate))\n      if (selectedColumns.createdAt) row.push(formatDate(employee.createdAt))\n      if (selectedColumns.updatedAt) row.push(formatDate(employee.updatedAt))\n      return row\n    })\n\n    // Create CSV content\n    const csvContent = [\n      headers.join(','),\n      ...rows.map(row => row.map(cell => `\"${cell}\"`).join(','))\n    ].join('\\n')\n\n    // Download file\n    const blob = new Blob([csvContent], { type: 'text/csv' })\n    const url = URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = `employees-export-${new Date().toISOString().split('T')[0]}.csv`\n    a.click()\n    URL.revokeObjectURL(url)\n  }\n\n  const handleColumnToggle = (column: string, checked: boolean) => {\n    setSelectedColumns(prev => ({\n      ...prev,\n      [column]: checked\n    }))\n  }\n\n  const filteredEmployees = getFilteredEmployees()\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Filters */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Filter className=\"h-5 w-5\" />\n            Export Filters\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid gap-4 md:grid-cols-2\">\n            <div className=\"space-y-2\">\n              <Label>Department</Label>\n              <Select\n                value={filters.department}\n                onValueChange={(value) => setFilters(prev => ({ ...prev, department: value }))}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">All Departments</SelectItem>\n                  <SelectItem value=\"unassigned\">Unassigned</SelectItem>\n                  {departments.map((dept) => (\n                    <SelectItem key={dept.id} value={dept.id}>\n                      {dept.name}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>Status</Label>\n              <Select\n                value={filters.status}\n                onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">All Statuses</SelectItem>\n                  <SelectItem value=\"ACTIVE\">Active</SelectItem>\n                  <SelectItem value=\"TRANSFERRED\">Transferred</SelectItem>\n                  <SelectItem value=\"PENDING_REMOVAL\">Pending Removal</SelectItem>\n                  <SelectItem value=\"ARCHIVED\">Archived</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Column Selection */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Select Columns to Export</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid gap-3 md:grid-cols-2\">\n            {availableColumns.map((column) => (\n              <div key={column.key} className=\"flex items-center space-x-2\">\n                <Checkbox\n                  id={column.key}\n                  checked={selectedColumns[column.key as keyof typeof selectedColumns]}\n                  onCheckedChange={(checked) => handleColumnToggle(column.key, checked as boolean)}\n                  disabled={column.required}\n                />\n                <Label \n                  htmlFor={column.key}\n                  className={column.required ? 'text-muted-foreground' : ''}\n                >\n                  {column.label}\n                  {column.required && ' (Required)'}\n                </Label>\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Export Summary */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <FileText className=\"h-5 w-5\" />\n            Export Summary\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            <div className=\"grid gap-2 md:grid-cols-3\">\n              <div className=\"text-center p-4 bg-muted rounded-lg\">\n                <div className=\"text-2xl font-bold\">{filteredEmployees.length}</div>\n                <div className=\"text-sm text-muted-foreground\">Employees to export</div>\n              </div>\n              <div className=\"text-center p-4 bg-muted rounded-lg\">\n                <div className=\"text-2xl font-bold\">\n                  {Object.values(selectedColumns).filter(Boolean).length}\n                </div>\n                <div className=\"text-sm text-muted-foreground\">Columns selected</div>\n              </div>\n              <div className=\"text-center p-4 bg-muted rounded-lg\">\n                <div className=\"text-2xl font-bold\">CSV</div>\n                <div className=\"text-sm text-muted-foreground\">Export format</div>\n              </div>\n            </div>\n\n            <Button \n              onClick={generateCSV} \n              disabled={filteredEmployees.length === 0}\n              className=\"w-full\"\n            >\n              <Download className=\"h-4 w-4 mr-2\" />\n              Export {filteredEmployees.length} Employees to CSV\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAhBA;;;;;;;;;;AA2BO,SAAS,iBAAiB,EAAE,SAAS,EAAE,WAAW,EAAyB;IAChF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,YAAY;QACZ,QAAQ;QACR,wBAAwB;IAC1B;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,IAAI;QACJ,MAAM;QACN,OAAO;QACP,YAAY;QACZ,QAAQ;QACR,UAAU;QACV,WAAW;QACX,WAAW;IACb;IAEA,MAAM,mBAAmB;QACvB;YAAE,KAAK;YAAM,OAAO;YAAe,UAAU;QAAK;QAClD;YAAE,KAAK;YAAQ,OAAO;YAAQ,UAAU;QAAK;QAC7C;YAAE,KAAK;YAAS,OAAO;YAAS,UAAU;QAAK;QAC/C;YAAE,KAAK;YAAc,OAAO;YAAc,UAAU;QAAM;QAC1D;YAAE,KAAK;YAAU,OAAO;YAAU,UAAU;QAAM;QAClD;YAAE,KAAK;YAAY,OAAO;YAAa,UAAU;QAAM;QACvD;YAAE,KAAK;YAAa,OAAO;YAAgB,UAAU;QAAM;QAC3D;YAAE,KAAK;YAAa,OAAO;YAAgB,UAAU;QAAM;KAC5D;IAED,MAAM,uBAAuB;QAC3B,OAAO,UAAU,MAAM,CAAC,CAAA;YACtB,oBAAoB;YACpB,IAAI,QAAQ,UAAU,KAAK,OAAO;gBAChC,IAAI,QAAQ,UAAU,KAAK,cAAc;oBACvC,IAAI,SAAS,YAAY,KAAK,MAAM,OAAO;gBAC7C,OAAO;oBACL,IAAI,SAAS,YAAY,KAAK,QAAQ,UAAU,EAAE,OAAO;gBAC3D;YACF;YAEA,gBAAgB;YAChB,IAAI,QAAQ,MAAM,KAAK,SAAS,SAAS,MAAM,KAAK,QAAQ,MAAM,EAAE;gBAClE,OAAO;YACT;YAEA,OAAO;QACT;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,cAAc,OAAO;QAC1B,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACxD,OAAO,YAAY,QAAQ;IAC7B;IAEA,MAAM,cAAc;QAClB,MAAM,oBAAoB;QAE1B,IAAI,kBAAkB,MAAM,KAAK,GAAG;YAClC,MAAM;YACN;QACF;QAEA,mBAAmB;QACnB,MAAM,UAAoB,EAAE;QAC5B,IAAI,gBAAgB,EAAE,EAAE,QAAQ,IAAI,CAAC;QACrC,IAAI,gBAAgB,IAAI,EAAE,QAAQ,IAAI,CAAC;QACvC,IAAI,gBAAgB,KAAK,EAAE,QAAQ,IAAI,CAAC;QACxC,IAAI,gBAAgB,UAAU,EAAE,QAAQ,IAAI,CAAC;QAC7C,IAAI,gBAAgB,MAAM,EAAE,QAAQ,IAAI,CAAC;QACzC,IAAI,gBAAgB,QAAQ,EAAE,QAAQ,IAAI,CAAC;QAC3C,IAAI,gBAAgB,SAAS,EAAE,QAAQ,IAAI,CAAC;QAC5C,IAAI,gBAAgB,SAAS,EAAE,QAAQ,IAAI,CAAC;QAE5C,gBAAgB;QAChB,MAAM,OAAO,kBAAkB,GAAG,CAAC,CAAA;YACjC,MAAM,MAAgB,EAAE;YACxB,IAAI,gBAAgB,EAAE,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE;YAC5C,IAAI,gBAAgB,IAAI,EAAE,IAAI,IAAI,CAAC,SAAS,IAAI;YAChD,IAAI,gBAAgB,KAAK,EAAE,IAAI,IAAI,CAAC,SAAS,KAAK;YAClD,IAAI,gBAAgB,UAAU,EAAE,IAAI,IAAI,CAAC,kBAAkB,SAAS,YAAY;YAChF,IAAI,gBAAgB,MAAM,EAAE,IAAI,IAAI,CAAC,SAAS,MAAM;YACpD,IAAI,gBAAgB,QAAQ,EAAE,IAAI,IAAI,CAAC,CAAA,GAAA,4GAAA,CAAA,aAAU,AAAD,EAAE,SAAS,QAAQ;YACnE,IAAI,gBAAgB,SAAS,EAAE,IAAI,IAAI,CAAC,CAAA,GAAA,4GAAA,CAAA,aAAU,AAAD,EAAE,SAAS,SAAS;YACrE,IAAI,gBAAgB,SAAS,EAAE,IAAI,IAAI,CAAC,CAAA,GAAA,4GAAA,CAAA,aAAU,AAAD,EAAE,SAAS,SAAS;YACrE,OAAO;QACT;QAEA,qBAAqB;QACrB,MAAM,aAAa;YACjB,QAAQ,IAAI,CAAC;eACV,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,GAAG,CAAC,CAAA,OAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC;SACtD,CAAC,IAAI,CAAC;QAEP,gBAAgB;QAChB,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAW;QACvD,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,iBAAiB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QAC7E,EAAE,KAAK;QACP,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,qBAAqB,CAAC,QAAgB;QAC1C,mBAAmB,CAAA,OAAQ,CAAC;gBAC1B,GAAG,IAAI;gBACP,CAAC,OAAO,EAAE;YACZ,CAAC;IACH;IAEA,MAAM,oBAAoB;IAE1B,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIlC,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0HAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,2HAAA,CAAA,SAAM;4CACL,OAAO,QAAQ,UAAU;4CACzB,eAAe,CAAC,QAAU,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,YAAY;oDAAM,CAAC;;8DAE5E,8OAAC,2HAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;;;;;;;;;;8DAEd,8OAAC,2HAAA,CAAA,gBAAa;;sEACZ,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAa;;;;;;wDAC9B,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC,2HAAA,CAAA,aAAU;gEAAe,OAAO,KAAK,EAAE;0EACrC,KAAK,IAAI;+DADK,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;8CAQhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0HAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,2HAAA,CAAA,SAAM;4CACL,OAAO,QAAQ,MAAM;4CACrB,eAAe,CAAC,QAAU,WAAW,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,QAAQ;oDAAM,CAAC;;8DAExE,8OAAC,2HAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;;;;;;;;;;8DAEd,8OAAC,2HAAA,CAAA,gBAAa;;sEACZ,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAc;;;;;;sEAChC,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAkB;;;;;;sEACpC,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASzC,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,uBACrB,8OAAC;oCAAqB,WAAU;;sDAC9B,8OAAC,6HAAA,CAAA,WAAQ;4CACP,IAAI,OAAO,GAAG;4CACd,SAAS,eAAe,CAAC,OAAO,GAAG,CAAiC;4CACpE,iBAAiB,CAAC,UAAY,mBAAmB,OAAO,GAAG,EAAE;4CAC7D,UAAU,OAAO,QAAQ;;;;;;sDAE3B,8OAAC,0HAAA,CAAA,QAAK;4CACJ,SAAS,OAAO,GAAG;4CACnB,WAAW,OAAO,QAAQ,GAAG,0BAA0B;;gDAEtD,OAAO,KAAK;gDACZ,OAAO,QAAQ,IAAI;;;;;;;;mCAZd,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;0BAqB5B,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIpC,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAsB,kBAAkB,MAAM;;;;;;8DAC7D,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,OAAO,MAAM,CAAC,iBAAiB,MAAM,CAAC,SAAS,MAAM;;;;;;8DAExD,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAqB;;;;;;8DACpC,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAInD,8OAAC,2HAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,kBAAkB,MAAM,KAAK;oCACvC,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;wCAC7B,kBAAkB,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 1763, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/bulk/bulk-transfer-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Employee, Department } from \"@/lib/types\"\nimport { useHRStore } from \"@/lib/store/hr-store\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Label } from \"@/components/ui/label\"\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { \n  ArrowRight, \n  Users,\n  AlertCircle,\n  CheckCircle\n} from \"lucide-react\"\n\ninterface BulkTransferSectionProps {\n  employees: Employee[]\n  departments: Department[]\n  selectedEmployees: string[]\n}\n\nexport function BulkTransferSection({ \n  employees, \n  departments, \n  selectedEmployees \n}: BulkTransferSectionProps) {\n  const { executeBulkOperation, clearSelection } = useHRStore()\n  const [targetDepartment, setTargetDepartment] = useState<string>(\"\")\n  const [isTransferring, setIsTransferring] = useState(false)\n\n  const selectedEmployeeData = employees.filter(emp => \n    selectedEmployees.includes(emp.id)\n  )\n\n  const getDepartmentName = (departmentId: string | null) => {\n    if (!departmentId) return \"Unassigned\"\n    const department = departments.find(dept => dept.id === departmentId)\n    return department?.name || \"Unknown\"\n  }\n\n  const getTargetDepartment = () => {\n    if (targetDepartment === \"unassigned\") return null\n    return departments.find(dept => dept.id === targetDepartment) || null\n  }\n\n  const getCapacityInfo = () => {\n    const target = getTargetDepartment()\n    if (!target) return null\n\n    const currentEmployees = employees.filter(emp => emp.departmentId === target.id)\n    const afterTransfer = currentEmployees.length + selectedEmployees.length\n    const utilization = Math.round((afterTransfer / target.capacity) * 100)\n\n    return {\n      current: currentEmployees.length,\n      afterTransfer,\n      capacity: target.capacity,\n      utilization,\n      isOverCapacity: afterTransfer > target.capacity\n    }\n  }\n\n  const handleTransfer = async () => {\n    if (!targetDepartment || selectedEmployees.length === 0) return\n\n    const targetDeptId = targetDepartment === \"unassigned\" ? null : targetDepartment\n\n    setIsTransferring(true)\n    try {\n      await executeBulkOperation({\n        type: 'transfer',\n        employeeIds: selectedEmployees,\n        targetDepartmentId: targetDeptId\n      })\n\n      clearSelection()\n      setTargetDepartment(\"\")\n      alert(`Successfully transferred ${selectedEmployees.length} employees`)\n    } catch (error) {\n      console.error('Bulk transfer failed:', error)\n      alert('Transfer failed. Please try again.')\n    } finally {\n      setIsTransferring(false)\n    }\n  }\n\n  const capacityInfo = getCapacityInfo()\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Selection Summary */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Users className=\"h-5 w-5\" />\n            Selected Employees ({selectedEmployees.length})\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          {selectedEmployees.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <AlertCircle className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n              <p className=\"text-muted-foreground\">\n                No employees selected. Go to the Employees page to select employees for transfer.\n              </p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              <div className=\"grid gap-2 max-h-40 overflow-y-auto\">\n                {selectedEmployeeData.map((employee) => (\n                  <div key={employee.id} className=\"flex items-center justify-between p-2 bg-muted rounded\">\n                    <div>\n                      <div className=\"font-medium\">{employee.name}</div>\n                      <div className=\"text-sm text-muted-foreground\">{employee.email}</div>\n                    </div>\n                    <Badge variant=\"outline\">\n                      {getDepartmentName(employee.departmentId)}\n                    </Badge>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Transfer Configuration */}\n      {selectedEmployees.length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <ArrowRight className=\"h-5 w-5\" />\n              Transfer Configuration\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label>Target Department</Label>\n                <Select\n                  value={targetDepartment}\n                  onValueChange={setTargetDepartment}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select target department\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"unassigned\">Unassigned (Free Bucket)</SelectItem>\n                    {departments.map((dept) => (\n                      <SelectItem key={dept.id} value={dept.id}>\n                        {dept.name} ({dept.code})\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Capacity Warning */}\n              {capacityInfo && (\n                <Card className={`border-2 ${\n                  capacityInfo.isOverCapacity \n                    ? 'border-red-200 bg-red-50' \n                    : capacityInfo.utilization >= 80 \n                    ? 'border-orange-200 bg-orange-50'\n                    : 'border-green-200 bg-green-50'\n                }`}>\n                  <CardContent className=\"pt-4\">\n                    <div className=\"flex items-center gap-2 mb-2\">\n                      {capacityInfo.isOverCapacity ? (\n                        <AlertCircle className=\"h-5 w-5 text-red-600\" />\n                      ) : (\n                        <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                      )}\n                      <span className=\"font-medium\">\n                        Capacity Check\n                      </span>\n                    </div>\n                    \n                    <div className=\"space-y-1 text-sm\">\n                      <div>Current: {capacityInfo.current} / {capacityInfo.capacity}</div>\n                      <div>After transfer: {capacityInfo.afterTransfer} / {capacityInfo.capacity}</div>\n                      <div>Utilization: {capacityInfo.utilization}%</div>\n                    </div>\n\n                    {capacityInfo.isOverCapacity && (\n                      <div className=\"mt-2 text-sm text-red-700\">\n                        ⚠️ This transfer will exceed department capacity by {capacityInfo.afterTransfer - capacityInfo.capacity} employees.\n                      </div>\n                    )}\n                  </CardContent>\n                </Card>\n              )}\n\n              <Button \n                onClick={handleTransfer}\n                disabled={!targetDepartment || isTransferring}\n                className=\"w-full\"\n              >\n                {isTransferring ? 'Transferring...' : `Transfer ${selectedEmployees.length} Employees`}\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Instructions */}\n      <Card className=\"border-blue-200 bg-blue-50\">\n        <CardHeader>\n          <CardTitle className=\"text-blue-800\">Transfer Instructions</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-2 text-sm text-blue-700\">\n            <p>1. Go to the <strong>Employees</strong> page and select the employees you want to transfer</p>\n            <p>2. Return to this page and select the target department</p>\n            <p>3. Review the capacity information and confirm the transfer</p>\n            <p>4. The system will update all selected employees' department assignments</p>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AAhBA;;;;;;;;;;AA6BO,SAAS,oBAAoB,EAClC,SAAS,EACT,WAAW,EACX,iBAAiB,EACQ;IACzB,MAAM,EAAE,oBAAoB,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,aAAU,AAAD;IAC1D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,uBAAuB,UAAU,MAAM,CAAC,CAAA,MAC5C,kBAAkB,QAAQ,CAAC,IAAI,EAAE;IAGnC,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,cAAc,OAAO;QAC1B,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACxD,OAAO,YAAY,QAAQ;IAC7B;IAEA,MAAM,sBAAsB;QAC1B,IAAI,qBAAqB,cAAc,OAAO;QAC9C,OAAO,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,qBAAqB;IACnE;IAEA,MAAM,kBAAkB;QACtB,MAAM,SAAS;QACf,IAAI,CAAC,QAAQ,OAAO;QAEpB,MAAM,mBAAmB,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK,OAAO,EAAE;QAC/E,MAAM,gBAAgB,iBAAiB,MAAM,GAAG,kBAAkB,MAAM;QACxE,MAAM,cAAc,KAAK,KAAK,CAAC,AAAC,gBAAgB,OAAO,QAAQ,GAAI;QAEnE,OAAO;YACL,SAAS,iBAAiB,MAAM;YAChC;YACA,UAAU,OAAO,QAAQ;YACzB;YACA,gBAAgB,gBAAgB,OAAO,QAAQ;QACjD;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,oBAAoB,kBAAkB,MAAM,KAAK,GAAG;QAEzD,MAAM,eAAe,qBAAqB,eAAe,OAAO;QAEhE,kBAAkB;QAClB,IAAI;YACF,MAAM,qBAAqB;gBACzB,MAAM;gBACN,aAAa;gBACb,oBAAoB;YACtB;YAEA;YACA,oBAAoB;YACpB,MAAM,CAAC,yBAAyB,EAAE,kBAAkB,MAAM,CAAC,UAAU,CAAC;QACxE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,eAAe;IAErB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;gCACR,kBAAkB,MAAM;gCAAC;;;;;;;;;;;;kCAGlD,8OAAC,yHAAA,CAAA,cAAW;kCACT,kBAAkB,MAAM,KAAK,kBAC5B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;iDAKvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,qBAAqB,GAAG,CAAC,CAAC,yBACzB,8OAAC;wCAAsB,WAAU;;0DAC/B,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAe,SAAS,IAAI;;;;;;kEAC3C,8OAAC;wDAAI,WAAU;kEAAiC,SAAS,KAAK;;;;;;;;;;;;0DAEhE,8OAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DACZ,kBAAkB,SAAS,YAAY;;;;;;;uCANlC,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;YAiBhC,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAItC,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0HAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,2HAAA,CAAA,SAAM;4CACL,OAAO;4CACP,eAAe;;8DAEf,8OAAC,2HAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,2HAAA,CAAA,gBAAa;;sEACZ,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;sEAAa;;;;;;wDAC9B,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC,2HAAA,CAAA,aAAU;gEAAe,OAAO,KAAK,EAAE;;oEACrC,KAAK,IAAI;oEAAC;oEAAG,KAAK,IAAI;oEAAC;;+DADT,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;gCAS/B,8BACC,8OAAC,yHAAA,CAAA,OAAI;oCAAC,WAAW,CAAC,SAAS,EACzB,aAAa,cAAc,GACvB,6BACA,aAAa,WAAW,IAAI,KAC5B,mCACA,gCACJ;8CACA,cAAA,8OAAC,yHAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;oDACZ,aAAa,cAAc,iBAC1B,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEAEzB,8OAAC;wDAAK,WAAU;kEAAc;;;;;;;;;;;;0DAKhC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DAAI;4DAAU,aAAa,OAAO;4DAAC;4DAAI,aAAa,QAAQ;;;;;;;kEAC7D,8OAAC;;4DAAI;4DAAiB,aAAa,aAAa;4DAAC;4DAAI,aAAa,QAAQ;;;;;;;kEAC1E,8OAAC;;4DAAI;4DAAc,aAAa,WAAW;4DAAC;;;;;;;;;;;;;4CAG7C,aAAa,cAAc,kBAC1B,8OAAC;gDAAI,WAAU;;oDAA4B;oDACY,aAAa,aAAa,GAAG,aAAa,QAAQ;oDAAC;;;;;;;;;;;;;;;;;;8CAOlH,8OAAC,2HAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,CAAC,oBAAoB;oCAC/B,WAAU;8CAET,iBAAiB,oBAAoB,CAAC,SAAS,EAAE,kBAAkB,MAAM,CAAC,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;0BAQhG,8OAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;sCAAgB;;;;;;;;;;;kCAEvC,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;wCAAE;sDAAa,8OAAC;sDAAO;;;;;;wCAAkB;;;;;;;8CAC1C,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 2270, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/bulk/bulk-status-update-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Employee } from \"@/lib/types\"\nimport { useHRStore } from \"@/lib/store/hr-store\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Label } from \"@/components/ui/label\"\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { \n  Users,\n  AlertCircle,\n  RefreshCw\n} from \"lucide-react\"\n\ninterface BulkStatusUpdateSectionProps {\n  employees: Employee[]\n  selectedEmployees: string[]\n}\n\nexport function BulkStatusUpdateSection({ \n  employees, \n  selectedEmployees \n}: BulkStatusUpdateSectionProps) {\n  const { executeBulkOperation, clearSelection } = useHRStore()\n  const [newStatus, setNewStatus] = useState<Employee['status'] | \"\">(\"\")\n  const [isUpdating, setIsUpdating] = useState(false)\n\n  const selectedEmployeeData = employees.filter(emp => \n    selectedEmployees.includes(emp.id)\n  )\n\n  const statusOptions = [\n    { value: 'ACTIVE', label: 'Active', description: 'Employee is currently active' },\n    { value: 'TRANSFERRED', label: 'Transferred', description: 'Employee has been transferred' },\n    { value: 'PENDING_REMOVAL', label: 'Pending Removal', description: 'Employee is scheduled for removal' },\n    { value: 'ARCHIVED', label: 'Archived', description: 'Employee record is archived' }\n  ] as const\n\n  const getStatusBadge = (status: Employee['status']) => {\n    const variants = {\n      ACTIVE: \"default\",\n      TRANSFERRED: \"secondary\",\n      PENDING_REMOVAL: \"destructive\",\n      ARCHIVED: \"outline\"\n    } as const\n\n    return (\n      <Badge variant={variants[status]}>\n        {status.replace('_', ' ')}\n      </Badge>\n    )\n  }\n\n  const getStatusCounts = () => {\n    const counts = selectedEmployeeData.reduce((acc, emp) => {\n      acc[emp.status] = (acc[emp.status] || 0) + 1\n      return acc\n    }, {} as Record<Employee['status'], number>)\n\n    return counts\n  }\n\n  const handleStatusUpdate = async () => {\n    if (!newStatus || selectedEmployees.length === 0) return\n\n    setIsUpdating(true)\n    try {\n      await executeBulkOperation({\n        type: 'status_update',\n        employeeIds: selectedEmployees,\n        newStatus: newStatus as Employee['status']\n      })\n\n      clearSelection()\n      setNewStatus(\"\")\n      alert(`Successfully updated status for ${selectedEmployees.length} employees`)\n    } catch (error) {\n      console.error('Bulk status update failed:', error)\n      alert('Status update failed. Please try again.')\n    } finally {\n      setIsUpdating(false)\n    }\n  }\n\n  const statusCounts = getStatusCounts()\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Selection Summary */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Users className=\"h-5 w-5\" />\n            Selected Employees ({selectedEmployees.length})\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          {selectedEmployees.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <AlertCircle className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n              <p className=\"text-muted-foreground\">\n                No employees selected. Go to the Employees page to select employees for status update.\n              </p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {/* Current Status Distribution */}\n              <div>\n                <h4 className=\"font-medium mb-2\">Current Status Distribution:</h4>\n                <div className=\"flex flex-wrap gap-2\">\n                  {Object.entries(statusCounts).map(([status, count]) => (\n                    <div key={status} className=\"flex items-center gap-2\">\n                      {getStatusBadge(status as Employee['status'])}\n                      <span className=\"text-sm text-muted-foreground\">({count})</span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Employee List */}\n              <div className=\"grid gap-2 max-h-40 overflow-y-auto\">\n                {selectedEmployeeData.map((employee) => (\n                  <div key={employee.id} className=\"flex items-center justify-between p-2 bg-muted rounded\">\n                    <div>\n                      <div className=\"font-medium\">{employee.name}</div>\n                      <div className=\"text-sm text-muted-foreground\">{employee.email}</div>\n                    </div>\n                    {getStatusBadge(employee.status)}\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Status Update Configuration */}\n      {selectedEmployees.length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <RefreshCw className=\"h-5 w-5\" />\n              Status Update Configuration\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label>New Status</Label>\n                <Select\n                  value={newStatus}\n                  onValueChange={(value) => setNewStatus(value as Employee['status'])}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select new status\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {statusOptions.map((option) => (\n                      <SelectItem key={option.value} value={option.value}>\n                        <div>\n                          <div className=\"font-medium\">{option.label}</div>\n                          <div className=\"text-xs text-muted-foreground\">{option.description}</div>\n                        </div>\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Preview */}\n              {newStatus && (\n                <Card className=\"border-blue-200 bg-blue-50\">\n                  <CardContent className=\"pt-4\">\n                    <div className=\"flex items-center gap-2 mb-2\">\n                      <RefreshCw className=\"h-4 w-4 text-blue-600\" />\n                      <span className=\"font-medium text-blue-800\">Preview Changes</span>\n                    </div>\n                    <div className=\"text-sm text-blue-700\">\n                      {selectedEmployees.length} employees will be updated to: {getStatusBadge(newStatus as Employee['status'])}\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              <Button \n                onClick={handleStatusUpdate}\n                disabled={!newStatus || isUpdating}\n                className=\"w-full\"\n              >\n                {isUpdating ? 'Updating...' : `Update Status for ${selectedEmployees.length} Employees`}\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Status Descriptions */}\n      <Card className=\"border-gray-200 bg-gray-50\">\n        <CardHeader>\n          <CardTitle className=\"text-gray-800\">Status Descriptions</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-2 text-sm text-gray-700\">\n            {statusOptions.map((option) => (\n              <div key={option.value} className=\"flex items-center gap-2\">\n                {getStatusBadge(option.value)}\n                <span>{option.description}</span>\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Instructions */}\n      <Card className=\"border-blue-200 bg-blue-50\">\n        <CardHeader>\n          <CardTitle className=\"text-blue-800\">Status Update Instructions</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-2 text-sm text-blue-700\">\n            <p>1. Go to the <strong>Employees</strong> page and select the employees whose status you want to update</p>\n            <p>2. Return to this page and select the new status</p>\n            <p>3. Review the preview and confirm the update</p>\n            <p>4. All selected employees will have their status updated simultaneously</p>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAhBA;;;;;;;;;;AA2BO,SAAS,wBAAwB,EACtC,SAAS,EACT,iBAAiB,EACY;IAC7B,MAAM,EAAE,oBAAoB,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,aAAU,AAAD;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,uBAAuB,UAAU,MAAM,CAAC,CAAA,MAC5C,kBAAkB,QAAQ,CAAC,IAAI,EAAE;IAGnC,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAU,OAAO;YAAU,aAAa;QAA+B;QAChF;YAAE,OAAO;YAAe,OAAO;YAAe,aAAa;QAAgC;QAC3F;YAAE,OAAO;YAAmB,OAAO;YAAmB,aAAa;QAAoC;QACvG;YAAE,OAAO;YAAY,OAAO;YAAY,aAAa;QAA8B;KACpF;IAED,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAW;YACf,QAAQ;YACR,aAAa;YACb,iBAAiB;YACjB,UAAU;QACZ;QAEA,qBACE,8OAAC,0HAAA,CAAA,QAAK;YAAC,SAAS,QAAQ,CAAC,OAAO;sBAC7B,OAAO,OAAO,CAAC,KAAK;;;;;;IAG3B;IAEA,MAAM,kBAAkB;QACtB,MAAM,SAAS,qBAAqB,MAAM,CAAC,CAAC,KAAK;YAC/C,GAAG,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI;YAC3C,OAAO;QACT,GAAG,CAAC;QAEJ,OAAO;IACT;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,aAAa,kBAAkB,MAAM,KAAK,GAAG;QAElD,cAAc;QACd,IAAI;YACF,MAAM,qBAAqB;gBACzB,MAAM;gBACN,aAAa;gBACb,WAAW;YACb;YAEA;YACA,aAAa;YACb,MAAM,CAAC,gCAAgC,EAAE,kBAAkB,MAAM,CAAC,UAAU,CAAC;QAC/E,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,eAAe;IAErB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;gCACR,kBAAkB,MAAM;gCAAC;;;;;;;;;;;;kCAGlD,8OAAC,yHAAA,CAAA,cAAW;kCACT,kBAAkB,MAAM,KAAK,kBAC5B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;iDAKvC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmB;;;;;;sDACjC,8OAAC;4CAAI,WAAU;sDACZ,OAAO,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,QAAQ,MAAM,iBAChD,8OAAC;oDAAiB,WAAU;;wDACzB,eAAe;sEAChB,8OAAC;4DAAK,WAAU;;gEAAgC;gEAAE;gEAAM;;;;;;;;mDAFhD;;;;;;;;;;;;;;;;8CAShB,8OAAC;oCAAI,WAAU;8CACZ,qBAAqB,GAAG,CAAC,CAAC,yBACzB,8OAAC;4CAAsB,WAAU;;8DAC/B,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAe,SAAS,IAAI;;;;;;sEAC3C,8OAAC;4DAAI,WAAU;sEAAiC,SAAS,KAAK;;;;;;;;;;;;gDAE/D,eAAe,SAAS,MAAM;;2CALvB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;YAehC,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIrC,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0HAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,2HAAA,CAAA,SAAM;4CACL,OAAO;4CACP,eAAe,CAAC,QAAU,aAAa;;8DAEvC,8OAAC,2HAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,2HAAA,CAAA,gBAAa;8DACX,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC,2HAAA,CAAA,aAAU;4DAAoB,OAAO,OAAO,KAAK;sEAChD,cAAA,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;kFAAe,OAAO,KAAK;;;;;;kFAC1C,8OAAC;wEAAI,WAAU;kFAAiC,OAAO,WAAW;;;;;;;;;;;;2DAHrD,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;gCAYpC,2BACC,8OAAC,yHAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,8OAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;0DAE9C,8OAAC;gDAAI,WAAU;;oDACZ,kBAAkB,MAAM;oDAAC;oDAAgC,eAAe;;;;;;;;;;;;;;;;;;8CAMjF,8OAAC,2HAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,CAAC,aAAa;oCACxB,WAAU;8CAET,aAAa,gBAAgB,CAAC,kBAAkB,EAAE,kBAAkB,MAAM,CAAC,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;0BAQjG,8OAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;sCAAgB;;;;;;;;;;;kCAEvC,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC;oCAAuB,WAAU;;wCAC/B,eAAe,OAAO,KAAK;sDAC5B,8OAAC;sDAAM,OAAO,WAAW;;;;;;;mCAFjB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;0BAU9B,8OAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;sCAAgB;;;;;;;;;;;kCAEvC,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;wCAAE;sDAAa,8OAAC;sDAAO;;;;;;wCAAkB;;;;;;;8CAC1C,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 2848, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/lib/sample-data.ts"], "sourcesContent": ["import { Department, Employee } from './types'\n\nexport const sampleDepartments: Department[] = [\n  {\n    id: 'dept-001',\n    name: 'Engineering',\n    code: 'ENG',\n    capacity: 25,\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-01-15'),\n  },\n  {\n    id: 'dept-002',\n    name: 'Marketing',\n    code: 'MKT',\n    capacity: 15,\n    createdAt: new Date('2024-01-16'),\n    updatedAt: new Date('2024-01-16'),\n  },\n  {\n    id: 'dept-003',\n    name: 'Sales',\n    code: 'SAL',\n    capacity: 20,\n    createdAt: new Date('2024-01-17'),\n    updatedAt: new Date('2024-01-17'),\n  },\n  {\n    id: 'dept-004',\n    name: 'Human Resources',\n    code: 'HR',\n    capacity: 8,\n    createdAt: new Date('2024-01-18'),\n    updatedAt: new Date('2024-01-18'),\n  },\n  {\n    id: 'dept-005',\n    name: 'Finance',\n    code: 'FIN',\n    capacity: 12,\n    createdAt: new Date('2024-01-19'),\n    updatedAt: new Date('2024-01-19'),\n  },\n  {\n    id: 'dept-006',\n    name: 'Operations',\n    code: 'OPS',\n    capacity: 18,\n    createdAt: new Date('2024-01-20'),\n    updatedAt: new Date('2024-01-20'),\n  },\n]\n\nexport const sampleEmployees: Employee[] = [\n  // Engineering Department\n  {\n    id: 'ENG-001',\n    name: 'John Smith',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-03-15'),\n    transferHistory: [],\n    createdAt: new Date('2023-03-15'),\n    updatedAt: new Date('2023-03-15'),\n  },\n  {\n    id: 'ENG-002',\n    name: 'Sarah Johnson',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-05-20'),\n    transferHistory: [],\n    createdAt: new Date('2023-05-20'),\n    updatedAt: new Date('2023-05-20'),\n  },\n  {\n    id: 'ENG-003',\n    name: 'Michael Chen',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-07-10'),\n    transferHistory: [],\n    createdAt: new Date('2023-07-10'),\n    updatedAt: new Date('2023-07-10'),\n  },\n  {\n    id: 'ENG-004',\n    name: 'Emily Davis',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-09-05'),\n    transferHistory: [],\n    createdAt: new Date('2023-09-05'),\n    updatedAt: new Date('2023-09-05'),\n  },\n  {\n    id: 'ENG-005',\n    name: 'David Wilson',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-11-12'),\n    transferHistory: [],\n    createdAt: new Date('2023-11-12'),\n    updatedAt: new Date('2023-11-12'),\n  },\n  {\n    id: 'ENG-006',\n    name: 'Lisa Anderson',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-01-08'),\n    transferHistory: [],\n    createdAt: new Date('2024-01-08'),\n    updatedAt: new Date('2024-01-08'),\n  },\n  {\n    id: 'ENG-007',\n    name: 'Robert Taylor',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-02-14'),\n    transferHistory: [],\n    createdAt: new Date('2024-02-14'),\n    updatedAt: new Date('2024-02-14'),\n  },\n  {\n    id: 'ENG-008',\n    name: 'Jennifer Brown',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-03-22'),\n    transferHistory: [],\n    createdAt: new Date('2024-03-22'),\n    updatedAt: new Date('2024-03-22'),\n  },\n  {\n    id: 'ENG-009',\n    name: 'Christopher Lee',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-04-18'),\n    transferHistory: [],\n    createdAt: new Date('2024-04-18'),\n    updatedAt: new Date('2024-04-18'),\n  },\n  {\n    id: 'ENG-010',\n    name: 'Amanda White',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-05-25'),\n    transferHistory: [],\n    createdAt: new Date('2024-05-25'),\n    updatedAt: new Date('2024-05-25'),\n  },\n\n  // Marketing Department\n  {\n    id: 'MKT-001',\n    name: 'Jessica Garcia',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-04-10'),\n    transferHistory: [],\n    createdAt: new Date('2023-04-10'),\n    updatedAt: new Date('2023-04-10'),\n  },\n  {\n    id: 'MKT-002',\n    name: 'Daniel Martinez',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-06-15'),\n    transferHistory: [],\n    createdAt: new Date('2023-06-15'),\n    updatedAt: new Date('2023-06-15'),\n  },\n  {\n    id: 'MKT-003',\n    name: 'Ashley Rodriguez',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-08-20'),\n    transferHistory: [],\n    createdAt: new Date('2023-08-20'),\n    updatedAt: new Date('2023-08-20'),\n  },\n  {\n    id: 'MKT-004',\n    name: 'Matthew Thompson',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-10-12'),\n    transferHistory: [],\n    createdAt: new Date('2023-10-12'),\n    updatedAt: new Date('2023-10-12'),\n  },\n  {\n    id: 'MKT-005',\n    name: 'Stephanie Clark',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-01-30'),\n    transferHistory: [],\n    createdAt: new Date('2024-01-30'),\n    updatedAt: new Date('2024-01-30'),\n  },\n  {\n    id: 'MKT-006',\n    name: 'Kevin Lewis',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-03-15'),\n    transferHistory: [],\n    createdAt: new Date('2024-03-15'),\n    updatedAt: new Date('2024-03-15'),\n  },\n\n  // Sales Department\n  {\n    id: 'SAL-001',\n    name: 'Ryan Walker',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-02-28'),\n    transferHistory: [],\n    createdAt: new Date('2023-02-28'),\n    updatedAt: new Date('2023-02-28'),\n  },\n  {\n    id: 'SAL-002',\n    name: 'Nicole Hall',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-05-10'),\n    transferHistory: [],\n    createdAt: new Date('2023-05-10'),\n    updatedAt: new Date('2023-05-10'),\n  },\n  {\n    id: 'SAL-003',\n    name: 'Brandon Allen',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-07-25'),\n    transferHistory: [],\n    createdAt: new Date('2023-07-25'),\n    updatedAt: new Date('2023-07-25'),\n  },\n  {\n    id: 'SAL-004',\n    name: 'Megan Young',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-09-18'),\n    transferHistory: [],\n    createdAt: new Date('2023-09-18'),\n    updatedAt: new Date('2023-09-18'),\n  },\n  {\n    id: 'SAL-005',\n    name: 'Tyler King',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-11-30'),\n    transferHistory: [],\n    createdAt: new Date('2023-11-30'),\n    updatedAt: new Date('2023-11-30'),\n  },\n  {\n    id: 'SAL-006',\n    name: 'Rachel Wright',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-02-05'),\n    transferHistory: [],\n    createdAt: new Date('2024-02-05'),\n    updatedAt: new Date('2024-02-05'),\n  },\n  {\n    id: 'SAL-007',\n    name: 'Justin Lopez',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-04-12'),\n    transferHistory: [],\n    createdAt: new Date('2024-04-12'),\n    updatedAt: new Date('2024-04-12'),\n  },\n\n  // HR Department\n  {\n    id: 'HR-001',\n    name: 'Laura Hill',\n    email: '<EMAIL>',\n    departmentId: 'dept-004',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-01-20'),\n    transferHistory: [],\n    createdAt: new Date('2023-01-20'),\n    updatedAt: new Date('2023-01-20'),\n  },\n  {\n    id: 'HR-002',\n    name: 'Steven Green',\n    email: '<EMAIL>',\n    departmentId: 'dept-004',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-06-08'),\n    transferHistory: [],\n    createdAt: new Date('2023-06-08'),\n    updatedAt: new Date('2023-06-08'),\n  },\n  {\n    id: 'HR-003',\n    name: 'Kimberly Adams',\n    email: '<EMAIL>',\n    departmentId: 'dept-004',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-10-25'),\n    transferHistory: [],\n    createdAt: new Date('2023-10-25'),\n    updatedAt: new Date('2023-10-25'),\n  },\n\n  // Finance Department\n  {\n    id: 'FIN-001',\n    name: 'Andrew Baker',\n    email: '<EMAIL>',\n    departmentId: 'dept-005',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-03-08'),\n    transferHistory: [],\n    createdAt: new Date('2023-03-08'),\n    updatedAt: new Date('2023-03-08'),\n  },\n  {\n    id: 'FIN-002',\n    name: 'Michelle Nelson',\n    email: '<EMAIL>',\n    departmentId: 'dept-005',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-07-15'),\n    transferHistory: [],\n    createdAt: new Date('2023-07-15'),\n    updatedAt: new Date('2023-07-15'),\n  },\n  {\n    id: 'FIN-003',\n    name: 'Joshua Carter',\n    email: '<EMAIL>',\n    departmentId: 'dept-005',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-11-20'),\n    transferHistory: [],\n    createdAt: new Date('2023-11-20'),\n    updatedAt: new Date('2023-11-20'),\n  },\n  {\n    id: 'FIN-004',\n    name: 'Samantha Mitchell',\n    email: '<EMAIL>',\n    departmentId: 'dept-005',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-02-28'),\n    transferHistory: [],\n    createdAt: new Date('2024-02-28'),\n    updatedAt: new Date('2024-02-28'),\n  },\n\n  // Operations Department\n  {\n    id: 'OPS-001',\n    name: 'Gregory Perez',\n    email: '<EMAIL>',\n    departmentId: 'dept-006',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-04-05'),\n    transferHistory: [],\n    createdAt: new Date('2023-04-05'),\n    updatedAt: new Date('2023-04-05'),\n  },\n  {\n    id: 'OPS-002',\n    name: 'Heather Roberts',\n    email: '<EMAIL>',\n    departmentId: 'dept-006',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-08-12'),\n    transferHistory: [],\n    createdAt: new Date('2023-08-12'),\n    updatedAt: new Date('2023-08-12'),\n  },\n  {\n    id: 'OPS-003',\n    name: 'Nathan Turner',\n    email: '<EMAIL>',\n    departmentId: 'dept-006',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-12-01'),\n    transferHistory: [],\n    createdAt: new Date('2023-12-01'),\n    updatedAt: new Date('2023-12-01'),\n  },\n  {\n    id: 'OPS-004',\n    name: 'Brittany Phillips',\n    email: '<EMAIL>',\n    departmentId: 'dept-006',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-03-10'),\n    transferHistory: [],\n    createdAt: new Date('2024-03-10'),\n    updatedAt: new Date('2024-03-10'),\n  },\n\n  // Free Bucket (Unassigned) Employees\n  {\n    id: 'FB-001',\n    name: 'Alex Campbell',\n    email: '<EMAIL>',\n    departmentId: null,\n    status: 'ACTIVE',\n    hireDate: new Date('2024-06-01'),\n    transferHistory: [],\n    createdAt: new Date('2024-06-01'),\n    updatedAt: new Date('2024-06-01'),\n  },\n  {\n    id: 'FB-002',\n    name: 'Morgan Parker',\n    email: '<EMAIL>',\n    departmentId: null,\n    status: 'ACTIVE',\n    hireDate: new Date('2024-06-15'),\n    transferHistory: [],\n    createdAt: new Date('2024-06-15'),\n    updatedAt: new Date('2024-06-15'),\n  },\n]\n\n// Helper function to populate departments with their employees\nexport const getDepartmentsWithEmployees = (): Department[] => {\n  return sampleDepartments.map(dept => ({\n    ...dept,\n    employees: sampleEmployees.filter(emp => emp.departmentId === dept.id)\n  }))\n}\n\n// Helper function to get free bucket employees\nexport const getFreeBucketEmployees = (): Employee[] => {\n  return sampleEmployees.filter(emp => emp.departmentId === null)\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,oBAAkC;IAC7C;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAEM,MAAM,kBAA8B;IACzC,yBAAyB;IACzB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,uBAAuB;IACvB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,qBAAqB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,wBAAwB;IACxB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,qCAAqC;IACrC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,8BAA8B;IACzC,OAAO,kBAAkB,GAAG,CAAC,CAAA,OAAQ,CAAC;YACpC,GAAG,IAAI;YACP,WAAW,gBAAgB,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK,KAAK,EAAE;QACvE,CAAC;AACH;AAGO,MAAM,yBAAyB;IACpC,OAAO,gBAAgB,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK;AAC5D", "debugId": null}}, {"offset": {"line": 3322, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/lib/hooks/use-sample-data.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { useHRStore } from '@/lib/store/hr-store'\nimport { \n  sampleEmployees, \n  getDepartmentsWithEmployees, \n  getFreeBucketEmployees \n} from '@/lib/sample-data'\n\nexport function useSampleData() {\n  const { setEmployees, setDepartments } = useHRStore()\n\n  useEffect(() => {\n    // Initialize with sample data\n    const departmentsWithEmployees = getDepartmentsWithEmployees()\n    const freeBucketEmployees = getFreeBucketEmployees()\n    \n    setEmployees(sampleEmployees)\n    setDepartments(departmentsWithEmployees)\n    \n    // Note: Free bucket employees are automatically calculated in the store\n    // based on employees with departmentId === null\n    \n    console.log('Sample data loaded:', {\n      employees: sampleEmployees.length,\n      departments: departmentsWithEmployees.length,\n      freeBucket: freeBucketEmployees.length\n    })\n  }, [setEmployees, setDepartments])\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAMO,SAAS;IACd,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,aAAU,AAAD;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8BAA8B;QAC9B,MAAM,2BAA2B,CAAA,GAAA,qHAAA,CAAA,8BAA2B,AAAD;QAC3D,MAAM,sBAAsB,CAAA,GAAA,qHAAA,CAAA,yBAAsB,AAAD;QAEjD,aAAa,qHAAA,CAAA,kBAAe;QAC5B,eAAe;QAEf,wEAAwE;QACxE,gDAAgD;QAEhD,QAAQ,GAAG,CAAC,uBAAuB;YACjC,WAAW,qHAAA,CAAA,kBAAe,CAAC,MAAM;YACjC,aAAa,yBAAyB,MAAM;YAC5C,YAAY,oBAAoB,MAAM;QACxC;IACF,GAAG;QAAC;QAAc;KAAe;AACnC", "debugId": null}}, {"offset": {"line": 3355, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/app/%28dashboard%29/bulk/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useSession } from \"next-auth/react\"\nimport { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { CSVImportSection } from \"@/components/bulk/csv-import-section\"\nimport { CSVExportSection } from \"@/components/bulk/csv-export-section\"\nimport { BulkTransferSection } from \"@/components/bulk/bulk-transfer-section\"\nimport { BulkStatusUpdateSection } from \"@/components/bulk/bulk-status-update-section\"\nimport { useHRStore } from \"@/lib/store/hr-store\"\nimport { useSampleData } from \"@/lib/hooks/use-sample-data\"\nimport { \n  Upload, \n  Download, \n  ArrowRight,\n  Users,\n  FileText,\n  AlertCircle\n} from \"lucide-react\"\n\nexport default function BulkOperationsPage() {\n  const { data: session } = useSession()\n  const { employees, departments, selectedEmployees } = useHRStore()\n  const [activeTab, setActiveTab] = useState<'import' | 'export' | 'transfer' | 'status'>('import')\n\n  // Load sample data\n  useSampleData()\n\n  const tabs = [\n    {\n      id: 'import' as const,\n      label: 'Import CSV',\n      icon: Upload,\n      description: 'Import employees from CSV file'\n    },\n    {\n      id: 'export' as const,\n      label: 'Export CSV',\n      icon: Download,\n      description: 'Export employees to CSV file'\n    },\n    {\n      id: 'transfer' as const,\n      label: 'Bulk Transfer',\n      icon: ArrowRight,\n      description: 'Transfer multiple employees between departments'\n    },\n    {\n      id: 'status' as const,\n      label: 'Status Update',\n      icon: Users,\n      description: 'Update status for multiple employees'\n    }\n  ]\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Bulk Operations</h1>\n          <p className=\"text-muted-foreground\">\n            Perform bulk operations on employee data including CSV import/export and mass updates.\n          </p>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid gap-4 md:grid-cols-4\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total Employees</CardTitle>\n            <Users className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{employees.length}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Available for operations\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Selected</CardTitle>\n            <Users className=\"h-4 w-4 text-blue-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{selectedEmployees.length}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Currently selected\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Departments</CardTitle>\n            <FileText className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{departments.length}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Available departments\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Unassigned</CardTitle>\n            <AlertCircle className=\"h-4 w-4 text-orange-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">\n              {employees.filter(emp => emp.departmentId === null).length}\n            </div>\n            <p className=\"text-xs text-muted-foreground\">\n              In free bucket\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Tab Navigation */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex space-x-1 rounded-lg bg-muted p-1\">\n            {tabs.map((tab) => {\n              const Icon = tab.icon\n              return (\n                <Button\n                  key={tab.id}\n                  variant={activeTab === tab.id ? \"default\" : \"ghost\"}\n                  size=\"sm\"\n                  onClick={() => setActiveTab(tab.id)}\n                  className=\"flex-1\"\n                >\n                  <Icon className=\"h-4 w-4 mr-2\" />\n                  {tab.label}\n                </Button>\n              )\n            })}\n          </div>\n        </CardHeader>\n\n        <CardContent>\n          <div className=\"mb-4\">\n            <h3 className=\"text-lg font-medium\">\n              {tabs.find(tab => tab.id === activeTab)?.label}\n            </h3>\n            <p className=\"text-sm text-muted-foreground\">\n              {tabs.find(tab => tab.id === activeTab)?.description}\n            </p>\n          </div>\n\n          {/* Tab Content */}\n          {activeTab === 'import' && (\n            <CSVImportSection departments={departments} />\n          )}\n\n          {activeTab === 'export' && (\n            <CSVExportSection \n              employees={employees} \n              departments={departments} \n            />\n          )}\n\n          {activeTab === 'transfer' && (\n            <BulkTransferSection \n              employees={employees}\n              departments={departments}\n              selectedEmployees={selectedEmployees}\n            />\n          )}\n\n          {activeTab === 'status' && (\n            <BulkStatusUpdateSection \n              employees={employees}\n              selectedEmployees={selectedEmployees}\n            />\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Help Section */}\n      <Card className=\"border-blue-200 bg-blue-50\">\n        <CardHeader>\n          <CardTitle className=\"text-blue-800 flex items-center gap-2\">\n            <AlertCircle className=\"h-5 w-5\" />\n            Bulk Operations Guidelines\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-2 text-sm text-blue-700\">\n            <p><strong>CSV Import:</strong> Upload a CSV file with employee data. Required columns: name, email. Optional: departmentCode, status.</p>\n            <p><strong>CSV Export:</strong> Download employee data in CSV format with filtering options.</p>\n            <p><strong>Bulk Transfer:</strong> Move multiple employees between departments. Select employees from the Employees page first.</p>\n            <p><strong>Status Update:</strong> Change the status of multiple employees at once (Active, Transferred, Archived, etc.).</p>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAZA;;;;;;;;;;;;;AAqBe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,aAAU,AAAD;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+C;IAExF,mBAAmB;IACnB,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IAEZ,MAAM,OAAO;QACX;YACE,IAAI;YACJ,OAAO;YACP,MAAM,sMAAA,CAAA,SAAM;YACZ,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,0MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,kNAAA,CAAA,aAAU;YAChB,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM,oMAAA,CAAA,QAAK;YACX,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAOzC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;0CAEnB,8OAAC,yHAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAsB,UAAU,MAAM;;;;;;kDACrD,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAMjD,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;0CAEnB,8OAAC,yHAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAsB,kBAAkB,MAAM;;;;;;kDAC7D,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAMjD,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,8OAAC,yHAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDAAsB,YAAY,MAAM;;;;;;kDACvD,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAMjD,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;0CAEzB,8OAAC,yHAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDACZ,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK,MAAM,MAAM;;;;;;kDAE5D,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC;4BAAI,WAAU;sCACZ,KAAK,GAAG,CAAC,CAAC;gCACT,MAAM,OAAO,IAAI,IAAI;gCACrB,qBACE,8OAAC,2HAAA,CAAA,SAAM;oCAEL,SAAS,cAAc,IAAI,EAAE,GAAG,YAAY;oCAC5C,MAAK;oCACL,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;;;;;;wCACf,IAAI,KAAK;;mCAPL,IAAI,EAAE;;;;;4BAUjB;;;;;;;;;;;kCAIJ,8OAAC,yHAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY;;;;;;kDAE3C,8OAAC;wCAAE,WAAU;kDACV,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY;;;;;;;;;;;;4BAK5C,cAAc,0BACb,8OAAC,+IAAA,CAAA,mBAAgB;gCAAC,aAAa;;;;;;4BAGhC,cAAc,0BACb,8OAAC,+IAAA,CAAA,mBAAgB;gCACf,WAAW;gCACX,aAAa;;;;;;4BAIhB,cAAc,4BACb,8OAAC,kJAAA,CAAA,sBAAmB;gCAClB,WAAW;gCACX,aAAa;gCACb,mBAAmB;;;;;;4BAItB,cAAc,0BACb,8OAAC,0JAAA,CAAA,0BAAuB;gCACtB,WAAW;gCACX,mBAAmB;;;;;;;;;;;;;;;;;;0BAO3B,8OAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIvC,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAAoB;;;;;;;8CAC/B,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAAoB;;;;;;;8CAC/B,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAAuB;;;;;;;8CAClC,8OAAC;;sDAAE,8OAAC;sDAAO;;;;;;wCAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9C", "debugId": null}}]}