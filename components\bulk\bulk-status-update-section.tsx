"use client"

import { useState } from "react"
import { Employee } from "@/lib/types"
import { useHRStore } from "@/lib/store/hr-store"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  Users,
  AlertCircle,
  RefreshCw
} from "lucide-react"

interface BulkStatusUpdateSectionProps {
  employees: Employee[]
  selectedEmployees: string[]
}

export function BulkStatusUpdateSection({ 
  employees, 
  selectedEmployees 
}: BulkStatusUpdateSectionProps) {
  const { executeBulkOperation, clearSelection } = useHRStore()
  const [newStatus, setNewStatus] = useState<Employee['status'] | "">("")
  const [isUpdating, setIsUpdating] = useState(false)

  const selectedEmployeeData = employees.filter(emp => 
    selectedEmployees.includes(emp.id)
  )

  const statusOptions = [
    { value: 'ACTIVE', label: 'Active', description: 'Employee is currently active' },
    { value: 'TRANSFERRED', label: 'Transferred', description: 'Employee has been transferred' },
    { value: 'PENDING_REMOVAL', label: 'Pending Removal', description: 'Employee is scheduled for removal' },
    { value: 'ARCHIVED', label: 'Archived', description: 'Employee record is archived' }
  ] as const

  const getStatusBadge = (status: Employee['status']) => {
    const variants = {
      ACTIVE: "default",
      TRANSFERRED: "secondary",
      PENDING_REMOVAL: "destructive",
      ARCHIVED: "outline"
    } as const

    return (
      <Badge variant={variants[status]}>
        {status.replace('_', ' ')}
      </Badge>
    )
  }

  const getStatusCounts = () => {
    const counts = selectedEmployeeData.reduce((acc, emp) => {
      acc[emp.status] = (acc[emp.status] || 0) + 1
      return acc
    }, {} as Record<Employee['status'], number>)

    return counts
  }

  const handleStatusUpdate = async () => {
    if (!newStatus || selectedEmployees.length === 0) return

    setIsUpdating(true)
    try {
      await executeBulkOperation({
        type: 'status_update',
        employeeIds: selectedEmployees,
        newStatus: newStatus as Employee['status']
      })

      clearSelection()
      setNewStatus("")
      alert(`Successfully updated status for ${selectedEmployees.length} employees`)
    } catch (error) {
      console.error('Bulk status update failed:', error)
      alert('Status update failed. Please try again.')
    } finally {
      setIsUpdating(false)
    }
  }

  const statusCounts = getStatusCounts()

  return (
    <div className="space-y-6">
      {/* Selection Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Selected Employees ({selectedEmployees.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {selectedEmployees.length === 0 ? (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                No employees selected. Go to the Employees page to select employees for status update.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Current Status Distribution */}
              <div>
                <h4 className="font-medium mb-2">Current Status Distribution:</h4>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(statusCounts).map(([status, count]) => (
                    <div key={status} className="flex items-center gap-2">
                      {getStatusBadge(status as Employee['status'])}
                      <span className="text-sm text-muted-foreground">({count})</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Employee List */}
              <div className="grid gap-2 max-h-40 overflow-y-auto">
                {selectedEmployeeData.map((employee) => (
                  <div key={employee.id} className="flex items-center justify-between p-2 bg-muted rounded">
                    <div>
                      <div className="font-medium">{employee.name}</div>
                      <div className="text-sm text-muted-foreground">{employee.email}</div>
                    </div>
                    {getStatusBadge(employee.status)}
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Status Update Configuration */}
      {selectedEmployees.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5" />
              Status Update Configuration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>New Status</Label>
                <Select
                  value={newStatus}
                  onValueChange={(value) => setNewStatus(value as Employee['status'])}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select new status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div>
                          <div className="font-medium">{option.label}</div>
                          <div className="text-xs text-muted-foreground">{option.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Preview */}
              {newStatus && (
                <Card className="border-blue-200 bg-blue-50">
                  <CardContent className="pt-4">
                    <div className="flex items-center gap-2 mb-2">
                      <RefreshCw className="h-4 w-4 text-blue-600" />
                      <span className="font-medium text-blue-800">Preview Changes</span>
                    </div>
                    <div className="text-sm text-blue-700">
                      {selectedEmployees.length} employees will be updated to: {getStatusBadge(newStatus as Employee['status'])}
                    </div>
                  </CardContent>
                </Card>
              )}

              <Button 
                onClick={handleStatusUpdate}
                disabled={!newStatus || isUpdating}
                className="w-full"
              >
                {isUpdating ? 'Updating...' : `Update Status for ${selectedEmployees.length} Employees`}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Status Descriptions */}
      <Card className="border-gray-200 bg-gray-50">
        <CardHeader>
          <CardTitle className="text-gray-800">Status Descriptions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-gray-700">
            {statusOptions.map((option) => (
              <div key={option.value} className="flex items-center gap-2">
                {getStatusBadge(option.value)}
                <span>{option.description}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="text-blue-800">Status Update Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-blue-700">
            <p>1. Go to the <strong>Employees</strong> page and select the employees whose status you want to update</p>
            <p>2. Return to this page and select the new status</p>
            <p>3. Review the preview and confirm the update</p>
            <p>4. All selected employees will have their status updated simultaneously</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
