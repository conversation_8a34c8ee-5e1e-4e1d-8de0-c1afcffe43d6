"use client"

import { useState } from "react"
import { Department, Employee } from "@/lib/types"
import { useHRStore } from "@/lib/store/hr-store"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { calculateCapacityUtilization } from "@/lib/utils"
import { 
  Users,
  AlertTriangle,
  CheckCircle,
  Building2
} from "lucide-react"

interface AssignToDepartmentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedEmployees: string[]
  departments: Department[]
}

export function AssignToDepartmentDialog({ 
  open, 
  onOpenChange, 
  selectedEmployees,
  departments 
}: AssignToDepartmentDialogProps) {
  const { employees, executeBulkOperation, clearSelection } = useHRStore()
  const [targetDepartment, setTargetDepartment] = useState<string>("")
  const [isAssigning, setIsAssigning] = useState(false)

  const selectedEmployeeData = employees.filter(emp => 
    selectedEmployees.includes(emp.id)
  )

  const getTargetDepartment = () => {
    return departments.find(dept => dept.id === targetDepartment) || null
  }

  const getCapacityInfo = () => {
    const target = getTargetDepartment()
    if (!target) return null

    const currentEmployees = employees.filter(emp => emp.departmentId === target.id)
    const afterAssignment = currentEmployees.length + selectedEmployees.length
    const utilization = calculateCapacityUtilization(afterAssignment, target.capacity)

    return {
      current: currentEmployees.length,
      afterAssignment,
      capacity: target.capacity,
      utilization,
      isOverCapacity: afterAssignment > target.capacity,
      isNearCapacity: utilization >= 80 && utilization < 100
    }
  }

  const handleAssign = async () => {
    if (!targetDepartment || selectedEmployees.length === 0) return

    setIsAssigning(true)
    try {
      await executeBulkOperation({
        type: 'transfer',
        employeeIds: selectedEmployees,
        targetDepartmentId: targetDepartment
      })

      clearSelection()
      setTargetDepartment("")
      onOpenChange(false)
      alert(`Successfully assigned ${selectedEmployees.length} employees`)
    } catch (error) {
      console.error('Assignment failed:', error)
      alert('Assignment failed. Please try again.')
    } finally {
      setIsAssigning(false)
    }
  }

  const handleClose = () => {
    setTargetDepartment("")
    onOpenChange(false)
  }

  const capacityInfo = getCapacityInfo()

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Assign Employees to Department
          </DialogTitle>
          <DialogDescription>
            Assign {selectedEmployees.length} selected employees to a department.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Selected Employees */}
          <div className="space-y-2">
            <Label className="text-base font-medium">Selected Employees ({selectedEmployees.length})</Label>
            <Card>
              <CardContent className="pt-4">
                <div className="grid gap-2 max-h-32 overflow-y-auto">
                  {selectedEmployeeData.map((employee) => (
                    <div key={employee.id} className="flex items-center justify-between p-2 bg-muted rounded">
                      <div>
                        <div className="font-medium">{employee.name}</div>
                        <div className="text-sm text-muted-foreground">{employee.email}</div>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {employee.id}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Department Selection */}
          <div className="space-y-2">
            <Label htmlFor="department">Target Department</Label>
            <Select
              value={targetDepartment}
              onValueChange={setTargetDepartment}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a department" />
              </SelectTrigger>
              <SelectContent>
                {departments.map((dept) => {
                  const currentEmployees = employees.filter(emp => emp.departmentId === dept.id)
                  const utilization = calculateCapacityUtilization(currentEmployees.length, dept.capacity)
                  
                  return (
                    <SelectItem key={dept.id} value={dept.id}>
                      <div className="flex items-center justify-between w-full">
                        <div>
                          <div className="font-medium">{dept.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {currentEmployees.length}/{dept.capacity} ({utilization}%)
                          </div>
                        </div>
                      </div>
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Capacity Information */}
          {capacityInfo && (
            <Card className={`border-2 ${
              capacityInfo.isOverCapacity 
                ? 'border-red-200 bg-red-50' 
                : capacityInfo.isNearCapacity 
                ? 'border-orange-200 bg-orange-50'
                : 'border-green-200 bg-green-50'
            }`}>
              <CardContent className="pt-4">
                <div className="flex items-center gap-2 mb-3">
                  {capacityInfo.isOverCapacity ? (
                    <AlertTriangle className="h-5 w-5 text-red-600" />
                  ) : (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  )}
                  <span className="font-medium">
                    Capacity Analysis
                  </span>
                </div>
                
                <div className="grid gap-2 text-sm">
                  <div className="flex justify-between">
                    <span>Current employees:</span>
                    <span className="font-medium">{capacityInfo.current}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>After assignment:</span>
                    <span className="font-medium">{capacityInfo.afterAssignment}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Department capacity:</span>
                    <span className="font-medium">{capacityInfo.capacity}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Utilization:</span>
                    <span className={`font-medium ${
                      capacityInfo.isOverCapacity ? 'text-red-600' : 
                      capacityInfo.isNearCapacity ? 'text-orange-600' : 
                      'text-green-600'
                    }`}>
                      {capacityInfo.utilization}%
                    </span>
                  </div>
                </div>

                {capacityInfo.isOverCapacity && (
                  <div className="mt-3 p-2 bg-red-100 rounded text-sm text-red-700">
                    ⚠️ This assignment will exceed department capacity by {capacityInfo.afterAssignment - capacityInfo.capacity} employees.
                  </div>
                )}

                {capacityInfo.isNearCapacity && !capacityInfo.isOverCapacity && (
                  <div className="mt-3 p-2 bg-orange-100 rounded text-sm text-orange-700">
                    ⚠️ This assignment will bring the department near capacity ({capacityInfo.utilization}%).
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
        
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isAssigning}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleAssign}
            disabled={!targetDepartment || isAssigning}
          >
            {isAssigning ? 'Assigning...' : `Assign ${selectedEmployees.length} Employees`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
