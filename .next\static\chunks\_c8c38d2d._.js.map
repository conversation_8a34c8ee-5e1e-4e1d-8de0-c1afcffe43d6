{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function generateEmployeeId(departmentCode: string, sequence: number): string {\n  return `${departmentCode}-${sequence.toString().padStart(3, '0')}`\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric'\n  }).format(date)\n}\n\nexport function calculateCapacityUtilization(current: number, capacity: number): number {\n  return Math.round((current / capacity) * 100)\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,mBAAmB,cAAsB,EAAE,QAAgB;IACzE,OAAO,AAAC,GAAoB,OAAlB,gBAAe,KAAwC,OAArC,SAAS,QAAQ,GAAG,QAAQ,CAAC,GAAG;AAC9D;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,6BAA6B,OAAe,EAAE,QAAgB;IAC5E,OAAO,KAAK,KAAK,CAAC,AAAC,UAAU,WAAY;AAC3C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/layout/G%3A/Augment%20code/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { useSession } from \"next-auth/react\"\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\nimport {\n  LayoutDashboard,\n  Users,\n  Building2,\n  Upload,\n  Settings,\n  Archive,\n  UserCheck\n} from \"lucide-react\"\n\nconst navigation = [\n  {\n    name: \"Dashboard\",\n    href: \"/dashboard\",\n    icon: LayoutDashboard,\n    roles: [\"ADMIN\", \"HR_MANAGER\", \"VIEWER\"]\n  },\n  {\n    name: \"Employees\",\n    href: \"/employees\",\n    icon: Users,\n    roles: [\"ADMIN\", \"HR_MANAGER\", \"VIEWER\"]\n  },\n  {\n    name: \"Departments\",\n    href: \"/departments\",\n    icon: Building2,\n    roles: [\"ADMIN\", \"HR_MANAGER\", \"VIEWER\"]\n  },\n  {\n    name: \"Bulk Operations\",\n    href: \"/bulk\",\n    icon: Upload,\n    roles: [\"ADMIN\", \"HR_MANAGER\"]\n  },\n  {\n    name: \"Free Bucket\",\n    href: \"/free-bucket\",\n    icon: Archive,\n    roles: [\"ADMIN\", \"HR_MANAGER\"]\n  },\n  {\n    name: \"User Management\",\n    href: \"/admin/users\",\n    icon: UserCheck,\n    roles: [\"ADMIN\"]\n  },\n  {\n    name: \"Settings\",\n    href: \"/settings\",\n    icon: Settings,\n    roles: [\"ADMIN\", \"HR_MANAGER\"]\n  }\n]\n\nexport function Sidebar() {\n  const pathname = usePathname()\n  const { data: session } = useSession()\n  const userRole = session?.user?.role\n\n  const filteredNavigation = navigation.filter(item =>\n    item.roles.includes(userRole as string)\n  )\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-card border-r\">\n      <div className=\"flex h-16 items-center px-6 border-b\">\n        <h1 className=\"text-xl font-bold\">HR Synergy</h1>\n      </div>\n      \n      <nav className=\"flex-1 space-y-1 p-4\">\n        {filteredNavigation.map((item) => {\n          const isActive = pathname === item.href || pathname.startsWith(item.href + \"/\")\n          \n          return (\n            <Link key={item.name} href={item.href}>\n              <Button\n                variant={isActive ? \"secondary\" : \"ghost\"}\n                className={cn(\n                  \"w-full justify-start\",\n                  isActive && \"bg-secondary\"\n                )}\n              >\n                <item.icon className=\"mr-2 h-4 w-4\" />\n                {item.name}\n              </Button>\n            </Link>\n          )\n        })}\n      </nav>\n    </div>\n  )\n}"], "names": ["c", "_c", "Link", "usePathname", "useSession", "cn", "<PERSON><PERSON>", "LayoutDashboard", "Users", "Building2", "Upload", "Settings", "Archive", "UserCheck", "navigation", "name", "href", "icon", "roles", "Sidebar", "$", "$i", "Symbol", "for", "pathname", "data", "session", "userRole", "user", "role", "t0", "t1", "t2", "t3", "t4", "item", "includes", "filteredNavigation", "filter", "t5", "item_0", "isActive", "startsWith", "map"], "mappings": ";;;;AAAY,SAAAA,CAAA,IAAAC,EAAA;AAEZ,OAAOC,IAAI,MAAM,WAAW;AAC5B,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,EAAE,QAAQ,aAAa;AAChC,SAASC,MAAM,QAAQ,wBAAwB;;;;;;;AAC/C,SACEC,eAAe,EACfC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,SAAS,QACJ,cAAc;;;AAfrB,YAAY;;;;;;;;AAiBZ,MAAMC,UAAU,GAAG;IACjB;QACEC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,YAAY;QAClBC,IAAI,kOAAEV,kBAAe;QACrBW,KAAK,EAAE;YAAC,OAAO;YAAE,YAAY;YAAE,QAAQ;SAAA;IACzC,CAAC;IACD;QACEH,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE,YAAY;QAClBC,IAAI,0MAAET,QAAK;QACXU,KAAK,EAAE;YAAC,OAAO;YAAE,YAAY;YAAE,QAAQ;SAAA;IACzC,CAAC;IACD;QACEH,IAAI,EAAE,aAAa;QACnBC,IAAI,EAAE,cAAc;QACpBC,IAAI,qNAAER,aAAS;QACfS,KAAK,EAAE;YAAC,OAAO;YAAE,YAAY;YAAE,QAAQ;SAAA;IACzC,CAAC;IACD;QACEH,IAAI,EAAE,iBAAiB;QACvBC,IAAI,EAAE,OAAO;QACbC,IAAI,4MAAEP,SAAM;QACZQ,KAAK,EAAE;YAAC,OAAO;YAAE,YAAY;SAAA;IAC/B,CAAC;IACD;QACEH,IAAI,EAAE,aAAa;QACnBC,IAAI,EAAE,cAAc;QACpBC,IAAI,8MAAEL,UAAO;QACbM,KAAK,EAAE;YAAC,OAAO;YAAE,YAAY;SAAA;IAC/B,CAAC;IACD;QACEH,IAAI,EAAE,iBAAiB;QACvBC,IAAI,EAAE,cAAc;QACpBC,IAAI,sNAAEJ,YAAS;QACfK,KAAK,EAAE;YAAC,OAAO;SAAA;IACjB,CAAC;IACD;QACEH,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE,WAAW;QACjBC,IAAI,gNAAEN,WAAQ;QACdO,KAAK,EAAE;YAAC,OAAO;YAAE,YAAY;SAAA;IAC/B,CAAC;CACF;AAEM;;;IAAA,MAAAE,CAAA,mLAAAnB,IAAAA,AAAA,EAAA;IAAA,IAAAmB,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IACL,MAAAI,QAAA,OAAiBrB,oJAAAA;IACjB,MAAA,EAAAsB,IAAA,EAAAC,OAAAA,EAAA,OAA0BtB,+JAAAA;IAC1B,MAAAuB,QAAA,8EAAwBC,IAAA,kDAAPF,OAAO,OAAAG,IAAA;IAAY,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAb,CAAA,CAAA,EAAA,KAAAI,QAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAO,QAAA,EAAA;QAAA,IAAAO,EAAA;QAAA,IAAAd,CAAA,CAAA,EAAA,KAAAO,QAAA,EAAA;YAESO,EAAA,IAAAC,IAAA,GAC3CA,IAAI,CAAAjB,KAAA,CAAAkB,QAAA,CAAgBT,QAAkB,CAAC;YAAAP,CAAA,CAAA,EAAA,GAAAO,QAAA;YAAAP,CAAA,CAAA,EAAA,GAAAc,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAAd,CAAA,CAAA,EAAA;QAAA;QADzC,MAAAiB,kBAAA,GAA2BvB,UAAA,CAAAwB,MAAA,CAAkBJ,EAE7C,CAAC;QAGgBF,EAAA,GAAA,4CAA4C;QAAA,IAAAZ,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;YACzDU,EAAA,iBAAA,6LAAA,GAEM;gBAFS,SAAsC,EAAtC,sCAAsC;wCACnD,6LAAA,EAAiD;oBAAnC,SAAmB,EAAnB,mBAAmB;8BAAC,UAAU,EAA5C,EAAiD,CACnD,EAFA,GAEM;;;;;;;;;;;YAAAb,CAAA,CAAA,EAAA,GAAAa,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAAb,CAAA,CAAA,EAAA;QAAA;QAESU,EAAA,GAAA,sBAAsB;QAAA,IAAAS,EAAA;QAAA,IAAAnB,CAAA,CAAA,GAAA,KAAAI,QAAA,EAAA;YACXe,EAAA,IAAAC,MAAA;gBACtB,MAAAC,QAAA,GAAiBjB,QAAQ,KAAKW,MAAI,CAAAnB,IAAK,IAAIQ,QAAQ,CAAAkB,UAAA,CAAYP,MAAI,CAAAnB,IAAA,GAAQ,GAAG,CAAC;gBAAA,qBAG7E,6VAAC,UAAI,CAAM,GAAS,CAAT;oBAAiB,IAAS,CAAT,CAAAmB,MAAI,CAAAnB,IAAI,CAAC;4CACnC,6LAAC,wIAAM;wBACI,OAAgC,CAAhC,CAAAyB,QAAQ,GAAG,WAAW,GAAG,OAAM,CAAC;wBAC9B,SAGV,CAHU,qHAAApC,KAAAA,AAAA,EACT,sBAAsB,EACtBoC,QAAQ,IAAI,cACd,EAAC;;0CAED,6LAAA,OAAA,IAAA;gCAAqB,SAAc,EAAd,cAAc,GAClC;;;;;;4BAAAN,MAAI,CAAApB,IAAI,CACX,EATC,MAAM,CAUT,EAXC,IAAI,CAWE;;;;;;;mBAXIoB,MAAI,CAAApB,IAAI,CAAC;;;;;YAWb;YAEVK,CAAA,CAAA,GAAA,GAAAI,QAAA;YAAAJ,CAAA,CAAA,GAAA,GAAAmB,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAAnB,CAAA,CAAA,GAAA;QAAA;QAjBAW,EAAA,GAAAM,kBAAkB,CAAAM,GAAA,CAAKJ,EAiBvB,CAAC;QAAAnB,CAAA,CAAA,EAAA,GAAAI,QAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAO,QAAA;QAAAP,CAAA,CAAA,EAAA,GAAAU,EAAA;QAAAV,CAAA,CAAA,EAAA,GAAAW,EAAA;QAAAX,CAAA,CAAA,EAAA,GAAAY,EAAA;QAAAZ,CAAA,CAAA,EAAA,GAAAa,EAAA;IAAA,OAAA;QAAAH,EAAA,GAAAV,CAAA,CAAA,EAAA;QAAAW,EAAA,GAAAX,CAAA,CAAA,EAAA;QAAAY,EAAA,GAAAZ,CAAA,CAAA,EAAA;QAAAa,EAAA,GAAAb,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAc,EAAA;IAAA,IAAAd,CAAA,CAAA,GAAA,KAAAU,EAAA,IAAAV,CAAA,CAAA,GAAA,KAAAW,EAAA,EAAA;QAlBJG,EAAA,iBAAA,6LAAA,GAmBM;YAnBS,SAAsB,CAAtB,CAAAJ,EAAqB,CAAC,CAClC;sBAAAC,EAiBA,CACH,EAnBA,GAmBM;;;;;;QAAAX,CAAA,CAAA,GAAA,GAAAU,EAAA;QAAAV,CAAA,CAAA,GAAA,GAAAW,EAAA;QAAAX,CAAA,CAAA,GAAA,GAAAc,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAd,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAmB,EAAA;IAAA,IAAAnB,CAAA,CAAA,GAAA,KAAAY,EAAA,IAAAZ,CAAA,CAAA,GAAA,KAAAa,EAAA,IAAAb,CAAA,CAAA,GAAA,KAAAc,EAAA,EAAA;QAxBRK,EAAA,iBAAA,6LAAA,GAyBM;YAzBS,SAA4C,CAA5C,CAAAP,EAA2C,CAAC,CACzD;;gBAAAC,EAEK,CAEL;gBAAAC,EAmBK,CACP,EAzBA,GAyBM;;;;;;;QAAAd,CAAA,CAAA,GAAA,GAAAY,EAAA;QAAAZ,CAAA,CAAA,GAAA,GAAAa,EAAA;QAAAb,CAAA,CAAA,GAAA,GAAAc,EAAA;QAAAd,CAAA,CAAA,GAAA,GAAAmB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAnB,CAAA,CAAA,GAAA;IAAA;IAAA,OAzBNmB,EAyBM;AAAA;;;4JAlCS,CAAY,CAAC;uKACJ,CAAW,CAAC;;;KAFjCpB,QAAA", "ignoreList": [], "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/dropdown-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,eAAe,+KAAA,CAAA,OAA0B;AAC/C,MAAM,sBAAsB,+KAAA,CAAA,UAA6B;AACzD,MAAM,oBAAoB,+KAAA,CAAA,QAA2B;AACrD,MAAM,qBAAqB,+KAAA,CAAA,SAA4B;AACvD,MAAM,kBAAkB,+KAAA,CAAA,MAAyB;AACjD,MAAM,yBAAyB,+KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,6JAAA,CAAA,aAAgB,MAK7C,QAA2C;QAA1C,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO;yBACzC,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAAG,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAEjF,MAAM,uCAAyB,6JAAA,CAAA,aAAgB,OAG7C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;;;AAGb,uBAAuB,WAAW,GAAG,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAEjF,MAAM,oCAAsB,6JAAA,CAAA,aAAgB,OAG1C,QAA0C;QAAzC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO;yBACxC,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,+KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,6JAAA,CAAA,aAAgB,OAKvC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,6LAAC,+KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG,+KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,kCAAoB,6JAAA,CAAA,aAAgB,OAKxC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,+KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,6JAAA,CAAA,aAAgB,QAG5C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;;AAGb,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAG7B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;;AAGb,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,6JAAA,CAAA,aAAgB,OAGrC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;;AAGb,eAAe,WAAW,GAAG,qKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/layout/G%3A/Augment%20code/components/layout/header.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useSession, signOut } from \"next-auth/react\"\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { LogOut, Settings, User } from \"lucide-react\"\n\nexport function Header() {\n  const { data: session } = useSession()\n\n  const handleSignOut = () => {\n    signOut({ callbackUrl: \"/auth/signin\" })\n  }\n\n  return (\n    <header className=\"flex h-16 items-center justify-between border-b bg-background px-6\">\n      <div className=\"flex items-center space-x-4\">\n        <h2 className=\"text-lg font-semibold\">\n          Welcome back, {session?.user?.name}\n        </h2>\n      </div>\n\n      <div className=\"flex items-center space-x-4\">\n        <span className=\"text-sm text-muted-foreground\">\n          Role: {session?.user?.role}\n        </span>\n        \n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"ghost\" className=\"relative h-8 w-8 rounded-full\">\n              <Avatar className=\"h-8 w-8\">\n                <AvatarImage src={session?.user?.image || \"\"} alt={session?.user?.name || \"\"} />\n                <AvatarFallback>\n                  {session?.user?.name?.charAt(0).toUpperCase()}\n                </AvatarFallback>\n              </Avatar>\n            </Button>\n          </DropdownMenuTrigger>\n          \n          <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n            <DropdownMenuLabel className=\"font-normal\">\n              <div className=\"flex flex-col space-y-1\">\n                <p className=\"text-sm font-medium leading-none\">\n                  {session?.user?.name}\n                </p>\n                <p className=\"text-xs leading-none text-muted-foreground\">\n                  {session?.user?.email}\n                </p>\n              </div>\n            </DropdownMenuLabel>\n            \n            <DropdownMenuSeparator />\n            \n            <DropdownMenuItem>\n              <User className=\"mr-2 h-4 w-4\" />\n              <span>Profile</span>\n            </DropdownMenuItem>\n            \n            <DropdownMenuItem>\n              <Settings className=\"mr-2 h-4 w-4\" />\n              <span>Settings</span>\n            </DropdownMenuItem>\n            \n            <DropdownMenuSeparator />\n            \n            <DropdownMenuItem onClick={handleSignOut}>\n              <LogOut className=\"mr-2 h-4 w-4\" />\n              <span>Log out</span>\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n      </div>\n    </header>\n  )\n}"], "names": ["c", "_c", "useSession", "signOut", "<PERSON><PERSON>", "DropdownMenu", "DropdownMenuContent", "DropdownMenuItem", "DropdownMenuLabel", "DropdownMenuSeparator", "DropdownMenuTrigger", "Avatar", "AvatarFallback", "AvatarImage", "LogOut", "Settings", "User", "Header", "$", "$i", "Symbol", "for", "data", "session", "handleSignOut", "_temp", "t0", "user", "name", "t1", "t2", "role", "t3", "t4", "image", "t5", "t6", "t7", "char<PERSON>t", "toUpperCase", "t8", "t9", "t10", "t11", "t12", "email", "t13", "t14", "t15", "t16", "t17", "t18", "t19", "t20", "t21", "t22", "t23", "callbackUrl"], "mappings": ";;;;AAAY,SAAAA,CAAA,IAAAC,EAAA;AAEZ,SAASC,UAAU,EAAEC,OAAO,QAAQ,iBAAiB;AACrD,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SACEC,YAAY,EACZC,mBAAmB,EACnBC,gBAAgB,EAChBC,iBAAiB,EACjBC,qBAAqB,EACrBC,mBAAmB,QACd,+BAA+B;AACtC,SAASC,MAAM,EAAEC,cAAc,EAAEC,WAAW,QAAQ,wBAAwB;AAC5E,SAASC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,cAAc;;;;;AAbrD,YAAY;;;;;;;AAeL;QAWkBO,OAAO;;IAXzB,MAAAL,CAAA,GAAAjB,oLAAAA,AAAA,EAAA;IAAA,IAAAiB,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IACL,MAAA,EAAAI,IAAA,EAAAC,OAAAA,EAAA,OAA0BrB,+JAAAA;IAE1B,MAAAsB,aAAA,GAAAC,KAAA;IAQuB,MAAAC,EAAA,8EAAOC,IAAA,gEAAAC,IAAA;IAAY,IAAAC,EAAA;IAAA,IAAAX,CAAA,CAAA,EAAA,KAAAQ,EAAA,EAAA;QAFtCG,EAAA,iBAAA,6LAAA,GAIM;YAJS,SAA6B,EAA7B,6BAA6B;oCAC1C,6LAAA,EAEK;gBAFS,SAAuB,EAAvB,uBAAuB;;oBAAC,cACrB;oBAAAH,EAAkB,CACnC,EAFA,EAEK,CACP,EAJA,GAIM;;;;;;;;;;;;QAAAR,CAAA,CAAA,EAAA,GAAAQ,EAAA;QAAAR,CAAA,CAAA,EAAA,GAAAW,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAX,CAAA,CAAA,EAAA;IAAA;IAIK,MAAAY,EAAA,+EAAOH,IAAA,mDAAPJ,OAAO,QAAAQ,IAAA;IAAY,IAAAC,EAAA;IAAA,IAAAd,CAAA,CAAA,EAAA,KAAAY,EAAA,EAAA;QAD5BE,EAAA,iBAAA,6LAAA,IAEO;YAFS,SAA+B,EAA/B,+BAA+B;;gBAAC,MACvC;gBAAAF,EAAkB,CAC3B,EAFA,IAEO;;;;;;;QAAAZ,CAAA,CAAA,EAAA,GAAAY,EAAA;QAAAZ,CAAA,CAAA,EAAA,GAAAc,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAd,CAAA,CAAA,EAAA;IAAA;IAMmB,MAAAe,EAAA,uDAAAV,OAAO,kBAAAI,IAAA,kEAAAO,KAAA,KAAiB,EAAE;IAAO,MAAAC,EAAA,gFAAOR,IAAA,mDAAPJ,OAAO,QAAAK,IAAA,KAAgB,EAAE;IAAA,IAAAQ,EAAA;IAAA,IAAAlB,CAAA,CAAA,EAAA,KAAAe,EAAA,IAAAf,CAAA,CAAA,EAAA,KAAAiB,EAAA,EAAA;QAA5EC,EAAA,iBAAA,4TAAC,cAAW;YAAM,GAA0B,CAA1B,CAAAH,EAAyB,CAAC;YAAO,GAAyB,CAAzB,CAAAE,EAAwB,CAAC,GAAI;;;;;;QAAAjB,CAAA,CAAA,EAAA,GAAAe,EAAA;QAAAf,CAAA,CAAA,EAAA,GAAAiB,EAAA;QAAAjB,CAAA,CAAA,EAAA,GAAAkB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAlB,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAmB,EAAA;IAAA,IAAAnB,CAAA,CAAA,EAAA,0EAAAK,OAAA,CAAAI,IAAA,kEAAAC,IAAA,GAAA;gDAEjCL,OAAA;QAA5Cc,EAAA,+EAAOV,IAAA,wFAAAC,IAAA,uDAAPL,OAAO,YAAAe,MAAA,CAAA,GAAAC,WAAA;QAAqCrB,CAAA,CAAA,EAAA,+EAAAS,IAAA,kEAAAC,IAAA;QAAAV,CAAA,CAAA,EAAA,GAAAmB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAnB,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAsB,EAAA;IAAA,IAAAtB,CAAA,CAAA,GAAA,KAAAmB,EAAA,EAAA;QAD/CG,EAAA,iBAAA,4TAAC,iBAAc,CACZ;sBAAAH,EAA2C,CAC9C,EAFC,cAAc,CAEE;;;;;;QAAAnB,CAAA,CAAA,GAAA,GAAAmB,EAAA;QAAAnB,CAAA,CAAA,GAAA,GAAAsB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAtB,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAuB,EAAA;IAAA,IAAAvB,CAAA,CAAA,GAAA,KAAAkB,EAAA,IAAAlB,CAAA,CAAA,GAAA,KAAAsB,EAAA,EAAA;QANvBC,EAAA,iBAAA,sUAAC,sBAAmB;YAAC,OAAO,CAAP,CAAA,IAAM,CAAC;oCAC1B,4TAAC,SAAM;gBAAS,OAAO,EAAP,OAAO;gBAAW,SAA+B,EAA/B,+BAA+B;wCAC/D,4TAAC,SAAM;oBAAW,SAAS,EAAT,SAAS,CACzB;;wBAAAL,EAA+E,CAC/E;wBAAAI,EAEgB,CAClB,EALC,MAAM,CAMT,EAPC,MAAM,CAQT,EATC,mBAAmB,CASE;;;;;;;;;;;;;;;;;QAAAtB,CAAA,CAAA,GAAA,GAAAkB,EAAA;QAAAlB,CAAA,CAAA,GAAA,GAAAsB,EAAA;QAAAtB,CAAA,CAAA,GAAA,GAAAuB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAvB,CAAA,CAAA,GAAA;IAAA;IAMb,MAAAwB,GAAA,sDAAAnB,OAAO,kBAAAI,IAAA,kEAAAC,IAAA;IAAY,IAAAe,GAAA;IAAA,IAAAzB,CAAA,CAAA,GAAA,KAAAwB,GAAA,EAAA;QADtBC,GAAA,iBAAA,6LAAA,CAEI;YAFS,SAAkC,EAAlC,kCAAkC,CAC5C;sBAAAD,GAAkB,CACrB,EAFA,CAEI;;;;;;QAAAxB,CAAA,CAAA,GAAA,GAAAwB,GAAA;QAAAxB,CAAA,CAAA,GAAA,GAAAyB,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAzB,CAAA,CAAA,GAAA;IAAA;IAED,MAAA0B,GAAA,+EAAOjB,IAAA,mDAAPJ,OAAO,QAAAsB,KAAA;IAAa,IAAAC,GAAA;IAAA,IAAA5B,CAAA,CAAA,GAAA,KAAA0B,GAAA,EAAA;QADvBE,GAAA,iBAAA,6LAAA,CAEI;YAFS,SAA4C,EAA5C,4CAA4C,CACtD;sBAAAF,GAAmB,CACtB,EAFA,CAEI;;;;;;QAAA1B,CAAA,CAAA,GAAA,GAAA0B,GAAA;QAAA1B,CAAA,CAAA,GAAA,GAAA4B,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA5B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA6B,GAAA;IAAA,IAAA7B,CAAA,CAAA,GAAA,KAAAyB,GAAA,IAAAzB,CAAA,CAAA,GAAA,KAAA4B,GAAA,EAAA;QAPRC,GAAA,iBAAA,sUAAC,oBAAiB;YAAW,SAAa,EAAb,aAAa;oCACxC,6LAAA,GAOM;gBAPS,SAAyB,EAAzB,yBAAyB,CACtC;;oBAAAJ,GAEG,CACH;oBAAAG,GAEG,CACL,EAPA,GAOM,CACR,EATC,iBAAiB,CASE;;;;;;;;;;;;QAAA5B,CAAA,CAAA,GAAA,GAAAyB,GAAA;QAAAzB,CAAA,CAAA,GAAA,GAAA4B,GAAA;QAAA5B,CAAA,CAAA,GAAA,GAAA6B,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA7B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA8B,GAAA;IAAA,IAAA9B,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAEpB2B,GAAA,iBAAA,sUAAC,wBAAqB,GAAG;;;;;QAAA9B,CAAA,CAAA,GAAA,GAAA8B,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA9B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA+B,GAAA;IAAA,IAAA/B,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAEzB4B,GAAA,iBAAA,sUAAC,mBAAgB;;8BACf,mYAAC,OAAI;oBAAW,SAAc,EAAd,cAAc;;;;;;8BAC9B,6LAAA,IAAoB;8BAAd,OAAO,EAAb,IAAoB,CACtB,EAHC,gBAAgB,CAGE;;;;;;;;;;;;QAAA/B,CAAA,CAAA,GAAA,GAAA+B,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA/B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAgC,GAAA;IAAA,IAAAC,GAAA;IAAA,IAAAjC,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAEnB6B,GAAA,iBAAA,sUAAC,mBAAgB;;8BACf,2YAAC,WAAQ;oBAAW,SAAc,EAAd,cAAc;;;;;;8BAClC,6LAAA,IAAqB;8BAAf,QAAQ,EAAd,IAAqB,CACvB,EAHC,gBAAgB,CAGE;;;;;;;;;;;;QAEnBC,GAAA,iBAAA,sUAAC,wBAAqB,GAAG;;;;;QAAAjC,CAAA,CAAA,GAAA,GAAAgC,GAAA;QAAAhC,CAAA,CAAA,GAAA,GAAAiC,GAAA;IAAA,OAAA;QAAAD,GAAA,GAAAhC,CAAA,CAAA,GAAA;QAAAiC,GAAA,GAAAjC,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAkC,GAAA;IAAA,IAAAlC,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAEzB+B,GAAA,iBAAA,sUAAC,mBAAgB;YAAU5B,OAAa,CAAbA,CAAAA,aAAY,CAAC;;8BACtC,2YAAC,SAAM;oBAAW,SAAc,EAAd,cAAc;;;;;;8BAChC,6LAAA,IAAoB;8BAAd,OAAO,EAAb,IAAoB,CACtB,EAHC,gBAAgB,CAGE;;;;;;;;;;;;QAAAN,CAAA,CAAA,GAAA,GAAAkC,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAlC,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAmC,GAAA;IAAA,IAAAnC,CAAA,CAAA,GAAA,KAAA6B,GAAA,EAAA;QA7BrBM,GAAA,iBAAA,sUAAC,sBAAmB;YAAW,SAAM,EAAN,MAAM;YAAO,KAAK,EAAL,KAAK;YAAC,UAAU,CAAV,CAAA,IAAS,CAAC,CAC1D;;gBAAAN,GASmB,CAEnB;gBAAAC,GAAwB,CAExB;gBAAAC,GAGkB,CAElB;gBAAAC,GAGkB,CAElB;gBAAAC,GAAwB,CAExB;gBAAAC,GAGkB,CACpB,EA9BC,mBAAmB,CA8BE;;;;;;;QAAAlC,CAAA,CAAA,GAAA,GAAA6B,GAAA;QAAA7B,CAAA,CAAA,GAAA,GAAAmC,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAnC,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAoC,GAAA;IAAA,IAAApC,CAAA,CAAA,GAAA,KAAAmC,GAAA,IAAAnC,CAAA,CAAA,GAAA,KAAAuB,EAAA,EAAA;QA1CxBa,GAAA,iBAAA,sUAAC,eAAY,CACX;;gBAAAb,EASqB,CAErB;gBAAAY,GA8BqB,CACvB,EA3CC,YAAY,CA2CE;;;;;;;QAAAnC,CAAA,CAAA,GAAA,GAAAmC,GAAA;QAAAnC,CAAA,CAAA,GAAA,GAAAuB,EAAA;QAAAvB,CAAA,CAAA,GAAA,GAAAoC,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAApC,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAqC,GAAA;IAAA,IAAArC,CAAA,CAAA,GAAA,KAAAoC,GAAA,IAAApC,CAAA,CAAA,GAAA,KAAAc,EAAA,EAAA;QAhDjBuB,GAAA,iBAAA,6LAAA,GAiDM;YAjDS,SAA6B,EAA7B,6BAA6B,CAC1C;;gBAAAvB,EAEM,CAEN;gBAAAsB,GA2Cc,CAChB,EAjDA,GAiDM;;;;;;;QAAApC,CAAA,CAAA,GAAA,GAAAoC,GAAA;QAAApC,CAAA,CAAA,GAAA,GAAAc,EAAA;QAAAd,CAAA,CAAA,GAAA,GAAAqC,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAArC,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAsC,GAAA;IAAA,IAAAtC,CAAA,CAAA,GAAA,KAAAW,EAAA,IAAAX,CAAA,CAAA,GAAA,KAAAqC,GAAA,EAAA;QAxDRC,GAAA,iBAAA,6LAAA,MAyDS;YAzDS,SAAoE,EAApE,oEAAoE,CACpF;;gBAAA3B,EAIK,CAEL;gBAAA0B,GAiDK,CACP,EAzDA,MAyDS;;;;;;;QAAArC,CAAA,CAAA,GAAA,GAAAW,EAAA;QAAAX,CAAA,CAAA,GAAA,GAAAqC,GAAA;QAAArC,CAAA,CAAA,GAAA,GAAAsC,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAtC,CAAA,CAAA,GAAA;IAAA;IAAA,OAzDTsC,GAyDS;AAAA;;;uKAhEe,CAAW,CAAC;;;KADjCvC,OAAA;AAAA,SAAAQ,MAAA;0JAIHtB,UAAAA,AAAA,EAAA;QAAAsD,WAAA,EAAuB;IAAc,CAAE,CAAC;AAAA", "ignoreList": [], "debugId": null}}, {"offset": {"line": 1016, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/app/%28dashboard%29/G%3A/Augment%20code/app/%28dashboard%29/layout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useSession } from \"next-auth/react\"\nimport { useRouter } from \"next/navigation\"\nimport { useEffect } from \"react\"\nimport { Sidebar } from \"@/components/layout/sidebar\"\nimport { Header } from \"@/components/layout/header\"\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (status !== \"loading\" && !session) {\n      router.push(\"/auth/signin\")\n    }\n  }, [session, status, router])\n\n  if (status === \"loading\") {\n    return (\n      <div className=\"flex h-screen items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"></div>\n      </div>\n    )\n  }\n\n  if (!session) {\n    return null\n  }\n\n  return (\n    <div className=\"flex h-screen bg-background\">\n      <Sidebar />\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Header />\n        <main className=\"flex-1 overflow-auto p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}"], "names": ["c", "_c", "useSession", "useRouter", "useEffect", "Sidebar", "Header", "DashboardLayout", "t0", "$", "$i", "Symbol", "for", "children", "data", "session", "status", "router", "t1", "t2", "push", "t3", "t4", "t5"], "mappings": ";;;;AAAY,SAAAA,CAAA,IAAAC,EAAA;AAEZ,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,OAAO,QAAQ,6BAA6B;AACrD,SAASC,MAAM,QAAQ,4BAA4B;;;AANnD,YAAY;;;;;;;AAQG,yBAAAE,EAAA;;IAAA,MAAAC,CAAA,mLAAAR,IAAAA,AAAA,EAAA;IAAA,IAAAQ,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IAAyB,MAAA,EAAAI,QAAAA,EAAA,GAAAL,EAIvC;IACC,MAAA,EAAAM,IAAA,EAAAC,OAAA,EAAAC,MAAAA,EAAA,OAAkCd,+JAAAA,AAAA,CAAW,CAAC;IAC9C,MAAAe,MAAA,6IAAed,YAAAA;IAAW,IAAAe,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAV,CAAA,CAAA,EAAA,KAAAQ,MAAA,IAAAR,CAAA,CAAA,EAAA,KAAAM,OAAA,IAAAN,CAAA,CAAA,EAAA,KAAAO,MAAA,EAAA;QAEhBE,EAAA,GAAAA,CAAA;YAAA,IACJF,MAAM,KAAK,SAAS,IAAA,CAAKD,OAAO,EAAA;gBAClCE,MAAM,CAAAG,IAAA,CAAM,cAAc,CAAC;YAAA;QAAA;QAE5BD,EAAA,GAAA;YAACJ,OAAO;YAAEC,MAAM;YAAEC,MAAM;SAAA;QAACR,CAAA,CAAA,EAAA,GAAAQ,MAAA;QAAAR,CAAA,CAAA,EAAA,GAAAM,OAAA;QAAAN,CAAA,CAAA,EAAA,GAAAO,MAAA;QAAAP,CAAA,CAAA,EAAA,GAAAS,EAAA;QAAAT,CAAA,CAAA,EAAA,GAAAU,EAAA;IAAA,OAAA;QAAAD,EAAA,GAAAT,CAAA,CAAA,EAAA;QAAAU,EAAA,GAAAV,CAAA,CAAA,EAAA;IAAA;IAJ5BL,8KAAAA,AAAA,EAAUc,EAIT,EAAEC,EAAyB,CAAC;IAAA,IAEzBH,MAAM,KAAK,SAAS,EAAA;QAAA,IAAAK,EAAA;QAAA,IAAAZ,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;YAEpBS,EAAA,iBAAA,6LAAA,GAEM;gBAFS,SAA2C,EAA3C,2CAA2C;wCACxD,6LAAA,GAAqF;oBAAtE,SAA+D,EAA/D,+DAA+D,GAChF,EAFA,GAEM;;;;;;;;;;;YAAAZ,CAAA,CAAA,EAAA,GAAAY,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAAZ,CAAA,CAAA,EAAA;QAAA;QAAA,OAFNY,EAEM;IAAA;IAAA,IAAA,CAILN,OAAO,EAAA;QAAA,OAAA;IAAA;IAAA,IAAAM,EAAA;IAAA,IAAAZ,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAMRS,EAAA,iBAAA,iUAAC,UAAO,GAAG;;;;;QAAAZ,CAAA,CAAA,EAAA,GAAAY,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAZ,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAa,EAAA;IAAA,IAAAb,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAETU,EAAA,iBAAA,gUAAC,SAAM,GAAG;;;;;QAAAb,CAAA,CAAA,EAAA,GAAAa,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAb,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAc,EAAA;IAAA,IAAAd,CAAA,CAAA,EAAA,KAAAI,QAAA,EAAA;QAHdU,EAAA,iBAAA,6LAAA,GAQM;YARS,SAA6B,EAA7B,6BAA6B,CAC1C;;gBAAAF,EAAU;8BACV,6LAAA,GAKM;oBALS,SAAsC,EAAtC,sCAAsC,CACnD;;wBAAAC,EAAS;sCACT,6LAAA,IAEO;4BAFS,SAA0B,EAA1B,0BAA0B,CACvCT;sCAAAA,QAAO,CACV,EAFA,IAEO,CACT,EALA,GAKM,CACR,EARA,GAQM;;;;;;;;;;;;;;;;;;QAAAJ,CAAA,CAAA,EAAA,GAAAI,QAAA;QAAAJ,CAAA,CAAA,GAAA,GAAAc,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAd,CAAA,CAAA,GAAA;IAAA;IAAA,OARNc,EAQM;AAAA;GAnCKhB;;;0JAME,CAAU,CAAC", "ignoreList": [], "debugId": null}}]}