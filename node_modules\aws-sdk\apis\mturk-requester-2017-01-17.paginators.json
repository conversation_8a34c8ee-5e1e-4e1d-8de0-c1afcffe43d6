{"pagination": {"ListAssignmentsForHIT": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListBonusPayments": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListHITs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListHITsForQualificationType": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListQualificationRequests": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListQualificationTypes": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListReviewPolicyResultsForHIT": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListReviewableHITs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListWorkerBlocks": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListWorkersWithQualificationType": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}}}