"use client"

import { But<PERSON> } from "@/components/ui/button"
import { 
  ArrowRight, 
  X,
  Users
} from "lucide-react"

interface BulkAssignmentBarProps {
  selectedCount: number
  onAssign: () => void
  onClearSelection: () => void
}

export function BulkAssignmentBar({ 
  selectedCount, 
  onAssign,
  onClearSelection 
}: BulkAssignmentBarProps) {
  return (
    <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
      <div className="flex items-center gap-2">
        <Users className="h-4 w-4 text-blue-600" />
        <span className="text-sm font-medium text-blue-800">
          {selectedCount} employee{selectedCount !== 1 ? 's' : ''} selected for assignment
        </span>
      </div>

      <div className="flex items-center gap-2">
        <Button
          size="sm"
          onClick={onAssign}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <ArrowRight className="h-4 w-4 mr-2" />
          Assign to Department
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={onClearSelection}
          className="text-blue-600 hover:text-blue-700 hover:bg-blue-100"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
