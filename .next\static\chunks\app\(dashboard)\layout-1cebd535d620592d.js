(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[305],{198:(e,r,t)=>{"use strict";t.d(r,{Vy:()=>d,Yq:()=>o,cn:()=>a,pp:()=>n});var s=t(5403),l=t(9055);function a(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,l.QP)((0,s.$)(r))}function n(e,r){return"".concat(e,"-").concat(r.toString().padStart(3,"0"))}function o(e){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(e)}function d(e,r){return Math.round(e/r*100)}},425:(e,r,t)=>{"use strict";t.d(r,{$:()=>i});var s=t(5155),l=t(2115),a=t(5441),n=t(1335),o=t(198);let d=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),i=l.forwardRef((e,r)=>{let{className:t,variant:l,size:n,asChild:i=!1,...c}=e,m=i?a.DX:"button";return(0,s.jsx)(m,{className:(0,o.cn)(d({variant:l,size:n,className:t})),ref:r,...c})});i.displayName="Button"},650:(e,r,t)=>{"use strict";t.d(r,{SQ:()=>c,_2:()=>m,lp:()=>u,mB:()=>f,rI:()=>d,ty:()=>i});var s=t(5155),l=t(2115),a=t(8975),n=t(4627),o=t(198);let d=a.bL,i=a.l9;a.YJ,a.ZL,a.Pb,a.z6,l.forwardRef((e,r)=>{let{className:t,inset:l,children:d,...i}=e;return(0,s.jsxs)(a.ZP,{ref:r,className:(0,o.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",l&&"pl-8",t),...i,children:[d,(0,s.jsx)(n.A,{className:"ml-auto h-4 w-4"})]})}).displayName=a.ZP.displayName,l.forwardRef((e,r)=>{let{className:t,...l}=e;return(0,s.jsx)(a.G5,{ref:r,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...l})}).displayName=a.G5.displayName;let c=l.forwardRef((e,r)=>{let{className:t,sideOffset:l=4,...n}=e;return(0,s.jsx)(a.ZL,{children:(0,s.jsx)(a.UC,{ref:r,sideOffset:l,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...n})})});c.displayName=a.UC.displayName;let m=l.forwardRef((e,r)=>{let{className:t,inset:l,...n}=e;return(0,s.jsx)(a.q7,{ref:r,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",l&&"pl-8",t),...n})});m.displayName=a.q7.displayName;let u=l.forwardRef((e,r)=>{let{className:t,inset:l,...n}=e;return(0,s.jsx)(a.JU,{ref:r,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",l&&"pl-8",t),...n})});u.displayName=a.JU.displayName;let f=l.forwardRef((e,r)=>{let{className:t,...l}=e;return(0,s.jsx)(a.wv,{ref:r,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",t),...l})});f.displayName=a.wv.displayName},1328:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>M});var s=t(5155),l=t(9749),a=t(235),n=t(6066),o=t(2115),d=t(3329),i=t.n(d),c=t(198),m=t(425),u=t(6534),f=t(8379),x=t(130),h=t(5384),p=t(5369),b=t(2363),g=t(5600),v=t(4630);let N=[{name:"Dashboard",href:"/dashboard",icon:u.A,roles:["ADMIN","HR_MANAGER","VIEWER"]},{name:"Employees",href:"/employees",icon:f.A,roles:["ADMIN","HR_MANAGER","VIEWER"]},{name:"Departments",href:"/departments",icon:x.A,roles:["ADMIN","HR_MANAGER","VIEWER"]},{name:"Bulk Operations",href:"/bulk",icon:h.A,roles:["ADMIN","HR_MANAGER"]},{name:"Free Bucket",href:"/free-bucket",icon:p.A,roles:["ADMIN","HR_MANAGER"]},{name:"User Management",href:"/admin/users",icon:b.A,roles:["ADMIN"]},{name:"Settings",href:"/settings",icon:g.A,roles:["ADMIN","HR_MANAGER"]}];function j(){var e,r,t,o,d,u;let f,x,h,p,b,g,j,y,w,_,A,R,S=(0,l.c)(32),k=(0,n.usePathname)(),{data:E}=(0,a.useSession)(),M=null==E||null==(e=E.user)?void 0:e.role;if(S[0]!==k||S[1]!==M){let e,r;S[7]!==M?(e=e=>e.roles.includes(M),S[7]=M,S[8]=e):e=S[8];let t=N.filter(e);p="flex h-full w-72 flex-col bg-gradient-to-b from-slate-50 to-white border-r border-border/50 shadow-soft",S[9]===Symbol.for("react.memo_cache_sentinel")?(b=(0,s.jsx)("div",{className:"flex h-20 items-center px-8 border-b border-border/50",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center shadow-medium",children:(0,s.jsx)("div",{className:"text-white text-lg font-bold",children:"HR"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"HR Synergy"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground font-medium",children:"Employee Management"})]})]})}),S[9]=b):b=S[9],f="flex-1 space-y-2 p-6",S[10]===Symbol.for("react.memo_cache_sentinel")?(x=(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)("p",{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3",children:"Navigation"})}),S[10]=x):x=S[10],S[11]!==k?(r=e=>{let r=k===e.href||k.startsWith(e.href+"/");return(0,s.jsx)(i(),{href:e.href,children:(0,s.jsxs)(m.$,{variant:"ghost",className:(0,c.cn)("w-full justify-start h-12 px-4 rounded-xl font-medium transition-all duration-200 group",r?"bg-gradient-primary text-white shadow-medium hover:opacity-90":"hover:bg-muted/50 hover:translate-x-1 text-muted-foreground hover:text-foreground"),children:[(0,s.jsx)(e.icon,{className:(0,c.cn)("mr-3 h-5 w-5 transition-colors",r?"text-white":"text-muted-foreground group-hover:text-foreground")}),(0,s.jsx)("span",{className:"text-sm",children:e.name}),r&&(0,s.jsx)("div",{className:"ml-auto w-2 h-2 bg-white rounded-full opacity-80"})]})},e.name)},S[11]=k,S[12]=r):r=S[12],h=t.map(r),S[0]=k,S[1]=M,S[2]=f,S[3]=x,S[4]=h,S[5]=p,S[6]=b}else f=S[2],x=S[3],h=S[4],p=S[5],b=S[6];S[13]!==f||S[14]!==x||S[15]!==h?(g=(0,s.jsxs)("nav",{className:f,children:[x,h]}),S[13]=f,S[14]=x,S[15]=h,S[16]=g):g=S[16],S[17]===Symbol.for("react.memo_cache_sentinel")?(j=(0,s.jsx)("div",{className:"w-8 h-8 bg-gradient-accent rounded-lg flex items-center justify-center",children:(0,s.jsx)(v.A,{className:"h-4 w-4 text-white"})}),S[17]=j):j=S[17];let D=null==E||null==(r=E.user)?void 0:r.name;return S[18]!==D?(y=(0,s.jsx)("p",{className:"text-sm font-medium text-foreground truncate",children:D}),S[18]=D,S[19]=y):y=S[19],S[20]!==(null==E||null==(t=E.user)?void 0:t.role)?(w=null==E||null==(d=E.user)||null==(o=d.role)?void 0:o.replace("_"," "),S[20]=null==E||null==(u=E.user)?void 0:u.role,S[21]=w):w=S[21],S[22]!==w?(_=(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:w}),S[22]=w,S[23]=_):_=S[23],S[24]!==_||S[25]!==y?(A=(0,s.jsx)("div",{className:"p-6 border-t border-border/50",children:(0,s.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-200/50",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[j,(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[y,_]})]})})}),S[24]=_,S[25]=y,S[26]=A):A=S[26],S[27]!==A||S[28]!==p||S[29]!==b||S[30]!==g?(R=(0,s.jsxs)("div",{className:p,children:[b,g,A]}),S[27]=A,S[28]=p,S[29]=b,S[30]=g,S[31]=R):R=S[31],R}var y=t(650),w=t(5529);let _=o.forwardRef((e,r)=>{let{className:t,...l}=e;return(0,s.jsx)(w.bL,{ref:r,className:(0,c.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",t),...l})});_.displayName=w.bL.displayName;let A=o.forwardRef((e,r)=>{let{className:t,...l}=e;return(0,s.jsx)(w._V,{ref:r,className:(0,c.cn)("aspect-square h-full w-full",t),...l})});A.displayName=w._V.displayName;let R=o.forwardRef((e,r)=>{let{className:t,...l}=e;return(0,s.jsx)(w.H4,{ref:r,className:(0,c.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",t),...l})});R.displayName=w.H4.displayName;var S=t(5952);function k(){var e,r,t,n,o,d,i,c,u,f,x,h,p,b,N,j,w,k,M,D;let I,H,z,U,G,P,C,V,B,L,O,W,q,Z,$,F,J,Q,Y,T,X,K,ee,er,et,es=(0,l.c)(50),{data:el}=(0,a.useSession)();es[0]===Symbol.for("react.memo_cache_sentinel")?(I=(0,s.jsx)("div",{className:"w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center shadow-medium",children:(0,s.jsx)("div",{className:"text-white text-sm font-bold",children:"HR"})}),es[0]=I):I=es[0],es[1]!==(null==el||null==(e=el.user)?void 0:e.name)?(H=null==el||null==(f=el.user)||null==(u=f.name)?void 0:u.split(" ")[0],es[1]=null==el||null==(x=el.user)?void 0:x.name,es[2]=H):H=es[2],es[3]!==H?(z=(0,s.jsxs)("h2",{className:"text-xl font-semibold text-foreground",children:["Welcome back, ",H]}),es[3]=H,es[4]=z):z=es[4],es[5]===Symbol.for("react.memo_cache_sentinel")?(U=(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:new Date().toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}),es[5]=U):U=es[5],es[6]!==z?(G=(0,s.jsx)("div",{className:"flex items-center space-x-6",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[I,(0,s.jsxs)("div",{children:[z,U]})]})}),es[6]=z,es[7]=G):G=es[7],es[8]!==(null==el||null==(r=el.user)?void 0:r.role)?(P=null==el||null==(p=el.user)||null==(h=p.role)?void 0:h.replace("_"," "),es[8]=null==el||null==(b=el.user)?void 0:b.role,es[9]=P):P=es[9],es[10]!==P?(C=(0,s.jsx)("div",{className:"hidden md:flex items-center space-x-3",children:(0,s.jsx)("div",{className:"px-3 py-1.5 bg-gradient-to-r from-blue-50 to-purple-50 rounded-full border border-blue-200/50",children:(0,s.jsx)("span",{className:"text-sm font-medium text-blue-700",children:P})})}),es[10]=P,es[11]=C):C=es[11];let ea=(null==el||null==(t=el.user)?void 0:t.image)||"",en=(null==el||null==(n=el.user)?void 0:n.name)||"";es[12]!==ea||es[13]!==en?(V=(0,s.jsx)(A,{src:ea,alt:en}),es[12]=ea,es[13]=en,es[14]=V):V=es[14],es[15]!==(null==el||null==(o=el.user)?void 0:o.name)?(B=null==el||null==(j=el.user)||null==(N=j.name)?void 0:N.charAt(0).toUpperCase(),es[15]=null==el||null==(w=el.user)?void 0:w.name,es[16]=B):B=es[16],es[17]!==B?(L=(0,s.jsx)(R,{className:"bg-gradient-primary text-white font-semibold",children:B}),es[17]=B,es[18]=L):L=es[18],es[19]!==L||es[20]!==V?(O=(0,s.jsx)(y.ty,{asChild:!0,children:(0,s.jsx)(m.$,{variant:"ghost",className:"relative h-10 w-10 rounded-full hover:bg-muted/50 transition-colors",children:(0,s.jsxs)(_,{className:"h-10 w-10 border-2 border-white shadow-medium",children:[V,L]})})}),es[19]=L,es[20]=V,es[21]=O):O=es[21];let eo=null==el||null==(d=el.user)?void 0:d.name;es[22]!==eo?(W=(0,s.jsx)("p",{className:"text-base font-semibold leading-none text-foreground",children:eo}),es[22]=eo,es[23]=W):W=es[23];let ed=null==el||null==(i=el.user)?void 0:i.email;return es[24]!==ed?(q=(0,s.jsx)("p",{className:"text-sm leading-none text-muted-foreground",children:ed}),es[24]=ed,es[25]=q):q=es[25],es[26]!==(null==el||null==(c=el.user)?void 0:c.role)?(Z=null==el||null==(M=el.user)||null==(k=M.role)?void 0:k.replace("_"," "),es[26]=null==el||null==(D=el.user)?void 0:D.role,es[27]=Z):Z=es[27],es[28]!==Z?($=(0,s.jsx)("div",{className:"mt-2 px-2 py-1 bg-muted/50 rounded-md",children:(0,s.jsxs)("p",{className:"text-xs font-medium text-muted-foreground",children:["Role: ",Z]})}),es[28]=Z,es[29]=$):$=es[29],es[30]!==W||es[31]!==q||es[32]!==$?(F=(0,s.jsx)(y.lp,{className:"font-normal p-4",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[W,q,$]})}),es[30]=W,es[31]=q,es[32]=$,es[33]=F):F=es[33],es[34]===Symbol.for("react.memo_cache_sentinel")?(J=(0,s.jsx)(y.mB,{className:"bg-border/50"}),es[34]=J):J=es[34],es[35]===Symbol.for("react.memo_cache_sentinel")?(Q=(0,s.jsxs)(y._2,{className:"p-3 cursor-pointer hover:bg-muted/50 transition-colors",children:[(0,s.jsx)(v.A,{className:"mr-3 h-4 w-4 text-muted-foreground"}),(0,s.jsx)("span",{className:"font-medium",children:"Profile Settings"})]}),es[35]=Q):Q=es[35],es[36]===Symbol.for("react.memo_cache_sentinel")?(Y=(0,s.jsxs)(y._2,{className:"p-3 cursor-pointer hover:bg-muted/50 transition-colors",children:[(0,s.jsx)(g.A,{className:"mr-3 h-4 w-4 text-muted-foreground"}),(0,s.jsx)("span",{className:"font-medium",children:"Preferences"})]}),T=(0,s.jsx)(y.mB,{className:"bg-border/50"}),es[36]=Y,es[37]=T):(Y=es[36],T=es[37]),es[38]===Symbol.for("react.memo_cache_sentinel")?(X=(0,s.jsxs)(y._2,{onClick:E,className:"p-3 cursor-pointer hover:bg-red-50 hover:text-red-600 transition-colors",children:[(0,s.jsx)(S.A,{className:"mr-3 h-4 w-4"}),(0,s.jsx)("span",{className:"font-medium",children:"Sign Out"})]}),es[38]=X):X=es[38],es[39]!==F?(K=(0,s.jsxs)(y.SQ,{className:"w-64 shadow-strong border-0 bg-white/95 backdrop-blur-md",align:"end",forceMount:!0,children:[F,J,Q,Y,T,X]}),es[39]=F,es[40]=K):K=es[40],es[41]!==O||es[42]!==K?(ee=(0,s.jsxs)(y.rI,{children:[O,K]}),es[41]=O,es[42]=K,es[43]=ee):ee=es[43],es[44]!==ee||es[45]!==C?(er=(0,s.jsxs)("div",{className:"flex items-center space-x-6",children:[C,ee]}),es[44]=ee,es[45]=C,es[46]=er):er=es[46],es[47]!==er||es[48]!==G?(et=(0,s.jsxs)("header",{className:"flex h-20 items-center justify-between border-b border-border/50 bg-white/80 backdrop-blur-md px-8 shadow-soft",children:[G,er]}),es[47]=er,es[48]=G,es[49]=et):et=es[49],et}function E(){(0,a.signOut)({callbackUrl:"/auth/signin"})}function M(e){let r,t,d,i,c,m=(0,l.c)(10),{children:u}=e,{data:f,status:x}=(0,a.useSession)(),h=(0,n.useRouter)();if(m[0]!==h||m[1]!==f||m[2]!==x?(r=()=>{"loading"===x||f||h.push("/auth/signin")},t=[f,x,h],m[0]=h,m[1]=f,m[2]=x,m[3]=r,m[4]=t):(r=m[3],t=m[4]),(0,o.useEffect)(r,t),"loading"===x){let e;return m[5]===Symbol.for("react.memo_cache_sentinel")?(e=(0,s.jsx)("div",{className:"flex h-screen items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})}),m[5]=e):e=m[5],e}return f?(m[6]===Symbol.for("react.memo_cache_sentinel")?(d=(0,s.jsx)(j,{}),m[6]=d):d=m[6],m[7]===Symbol.for("react.memo_cache_sentinel")?(i=(0,s.jsx)(k,{}),m[7]=i):i=m[7],m[8]!==u?(c=(0,s.jsxs)("div",{className:"flex h-screen bg-gradient-to-br from-slate-50/30 to-white",children:[d,(0,s.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[i,(0,s.jsx)("main",{className:"flex-1 overflow-auto",children:(0,s.jsx)("div",{className:"animate-fade-in",children:u})})]})]}),m[8]=u,m[9]=c):c=m[9],c):null}},3830:(e,r,t)=>{Promise.resolve().then(t.bind(t,1328))}},e=>{e.O(0,[235,666,368,445,966,441,326,358],()=>e(e.s=3830)),_N_E=e.O()}]);