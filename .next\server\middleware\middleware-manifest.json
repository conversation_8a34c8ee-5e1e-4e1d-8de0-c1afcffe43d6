{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_a57d5dab._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_c1cf8224.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "UajYjgneHB5uLB/knA9x9rWrkAcqUKV94zpzH0CadVU=", "__NEXT_PREVIEW_MODE_ID": "430048a983ee3eafdbc11d5fe4d69ba4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d1d4c3e97a0a8833ef4cc172299557808b9fd53327ab793f95c9a137b7d28484", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a0b7fd7daf49db9446734afb84826668bfda5cdc7f94f49a710ce0df51ae017b"}}}, "instrumentation": null, "functions": {}}