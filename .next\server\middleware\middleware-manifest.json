{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_a57d5dab._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_c1cf8224.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "UajYjgneHB5uLB/knA9x9rWrkAcqUKV94zpzH0CadVU=", "__NEXT_PREVIEW_MODE_ID": "d9fc96c91383e4cb5347b7fabf74e0dd", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e6fda20d4563ac628888cadac59eceb7f8be31175141b541dfdb3a7c4075b078", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "32f6c846fb2979df69a3926c2fe4f122e0ab35f3531db3b97a20769560986850"}}}, "instrumentation": null, "functions": {}}