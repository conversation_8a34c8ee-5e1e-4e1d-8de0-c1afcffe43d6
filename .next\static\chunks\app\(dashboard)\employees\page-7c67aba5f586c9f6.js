(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[228],{560:(e,s,l)=>{Promise.resolve().then(l.bind(l,1803))},1803:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>Z});var n=l(5155),t=l(9749),a=l(2115),r=l(235),c=l(9683),i=l(425),d=l(7071),m=l(4373),o=l(6226),h=l(198),x=l(6203),u=l(650),j=l(9138),p=l(1895),f=l(5076),g=l(3689),y=l(8106);function N(e){let s,l,a,r,c,d,N,b,_,A,S,C,w,E,k=(0,t.c)(35),{employees:I,departments:D}=e,{selectedEmployees:R,toggleEmployeeSelection:T,selectAllEmployees:F,clearSelection:V}=(0,m.d)(),B=I.length>0&&R.length===I.length,$=R.length>0&&R.length<I.length;k[0]!==V||k[1]!==I||k[2]!==B||k[3]!==F?(s=()=>{B?V():F(I.map(v))},k[0]=V,k[1]=I,k[2]=B,k[3]=F,k[4]=s):s=k[4];let Z=s;k[5]!==D?(l=e=>{if(!e)return"Unassigned";let s=D.find(s=>s.id===e);return(null==s?void 0:s.name)||"Unknown"},k[5]=D,k[6]=l):l=k[6];let z=l;if(0===I.length){let e;return k[7]===Symbol.for("react.memo_cache_sentinel")?(e=(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-muted-foreground",children:"No employees found."})}),k[7]=e):e=k[7],e}if(k[8]!==Z||k[9]!==B||k[10]!==$?(a=(0,n.jsx)(x.nd,{className:"w-12",children:(0,n.jsx)(j.S,{checked:B,indeterminate:$,onCheckedChange:Z,"aria-label":"Select all employees"})}),k[8]=Z,k[9]=B,k[10]=$,k[11]=a):a=k[11],k[12]===Symbol.for("react.memo_cache_sentinel")?(c=(0,n.jsx)(x.nd,{children:"Employee ID"}),d=(0,n.jsx)(x.nd,{children:"Name"}),N=(0,n.jsx)(x.nd,{children:"Email"}),b=(0,n.jsx)(x.nd,{children:"Department"}),_=(0,n.jsx)(x.nd,{children:"Status"}),A=(0,n.jsx)(x.nd,{children:"Hire Date"}),r=(0,n.jsx)(x.nd,{className:"w-12"}),k[12]=r,k[13]=c,k[14]=d,k[15]=N,k[16]=b,k[17]=_,k[18]=A):(r=k[12],c=k[13],d=k[14],N=k[15],b=k[16],_=k[17],A=k[18]),k[19]!==a?(S=(0,n.jsx)(x.A0,{children:(0,n.jsxs)(x.Hj,{children:[a,c,d,N,b,_,A,r]})}),k[19]=a,k[20]=S):S=k[20],k[21]!==I||k[22]!==z||k[23]!==R||k[24]!==T){let e;k[26]!==z||k[27]!==R||k[28]!==T?(e=e=>(0,n.jsxs)(x.Hj,{children:[(0,n.jsx)(x.nA,{children:(0,n.jsx)(j.S,{checked:R.includes(e.id),onCheckedChange:()=>T(e.id),"aria-label":"Select ".concat(e.name)})}),(0,n.jsx)(x.nA,{className:"font-mono text-sm",children:e.id}),(0,n.jsx)(x.nA,{className:"font-medium",children:e.name}),(0,n.jsx)(x.nA,{className:"text-muted-foreground",children:e.email}),(0,n.jsx)(x.nA,{children:z(e.departmentId)}),(0,n.jsx)(x.nA,{children:function(e){return(0,n.jsx)(o.E,{variant:{ACTIVE:"default",TRANSFERRED:"secondary",PENDING_REMOVAL:"destructive",ARCHIVED:"outline"}[e],children:e.replace("_"," ")})}(e.status)}),(0,n.jsx)(x.nA,{className:"text-muted-foreground",children:(0,h.Yq)(e.hireDate)}),(0,n.jsx)(x.nA,{children:(0,n.jsxs)(u.rI,{children:[(0,n.jsx)(u.ty,{asChild:!0,children:(0,n.jsxs)(i.$,{variant:"ghost",className:"h-8 w-8 p-0",children:[(0,n.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,n.jsx)(p.A,{className:"h-4 w-4"})]})}),(0,n.jsxs)(u.SQ,{align:"end",children:[(0,n.jsx)(u.lp,{children:"Actions"}),(0,n.jsxs)(u._2,{onClick:()=>(function(e){console.log("Edit employee:",e.id)})(e),children:[(0,n.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Edit Employee"]}),(0,n.jsxs)(u._2,{onClick:()=>(function(e){console.log("Transfer employee:",e.id)})(e),children:[(0,n.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Transfer"]}),(0,n.jsx)(u.mB,{}),(0,n.jsxs)(u._2,{onClick:()=>(function(e){console.log("Delete employee:",e.id)})(e),className:"text-destructive",children:[(0,n.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})})]},e.id),k[26]=z,k[27]=R,k[28]=T,k[29]=e):e=k[29],C=I.map(e),k[21]=I,k[22]=z,k[23]=R,k[24]=T,k[25]=C}else C=k[25];return k[30]!==C?(w=(0,n.jsx)(x.BF,{children:C}),k[30]=C,k[31]=w):w=k[31],k[32]!==S||k[33]!==w?(E=(0,n.jsx)("div",{className:"rounded-md border",children:(0,n.jsxs)(x.XI,{children:[S,w]})}),k[32]=S,k[33]=w,k[34]=E):E=k[34],E}function v(e){return e.id}var b=l(7013),_=l(245);function A(e){let s,l,a,r,c,i,d,m,o,h,x,u,j,p,f,g=(0,t.c)(28),{filters:y,onFiltersChange:N,departments:v}=e;g[0]!==y||g[1]!==N?(s=e=>{N({...y,department:"all"===e?"":e})},g[0]=y,g[1]=N,g[2]=s):s=g[2];let A=s;g[3]!==y||g[4]!==N?(l=e=>{N({...y,status:"all"===e?"":e})},g[3]=y,g[4]=N,g[5]=l):l=g[5];let C=l;g[6]===Symbol.for("react.memo_cache_sentinel")?(a=(0,n.jsx)(b.J,{htmlFor:"department-filter",children:"Department"}),g[6]=a):a=g[6];let w=y.department||"all";g[7]===Symbol.for("react.memo_cache_sentinel")?(r=(0,n.jsx)(_.bq,{children:(0,n.jsx)(_.yv,{placeholder:"All departments"})}),g[7]=r):r=g[7],g[8]===Symbol.for("react.memo_cache_sentinel")?(c=(0,n.jsx)(_.eb,{value:"all",children:"All Departments"}),i=(0,n.jsx)(_.eb,{value:"",children:"Unassigned"}),g[8]=c,g[9]=i):(c=g[8],i=g[9]),g[10]!==v?(d=v.map(S),g[10]=v,g[11]=d):d=g[11],g[12]!==d?(m=(0,n.jsxs)(_.gC,{children:[c,i,d]}),g[12]=d,g[13]=m):m=g[13],g[14]!==A||g[15]!==w||g[16]!==m?(o=(0,n.jsxs)("div",{className:"space-y-2",children:[a,(0,n.jsxs)(_.l6,{value:w,onValueChange:A,children:[r,m]})]}),g[14]=A,g[15]=w,g[16]=m,g[17]=o):o=g[17],g[18]===Symbol.for("react.memo_cache_sentinel")?(h=(0,n.jsx)(b.J,{htmlFor:"status-filter",children:"Status"}),g[18]=h):h=g[18];let E=y.status||"all";return g[19]===Symbol.for("react.memo_cache_sentinel")?(x=(0,n.jsx)(_.bq,{children:(0,n.jsx)(_.yv,{placeholder:"All statuses"})}),g[19]=x):x=g[19],g[20]===Symbol.for("react.memo_cache_sentinel")?(u=(0,n.jsxs)(_.gC,{children:[(0,n.jsx)(_.eb,{value:"all",children:"All Statuses"}),(0,n.jsx)(_.eb,{value:"ACTIVE",children:"Active"}),(0,n.jsx)(_.eb,{value:"TRANSFERRED",children:"Transferred"}),(0,n.jsx)(_.eb,{value:"PENDING_REMOVAL",children:"Pending Removal"}),(0,n.jsx)(_.eb,{value:"ARCHIVED",children:"Archived"})]}),g[20]=u):u=g[20],g[21]!==C||g[22]!==E?(j=(0,n.jsxs)("div",{className:"space-y-2",children:[h,(0,n.jsxs)(_.l6,{value:E,onValueChange:C,children:[x,u]})]}),g[21]=C,g[22]=E,g[23]=j):j=g[23],g[24]===Symbol.for("react.memo_cache_sentinel")?(p=(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(b.J,{children:"Date Range"}),(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:"Date range filtering coming soon"})]}),g[24]=p):p=g[24],g[25]!==o||g[26]!==j?(f=(0,n.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[o,j,p]}),g[25]=o,g[26]=j,g[27]=f):f=g[27],f}function S(e){return(0,n.jsx)(_.eb,{value:e.id,children:e.name},e.id)}var C=l(7273);function w(e){let{open:s,onOpenChange:l,departments:t}=e,{addEmployee:r,employees:c}=(0,m.d)(),[o,x]=(0,a.useState)(!1),[u,j]=(0,a.useState)({name:"",email:"",departmentId:""}),p=async e=>{e.preventDefault(),x(!0);try{let e=t.find(e=>e.id===u.departmentId),s=(null==e?void 0:e.code)||"GEN",n=c.filter(e=>e.id.startsWith(s)).map(e=>parseInt(e.id.split("-")[1])||0),a=Math.max(0,...n)+1,i={id:(0,h.pp)(s,a),name:u.name,email:u.email,departmentId:u.departmentId||null,status:"ACTIVE",hireDate:new Date,transferHistory:[],createdAt:new Date,updatedAt:new Date};r(i),j({name:"",email:"",departmentId:""}),l(!1)}catch(e){console.error("Failed to add employee:",e)}finally{x(!1)}},f=(e,s)=>{j(l=>({...l,[e]:s}))};return(0,n.jsx)(C.lG,{open:s,onOpenChange:l,children:(0,n.jsxs)(C.Cf,{className:"sm:max-w-[425px]",children:[(0,n.jsxs)(C.c7,{children:[(0,n.jsx)(C.L3,{children:"Add New Employee"}),(0,n.jsx)(C.rr,{children:"Create a new employee record. They will be assigned to the selected department."})]}),(0,n.jsxs)("form",{onSubmit:p,children:[(0,n.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsx)(b.J,{htmlFor:"name",children:"Full Name"}),(0,n.jsx)(d.p,{id:"name",value:u.name,onChange:e=>f("name",e.target.value),placeholder:"Enter employee name",required:!0})]}),(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsx)(b.J,{htmlFor:"email",children:"Email Address"}),(0,n.jsx)(d.p,{id:"email",type:"email",value:u.email,onChange:e=>f("email",e.target.value),placeholder:"Enter email address",required:!0})]}),(0,n.jsxs)("div",{className:"grid gap-2",children:[(0,n.jsx)(b.J,{htmlFor:"department",children:"Department"}),(0,n.jsxs)(_.l6,{value:u.departmentId,onValueChange:e=>f("departmentId",e),children:[(0,n.jsx)(_.bq,{children:(0,n.jsx)(_.yv,{placeholder:"Select department"})}),(0,n.jsxs)(_.gC,{children:[(0,n.jsx)(_.eb,{value:"",children:"Unassigned (Free Bucket)"}),t.map(e=>(0,n.jsxs)(_.eb,{value:e.id,children:[e.name," (",e.code,")"]},e.id))]})]})]})]}),(0,n.jsxs)(C.Es,{children:[(0,n.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>l(!1),disabled:o,children:"Cancel"}),(0,n.jsx)(i.$,{type:"submit",disabled:o,children:o?"Adding...":"Add Employee"})]})]})]})})}var E=l(8379),k=l(5369),I=l(7747);function D(e){let{selectedCount:s,onClearSelection:l}=e,{selectedEmployees:t,executeBulkOperation:r}=(0,m.d)(),[c,d]=(0,a.useState)(!1),o=async()=>{d(!0);try{console.log("Bulk transfer:",t)}catch(e){console.error("Bulk transfer failed:",e)}finally{d(!1)}},h=async()=>{d(!0);try{await r({type:"status_update",employeeIds:t,newStatus:"ARCHIVED"})}catch(e){console.error("Bulk archive failed:",e)}finally{d(!1)}},x=async()=>{if(confirm("Are you sure you want to delete ".concat(s," employees? This action cannot be undone."))){d(!0);try{await r({type:"delete",employeeIds:t})}catch(e){console.error("Bulk delete failed:",e)}finally{d(!1)}}};return(0,n.jsxs)("div",{className:"flex items-center justify-between p-4 bg-muted/50 rounded-lg border",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(E.A,{className:"h-4 w-4"}),(0,n.jsxs)("span",{className:"text-sm font-medium",children:[s," employee",1!==s?"s":""," selected"]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)(i.$,{variant:"outline",size:"sm",onClick:o,disabled:c,children:[(0,n.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Transfer"]}),(0,n.jsxs)(i.$,{variant:"outline",size:"sm",onClick:h,disabled:c,children:[(0,n.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Archive"]}),(0,n.jsxs)(i.$,{variant:"outline",size:"sm",onClick:x,disabled:c,className:"text-destructive hover:text-destructive",children:[(0,n.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Delete"]}),(0,n.jsx)(i.$,{variant:"ghost",size:"sm",onClick:l,disabled:c,children:(0,n.jsx)(I.A,{className:"h-4 w-4"})})]})]})}var R=l(538),T=l(5384),F=l(9309),V=l(4825),B=l(553),$=l(3719);function Z(){let e,s,l,o,h,x,u,j,p,f,g,y,v,b,_,S,C,k,I,Z,W,M,U,G,P,L,Q,X,Y,K,ee,es,el,en,et,ea,er,ec,ei,ed,em,eo,eh,ex,eu=(0,t.c)(91);(0,r.useSession)();let{employees:ej,departments:ep,selectedEmployees:ef,searchQuery:eg,setSearchQuery:ey,getFilteredEmployees:eN,clearSelection:ev}=(0,m.d)(),[eb,e_]=(0,a.useState)(!1),[eA,eS]=(0,a.useState)(!1);eu[0]===Symbol.for("react.memo_cache_sentinel")?(e={department:"",status:"",dateRange:{from:null,to:null}},eu[0]=e):e=eu[0];let[eC,ew]=(0,a.useState)(e);(0,R.s)(),eu[1]!==eN?(s=eN(),eu[1]=eN,eu[2]=s):s=eu[2];let eE=s,ek=ef.length>0;eu[3]!==ey?(l=e=>{ey(e)},eu[3]=ey,eu[4]=l):l=eu[4];let eI=l;eu[5]!==ey?(o=()=>{ew({department:"",status:"",dateRange:{from:null,to:null}}),ey("")},eu[5]=ey,eu[6]=o):o=eu[6];let eD=o;return eu[7]===Symbol.for("react.memo_cache_sentinel")?(h=(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Employees"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Manage your organization's employees and their assignments."})]}),eu[7]=h):h=eu[7],eu[8]===Symbol.for("react.memo_cache_sentinel")?(x=(0,n.jsxs)(i.$,{variant:"outline",onClick:q,children:[(0,n.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"Import CSV"]}),eu[8]=x):x=eu[8],eu[9]===Symbol.for("react.memo_cache_sentinel")?(u=(0,n.jsxs)(i.$,{variant:"outline",onClick:J,children:[(0,n.jsx)(F.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]}),eu[9]=u):u=eu[9],eu[10]===Symbol.for("react.memo_cache_sentinel")?(j=(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[h,(0,n.jsxs)("div",{className:"flex gap-2",children:[x,u,(0,n.jsxs)(i.$,{onClick:()=>e_(!0),children:[(0,n.jsx)(V.A,{className:"h-4 w-4 mr-2"}),"Add Employee"]})]})]}),eu[10]=j):j=eu[10],eu[11]===Symbol.for("react.memo_cache_sentinel")?(p=(0,n.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,n.jsx)(c.ZB,{className:"text-sm font-medium",children:"Total Employees"}),(0,n.jsx)(E.A,{className:"h-4 w-4 text-muted-foreground"})]}),eu[11]=p):p=eu[11],eu[12]!==ej.length?(f=(0,n.jsx)("div",{className:"text-2xl font-bold",children:ej.length}),eu[12]=ej.length,eu[13]=f):f=eu[13],eu[14]===Symbol.for("react.memo_cache_sentinel")?(g=(0,n.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active employees"}),eu[14]=g):g=eu[14],eu[15]!==f?(y=(0,n.jsxs)(c.Zp,{children:[p,(0,n.jsxs)(c.Wu,{children:[f,g]})]}),eu[15]=f,eu[16]=y):y=eu[16],eu[17]===Symbol.for("react.memo_cache_sentinel")?(v=(0,n.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,n.jsx)(c.ZB,{className:"text-sm font-medium",children:"Active"}),(0,n.jsx)(E.A,{className:"h-4 w-4 text-green-600"})]}),eu[17]=v):v=eu[17],eu[18]!==ej?(b=ej.filter(O),eu[18]=ej,eu[19]=b):b=eu[19],eu[20]!==b.length?(_=(0,n.jsx)("div",{className:"text-2xl font-bold",children:b.length}),eu[20]=b.length,eu[21]=_):_=eu[21],eu[22]===Symbol.for("react.memo_cache_sentinel")?(S=(0,n.jsx)("p",{className:"text-xs text-muted-foreground",children:"Currently active"}),eu[22]=S):S=eu[22],eu[23]!==_?(C=(0,n.jsxs)(c.Zp,{children:[v,(0,n.jsxs)(c.Wu,{children:[_,S]})]}),eu[23]=_,eu[24]=C):C=eu[24],eu[25]===Symbol.for("react.memo_cache_sentinel")?(k=(0,n.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,n.jsx)(c.ZB,{className:"text-sm font-medium",children:"Transferred"}),(0,n.jsx)(E.A,{className:"h-4 w-4 text-blue-600"})]}),eu[25]=k):k=eu[25],eu[26]!==ej?(I=ej.filter(H),eu[26]=ej,eu[27]=I):I=eu[27],eu[28]!==I.length?(Z=(0,n.jsx)("div",{className:"text-2xl font-bold",children:I.length}),eu[28]=I.length,eu[29]=Z):Z=eu[29],eu[30]===Symbol.for("react.memo_cache_sentinel")?(W=(0,n.jsx)("p",{className:"text-xs text-muted-foreground",children:"Recently transferred"}),eu[30]=W):W=eu[30],eu[31]!==Z?(M=(0,n.jsxs)(c.Zp,{children:[k,(0,n.jsxs)(c.Wu,{children:[Z,W]})]}),eu[31]=Z,eu[32]=M):M=eu[32],eu[33]===Symbol.for("react.memo_cache_sentinel")?(U=(0,n.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,n.jsx)(c.ZB,{className:"text-sm font-medium",children:"Unassigned"}),(0,n.jsx)(E.A,{className:"h-4 w-4 text-orange-600"})]}),eu[33]=U):U=eu[33],eu[34]!==ej?(G=ej.filter(z),eu[34]=ej,eu[35]=G):G=eu[35],eu[36]!==G.length?(P=(0,n.jsx)("div",{className:"text-2xl font-bold",children:G.length}),eu[36]=G.length,eu[37]=P):P=eu[37],eu[38]===Symbol.for("react.memo_cache_sentinel")?(L=(0,n.jsx)("p",{className:"text-xs text-muted-foreground",children:"In free bucket"}),eu[38]=L):L=eu[38],eu[39]!==P?(Q=(0,n.jsxs)(c.Zp,{children:[U,(0,n.jsxs)(c.Wu,{children:[P,L]})]}),eu[39]=P,eu[40]=Q):Q=eu[40],eu[41]!==y||eu[42]!==C||eu[43]!==M||eu[44]!==Q?(X=(0,n.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[y,C,M,Q]}),eu[41]=y,eu[42]=C,eu[43]=M,eu[44]=Q,eu[45]=X):X=eu[45],eu[46]===Symbol.for("react.memo_cache_sentinel")?(Y=(0,n.jsx)(c.ZB,{children:"Employee Management"}),eu[46]=Y):Y=eu[46],eu[47]===Symbol.for("react.memo_cache_sentinel")?(K=(0,n.jsx)(B.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),eu[47]=K):K=eu[47],eu[48]!==eI?(ee=e=>eI(e.target.value),eu[48]=eI,eu[49]=ee):ee=eu[49],eu[50]!==eg||eu[51]!==ee?(es=(0,n.jsxs)("div",{className:"relative",children:[K,(0,n.jsx)(d.p,{placeholder:"Search employees...",value:eg,onChange:ee,className:"pl-8 w-[300px]"})]}),eu[50]=eg,eu[51]=ee,eu[52]=es):es=eu[52],eu[53]!==eA?(el=()=>eS(!eA),eu[53]=eA,eu[54]=el):el=eu[54],eu[55]===Symbol.for("react.memo_cache_sentinel")?(en=(0,n.jsx)($.A,{className:"h-4 w-4 mr-2"}),eu[55]=en):en=eu[55],eu[56]!==el?(et=(0,n.jsxs)(i.$,{variant:"outline",size:"sm",onClick:el,children:[en,"Filters"]}),eu[56]=el,eu[57]=et):et=eu[57],eu[58]!==eC||eu[59]!==eD||eu[60]!==eg?(ea=(eg||eC.department||eC.status)&&(0,n.jsx)(i.$,{variant:"ghost",size:"sm",onClick:eD,children:"Clear"}),eu[58]=eC,eu[59]=eD,eu[60]=eg,eu[61]=ea):ea=eu[61],eu[62]!==es||eu[63]!==et||eu[64]!==ea?(er=(0,n.jsx)(c.aR,{children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[Y,(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[es,et,ea]})]})}),eu[62]=es,eu[63]=et,eu[64]=ea,eu[65]=er):er=eu[65],eu[66]!==ep||eu[67]!==eC||eu[68]!==eA?(ec=eA&&(0,n.jsx)("div",{className:"mb-4",children:(0,n.jsx)(A,{filters:eC,onFiltersChange:ew,departments:ep})}),eu[66]=ep,eu[67]=eC,eu[68]=eA,eu[69]=ec):ec=eu[69],eu[70]!==ev||eu[71]!==ek||eu[72]!==ef.length?(ei=ek&&(0,n.jsx)("div",{className:"mb-4",children:(0,n.jsx)(D,{selectedCount:ef.length,onClearSelection:ev})}),eu[70]=ev,eu[71]=ek,eu[72]=ef.length,eu[73]=ei):ei=eu[73],eu[74]!==ep||eu[75]!==eE?(ed=(0,n.jsx)(N,{employees:eE,departments:ep}),eu[74]=ep,eu[75]=eE,eu[76]=ed):ed=eu[76],eu[77]!==ec||eu[78]!==ei||eu[79]!==ed?(em=(0,n.jsxs)(c.Wu,{children:[ec,ei,ed]}),eu[77]=ec,eu[78]=ei,eu[79]=ed,eu[80]=em):em=eu[80],eu[81]!==er||eu[82]!==em?(eo=(0,n.jsxs)(c.Zp,{children:[er,em]}),eu[81]=er,eu[82]=em,eu[83]=eo):eo=eu[83],eu[84]!==ep||eu[85]!==eb?(eh=(0,n.jsx)(w,{open:eb,onOpenChange:e_,departments:ep}),eu[84]=ep,eu[85]=eb,eu[86]=eh):eh=eu[86],eu[87]!==X||eu[88]!==eo||eu[89]!==eh?(ex=(0,n.jsxs)("div",{className:"space-y-6",children:[j,X,eo,eh]}),eu[87]=X,eu[88]=eo,eu[89]=eh,eu[90]=ex):ex=eu[90],ex}function z(e){return null===e.departmentId}function H(e){return"TRANSFERRED"===e.status}function O(e){return"ACTIVE"===e.status}function q(){console.log("Importing employees...")}function J(){console.log("Exporting employees...")}}},e=>{e.O(0,[235,666,368,18,445,3,654,816,210,441,326,358],()=>e(e.s=560)),_N_E=e.O()}]);