{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { withA<PERSON> } from \"next-auth/middleware\"\nimport { NextResponse } from \"next/server\"\nimport type { NextRequestWithAuth } from \"next-auth/middleware\"\n\nexport default withAuth(\n  function middleware(req: NextRequestWithAuth) {\n    const { pathname } = req.nextUrl\n    const token = req.nextauth.token\n\n    // Public routes that don't require authentication\n    const publicRoutes = ['/auth/signin', '/auth/signup', '/auth/error']\n    if (publicRoutes.includes(pathname)) {\n      return NextResponse.next()\n    }\n\n    // Redirect to signin if not authenticated\n    if (!token) {\n      return NextResponse.redirect(new URL('/auth/signin', req.url))\n    }\n\n    // Role-based access control\n    const userRole = token.role as string\n    \n    // Admin routes\n    if (pathname.startsWith('/admin') && userRole !== 'ADMIN') {\n      return NextResponse.redirect(new URL('/dashboard', req.url))\n    }\n\n    // HR Manager routes\n    const hrRoutes = ['/employees/create', '/employees/bulk', '/departments/create']\n    if (hrRoutes.some(route => pathname.startsWith(route)) && \n        !['ADMIN', 'HR_MANAGER'].includes(userRole)) {\n      return NextResponse.redirect(new URL('/dashboard', req.url))\n    }\n\n    return NextResponse.next()\n  },\n  {\n    callbacks: {\n      authorized: ({ token, req }) => {\n        const { pathname } = req.nextUrl\n        const publicRoutes = ['/auth/signin', '/auth/signup', '/auth/error']\n        \n        // Allow public routes\n        if (publicRoutes.includes(pathname)) return true\n        \n        // Require token for all other routes\n        return !!token\n      }\n    }\n  }\n)\n\nexport const config = {\n  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)']\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;uCAGe,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACpB,SAAS,WAAW,GAAwB;IAC1C,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO;IAChC,MAAM,QAAQ,IAAI,QAAQ,CAAC,KAAK;IAEhC,kDAAkD;IAClD,MAAM,eAAe;QAAC;QAAgB;QAAgB;KAAc;IACpE,IAAI,aAAa,QAAQ,CAAC,WAAW;QACnC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,0CAA0C;IAC1C,IAAI,CAAC,OAAO;QACV,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,gBAAgB,IAAI,GAAG;IAC9D;IAEA,4BAA4B;IAC5B,MAAM,WAAW,MAAM,IAAI;IAE3B,eAAe;IACf,IAAI,SAAS,UAAU,CAAC,aAAa,aAAa,SAAS;QACzD,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,IAAI,GAAG;IAC5D;IAEA,oBAAoB;IACpB,MAAM,WAAW;QAAC;QAAqB;QAAmB;KAAsB;IAChF,IAAI,SAAS,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC,WAC3C,CAAC;QAAC;QAAS;KAAa,CAAC,QAAQ,CAAC,WAAW;QAC/C,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,IAAI,GAAG;IAC5D;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;YACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO;YAChC,MAAM,eAAe;gBAAC;gBAAgB;gBAAgB;aAAc;YAEpE,sBAAsB;YACtB,IAAI,aAAa,QAAQ,CAAC,WAAW,OAAO;YAE5C,qCAAqC;YACrC,OAAO,CAAC,CAAC;QACX;IACF;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QAAC;KAAoD;AAChE"}}]}