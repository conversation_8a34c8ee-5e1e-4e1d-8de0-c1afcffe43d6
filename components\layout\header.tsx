"use client"

import { useSession, signOut } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { LogOut, Settings, User } from "lucide-react"

export function Header() {
  const { data: session } = useSession()

  const handleSignOut = () => {
    signOut({ callbackUrl: "/auth/signin" })
  }

  return (
    <header className="flex h-20 items-center justify-between border-b border-border/50 bg-white/80 backdrop-blur-md px-8 shadow-soft">
      <div className="flex items-center space-x-6">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center shadow-medium">
            <div className="text-white text-sm font-bold">HR</div>
          </div>
          <div>
            <h2 className="text-xl font-semibold text-foreground">
              Welcome back, {session?.user?.name?.split(' ')[0]}
            </h2>
            <p className="text-sm text-muted-foreground">
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>
          </div>
        </div>
      </div>

      <div className="flex items-center space-x-6">
        <div className="hidden md:flex items-center space-x-3">
          <div className="px-3 py-1.5 bg-gradient-to-r from-blue-50 to-purple-50 rounded-full border border-blue-200/50">
            <span className="text-sm font-medium text-blue-700">
              {session?.user?.role?.replace('_', ' ')}
            </span>
          </div>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="relative h-10 w-10 rounded-full hover:bg-muted/50 transition-colors">
              <Avatar className="h-10 w-10 border-2 border-white shadow-medium">
                <AvatarImage src={session?.user?.image || ""} alt={session?.user?.name || ""} />
                <AvatarFallback className="bg-gradient-primary text-white font-semibold">
                  {session?.user?.name?.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>

          <DropdownMenuContent className="w-64 shadow-strong border-0 bg-white/95 backdrop-blur-md" align="end" forceMount>
            <DropdownMenuLabel className="font-normal p-4">
              <div className="flex flex-col space-y-2">
                <p className="text-base font-semibold leading-none text-foreground">
                  {session?.user?.name}
                </p>
                <p className="text-sm leading-none text-muted-foreground">
                  {session?.user?.email}
                </p>
                <div className="mt-2 px-2 py-1 bg-muted/50 rounded-md">
                  <p className="text-xs font-medium text-muted-foreground">
                    Role: {session?.user?.role?.replace('_', ' ')}
                  </p>
                </div>
              </div>
            </DropdownMenuLabel>

            <DropdownMenuSeparator className="bg-border/50" />

            <DropdownMenuItem className="p-3 cursor-pointer hover:bg-muted/50 transition-colors">
              <User className="mr-3 h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Profile Settings</span>
            </DropdownMenuItem>

            <DropdownMenuItem className="p-3 cursor-pointer hover:bg-muted/50 transition-colors">
              <Settings className="mr-3 h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Preferences</span>
            </DropdownMenuItem>

            <DropdownMenuSeparator className="bg-border/50" />

            <DropdownMenuItem
              onClick={handleSignOut}
              className="p-3 cursor-pointer hover:bg-red-50 hover:text-red-600 transition-colors"
            >
              <LogOut className="mr-3 h-4 w-4" />
              <span className="font-medium">Sign Out</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  )
}