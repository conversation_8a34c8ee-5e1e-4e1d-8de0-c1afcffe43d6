"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { FreeBucketTable } from "@/components/free-bucket/free-bucket-table"
import { AssignToDepartmentDialog } from "@/components/free-bucket/assign-to-department-dialog"
import { BulkAssignmentBar } from "@/components/free-bucket/bulk-assignment-bar"
import { useHRStore } from "@/lib/store/hr-store"
import { useSampleData } from "@/lib/hooks/use-sample-data"
import { 
  Archive, 
  Users, 
  Search,
  UserPlus,
  ArrowRight,
  AlertTriangle
} from "lucide-react"

export default function FreeBucketPage() {
  const { data: session } = useSession()
  const { 
    employees, 
    departments, 
    selectedEmployees, 
    searchQuery, 
    setSearchQuery,
    clearSelection
  } = useHRStore()
  
  const [showAssignDialog, setShowAssignDialog] = useState(false)
  const [searchFilter, setSearchFilter] = useState("")

  // Load sample data
  useSampleData()

  // Get unassigned employees (free bucket)
  const freeBucketEmployees = employees.filter(emp => emp.departmentId === null)
  
  // Filter employees based on search
  const filteredEmployees = freeBucketEmployees.filter(emp =>
    emp.name.toLowerCase().includes(searchFilter.toLowerCase()) ||
    emp.email.toLowerCase().includes(searchFilter.toLowerCase()) ||
    emp.id.toLowerCase().includes(searchFilter.toLowerCase())
  )

  const selectedFreeBucketEmployees = selectedEmployees.filter(id =>
    freeBucketEmployees.some(emp => emp.id === id)
  )

  const hasSelection = selectedFreeBucketEmployees.length > 0

  // Calculate statistics
  const totalUnassigned = freeBucketEmployees.length
  const activeUnassigned = freeBucketEmployees.filter(emp => emp.status === 'ACTIVE').length
  const recentlyAdded = freeBucketEmployees.filter(emp => {
    const daysSinceCreated = Math.floor((Date.now() - emp.createdAt.getTime()) / (1000 * 60 * 60 * 24))
    return daysSinceCreated <= 7
  }).length

  const handleSearch = (value: string) => {
    setSearchFilter(value)
  }

  const handleAssignSelected = () => {
    if (selectedFreeBucketEmployees.length > 0) {
      setShowAssignDialog(true)
    }
  }

  const handleClearSearch = () => {
    setSearchFilter("")
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Free Bucket</h1>
          <p className="text-muted-foreground">
            Manage unassigned employees and assign them to departments.
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={handleAssignSelected}
            disabled={!hasSelection}
          >
            <ArrowRight className="h-4 w-4 mr-2" />
            Assign Selected ({selectedFreeBucketEmployees.length})
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Unassigned</CardTitle>
            <Archive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalUnassigned}</div>
            <p className="text-xs text-muted-foreground">
              Employees in free bucket
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <Users className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeUnassigned}</div>
            <p className="text-xs text-muted-foreground">
              Active unassigned
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recently Added</CardTitle>
            <UserPlus className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{recentlyAdded}</div>
            <p className="text-xs text-muted-foreground">
              Added this week
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Departments</CardTitle>
            <ArrowRight className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{departments.length}</div>
            <p className="text-xs text-muted-foreground">
              Available for assignment
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Alert for high number of unassigned employees */}
      {totalUnassigned > 10 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="text-orange-800 flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              High Number of Unassigned Employees
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-orange-700">
              You have {totalUnassigned} unassigned employees. Consider assigning them to departments 
              to improve organization and capacity utilization.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Search and Employee Management */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Unassigned Employees</CardTitle>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search unassigned employees..."
                  value={searchFilter}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-8 w-[300px]"
                />
              </div>
              {searchFilter && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearSearch}
                >
                  Clear
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {hasSelection && (
            <div className="mb-4">
              <BulkAssignmentBar
                selectedCount={selectedFreeBucketEmployees.length}
                onAssign={handleAssignSelected}
                onClearSelection={clearSelection}
              />
            </div>
          )}

          {totalUnassigned === 0 ? (
            <div className="text-center py-12">
              <Archive className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Unassigned Employees</h3>
              <p className="text-muted-foreground">
                All employees are currently assigned to departments. Great job!
              </p>
            </div>
          ) : filteredEmployees.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                No employees match your search criteria.
              </p>
            </div>
          ) : (
            <FreeBucketTable
              employees={filteredEmployees}
              departments={departments}
            />
          )}
        </CardContent>
      </Card>

      {/* Assign to Department Dialog */}
      <AssignToDepartmentDialog
        open={showAssignDialog}
        onOpenChange={setShowAssignDialog}
        selectedEmployees={selectedFreeBucketEmployees}
        departments={departments}
      />
    </div>
  )
}
