"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[814],{130:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(8776).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},208:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(8776).A)("<PERSON>ertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},553:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(8776).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},1895:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(8776).A)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},4825:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(8776).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},5076:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(8776).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},7747:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(8776).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},8106:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(8776).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},8181:(e,t,r)=>{r.d(t,{UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>Q,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>$});var n=r(2115),o=r(3990),a=r(5896),l=r(9602),i=r(3323),s=r(7396),d=r(2800),c=r(8552),u=r(2799),p=r(400),f=r(4372),y=r(5704),h=r(3382),g=r(4103),v=r(5441),k=r(5155),m="Dialog",[x,j]=(0,l.A)(m),[A,D]=x(m),b=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:d=!0}=e,c=n.useRef(null),u=n.useRef(null),[p,f]=(0,s.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:m});return(0,k.jsx)(A,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};b.displayName=m;var C="DialogTrigger",w=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=D(C,r),i=(0,a.s)(t,l.triggerRef);return(0,k.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":W(l.open),...n,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});w.displayName=C;var M="DialogPortal",[R,I]=x(M,{forceMount:void 0}),N=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=D(M,t);return(0,k.jsx)(R,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,k.jsx)(p.C,{present:r||l.open,children:(0,k.jsx)(u.Z,{asChild:!0,container:a,children:e})}))})};N.displayName=M;var O="DialogOverlay",_=n.forwardRef((e,t)=>{let r=I(O,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=D(O,e.__scopeDialog);return a.modal?(0,k.jsx)(p.C,{present:n||a.open,children:(0,k.jsx)(F,{...o,ref:t})}):null});_.displayName=O;var E=(0,v.TL)("DialogOverlay.RemoveScroll"),F=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(O,r);return(0,k.jsx)(h.A,{as:E,allowPinchZoom:!0,shards:[o.contentRef],children:(0,k.jsx)(f.sG.div,{"data-state":W(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),q="DialogContent",P=n.forwardRef((e,t)=>{let r=I(q,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=D(q,e.__scopeDialog);return(0,k.jsx)(p.C,{present:n||a.open,children:a.modal?(0,k.jsx)(T,{...o,ref:t}):(0,k.jsx)(B,{...o,ref:t})})});P.displayName=q;var T=n.forwardRef((e,t)=>{let r=D(q,e.__scopeDialog),l=n.useRef(null),i=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,g.Eq)(e)},[]),(0,k.jsx)(H,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),B=n.forwardRef((e,t)=>{let r=D(q,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,k.jsx)(H,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(o.current||null==(l=r.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,l;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let i=t.target;(null==(l=r.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),H=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,u=D(q,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,y.Oh)(),(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,k.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":W(u.open),...s,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)(X,{titleId:u.titleId}),(0,k.jsx)(Y,{contentRef:p,descriptionId:u.descriptionId})]})]})}),V="DialogTitle",Z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(V,r);return(0,k.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});Z.displayName=V;var z="DialogDescription",G=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(z,r);return(0,k.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});G.displayName=z;var L="DialogClose",S=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=D(L,r);return(0,k.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function W(e){return e?"open":"closed"}S.displayName=L;var U="DialogTitleWarning",[J,K]=(0,l.q)(U,{contentName:q,titleName:V,docsSlug:"dialog"}),X=e=>{let{titleId:t}=e,r=K(U),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},Y=e=>{let{contentRef:t,descriptionId:r}=e,o=K("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(a))},[a,t,r]),null},Q=b,$=w,ee=N,et=_,er=P,en=Z,eo=G,ea=S},8778:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(8776).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])}}]);