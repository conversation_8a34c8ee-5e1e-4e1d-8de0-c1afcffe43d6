import { withAuth } from "next-auth/middleware"
import { NextResponse } from "next/server"

export default withAuth(
  function middleware(req) {
    const { pathname } = req.nextUrl
    const token = req.nextauth.token

    // Public routes that don't require authentication
    const publicRoutes = ['/auth/signin', '/auth/signup', '/auth/error']
    if (publicRoutes.includes(pathname)) {
      return NextResponse.next()
    }

    // Redirect to signin if not authenticated
    if (!token) {
      return NextResponse.redirect(new URL('/auth/signin', req.url))
    }

    // Role-based access control
    const userRole = token.role as string
    
    // Admin routes
    if (pathname.startsWith('/admin') && userRole !== 'ADMIN') {
      return NextResponse.redirect(new URL('/dashboard', req.url))
    }

    // HR Manager routes
    const hrRoutes = ['/employees/create', '/employees/bulk', '/departments/create']
    if (hrRoutes.some(route => pathname.startsWith(route)) && 
        !['ADMIN', 'HR_MANAGER'].includes(userRole)) {
      return NextResponse.redirect(new URL('/dashboard', req.url))
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl
        const publicRoutes = ['/auth/signin', '/auth/signup', '/auth/error']
        
        // Allow public routes
        if (publicRoutes.includes(pathname)) return true
        
        // Require token for all other routes
        return !!token
      }
    }
  }
)

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)']
}