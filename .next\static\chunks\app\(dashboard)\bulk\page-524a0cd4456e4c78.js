(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[147],{245:(e,s,t)=>{"use strict";t.d(s,{bq:()=>u,eb:()=>f,gC:()=>p,l6:()=>o,yv:()=>m});var a=t(5155),l=t(2115),r=t(5371),n=t(4475),i=t(9322),c=t(8079),d=t(198);let o=r.bL;r.YJ;let m=r.WT,u=l.forwardRef((e,s)=>{let{className:t,children:l,...i}=e;return(0,a.jsxs)(r.l9,{ref:s,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...i,children:[l,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=r.l9.displayName;let h=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.PP,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})});h.displayName=r.PP.displayName;let x=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.wn,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});x.displayName=r.wn.displayName;let p=l.forwardRef((e,s)=>{let{className:t,children:l,position:n="popper",...i}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{ref:s,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,a.jsx)(h,{}),(0,a.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,a.jsx)(x,{})]})})});p.displayName=r.UC.displayName,l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.JU,{ref:s,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...l})}).displayName=r.JU.displayName;let f=l.forwardRef((e,s)=>{let{className:t,children:l,...n}=e;return(0,a.jsxs)(r.q7,{ref:s,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(c.A,{className:"h-4 w-4"})})}),(0,a.jsx)(r.p4,{children:l})]})});f.displayName=r.q7.displayName,l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.wv,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",t),...l})}).displayName=r.wv.displayName},2993:(e,s,t)=>{Promise.resolve().then(t.bind(t,3985))},3719:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(8776).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},3985:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>Z});var a=t(5155),l=t(9749),r=t(2115),n=t(235),i=t(9683),c=t(425),d=t(4373),o=t(6226),m=t(9159),u=t(198),h=t(5384),x=t(8776);let p=(0,x.A)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]);var f=t(9309),j=t(8778);function y(e){let{departments:s}=e,{employees:t,addEmployee:l}=(0,d.d)(),[n,x]=(0,r.useState)(!1),[y,g]=(0,r.useState)(0),[v,b]=(0,r.useState)(null),N=(0,r.useRef)(null),w=async e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];if(t){if(!t.name.endsWith(".csv"))return void alert("Please select a CSV file");x(!0),g(0),b(null);try{let e=await t.text(),s=await S(e);b(s)}catch(e){console.error("CSV import failed:",e),alert("Failed to import CSV file")}finally{x(!1),g(0),N.current&&(N.current.value="")}}},S=async e=>{let a=e.trim().split("\n"),l=a[0].split(",").map(e=>e.trim().toLowerCase()),r=["name","email"].filter(e=>!l.includes(e));if(r.length>0)throw Error("Missing required headers: ".concat(r.join(", ")));let n={success:[],errors:[]};for(let e=1;e<a.length;e++){g(e/(a.length-1)*100);let r=a[e].split(",").map(e=>e.trim()),c={};l.forEach((e,s)=>{c[e]=r[s]||""});try{var i;if(!c.name||!c.email)throw Error("Name and email are required");if(t.some(e=>e.email.toLowerCase()===c.email.toLowerCase()))throw Error("Email already exists");let e=null;if(c.departmentcode){let t=s.find(e=>e.code.toLowerCase()===c.departmentcode.toLowerCase());if(!t)throw Error("Department with code '".concat(c.departmentcode,"' not found"));e=t.id}let a=s.find(s=>s.id===e),l=(null==a?void 0:a.code)||"GEN",r=t.filter(e=>e.id.startsWith(l)).map(e=>parseInt(e.id.split("-")[1])||0),d=Math.max(0,...r)+n.success.length+1,o=(0,u.pp)(l,d),m=["ACTIVE","TRANSFERRED","PENDING_REMOVAL","ARCHIVED"],h=(null==(i=c.status)?void 0:i.toUpperCase())||"ACTIVE";if(!m.includes(h))throw Error("Invalid status '".concat(c.status,"'. Must be one of: ").concat(m.join(", ")));let x={id:o,name:c.name,email:c.email,departmentId:e,status:h,hireDate:new Date,transferHistory:[],createdAt:new Date,updatedAt:new Date};n.success.push(x)}catch(s){n.errors.push({row:e+1,data:c,error:s instanceof Error?s.message:"Unknown error"})}await new Promise(e=>setTimeout(e,10))}return n};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5"}),"Upload CSV File"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(c.$,{onClick:()=>{var e;null==(e=N.current)||e.click()},disabled:n,children:[(0,a.jsx)(p,{className:"h-4 w-4 mr-2"}),"Select CSV File"]}),(0,a.jsxs)(c.$,{variant:"outline",onClick:()=>{let e=new Blob(["name,email,departmentcode,status\nJohn Doe,<EMAIL>,ENG,ACTIVE\nJane Smith,<EMAIL>,MKT,ACTIVE\nBob Johnson,<EMAIL>,,ACTIVE"],{type:"text/csv"}),s=URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download="employee-import-template.csv",t.click(),URL.revokeObjectURL(s)},children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Download Template"]})]}),n&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{children:"Processing CSV..."}),(0,a.jsxs)("span",{children:[Math.round(y),"%"]})]}),(0,a.jsx)(m.k,{value:y})]}),(0,a.jsx)("input",{ref:N,type:"file",accept:".csv",onChange:w,className:"hidden"})]})})]}),v&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 text-green-600"}),"Import Results"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsxs)(o.E,{variant:"default",className:"bg-green-100 text-green-800",children:[v.success.length," Successful"]}),v.errors.length>0&&(0,a.jsxs)(o.E,{variant:"destructive",children:[v.errors.length," Errors"]})]}),v.errors.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"font-medium text-red-800",children:"Errors:"}),(0,a.jsx)("div",{className:"max-h-40 overflow-y-auto space-y-1",children:v.errors.map((e,s)=>(0,a.jsxs)("div",{className:"text-sm p-2 bg-red-50 rounded border-l-4 border-red-400",children:[(0,a.jsxs)("div",{className:"font-medium",children:["Row ",e.row,":"]}),(0,a.jsx)("div",{className:"text-red-700",children:e.error}),(0,a.jsxs)("div",{className:"text-xs text-red-600 mt-1",children:["Data: ",JSON.stringify(e.data)]})]},s))})]}),v.success.length>0&&(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(c.$,{onClick:()=>{v&&(v.success.forEach(e=>{l(e)}),alert("Successfully imported ".concat(v.success.length," employees")),b(null))},children:["Import ",v.success.length," Employees"]}),(0,a.jsx)(c.$,{variant:"outline",onClick:()=>b(null),children:"Cancel"})]})]})})]}),(0,a.jsxs)(i.Zp,{className:"border-blue-200 bg-blue-50",children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{className:"text-blue-800",children:"CSV Format Instructions"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2 text-sm text-blue-700",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Required columns:"})," name, email"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Optional columns:"})," departmentcode, status"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Department codes:"})," ",s.map(e=>e.code).join(", ")]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Valid statuses:"})," ACTIVE, TRANSFERRED, PENDING_REMOVAL, ARCHIVED"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Note:"})," If departmentcode is empty, employee will be added to the free bucket"]})]})})]})]})}var g=t(7013),v=t(9138),b=t(245),N=t(3719);function w(e){let s,t,n,d,o,m,h,x,j,y,w,C,A,_,k,R,D,I,V,Z,T,U,q,B,L,O,W,P,M,F,G,H,J,z,$,Y,K,X,Q,ee=(0,l.c)(70),{employees:es,departments:et}=e;ee[0]===Symbol.for("react.memo_cache_sentinel")?(s={department:"all",status:"all",includeTransferHistory:!1},ee[0]=s):s=ee[0];let[ea,el]=(0,r.useState)(s);ee[1]===Symbol.for("react.memo_cache_sentinel")?(t={id:!0,name:!0,email:!0,department:!0,status:!0,hireDate:!0,createdAt:!1,updatedAt:!1},ee[1]=t):t=ee[1];let[er,en]=(0,r.useState)(t);ee[2]===Symbol.for("react.memo_cache_sentinel")?(n=[{key:"id",label:"Employee ID",required:!0},{key:"name",label:"Name",required:!0},{key:"email",label:"Email",required:!0},{key:"department",label:"Department",required:!1},{key:"status",label:"Status",required:!1},{key:"hireDate",label:"Hire Date",required:!1},{key:"createdAt",label:"Created Date",required:!1},{key:"updatedAt",label:"Updated Date",required:!1}],ee[2]=n):n=ee[2];let ei=n;ee[3]!==es||ee[4]!==ea.department||ee[5]!==ea.status?(d=()=>es.filter(e=>{if("all"!==ea.department){if("unassigned"===ea.department){if(null!==e.departmentId)return!1}else if(e.departmentId!==ea.department)return!1}return"all"===ea.status||e.status===ea.status}),ee[3]=es,ee[4]=ea.department,ee[5]=ea.status,ee[6]=d):d=ee[6];let ec=d;ee[7]!==et?(o=e=>{if(!e)return"Unassigned";let s=et.find(s=>s.id===e);return(null==s?void 0:s.name)||"Unknown"},ee[7]=et,ee[8]=o):o=ee[8];let ed=o;ee[9]!==ed||ee[10]!==ec||ee[11]!==er?(m=()=>{let e=ec();if(0===e.length)return void alert("No employees match the current filters");let s=[];er.id&&s.push("Employee ID"),er.name&&s.push("Name"),er.email&&s.push("Email"),er.department&&s.push("Department"),er.status&&s.push("Status"),er.hireDate&&s.push("Hire Date"),er.createdAt&&s.push("Created Date"),er.updatedAt&&s.push("Updated Date");let t=e.map(e=>{let s=[];return er.id&&s.push(e.id),er.name&&s.push(e.name),er.email&&s.push(e.email),er.department&&s.push(ed(e.departmentId)),er.status&&s.push(e.status),er.hireDate&&s.push((0,u.Yq)(e.hireDate)),er.createdAt&&s.push((0,u.Yq)(e.createdAt)),er.updatedAt&&s.push((0,u.Yq)(e.updatedAt)),s}),a=new Blob([[s.join(","),...t.map(E)].join("\n")],{type:"text/csv"}),l=URL.createObjectURL(a),r=document.createElement("a");r.href=l,r.download="employees-export-".concat(new Date().toISOString().split("T")[0],".csv"),r.click(),URL.revokeObjectURL(l)},ee[9]=ed,ee[10]=ec,ee[11]=er,ee[12]=m):m=ee[12];let eo=m;ee[13]===Symbol.for("react.memo_cache_sentinel")?(h=(e,s)=>{en(t=>({...t,[e]:s}))},ee[13]=h):h=ee[13];let em=h;ee[14]!==ec?(x=ec(),ee[14]=ec,ee[15]=x):x=ee[15];let eu=x;ee[16]===Symbol.for("react.memo_cache_sentinel")?(j=(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(N.A,{className:"h-5 w-5"}),"Export Filters"]})}),ee[16]=j):j=ee[16],ee[17]===Symbol.for("react.memo_cache_sentinel")?(y=(0,a.jsx)(g.J,{children:"Department"}),ee[17]=y):y=ee[17];let eh=ea.department;ee[18]===Symbol.for("react.memo_cache_sentinel")?(w=e=>el(s=>({...s,department:e})),ee[18]=w):w=ee[18],ee[19]===Symbol.for("react.memo_cache_sentinel")?(C=(0,a.jsx)(b.bq,{children:(0,a.jsx)(b.yv,{})}),ee[19]=C):C=ee[19],ee[20]===Symbol.for("react.memo_cache_sentinel")?(A=(0,a.jsx)(b.eb,{value:"all",children:"All Departments"}),_=(0,a.jsx)(b.eb,{value:"unassigned",children:"Unassigned"}),ee[20]=A,ee[21]=_):(A=ee[20],_=ee[21]),ee[22]!==et?(k=et.map(S),ee[22]=et,ee[23]=k):k=ee[23],ee[24]!==k?(R=(0,a.jsxs)(b.gC,{children:[A,_,k]}),ee[24]=k,ee[25]=R):R=ee[25],ee[26]!==ea.department||ee[27]!==R?(D=(0,a.jsxs)("div",{className:"space-y-2",children:[y,(0,a.jsxs)(b.l6,{value:eh,onValueChange:w,children:[C,R]})]}),ee[26]=ea.department,ee[27]=R,ee[28]=D):D=ee[28],ee[29]===Symbol.for("react.memo_cache_sentinel")?(I=(0,a.jsx)(g.J,{children:"Status"}),ee[29]=I):I=ee[29],ee[30]===Symbol.for("react.memo_cache_sentinel")?(V=e=>el(s=>({...s,status:e})),ee[30]=V):V=ee[30],ee[31]===Symbol.for("react.memo_cache_sentinel")?(Z=(0,a.jsx)(b.bq,{children:(0,a.jsx)(b.yv,{})}),ee[31]=Z):Z=ee[31],ee[32]===Symbol.for("react.memo_cache_sentinel")?(T=(0,a.jsxs)(b.gC,{children:[(0,a.jsx)(b.eb,{value:"all",children:"All Statuses"}),(0,a.jsx)(b.eb,{value:"ACTIVE",children:"Active"}),(0,a.jsx)(b.eb,{value:"TRANSFERRED",children:"Transferred"}),(0,a.jsx)(b.eb,{value:"PENDING_REMOVAL",children:"Pending Removal"}),(0,a.jsx)(b.eb,{value:"ARCHIVED",children:"Archived"})]}),ee[32]=T):T=ee[32],ee[33]!==ea.status?(U=(0,a.jsxs)("div",{className:"space-y-2",children:[I,(0,a.jsxs)(b.l6,{value:ea.status,onValueChange:V,children:[Z,T]})]}),ee[33]=ea.status,ee[34]=U):U=ee[34],ee[35]!==D||ee[36]!==U?(q=(0,a.jsxs)(i.Zp,{children:[j,(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[D,U]})})]}),ee[35]=D,ee[36]=U,ee[37]=q):q=ee[37],ee[38]===Symbol.for("react.memo_cache_sentinel")?(B=(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Select Columns to Export"})}),ee[38]=B):B=ee[38],ee[39]!==er?(L=(0,a.jsxs)(i.Zp,{children:[B,(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"grid gap-3 md:grid-cols-2",children:ei.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(v.S,{id:e.key,checked:er[e.key],onCheckedChange:s=>em(e.key,s),disabled:e.required}),(0,a.jsxs)(g.J,{htmlFor:e.key,className:e.required?"text-muted-foreground":"",children:[e.label,e.required&&" (Required)"]})]},e.key))})})]}),ee[39]=er,ee[40]=L):L=ee[40],ee[41]===Symbol.for("react.memo_cache_sentinel")?(O=(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(p,{className:"h-5 w-5"}),"Export Summary"]})}),ee[41]=O):O=ee[41],ee[42]!==eu.length?(W=(0,a.jsx)("div",{className:"text-2xl font-bold",children:eu.length}),ee[42]=eu.length,ee[43]=W):W=ee[43],ee[44]===Symbol.for("react.memo_cache_sentinel")?(P=(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Employees to export"}),ee[44]=P):P=ee[44],ee[45]!==W?(M=(0,a.jsxs)("div",{className:"text-center p-4 bg-muted rounded-lg",children:[W,P]}),ee[45]=W,ee[46]=M):M=ee[46],ee[47]!==er?(F=Object.values(er).filter(Boolean),ee[47]=er,ee[48]=F):F=ee[48],ee[49]!==F.length?(G=(0,a.jsx)("div",{className:"text-2xl font-bold",children:F.length}),ee[49]=F.length,ee[50]=G):G=ee[50],ee[51]===Symbol.for("react.memo_cache_sentinel")?(H=(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Columns selected"}),ee[51]=H):H=ee[51],ee[52]!==G?(J=(0,a.jsxs)("div",{className:"text-center p-4 bg-muted rounded-lg",children:[G,H]}),ee[52]=G,ee[53]=J):J=ee[53],ee[54]===Symbol.for("react.memo_cache_sentinel")?(z=(0,a.jsxs)("div",{className:"text-center p-4 bg-muted rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"CSV"}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Export format"})]}),ee[54]=z):z=ee[54],ee[55]!==M||ee[56]!==J?($=(0,a.jsxs)("div",{className:"grid gap-2 md:grid-cols-3",children:[M,J,z]}),ee[55]=M,ee[56]=J,ee[57]=$):$=ee[57];let ex=0===eu.length;return ee[58]===Symbol.for("react.memo_cache_sentinel")?(Y=(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),ee[58]=Y):Y=ee[58],ee[59]!==eu.length||ee[60]!==eo||ee[61]!==ex?(K=(0,a.jsxs)(c.$,{onClick:eo,disabled:ex,className:"w-full",children:[Y,"Export ",eu.length," Employees to CSV"]}),ee[59]=eu.length,ee[60]=eo,ee[61]=ex,ee[62]=K):K=ee[62],ee[63]!==$||ee[64]!==K?(X=(0,a.jsxs)(i.Zp,{children:[O,(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[$,K]})})]}),ee[63]=$,ee[64]=K,ee[65]=X):X=ee[65],ee[66]!==q||ee[67]!==L||ee[68]!==X?(Q=(0,a.jsxs)("div",{className:"space-y-6",children:[q,L,X]}),ee[66]=q,ee[67]=L,ee[68]=X,ee[69]=Q):Q=ee[69],Q}function S(e){return(0,a.jsx)(b.eb,{value:e.id,children:e.name},e.id)}function E(e){return e.map(C).join(",")}function C(e){return'"'.concat(e,'"')}var A=t(8379);let _=(0,x.A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var k=t(3689);function R(e){let{employees:s,departments:t,selectedEmployees:l}=e,{executeBulkOperation:n,clearSelection:m}=(0,d.d)(),[u,h]=(0,r.useState)(""),[x,p]=(0,r.useState)(!1),f=s.filter(e=>l.includes(e.id)),y=async()=>{if(u&&0!==l.length){p(!0);try{await n({type:"transfer",employeeIds:l,targetDepartmentId:"unassigned"===u?null:u}),m(),h(""),alert("Successfully transferred ".concat(l.length," employees"))}catch(e){console.error("Bulk transfer failed:",e),alert("Transfer failed. Please try again.")}finally{p(!1)}}},v=(()=>{let e="unassigned"===u?null:t.find(e=>e.id===u)||null;if(!e)return null;let a=s.filter(s=>s.departmentId===e.id),r=a.length+l.length,n=Math.round(r/e.capacity*100);return{current:a.length,afterTransfer:r,capacity:e.capacity,utilization:n,isOverCapacity:r>e.capacity}})();return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(A.A,{className:"h-5 w-5"}),"Selected Employees (",l.length,")"]})}),(0,a.jsx)(i.Wu,{children:0===l.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(_,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"No employees selected. Go to the Employees page to select employees for transfer."})]}):(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsx)("div",{className:"grid gap-2 max-h-40 overflow-y-auto",children:f.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted rounded",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:e.email})]}),(0,a.jsx)(o.E,{variant:"outline",children:(e=>{if(!e)return"Unassigned";let s=t.find(s=>s.id===e);return(null==s?void 0:s.name)||"Unknown"})(e.departmentId)})]},e.id))})})})]}),l.length>0&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(k.A,{className:"h-5 w-5"}),"Transfer Configuration"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(g.J,{children:"Target Department"}),(0,a.jsxs)(b.l6,{value:u,onValueChange:h,children:[(0,a.jsx)(b.bq,{children:(0,a.jsx)(b.yv,{placeholder:"Select target department"})}),(0,a.jsxs)(b.gC,{children:[(0,a.jsx)(b.eb,{value:"unassigned",children:"Unassigned (Free Bucket)"}),t.map(e=>(0,a.jsxs)(b.eb,{value:e.id,children:[e.name," (",e.code,")"]},e.id))]})]})]}),v&&(0,a.jsx)(i.Zp,{className:"border-2 ".concat(v.isOverCapacity?"border-red-200 bg-red-50":v.utilization>=80?"border-orange-200 bg-orange-50":"border-green-200 bg-green-50"),children:(0,a.jsxs)(i.Wu,{className:"pt-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[v.isOverCapacity?(0,a.jsx)(_,{className:"h-5 w-5 text-red-600"}):(0,a.jsx)(j.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsx)("span",{className:"font-medium",children:"Capacity Check"})]}),(0,a.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,a.jsxs)("div",{children:["Current: ",v.current," / ",v.capacity]}),(0,a.jsxs)("div",{children:["After transfer: ",v.afterTransfer," / ",v.capacity]}),(0,a.jsxs)("div",{children:["Utilization: ",v.utilization,"%"]})]}),v.isOverCapacity&&(0,a.jsxs)("div",{className:"mt-2 text-sm text-red-700",children:["⚠️ This transfer will exceed department capacity by ",v.afterTransfer-v.capacity," employees."]})]})}),(0,a.jsx)(c.$,{onClick:y,disabled:!u||x,className:"w-full",children:x?"Transferring...":"Transfer ".concat(l.length," Employees")})]})})]}),(0,a.jsxs)(i.Zp,{className:"border-blue-200 bg-blue-50",children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{className:"text-blue-800",children:"Transfer Instructions"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2 text-sm text-blue-700",children:[(0,a.jsxs)("p",{children:["1. Go to the ",(0,a.jsx)("strong",{children:"Employees"})," page and select the employees you want to transfer"]}),(0,a.jsx)("p",{children:"2. Return to this page and select the target department"}),(0,a.jsx)("p",{children:"3. Review the capacity information and confirm the transfer"}),(0,a.jsx)("p",{children:"4. The system will update all selected employees' department assignments"})]})})]})]})}let D=(0,x.A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);function I(e){let{employees:s,selectedEmployees:t}=e,{executeBulkOperation:l,clearSelection:n}=(0,d.d)(),[m,u]=(0,r.useState)(""),[h,x]=(0,r.useState)(!1),p=s.filter(e=>t.includes(e.id)),f=[{value:"ACTIVE",label:"Active",description:"Employee is currently active"},{value:"TRANSFERRED",label:"Transferred",description:"Employee has been transferred"},{value:"PENDING_REMOVAL",label:"Pending Removal",description:"Employee is scheduled for removal"},{value:"ARCHIVED",label:"Archived",description:"Employee record is archived"}],j=e=>(0,a.jsx)(o.E,{variant:{ACTIVE:"default",TRANSFERRED:"secondary",PENDING_REMOVAL:"destructive",ARCHIVED:"outline"}[e],children:e.replace("_"," ")}),y=async()=>{if(m&&0!==t.length){x(!0);try{await l({type:"status_update",employeeIds:t,newStatus:m}),n(),u(""),alert("Successfully updated status for ".concat(t.length," employees"))}catch(e){console.error("Bulk status update failed:",e),alert("Status update failed. Please try again.")}finally{x(!1)}}},v=p.reduce((e,s)=>(e[s.status]=(e[s.status]||0)+1,e),{});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(A.A,{className:"h-5 w-5"}),"Selected Employees (",t.length,")"]})}),(0,a.jsx)(i.Wu,{children:0===t.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(_,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"No employees selected. Go to the Employees page to select employees for status update."})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:"Current Status Distribution:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:Object.entries(v).map(e=>{let[s,t]=e;return(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[j(s),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["(",t,")"]})]},s)})})]}),(0,a.jsx)("div",{className:"grid gap-2 max-h-40 overflow-y-auto",children:p.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted rounded",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:e.email})]}),j(e.status)]},e.id))})]})})]}),t.length>0&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(D,{className:"h-5 w-5"}),"Status Update Configuration"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(g.J,{children:"New Status"}),(0,a.jsxs)(b.l6,{value:m,onValueChange:e=>u(e),children:[(0,a.jsx)(b.bq,{children:(0,a.jsx)(b.yv,{placeholder:"Select new status"})}),(0,a.jsx)(b.gC,{children:f.map(e=>(0,a.jsx)(b.eb,{value:e.value,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})},e.value))})]})]}),m&&(0,a.jsx)(i.Zp,{className:"border-blue-200 bg-blue-50",children:(0,a.jsxs)(i.Wu,{className:"pt-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(D,{className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{className:"font-medium text-blue-800",children:"Preview Changes"})]}),(0,a.jsxs)("div",{className:"text-sm text-blue-700",children:[t.length," employees will be updated to: ",j(m)]})]})}),(0,a.jsx)(c.$,{onClick:y,disabled:!m||h,className:"w-full",children:h?"Updating...":"Update Status for ".concat(t.length," Employees")})]})})]}),(0,a.jsxs)(i.Zp,{className:"border-gray-200 bg-gray-50",children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{className:"text-gray-800",children:"Status Descriptions"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-2 text-sm text-gray-700",children:f.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[j(e.value),(0,a.jsx)("span",{children:e.description})]},e.value))})})]}),(0,a.jsxs)(i.Zp,{className:"border-blue-200 bg-blue-50",children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{className:"text-blue-800",children:"Status Update Instructions"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2 text-sm text-blue-700",children:[(0,a.jsxs)("p",{children:["1. Go to the ",(0,a.jsx)("strong",{children:"Employees"})," page and select the employees whose status you want to update"]}),(0,a.jsx)("p",{children:"2. Return to this page and select the new status"}),(0,a.jsx)("p",{children:"3. Review the preview and confirm the update"}),(0,a.jsx)("p",{children:"4. All selected employees will have their status updated simultaneously"})]})})]})]})}var V=t(538);function Z(){var e,s;let t,o,m,u,x,j,g,v,b,N,S,E,C,D,Z,U,q,B,L,O,W,P,M,F,G,H,J,z,$,Y,K,X,Q,ee,es,et,ea=(0,l.c)(75);(0,n.useSession)();let{employees:el,departments:er,selectedEmployees:en}=(0,d.d)(),[ei,ec]=(0,r.useState)("import");(0,V.s)(),ea[0]===Symbol.for("react.memo_cache_sentinel")?(t={id:"import",label:"Import CSV",icon:h.A,description:"Import employees from CSV file"},ea[0]=t):t=ea[0],ea[1]===Symbol.for("react.memo_cache_sentinel")?(o={id:"export",label:"Export CSV",icon:f.A,description:"Export employees to CSV file"},ea[1]=o):o=ea[1],ea[2]===Symbol.for("react.memo_cache_sentinel")?(m={id:"transfer",label:"Bulk Transfer",icon:k.A,description:"Transfer multiple employees between departments"},ea[2]=m):m=ea[2],ea[3]===Symbol.for("react.memo_cache_sentinel")?(u=[t,o,m,{id:"status",label:"Status Update",icon:A.A,description:"Update status for multiple employees"}],ea[3]=u):u=ea[3];let ed=u;ea[4]===Symbol.for("react.memo_cache_sentinel")?(x=(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Bulk Operations"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Perform bulk operations on employee data including CSV import/export and mass updates."})]})}),ea[4]=x):x=ea[4],ea[5]===Symbol.for("react.memo_cache_sentinel")?(j=(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Total Employees"}),(0,a.jsx)(A.A,{className:"h-4 w-4 text-muted-foreground"})]}),ea[5]=j):j=ea[5],ea[6]!==el.length?(g=(0,a.jsx)("div",{className:"text-2xl font-bold",children:el.length}),ea[6]=el.length,ea[7]=g):g=ea[7],ea[8]===Symbol.for("react.memo_cache_sentinel")?(v=(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Available for operations"}),ea[8]=v):v=ea[8],ea[9]!==g?(b=(0,a.jsxs)(i.Zp,{children:[j,(0,a.jsxs)(i.Wu,{children:[g,v]})]}),ea[9]=g,ea[10]=b):b=ea[10],ea[11]===Symbol.for("react.memo_cache_sentinel")?(N=(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Selected"}),(0,a.jsx)(A.A,{className:"h-4 w-4 text-blue-600"})]}),ea[11]=N):N=ea[11],ea[12]!==en.length?(S=(0,a.jsx)("div",{className:"text-2xl font-bold",children:en.length}),ea[12]=en.length,ea[13]=S):S=ea[13],ea[14]===Symbol.for("react.memo_cache_sentinel")?(E=(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Currently selected"}),ea[14]=E):E=ea[14],ea[15]!==S?(C=(0,a.jsxs)(i.Zp,{children:[N,(0,a.jsxs)(i.Wu,{children:[S,E]})]}),ea[15]=S,ea[16]=C):C=ea[16],ea[17]===Symbol.for("react.memo_cache_sentinel")?(D=(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Departments"}),(0,a.jsx)(p,{className:"h-4 w-4 text-muted-foreground"})]}),ea[17]=D):D=ea[17],ea[18]!==er.length?(Z=(0,a.jsx)("div",{className:"text-2xl font-bold",children:er.length}),ea[18]=er.length,ea[19]=Z):Z=ea[19],ea[20]===Symbol.for("react.memo_cache_sentinel")?(U=(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Available departments"}),ea[20]=U):U=ea[20],ea[21]!==Z?(q=(0,a.jsxs)(i.Zp,{children:[D,(0,a.jsxs)(i.Wu,{children:[Z,U]})]}),ea[21]=Z,ea[22]=q):q=ea[22],ea[23]===Symbol.for("react.memo_cache_sentinel")?(B=(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Unassigned"}),(0,a.jsx)(_,{className:"h-4 w-4 text-orange-600"})]}),ea[23]=B):B=ea[23],ea[24]!==el?(L=el.filter(T),ea[24]=el,ea[25]=L):L=ea[25],ea[26]!==L.length?(O=(0,a.jsx)("div",{className:"text-2xl font-bold",children:L.length}),ea[26]=L.length,ea[27]=O):O=ea[27],ea[28]===Symbol.for("react.memo_cache_sentinel")?(W=(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"In free bucket"}),ea[28]=W):W=ea[28],ea[29]!==O?(P=(0,a.jsxs)(i.Zp,{children:[B,(0,a.jsxs)(i.Wu,{children:[O,W]})]}),ea[29]=O,ea[30]=P):P=ea[30],ea[31]!==C||ea[32]!==q||ea[33]!==P||ea[34]!==b?(M=(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[b,C,q,P]}),ea[31]=C,ea[32]=q,ea[33]=P,ea[34]=b,ea[35]=M):M=ea[35],ea[36]!==ei?(F=(0,a.jsx)(i.aR,{children:(0,a.jsx)("div",{className:"flex space-x-1 rounded-lg bg-muted p-1",children:ed.map(e=>{let s=e.icon;return(0,a.jsxs)(c.$,{variant:ei===e.id?"default":"ghost",size:"sm",onClick:()=>ec(e.id),className:"flex-1",children:[(0,a.jsx)(s,{className:"h-4 w-4 mr-2"}),e.label]},e.id)})})}),ea[36]=ei,ea[37]=F):F=ea[37];let eo=null==(e=ed.find(e=>e.id===ei))?void 0:e.label;ea[38]!==eo?(G=(0,a.jsx)("h3",{className:"text-lg font-medium",children:eo}),ea[38]=eo,ea[39]=G):G=ea[39];let em=null==(s=ed.find(e=>e.id===ei))?void 0:s.description;return ea[40]!==em?(H=(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:em}),ea[40]=em,ea[41]=H):H=ea[41],ea[42]!==G||ea[43]!==H?(J=(0,a.jsxs)("div",{className:"mb-4",children:[G,H]}),ea[42]=G,ea[43]=H,ea[44]=J):J=ea[44],ea[45]!==ei||ea[46]!==er?(z="import"===ei&&(0,a.jsx)(y,{departments:er}),ea[45]=ei,ea[46]=er,ea[47]=z):z=ea[47],ea[48]!==ei||ea[49]!==er||ea[50]!==el?($="export"===ei&&(0,a.jsx)(w,{employees:el,departments:er}),ea[48]=ei,ea[49]=er,ea[50]=el,ea[51]=$):$=ea[51],ea[52]!==ei||ea[53]!==er||ea[54]!==el||ea[55]!==en?(Y="transfer"===ei&&(0,a.jsx)(R,{employees:el,departments:er,selectedEmployees:en}),ea[52]=ei,ea[53]=er,ea[54]=el,ea[55]=en,ea[56]=Y):Y=ea[56],ea[57]!==ei||ea[58]!==el||ea[59]!==en?(K="status"===ei&&(0,a.jsx)(I,{employees:el,selectedEmployees:en}),ea[57]=ei,ea[58]=el,ea[59]=en,ea[60]=K):K=ea[60],ea[61]!==J||ea[62]!==z||ea[63]!==$||ea[64]!==Y||ea[65]!==K?(X=(0,a.jsxs)(i.Wu,{children:[J,z,$,Y,K]}),ea[61]=J,ea[62]=z,ea[63]=$,ea[64]=Y,ea[65]=K,ea[66]=X):X=ea[66],ea[67]!==F||ea[68]!==X?(Q=(0,a.jsxs)(i.Zp,{children:[F,X]}),ea[67]=F,ea[68]=X,ea[69]=Q):Q=ea[69],ea[70]===Symbol.for("react.memo_cache_sentinel")?(ee=(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"text-blue-800 flex items-center gap-2",children:[(0,a.jsx)(_,{className:"h-5 w-5"}),"Bulk Operations Guidelines"]})}),ea[70]=ee):ee=ea[70],ea[71]===Symbol.for("react.memo_cache_sentinel")?(es=(0,a.jsxs)(i.Zp,{className:"border-blue-200 bg-blue-50",children:[ee,(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2 text-sm text-blue-700",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"CSV Import:"})," Upload a CSV file with employee data. Required columns: name, email. Optional: departmentCode, status."]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"CSV Export:"})," Download employee data in CSV format with filtering options."]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Bulk Transfer:"})," Move multiple employees between departments. Select employees from the Employees page first."]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Status Update:"})," Change the status of multiple employees at once (Active, Transferred, Archived, etc.)."]})]})})]}),ea[71]=es):es=ea[71],ea[72]!==M||ea[73]!==Q?(et=(0,a.jsxs)("div",{className:"space-y-6",children:[x,M,Q,es]}),ea[72]=M,ea[73]=Q,ea[74]=et):et=ea[74],et}function T(e){return null===e.departmentId}},5384:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(8776).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},6226:(e,s,t)=>{"use strict";t.d(s,{E:()=>c});var a=t(5155),l=t(9749);t(2115);var r=t(1335),n=t(198);let i=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function c(e){let s,t,r,c,d,o=(0,l.c)(10);return o[0]!==e?({className:s,variant:r,...t}=e,o[0]=e,o[1]=s,o[2]=t,o[3]=r):(s=o[1],t=o[2],r=o[3]),o[4]!==s||o[5]!==r?(c=(0,n.cn)(i({variant:r}),s),o[4]=s,o[5]=r,o[6]=c):c=o[6],o[7]!==t||o[8]!==c?(d=(0,a.jsx)("div",{className:c,...t}),o[7]=t,o[8]=c,o[9]=d):d=o[9],d}},7013:(e,s,t)=>{"use strict";t.d(s,{J:()=>d});var a=t(5155),l=t(2115),r=t(6031),n=t(1335),i=t(198);let c=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.b,{ref:s,className:(0,i.cn)(c(),t),...l})});d.displayName=r.b.displayName},8778:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(8776).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},9138:(e,s,t)=>{"use strict";t.d(s,{S:()=>c});var a=t(5155),l=t(2115),r=t(1676),n=t(8079),i=t(198);let c=l.forwardRef((e,s)=>{let{className:t,indeterminate:l,...c}=e;return(0,a.jsx)(r.bL,{ref:s,className:(0,i.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...c,children:(0,a.jsx)(r.C1,{className:(0,i.cn)("flex items-center justify-center text-current"),children:l?(0,a.jsx)("div",{className:"h-2 w-2 bg-current rounded-sm"}):(0,a.jsx)(n.A,{className:"h-4 w-4"})})})});c.displayName=r.bL.displayName},9159:(e,s,t)=>{"use strict";t.d(s,{k:()=>b});var a=t(5155),l=t(2115),r=t(9602),n=t(4372),i="Progress",[c,d]=(0,r.A)(i),[o,m]=c(i),u=l.forwardRef((e,s)=>{var t,l,r,i;let{__scopeProgress:c,value:d=null,max:m,getValueLabel:u=p,...h}=e;(m||0===m)&&!y(m)&&console.error((t="".concat(m),l="Progress","Invalid prop `max` of value `".concat(t,"` supplied to `").concat(l,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let x=y(m)?m:100;null===d||g(d,x)||console.error((r="".concat(d),i="Progress","Invalid prop `value` of value `".concat(r,"` supplied to `").concat(i,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let v=g(d,x)?d:null,b=j(v)?u(v,x):void 0;return(0,a.jsx)(o,{scope:c,value:v,max:x,children:(0,a.jsx)(n.sG.div,{"aria-valuemax":x,"aria-valuemin":0,"aria-valuenow":j(v)?v:void 0,"aria-valuetext":b,role:"progressbar","data-state":f(v,x),"data-value":null!=v?v:void 0,"data-max":x,...h,ref:s})})});u.displayName=i;var h="ProgressIndicator",x=l.forwardRef((e,s)=>{var t;let{__scopeProgress:l,...r}=e,i=m(h,l);return(0,a.jsx)(n.sG.div,{"data-state":f(i.value,i.max),"data-value":null!=(t=i.value)?t:void 0,"data-max":i.max,...r,ref:s})});function p(e,s){return"".concat(Math.round(e/s*100),"%")}function f(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function j(e){return"number"==typeof e}function y(e){return j(e)&&!isNaN(e)&&e>0}function g(e,s){return j(e)&&!isNaN(e)&&e<=s&&e>=0}x.displayName=h;var v=t(198);let b=l.forwardRef((e,s)=>{let{className:t,value:l,...r}=e;return(0,a.jsx)(u,{ref:s,className:(0,v.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...r,children:(0,a.jsx)(x,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(l||0),"%)")}})})});b.displayName=u.displayName},9309:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(8776).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])}},e=>{e.O(0,[235,666,368,18,3,816,441,326,358],()=>e(e.s=2993)),_N_E=e.O()}]);