(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[66],{650:(e,t,a)=>{"use strict";a.d(t,{SQ:()=>o,_2:()=>m,lp:()=>p,mB:()=>x,rI:()=>c,ty:()=>d});var s=a(5155),r=a(2115),n=a(8975),l=a(4627),i=a(198);let c=n.bL,d=n.l9;n.YJ,n.ZL,n.Pb,n.z6,r.forwardRef((e,t)=>{let{className:a,inset:r,children:c,...d}=e;return(0,s.jsxs)(n.ZP,{ref:t,className:(0,i.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",r&&"pl-8",a),...d,children:[c,(0,s.jsx)(l.A,{className:"ml-auto h-4 w-4"})]})}).displayName=n.ZP.displayName,r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.G5,{ref:t,className:(0,i.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...r})}).displayName=n.G5.displayName;let o=r.forwardRef((e,t)=>{let{className:a,sideOffset:r=4,...l}=e;return(0,s.jsx)(n.ZL,{children:(0,s.jsx)(n.UC,{ref:t,sideOffset:r,className:(0,i.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...l})})});o.displayName=n.UC.displayName;let m=r.forwardRef((e,t)=>{let{className:a,inset:r,...l}=e;return(0,s.jsx)(n.q7,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r&&"pl-8",a),...l})});m.displayName=n.q7.displayName;let p=r.forwardRef((e,t)=>{let{className:a,inset:r,...l}=e;return(0,s.jsx)(n.JU,{ref:t,className:(0,i.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",a),...l})});p.displayName=n.JU.displayName;let x=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.wv,{ref:t,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",a),...r})});x.displayName=n.wv.displayName},1814:(e,t,a)=>{Promise.resolve().then(a.bind(a,7587))},5970:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(8776).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},6031:(e,t,a)=>{"use strict";a.d(t,{b:()=>i});var s=a(2115),r=a(4372),n=a(5155),l=s.forwardRef((e,t)=>(0,n.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l},6226:(e,t,a)=>{"use strict";a.d(t,{E:()=>c});var s=a(5155),r=a(9749);a(2115);var n=a(1335),l=a(198);let i=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function c(e){let t,a,n,c,d,o=(0,r.c)(10);return o[0]!==e?({className:t,variant:n,...a}=e,o[0]=e,o[1]=t,o[2]=a,o[3]=n):(t=o[1],a=o[2],n=o[3]),o[4]!==t||o[5]!==n?(c=(0,l.cn)(i({variant:n}),t),o[4]=t,o[5]=n,o[6]=c):c=o[6],o[7]!==a||o[8]!==c?(d=(0,s.jsx)("div",{className:c,...a}),o[7]=a,o[8]=c,o[9]=d):d=o[9],d}},7013:(e,t,a)=>{"use strict";a.d(t,{J:()=>d});var s=a(5155),r=a(2115),n=a(6031),l=a(1335),i=a(198);let c=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.b,{ref:t,className:(0,i.cn)(c(),a),...r})});d.displayName=n.b.displayName},7071:(e,t,a)=>{"use strict";a.d(t,{p:()=>l});var s=a(5155),r=a(2115),n=a(198);let l=r.forwardRef((e,t)=>{let{className:a,type:r,...l}=e;return(0,s.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...l})});l.displayName="Input"},7273:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>p,Es:()=>u,L3:()=>f,c7:()=>x,lG:()=>d,rr:()=>h});var s=a(5155),r=a(9749),n=a(2115),l=a(8181),i=a(7747),c=a(198);let d=l.bL;l.l9;let o=l.ZL;l.bm;let m=n.forwardRef((e,t)=>{let a,n,i,d,o=(0,r.c)(9);return o[0]!==e?({className:a,...n}=e,o[0]=e,o[1]=a,o[2]=n):(a=o[1],n=o[2]),o[3]!==a?(i=(0,c.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),o[3]=a,o[4]=i):i=o[4],o[5]!==n||o[6]!==t||o[7]!==i?(d=(0,s.jsx)(l.hJ,{ref:t,className:i,...n}),o[5]=n,o[6]=t,o[7]=i,o[8]=d):d=o[8],d});m.displayName=l.hJ.displayName;let p=n.forwardRef((e,t)=>{let a,n,d,p,x,u,f,h=(0,r.c)(13);return h[0]!==e?({className:n,children:a,...d}=e,h[0]=e,h[1]=a,h[2]=n,h[3]=d):(a=h[1],n=h[2],d=h[3]),h[4]===Symbol.for("react.memo_cache_sentinel")?(p=(0,s.jsx)(m,{}),h[4]=p):p=h[4],h[5]!==n?(x=(0,c.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",n),h[5]=n,h[6]=x):x=h[6],h[7]===Symbol.for("react.memo_cache_sentinel")?(u=(0,s.jsxs)(l.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]}),h[7]=u):u=h[7],h[8]!==a||h[9]!==d||h[10]!==t||h[11]!==x?(f=(0,s.jsxs)(o,{children:[p,(0,s.jsxs)(l.UC,{ref:t,className:x,...d,children:[a,u]})]}),h[8]=a,h[9]=d,h[10]=t,h[11]=x,h[12]=f):f=h[12],f});p.displayName=l.UC.displayName;let x=e=>{let t,a,n,l,i=(0,r.c)(8);return i[0]!==e?({className:t,...a}=e,i[0]=e,i[1]=t,i[2]=a):(t=i[1],a=i[2]),i[3]!==t?(n=(0,c.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),i[3]=t,i[4]=n):n=i[4],i[5]!==a||i[6]!==n?(l=(0,s.jsx)("div",{className:n,...a}),i[5]=a,i[6]=n,i[7]=l):l=i[7],l};x.displayName="DialogHeader";let u=e=>{let t,a,n,l,i=(0,r.c)(8);return i[0]!==e?({className:t,...a}=e,i[0]=e,i[1]=t,i[2]=a):(t=i[1],a=i[2]),i[3]!==t?(n=(0,c.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),i[3]=t,i[4]=n):n=i[4],i[5]!==a||i[6]!==n?(l=(0,s.jsx)("div",{className:n,...a}),i[5]=a,i[6]=n,i[7]=l):l=i[7],l};u.displayName="DialogFooter";let f=n.forwardRef((e,t)=>{let a,n,i,d,o=(0,r.c)(9);return o[0]!==e?({className:a,...n}=e,o[0]=e,o[1]=a,o[2]=n):(a=o[1],n=o[2]),o[3]!==a?(i=(0,c.cn)("text-lg font-semibold leading-none tracking-tight",a),o[3]=a,o[4]=i):i=o[4],o[5]!==n||o[6]!==t||o[7]!==i?(d=(0,s.jsx)(l.hE,{ref:t,className:i,...n}),o[5]=n,o[6]=t,o[7]=i,o[8]=d):d=o[8],d});f.displayName=l.hE.displayName;let h=n.forwardRef((e,t)=>{let a,n,i,d,o=(0,r.c)(9);return o[0]!==e?({className:a,...n}=e,o[0]=e,o[1]=a,o[2]=n):(a=o[1],n=o[2]),o[3]!==a?(i=(0,c.cn)("text-sm text-muted-foreground",a),o[3]=a,o[4]=i):i=o[4],o[5]!==n||o[6]!==t||o[7]!==i?(d=(0,s.jsx)(l.VY,{ref:t,className:i,...n}),o[5]=n,o[6]=t,o[7]=i,o[8]=d):d=o[8],d});h.displayName=l.VY.displayName},7587:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>O});var s=a(5155),r=a(9749),n=a(2115),l=a(235),i=a(9683),c=a(425),d=a(7071),o=a(6226),m=a(9159),p=a(650),x=a(130),u=a(1895),f=a(5076),h=a(4825),y=a(8106),j=a(8379),g=a(208),N=a(8778);function v(e){let t,a,n=(0,r.c)(6),{departments:l}=e,d=C,v=w;if(0===l.length){let e;return n[0]===Symbol.for("react.memo_cache_sentinel")?(e=(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(x.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"No departments found."})]}),n[0]=e):e=n[0],e}if(n[1]!==l){let e;n[3]===Symbol.for("react.memo_cache_sentinel")?(e=e=>(0,s.jsxs)(i.Zp,{className:"hover:shadow-md transition-shadow",children:[(0,s.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"h-5 w-5 text-muted-foreground"}),(0,s.jsx)(i.ZB,{className:"text-lg font-medium",children:e.name}),function(e){return e.isOverCapacity?(0,s.jsx)(g.A,{className:"h-4 w-4 text-red-500"}):e.isNearCapacity?(0,s.jsx)(g.A,{className:"h-4 w-4 text-orange-500"}):(0,s.jsx)(N.A,{className:"h-4 w-4 text-green-500"})}(e)]}),(0,s.jsxs)(p.rI,{children:[(0,s.jsx)(p.ty,{asChild:!0,children:(0,s.jsxs)(c.$,{variant:"ghost",className:"h-8 w-8 p-0",children:[(0,s.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,s.jsx)(u.A,{className:"h-4 w-4"})]})}),(0,s.jsxs)(p.SQ,{align:"end",children:[(0,s.jsx)(p.lp,{children:"Actions"}),(0,s.jsxs)(p._2,{onClick:()=>d(e),children:[(0,s.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Edit Department"]}),(0,s.jsxs)(p._2,{onClick:()=>v(e),children:[(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Add Employee"]}),(0,s.jsx)(p.mB,{}),(0,s.jsxs)(p._2,{onClick:()=>(function(e){console.log("Delete department:",e.id)})(e),className:"text-destructive",children:[(0,s.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Code: ",(0,s.jsx)("span",{className:"font-mono font-medium",children:e.code})]}),function(e){return e.isOverCapacity?(0,s.jsx)(o.E,{variant:"destructive",children:"Over Capacity"}):e.isNearCapacity?(0,s.jsx)(o.E,{variant:"secondary",children:"Near Capacity"}):(0,s.jsx)(o.E,{variant:"default",children:"Healthy"})}(e)]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(j.A,{className:"h-4 w-4"}),"Employees"]}),(0,s.jsxs)("span",{className:"font-medium",children:[e.employees.length," / ",e.capacity]})]}),(0,s.jsx)(m.k,{value:e.utilization,className:"h-2"}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground",children:[(0,s.jsx)("span",{children:"Capacity Utilization"}),(0,s.jsxs)("span",{className:"font-medium ".concat(e.isOverCapacity?"text-red-600":e.isNearCapacity?"text-orange-600":"text-green-600"),children:[e.utilization,"%"]})]})]}),e.employees.length>0&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"text-sm font-medium",children:"Recent Employees:"}),(0,s.jsxs)("div",{className:"space-y-1",children:[e.employees.slice(0,3).map(b),e.employees.length>3&&(0,s.jsxs)("div",{className:"text-xs text-muted-foreground",children:["+",e.employees.length-3," more..."]})]})]}),(0,s.jsxs)("div",{className:"flex gap-2 pt-2",children:[(0,s.jsxs)(c.$,{size:"sm",variant:"outline",onClick:()=>v(e),className:"flex-1",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-1"}),"Add Employee"]}),(0,s.jsxs)(c.$,{size:"sm",variant:"secondary",onClick:()=>d(e),className:"flex-1",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 mr-1"}),"Edit"]})]})]})})]},e.id),n[3]=e):e=n[3],t=l.map(e),n[1]=l,n[2]=t}else t=n[2];return n[4]!==t?(a=(0,s.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:t}),n[4]=t,n[5]=a):a=n[5],a}function b(e){return(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:e.name},e.id)}function w(e){console.log("Add employee to department:",e.id)}function C(e){console.log("Edit department:",e.id)}var _=a(4373),A=a(7013),S=a(7273);function k(e){let{open:t,onOpenChange:a}=e,{departments:r,setDepartments:l}=(0,_.d)(),[i,o]=(0,n.useState)(!1),[m,p]=(0,n.useState)({name:"",code:"",capacity:""}),[x,u]=(0,n.useState)({}),f=async e=>{if(e.preventDefault(),(()=>{let e={};if(m.name.trim()||(e.name="Department name is required"),m.code.trim()?m.code.length>5?e.code="Department code must be 5 characters or less":r.some(e=>e.code.toLowerCase()===m.code.toLowerCase())&&(e.code="Department code already exists"):e.code="Department code is required",m.capacity.trim()){let t=parseInt(m.capacity);isNaN(t)||t<=0?e.capacity="Capacity must be a positive number":t>1e3&&(e.capacity="Capacity cannot exceed 1000")}else e.capacity="Capacity is required";return u(e),0===Object.keys(e).length})()){o(!0);try{let e=r.map(e=>parseInt(e.id.replace("dept-",""))||0),t=Math.max(0,...e)+1,s={id:"dept-".concat(t.toString().padStart(3,"0")),name:m.name.trim(),code:m.code.trim().toUpperCase(),capacity:parseInt(m.capacity),employees:[],createdAt:new Date,updatedAt:new Date};l([...r,s]),p({name:"",code:"",capacity:""}),u({}),a(!1)}catch(e){console.error("Failed to add department:",e)}finally{o(!1)}}},h=(e,t)=>{p(a=>({...a,[e]:t})),x[e]&&u(t=>({...t,[e]:""}))},y=()=>{p({name:"",code:"",capacity:""}),u({}),a(!1)};return(0,s.jsx)(S.lG,{open:t,onOpenChange:y,children:(0,s.jsxs)(S.Cf,{className:"sm:max-w-[425px]",children:[(0,s.jsxs)(S.c7,{children:[(0,s.jsx)(S.L3,{children:"Add New Department"}),(0,s.jsx)(S.rr,{children:"Create a new department with a specific capacity for employees."})]}),(0,s.jsxs)("form",{onSubmit:f,children:[(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(A.J,{htmlFor:"name",children:"Department Name"}),(0,s.jsx)(d.p,{id:"name",value:m.name,onChange:e=>h("name",e.target.value),placeholder:"Enter department name",className:x.name?"border-red-500":""}),x.name&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:x.name})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(A.J,{htmlFor:"code",children:"Department Code"}),(0,s.jsx)(d.p,{id:"code",value:m.code,onChange:e=>h("code",e.target.value.toUpperCase()),placeholder:"Enter department code (e.g., ENG)",maxLength:5,className:x.code?"border-red-500":""}),x.code&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:x.code}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Maximum 5 characters, will be converted to uppercase"})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(A.J,{htmlFor:"capacity",children:"Capacity"}),(0,s.jsx)(d.p,{id:"capacity",type:"number",value:m.capacity,onChange:e=>h("capacity",e.target.value),placeholder:"Enter maximum number of employees",min:"1",max:"1000",className:x.capacity?"border-red-500":""}),x.capacity&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:x.capacity}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Maximum number of employees this department can hold"})]})]}),(0,s.jsxs)(S.Es,{children:[(0,s.jsx)(c.$,{type:"button",variant:"outline",onClick:y,disabled:i,children:"Cancel"}),(0,s.jsx)(c.$,{type:"submit",disabled:i,children:i?"Creating...":"Create Department"})]})]})]})})}var D=a(538),R=a(198),E=a(8685),z=a(5970),Z=a(553);function O(){let e,t,a,o,m,p,u,f,h,y,N,b,w,C,A,S=(0,r.c)(70);(0,l.useSession)();let{departments:O,employees:J}=(0,_.d)(),[G,F]=(0,n.useState)(!1),[W,$]=(0,n.useState)("");(0,D.s)();let q=O.length,T=O.reduce(B,0);S[0]!==J?(e=J.filter(P),S[0]=J,S[1]=e):e=S[1];let V=e.length;S[2]!==V||S[3]!==T?(t=T>0?Math.round(V/T*100):0,S[2]=V,S[3]=T,S[4]=t):t=S[4];let Y=t;if(S[5]!==Y||S[6]!==O||S[7]!==J||S[8]!==W||S[9]!==T||S[10]!==q){let e,t,r,n,l,d,f,h,y,N,v,b,w,C,_,A,k,D,Z;S[16]!==J?(e=e=>{let t=J.filter(t=>t.departmentId===e.id),a=(0,R.Vy)(t.length,e.capacity);return{...e,employees:t,utilization:a,isOverCapacity:t.length>e.capacity,isNearCapacity:a>=80&&a<100}},S[16]=J,S[17]=e):e=S[17];let P=O.map(e);S[18]!==W?(t=e=>e.name.toLowerCase().includes(W.toLowerCase())||e.code.toLowerCase().includes(W.toLowerCase()),S[18]=W,S[19]=t):t=S[19],a=P.filter(t);let B=P.filter(U),G=P.filter(I);o="space-y-6",S[20]===Symbol.for("react.memo_cache_sentinel")?(r=(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Departments"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Manage your organization's departments and their capacity."})]}),S[20]=r):r=S[20],S[21]===Symbol.for("react.memo_cache_sentinel")?(m=(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[r,(0,s.jsxs)(c.$,{onClick:()=>F(!0),children:[(0,s.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Add Department"]})]}),S[21]=m):m=S[21],S[22]===Symbol.for("react.memo_cache_sentinel")?(n=(0,s.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(i.ZB,{className:"text-sm font-medium",children:"Total Departments"}),(0,s.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),S[22]=n):n=S[22],S[23]!==q?(l=(0,s.jsx)("div",{className:"text-2xl font-bold",children:q}),S[23]=q,S[24]=l):l=S[24],S[25]===Symbol.for("react.memo_cache_sentinel")?(d=(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active departments"}),S[25]=d):d=S[25],S[26]!==l?(f=(0,s.jsxs)(i.Zp,{children:[n,(0,s.jsxs)(i.Wu,{children:[l,d]})]}),S[26]=l,S[27]=f):f=S[27],S[28]===Symbol.for("react.memo_cache_sentinel")?(h=(0,s.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(i.ZB,{className:"text-sm font-medium",children:"Total Capacity"}),(0,s.jsx)(j.A,{className:"h-4 w-4 text-muted-foreground"})]}),S[28]=h):h=S[28],S[29]!==T?(y=(0,s.jsx)("div",{className:"text-2xl font-bold",children:T}),S[29]=T,S[30]=y):y=S[30],S[31]===Symbol.for("react.memo_cache_sentinel")?(N=(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Maximum employees"}),S[31]=N):N=S[31],S[32]!==y?(v=(0,s.jsxs)(i.Zp,{children:[h,(0,s.jsxs)(i.Wu,{children:[y,N]})]}),S[32]=y,S[33]=v):v=S[33],S[34]===Symbol.for("react.memo_cache_sentinel")?(b=(0,s.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(i.ZB,{className:"text-sm font-medium",children:"Avg. Utilization"}),(0,s.jsx)(z.A,{className:"h-4 w-4 text-muted-foreground"})]}),S[34]=b):b=S[34],S[35]!==Y?(w=(0,s.jsxs)("div",{className:"text-2xl font-bold",children:[Y,"%"]}),S[35]=Y,S[36]=w):w=S[36],S[37]===Symbol.for("react.memo_cache_sentinel")?(C=(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Capacity utilization"}),S[37]=C):C=S[37],S[38]!==w?(_=(0,s.jsxs)(i.Zp,{children:[b,(0,s.jsxs)(i.Wu,{children:[w,C]})]}),S[38]=w,S[39]=_):_=S[39],S[40]===Symbol.for("react.memo_cache_sentinel")?(A=(0,s.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(i.ZB,{className:"text-sm font-medium",children:"Needs Attention"}),(0,s.jsx)(g.A,{className:"h-4 w-4 text-orange-600"})]}),S[40]=A):A=S[40];let $=B.length+G.length;S[41]!==$?(k=(0,s.jsx)("div",{className:"text-2xl font-bold",children:$}),S[41]=$,S[42]=k):k=S[42],S[43]===Symbol.for("react.memo_cache_sentinel")?(D=(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Over/near capacity"}),S[43]=D):D=S[43],S[44]!==k?(Z=(0,s.jsxs)(i.Zp,{children:[A,(0,s.jsxs)(i.Wu,{children:[k,D]})]}),S[44]=k,S[45]=Z):Z=S[45],S[46]!==f||S[47]!==v||S[48]!==_||S[49]!==Z?(p=(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[f,v,_,Z]}),S[46]=f,S[47]=v,S[48]=_,S[49]=Z,S[50]=p):p=S[50],u=(B.length>0||G.length>0)&&(0,s.jsxs)(i.Zp,{className:"border-orange-200 bg-orange-50",children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)(i.ZB,{className:"text-orange-800 flex items-center gap-2",children:[(0,s.jsx)(g.A,{className:"h-5 w-5"}),"Departments Needing Attention"]})}),(0,s.jsxs)(i.Wu,{children:[B.length>0&&(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-red-800",children:"Over Capacity:"}),(0,s.jsx)("p",{className:"text-sm text-red-700",children:B.map(M).join(", ")})]}),G.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-orange-800",children:"Near Capacity (80%+):"}),(0,s.jsx)("p",{className:"text-sm text-orange-700",children:G.map(L).join(", ")})]})]})]}),S[5]=Y,S[6]=O,S[7]=J,S[8]=W,S[9]=T,S[10]=q,S[11]=a,S[12]=o,S[13]=m,S[14]=p,S[15]=u}else a=S[11],o=S[12],m=S[13],p=S[14],u=S[15];return S[51]===Symbol.for("react.memo_cache_sentinel")?(f=(0,s.jsx)(i.ZB,{children:"Department Management"}),S[51]=f):f=S[51],S[52]===Symbol.for("react.memo_cache_sentinel")?(h=(0,s.jsx)(Z.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),S[52]=h):h=S[52],S[53]===Symbol.for("react.memo_cache_sentinel")?(y=e=>$(e.target.value),S[53]=y):y=S[53],S[54]!==W?(N=(0,s.jsx)(i.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[f,(0,s.jsxs)("div",{className:"relative",children:[h,(0,s.jsx)(d.p,{placeholder:"Search departments...",value:W,onChange:y,className:"pl-8 w-[300px]"})]})]})}),S[54]=W,S[55]=N):N=S[55],S[56]!==a?(b=(0,s.jsx)(i.Wu,{children:(0,s.jsx)(v,{departments:a})}),S[56]=a,S[57]=b):b=S[57],S[58]!==b||S[59]!==N?(w=(0,s.jsxs)(i.Zp,{children:[N,b]}),S[58]=b,S[59]=N,S[60]=w):w=S[60],S[61]!==G?(C=(0,s.jsx)(k,{open:G,onOpenChange:F}),S[61]=G,S[62]=C):C=S[62],S[63]!==w||S[64]!==C||S[65]!==o||S[66]!==m||S[67]!==p||S[68]!==u?(A=(0,s.jsxs)("div",{className:o,children:[m,p,u,w,C]}),S[63]=w,S[64]=C,S[65]=o,S[66]=m,S[67]=p,S[68]=u,S[69]=A):A=S[69],A}function L(e){return e.name}function M(e){return e.name}function I(e){return e.isNearCapacity}function U(e){return e.isOverCapacity}function P(e){return null!==e.departmentId}function B(e,t){return e+t.capacity}},8685:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(8776).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},9159:(e,t,a)=>{"use strict";a.d(t,{k:()=>v});var s=a(5155),r=a(2115),n=a(9602),l=a(4372),i="Progress",[c,d]=(0,n.A)(i),[o,m]=c(i),p=r.forwardRef((e,t)=>{var a,r,n,i;let{__scopeProgress:c,value:d=null,max:m,getValueLabel:p=f,...x}=e;(m||0===m)&&!j(m)&&console.error((a="".concat(m),r="Progress","Invalid prop `max` of value `".concat(a,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let u=j(m)?m:100;null===d||g(d,u)||console.error((n="".concat(d),i="Progress","Invalid prop `value` of value `".concat(n,"` supplied to `").concat(i,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let N=g(d,u)?d:null,v=y(N)?p(N,u):void 0;return(0,s.jsx)(o,{scope:c,value:N,max:u,children:(0,s.jsx)(l.sG.div,{"aria-valuemax":u,"aria-valuemin":0,"aria-valuenow":y(N)?N:void 0,"aria-valuetext":v,role:"progressbar","data-state":h(N,u),"data-value":null!=N?N:void 0,"data-max":u,...x,ref:t})})});p.displayName=i;var x="ProgressIndicator",u=r.forwardRef((e,t)=>{var a;let{__scopeProgress:r,...n}=e,i=m(x,r);return(0,s.jsx)(l.sG.div,{"data-state":h(i.value,i.max),"data-value":null!=(a=i.value)?a:void 0,"data-max":i.max,...n,ref:t})});function f(e,t){return"".concat(Math.round(e/t*100),"%")}function h(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function y(e){return"number"==typeof e}function j(e){return y(e)&&!isNaN(e)&&e>0}function g(e,t){return y(e)&&!isNaN(e)&&e<=t&&e>=0}u.displayName=x;var N=a(198);let v=r.forwardRef((e,t)=>{let{className:a,value:r,...n}=e;return(0,s.jsx)(p,{ref:t,className:(0,N.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",a),...n,children:(0,s.jsx)(u,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})});v.displayName=p.displayName}},e=>{e.O(0,[235,666,368,18,445,814,816,441,326,358],()=>e(e.s=1814)),_N_E=e.O()}]);