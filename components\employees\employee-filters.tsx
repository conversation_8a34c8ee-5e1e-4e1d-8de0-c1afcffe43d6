"use client"

import { Department } from "@/lib/types"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface EmployeeFiltersProps {
  filters: {
    department: string
    status: string
    dateRange: { from: Date | null; to: Date | null }
  }
  onFiltersChange: (filters: any) => void
  departments: Department[]
}

export function EmployeeFilters({ 
  filters, 
  onFiltersChange, 
  departments 
}: EmployeeFiltersProps) {
  const handleDepartmentChange = (value: string) => {
    onFiltersChange({
      ...filters,
      department: value === "all" ? "" : value
    })
  }

  const handleStatusChange = (value: string) => {
    onFiltersChange({
      ...filters,
      status: value === "all" ? "" : value
    })
  }

  return (
    <div className="grid gap-4 md:grid-cols-3">
      <div className="space-y-2">
        <Label htmlFor="department-filter">Department</Label>
        <Select
          value={filters.department || "all"}
          onValueChange={handleDepartmentChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="All departments" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Departments</SelectItem>
            <SelectItem value="">Unassigned</SelectItem>
            {departments.map((dept) => (
              <SelectItem key={dept.id} value={dept.id}>
                {dept.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="status-filter">Status</Label>
        <Select
          value={filters.status || "all"}
          onValueChange={handleStatusChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="All statuses" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="ACTIVE">Active</SelectItem>
            <SelectItem value="TRANSFERRED">Transferred</SelectItem>
            <SelectItem value="PENDING_REMOVAL">Pending Removal</SelectItem>
            <SelectItem value="ARCHIVED">Archived</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label>Date Range</Label>
        <div className="text-sm text-muted-foreground">
          Date range filtering coming soon
        </div>
      </div>
    </div>
  )
}
