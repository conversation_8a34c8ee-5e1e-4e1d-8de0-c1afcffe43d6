{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/G%3A/Augment%20code/components/providers.tsx"], "sourcesContent": ["\"use client\"\n\nimport { SessionProvider } from \"next-auth/react\"\nimport { Toaster } from \"sonner\"\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  return (\n    <SessionProvider>\n      {children}\n      <Toaster position=\"top-right\" />\n    </SessionProvider>\n  )\n}\n"], "names": ["c", "_c", "Session<PERSON>rov<PERSON>", "Toaster", "Providers", "t0", "$", "$i", "Symbol", "for", "children", "t1", "t2"], "mappings": ";;;;AAAY,SAAAA,CAAA,IAAAC,EAAA;AAEZ,SAASC,eAAe,QAAQ,iBAAiB;AACjD,SAASC,OAAO,QAAQ,QAAQ;AAHhC,YAAY;;;;;AAKL,mBAAAE,EAAA;IAAA,MAAAC,CAAA,mLAAAL,IAAA,AAAAA,EAAA;IAAA,IAAAK,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IAAmB,MAAA,EAAAI,QAAAA,EAAA,GAAAL,EAA2C;IAAA,IAAAM,EAAA;IAAA,IAAAL,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAI/DE,EAAA,iBAAA,yUAAC,UAAO;YAAU,QAAW,EAAX,WAAW,GAAG;;;;;;QAAAL,CAAA,CAAA,EAAA,GAAAK,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAL,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAM,EAAA;IAAA,IAAAN,CAAA,CAAA,EAAA,KAAAI,QAAA,EAAA;QAFlCE,EAAA,iBAAA,+UAAC,kBAAe,CACbF;;gBAAAA,QAAO,CACR;gBAAAC,EAA+B,CACjC,EAHC,eAAe,CAGE;;;;;;;QAAAL,CAAA,CAAA,EAAA,GAAAI,QAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAM,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAN,CAAA,CAAA,EAAA;IAAA;IAAA,OAHlBM,EAGkB;AAAA;KALfR", "ignoreList": [], "debugId": null}}]}