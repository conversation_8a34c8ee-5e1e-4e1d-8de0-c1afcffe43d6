"use client"

import { useState } from "react"
import { Employee, Department } from "@/lib/types"
import { useHRStore } from "@/lib/store/hr-store"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { formatDate } from "@/lib/utils"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { MoreHorizontal, ArrowRight, Trash2, Edit } from "lucide-react"

interface FreeBucketTableProps {
  employees: Employee[]
  departments: Department[]
}

export function FreeBucketTable({ employees, departments }: FreeBucketTableProps) {
  const { 
    selectedEmployees, 
    toggleEmployeeSelection, 
    selectAllEmployees, 
    clearSelection,
    executeBulkOperation
  } = useHRStore()

  const [quickAssignments, setQuickAssignments] = useState<Record<string, string>>({})

  const isAllSelected = employees.length > 0 && 
    employees.every(emp => selectedEmployees.includes(emp.id))
  const isPartiallySelected = employees.some(emp => selectedEmployees.includes(emp.id)) && 
    !isAllSelected

  const handleSelectAll = () => {
    if (isAllSelected) {
      clearSelection()
    } else {
      selectAllEmployees(employees.map(emp => emp.id))
    }
  }

  const getStatusBadge = (status: Employee['status']) => {
    const variants = {
      ACTIVE: "default",
      TRANSFERRED: "secondary",
      PENDING_REMOVAL: "destructive",
      ARCHIVED: "outline"
    } as const

    return (
      <Badge variant={variants[status]}>
        {status.replace('_', ' ')}
      </Badge>
    )
  }

  const handleQuickAssign = async (employeeId: string, departmentId: string) => {
    try {
      await executeBulkOperation({
        type: 'transfer',
        employeeIds: [employeeId],
        targetDepartmentId: departmentId
      })
      
      // Clear the quick assignment selection
      setQuickAssignments(prev => {
        const updated = { ...prev }
        delete updated[employeeId]
        return updated
      })
    } catch (error) {
      console.error('Quick assignment failed:', error)
      alert('Assignment failed. Please try again.')
    }
  }

  const handleEdit = (employee: Employee) => {
    // TODO: Implement edit functionality
    console.log('Edit employee:', employee.id)
  }

  const handleDelete = (employee: Employee) => {
    // TODO: Implement delete functionality
    console.log('Delete employee:', employee.id)
  }

  const getDaysSinceCreated = (createdAt: Date) => {
    return Math.floor((Date.now() - createdAt.getTime()) / (1000 * 60 * 60 * 24))
  }

  if (employees.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No unassigned employees found.</p>
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={isAllSelected}
                indeterminate={isPartiallySelected}
                onCheckedChange={handleSelectAll}
                aria-label="Select all employees"
              />
            </TableHead>
            <TableHead>Employee ID</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Days Unassigned</TableHead>
            <TableHead>Quick Assign</TableHead>
            <TableHead className="w-12"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {employees.map((employee) => {
            const daysUnassigned = getDaysSinceCreated(employee.createdAt)
            const isRecentlyAdded = daysUnassigned <= 7
            
            return (
              <TableRow key={employee.id}>
                <TableCell>
                  <Checkbox
                    checked={selectedEmployees.includes(employee.id)}
                    onCheckedChange={() => toggleEmployeeSelection(employee.id)}
                    aria-label={`Select ${employee.name}`}
                  />
                </TableCell>
                <TableCell className="font-mono text-sm">
                  {employee.id}
                </TableCell>
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    {employee.name}
                    {isRecentlyAdded && (
                      <Badge variant="secondary" className="text-xs">
                        New
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell className="text-muted-foreground">
                  {employee.email}
                </TableCell>
                <TableCell>
                  {getStatusBadge(employee.status)}
                </TableCell>
                <TableCell>
                  <span className={`${
                    daysUnassigned > 30 ? 'text-red-600 font-medium' :
                    daysUnassigned > 14 ? 'text-orange-600' :
                    'text-muted-foreground'
                  }`}>
                    {daysUnassigned} days
                  </span>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Select
                      value={quickAssignments[employee.id] || ""}
                      onValueChange={(value) => setQuickAssignments(prev => ({
                        ...prev,
                        [employee.id]: value
                      }))}
                    >
                      <SelectTrigger className="w-40">
                        <SelectValue placeholder="Select dept..." />
                      </SelectTrigger>
                      <SelectContent>
                        {departments.map((dept) => (
                          <SelectItem key={dept.id} value={dept.id}>
                            {dept.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    
                    {quickAssignments[employee.id] && (
                      <Button
                        size="sm"
                        onClick={() => handleQuickAssign(employee.id, quickAssignments[employee.id])}
                      >
                        <ArrowRight className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => handleEdit(employee)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Employee
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => handleDelete(employee)}
                        className="text-destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </div>
  )
}
