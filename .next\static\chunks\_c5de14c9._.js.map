{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/G%3A/Augment%20code/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": ["React", "cva", "cn", "badgeVariants", "variants", "variant", "default", "secondary", "destructive", "outline", "defaultVariants", "Badge", "t0", "$", "_c", "$i", "Symbol", "for", "className", "props", "t1", "t2"], "mappings": ";;;;;;AACA,SAASC,GAAG,QAA2B,0BAA0B;AACjE,SAASC,EAAE,QAAQ,aAAa;;;;;AAEhC,MAAMC,aAAa,2KAAGF,MAAAA,AAAG,EACvB,wKAAwK,EACxK;IACEG,QAAQ,EAAE;QACRC,OAAO,EAAE;YACPC,OAAO,EACL,2EAA2E;YAC7EC,SAAS,EACP,iFAAiF;YACnFC,WAAW,EACT,uFAAuF;YACzFC,OAAO,EAAE;QACX;IACF,CAAC;IACDC,eAAe,EAAE;QACfL,OAAO,EAAE;IACX;AACF,CACF,CAAC;AAMD,eAAAO,EAAA;IAAA,MAAAC,CAAA,kLAAAC,KAAAA,AAAA,EAAA;IAAA,IAAAD,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAE,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAF,CAAA,CAAAE,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAJ,CAAA,CAAA,EAAA,GAAA;IAAA;IAAA,IAAAK,SAAA;IAAA,IAAAC,KAAA;IAAA,IAAAd,OAAA;IAAA,IAAAQ,CAAA,CAAA,EAAA,KAAAD,EAAA,EAAA;QAAe,CAAA,EAAAM,SAAA,EAAAb,OAAA,EAAA,GAAAc,OAAA,GAAAP,EAA4C;QAAAC,CAAA,CAAA,EAAA,GAAAD,EAAA;QAAAC,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAM,KAAA;QAAAN,CAAA,CAAA,EAAA,GAAAR,OAAA;IAAA,OAAA;QAAAa,SAAA,GAAAL,CAAA,CAAA,EAAA;QAAAM,KAAA,GAAAN,CAAA,CAAA,EAAA;QAAAR,OAAA,GAAAQ,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAO,EAAA;IAAA,IAAAP,CAAA,CAAA,EAAA,KAAAK,SAAA,IAAAL,CAAA,CAAA,EAAA,KAAAR,OAAA,EAAA;QAEvCe,EAAA,uHAAAlB,KAAAA,AAAA,EAAGC,aAAA,CAAA;YAAAE;QAAA,CAAyB,CAAC,EAAEa,SAAS,CAAC;QAAAL,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAR,OAAA;QAAAQ,CAAA,CAAA,EAAA,GAAAO,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAP,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAQ,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAM,KAAA,IAAAN,CAAA,CAAA,EAAA,KAAAO,EAAA,EAAA;QAAzDC,EAAA,iBAAA,6LAAA,GAAwE;YAAxD,SAAyC,CAAzC,CAAAD,EAAwC,CAAC;YAAA,GAAMD,KAAK,IAAI;;;;;;QAAAN,CAAA,CAAA,EAAA,GAAAM,KAAA;QAAAN,CAAA,CAAA,EAAA,GAAAO,EAAA;QAAAP,CAAA,CAAA,GAAA,GAAAQ,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAR,CAAA,CAAA,GAAA;IAAA;IAAA,OAAxEQ,EAAwE;AAAA;KAF5EV", "ignoreList": [], "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/progress.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAG/B,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,AAAC,eAAiC,OAAnB,MAAM,CAAC,SAAS,CAAC,GAAE;YAAI;;;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/departments/G%3A/Augment%20code/components/departments/department-grid.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Department, Employee } from \"@/lib/types\"\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Progress } from \"@/components/ui/progress\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { \n  Building2, \n  Users, \n  MoreHorizontal, \n  Edit, \n  Trash2, \n  UserPlus,\n  AlertTriangle,\n  CheckCircle\n} from \"lucide-react\"\n\ninterface DepartmentWithStats extends Department {\n  employees: Employee[]\n  utilization: number\n  isOverCapacity: boolean\n  isNearCapacity: boolean\n}\n\ninterface DepartmentGridProps {\n  departments: DepartmentWithStats[]\n}\n\nexport function DepartmentGrid({ departments }: DepartmentGridProps) {\n  const handleEdit = (department: DepartmentWithStats) => {\n    // TODO: Implement edit functionality\n    console.log('Edit department:', department.id)\n  }\n\n  const handleDelete = (department: DepartmentWithStats) => {\n    // TODO: Implement delete functionality\n    console.log('Delete department:', department.id)\n  }\n\n  const handleAddEmployee = (department: DepartmentWithStats) => {\n    // TODO: Implement add employee functionality\n    console.log('Add employee to department:', department.id)\n  }\n\n  const getUtilizationColor = (utilization: number, isOverCapacity: boolean) => {\n    if (isOverCapacity) return \"bg-red-500\"\n    if (utilization >= 80) return \"bg-orange-500\"\n    if (utilization >= 60) return \"bg-yellow-500\"\n    return \"bg-green-500\"\n  }\n\n  const getStatusIcon = (department: DepartmentWithStats) => {\n    if (department.isOverCapacity) {\n      return <AlertTriangle className=\"h-4 w-4 text-red-500\" />\n    }\n    if (department.isNearCapacity) {\n      return <AlertTriangle className=\"h-4 w-4 text-orange-500\" />\n    }\n    return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n  }\n\n  const getStatusBadge = (department: DepartmentWithStats) => {\n    if (department.isOverCapacity) {\n      return <Badge variant=\"destructive\">Over Capacity</Badge>\n    }\n    if (department.isNearCapacity) {\n      return <Badge variant=\"secondary\">Near Capacity</Badge>\n    }\n    return <Badge variant=\"default\">Healthy</Badge>\n  }\n\n  if (departments.length === 0) {\n    return (\n      <div className=\"text-center py-8\">\n        <Building2 className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n        <p className=\"text-muted-foreground\">No departments found.</p>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n      {departments.map((department) => (\n        <Card key={department.id} className=\"hover:shadow-md transition-shadow\">\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <div className=\"flex items-center gap-2\">\n              <Building2 className=\"h-5 w-5 text-muted-foreground\" />\n              <CardTitle className=\"text-lg font-medium\">\n                {department.name}\n              </CardTitle>\n              {getStatusIcon(department)}\n            </div>\n            \n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\n                  <span className=\"sr-only\">Open menu</span>\n                  <MoreHorizontal className=\"h-4 w-4\" />\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent align=\"end\">\n                <DropdownMenuLabel>Actions</DropdownMenuLabel>\n                <DropdownMenuItem onClick={() => handleEdit(department)}>\n                  <Edit className=\"mr-2 h-4 w-4\" />\n                  Edit Department\n                </DropdownMenuItem>\n                <DropdownMenuItem onClick={() => handleAddEmployee(department)}>\n                  <UserPlus className=\"mr-2 h-4 w-4\" />\n                  Add Employee\n                </DropdownMenuItem>\n                <DropdownMenuSeparator />\n                <DropdownMenuItem \n                  onClick={() => handleDelete(department)}\n                  className=\"text-destructive\"\n                >\n                  <Trash2 className=\"mr-2 h-4 w-4\" />\n                  Delete\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </CardHeader>\n          \n          <CardContent>\n            <div className=\"space-y-4\">\n              {/* Department Code and Status */}\n              <div className=\"flex items-center justify-between\">\n                <div className=\"text-sm text-muted-foreground\">\n                  Code: <span className=\"font-mono font-medium\">{department.code}</span>\n                </div>\n                {getStatusBadge(department)}\n              </div>\n              \n              {/* Employee Count and Capacity */}\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"flex items-center gap-1\">\n                    <Users className=\"h-4 w-4\" />\n                    Employees\n                  </span>\n                  <span className=\"font-medium\">\n                    {department.employees.length} / {department.capacity}\n                  </span>\n                </div>\n                \n                <Progress \n                  value={department.utilization} \n                  className=\"h-2\"\n                />\n                \n                <div className=\"flex justify-between text-xs text-muted-foreground\">\n                  <span>Capacity Utilization</span>\n                  <span className={`font-medium ${\n                    department.isOverCapacity ? 'text-red-600' : \n                    department.isNearCapacity ? 'text-orange-600' : \n                    'text-green-600'\n                  }`}>\n                    {department.utilization}%\n                  </span>\n                </div>\n              </div>\n              \n              {/* Employee List Preview */}\n              {department.employees.length > 0 && (\n                <div className=\"space-y-2\">\n                  <div className=\"text-sm font-medium\">Recent Employees:</div>\n                  <div className=\"space-y-1\">\n                    {department.employees.slice(0, 3).map((employee) => (\n                      <div key={employee.id} className=\"text-xs text-muted-foreground\">\n                        {employee.name}\n                      </div>\n                    ))}\n                    {department.employees.length > 3 && (\n                      <div className=\"text-xs text-muted-foreground\">\n                        +{department.employees.length - 3} more...\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n              \n              {/* Action Buttons */}\n              <div className=\"flex gap-2 pt-2\">\n                <Button \n                  size=\"sm\" \n                  variant=\"outline\" \n                  onClick={() => handleAddEmployee(department)}\n                  className=\"flex-1\"\n                >\n                  <UserPlus className=\"h-4 w-4 mr-1\" />\n                  Add Employee\n                </Button>\n                \n                <Button \n                  size=\"sm\" \n                  variant=\"secondary\" \n                  onClick={() => handleEdit(department)}\n                  className=\"flex-1\"\n                >\n                  <Edit className=\"h-4 w-4 mr-1\" />\n                  Edit\n                </Button>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      ))}\n    </div>\n  )\n}\n"], "names": ["c", "_c", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON>", "Badge", "Progress", "DropdownMenu", "DropdownMenuContent", "DropdownMenuItem", "DropdownMenuLabel", "DropdownMenuSeparator", "DropdownMenuTrigger", "Building2", "Users", "MoreHorizontal", "Edit", "Trash2", "UserPlus", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "DepartmentGrid", "t0", "$", "$i", "Symbol", "for", "departments", "handleEdit", "_temp", "handleDelete", "_temp2", "handleAddEmployee", "_temp3", "getStatusIcon", "_temp4", "getStatusBadge", "_temp5", "length", "t1", "t2", "department_4", "department", "id", "name", "code", "employees", "capacity", "utilization", "isOverCapacity", "isNearCapacity", "slice", "map", "_temp6", "employee", "department_3", "department_2", "department_1", "console", "log", "department_0"], "mappings": ";;;;AAAY,SAAAA,CAAA,IAAAC,EAAA;AAIZ,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,sBAAsB;AAC/E,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SACEC,YAAY,EACZC,mBAAmB,EACnBC,gBAAgB,EAChBC,iBAAiB,EACjBC,qBAAqB,EACrBC,mBAAmB,QACd,+BAA+B;;;;;;;;AACtC,SACEC,SAAS,EACTC,KAAK,EACLC,cAAc,EACdC,IAAI,EACJC,MAAM,EACNC,QAAQ,EACRC,aAAa,EACbC,WAAW,QACN,cAAc;AAzBrB,YAAY;;;;;;;;;AAsCL,wBAAAE,EAAA;IAAA,MAAAC,CAAA,mLAAAxB,IAAAA,AAAA,EAAA;IAAA,IAAAwB,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IAAwB,MAAA,EAAAI,WAAAA,EAAA,GAAAL,EAAoC;IACjE,MAAAM,UAAA,GAAAC,KAAA;IAKA,MAAAC,YAAA,GAAAC,MAAA;IAKA,MAAAC,iBAAA,GAAAC,MAAA;IAYA,MAAAC,aAAA,GAAAC,MAAA;IAUA,MAAAC,cAAA,GAAAC,MAAA;IAQC,IAEGV,WAAW,CAAAW,MAAA,KAAA,CAAa,EAAA;QAAA,IAAAC,EAAA;QAAA,IAAAhB,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;YAExBa,EAAA,iBAAA,6LAAA,GAGM;gBAHS,SAAkB,EAAlB,kBAAkB;;kCAC/B,iZAAC,YAAS;wBAAW,SAA8C,EAA9C,8CAA8C;;;;;;kCACnE,6LAAA,CAA8D;wBAAjD,SAAuB,EAAvB,uBAAuB;kCAAC,qBAAqB,EAA1D,CAA8D,CAChE,EAHA,GAGM;;;;;;;;;;;;YAAAhB,CAAA,CAAA,EAAA,GAAAgB,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAAhB,CAAA,CAAA,EAAA;QAAA;QAAA,OAHNgB,EAGM;IAAA;IAAA,IAAAA,EAAA;IAAA,IAAAhB,CAAA,CAAA,EAAA,KAAAI,WAAA,EAAA;QAAA,IAAAa,EAAA;QAAA,IAAAjB,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;YAMWc,EAAA,IAAAC,YAAA,iBACf,6LAAC,oIAAI,CAAM,GAAa,CAAb;oBAAyB,SAAmC,EAAnC,mCAAmC;;sCACrE,0TAAC,aAAU;4BAAW,SAA2D,EAA3D,2DAA2D;;8CAC/E,6LAAA,GAMM;oCANS,SAAyB,EAAzB,yBAAyB;;sDACtC,iZAAC,YAAS;4CAAW,SAA+B,EAA/B,+BAA+B;;;;;;sDACpD,0TAAC,YAAS;4CAAW,SAAqB,EAArB,qBAAqB,CACvC;sDAAAC,YAAU,CAAAE,IAAI,CACjB,EAFC,SAAS,CAGT;;;;;;wCAAAV,aAAa,CAACQ,YAAU,EAC3B,EANA,GAMM;;;;;;;8CAEN,sUAAC,eAAY;;sDACX,sUAAC,sBAAmB;4CAAC,OAAO,CAAP,CAAA,IAAM,CAAC;oEAC1B,6LAAC,wIAAM;gDAAS,OAAO,EAAP,OAAO;gDAAW,SAAa,EAAb,aAAa;;kEAC7C,6LAAA,IAA0C;wDAA1B,SAAS,EAAT,SAAS;kEAAC,SAAS,EAAnC,IAA0C;;;;;;kEAC1C,2ZAAC,iBAAc;wDAAW,SAAS,EAAT,SAAS,GACrC,EAHC,MAAM,CAIT,EALC,mBAAmB;;;;;;;;;;;;;;;;;sDAMpB,qUAAC,uBAAmB;4CAAO,KAAK,EAAL,KAAK;;8DAC9B,sUAAC,oBAAiB;8DAAC,OAAO,EAAzB,iBAAiB;;;;;;8DAClB,sUAAC,mBAAgB;oDAAU,OAA4B,CAA5B,CAAA,IAAMd,UAAU,CAACc,YAAU,EAAC;;sEACrD,4YAAC,OAAI;4DAAW,SAAc,EAAd,cAAc;;;;;;wDAAG,eAEnC,EAHC,gBAAgB;;;;;;;8DAIjB,sUAAC,mBAAgB;oDAAU,OAAmC,CAAnC,CAAA,IAAMV,iBAAiB,CAACU,YAAU,EAAC;;sEAC5D,8YAAC,YAAQ;4DAAW,SAAc,EAAd,cAAc;;;;;;wDAAG,YAEvC,EAHC,gBAAgB;;;;;;;8DAIjB,sUAAC,wBAAqB;;;;;8DACtB,sUAAC,mBAAgB;oDACN,OAA8B,CAA9B,CAAA,IAAMZ,YAAY,CAACY,YAAU,EAAC;oDAC7B,SAAkB,EAAlB,kBAAkB;;sEAE5B,6LAAC,uNAAM;4DAAW,SAAc,EAAd,cAAc;;;;;;wDAAG,MAErC,EANC,gBAAgB,CAOnB,EAlBC,mBAAmB,CAmBtB,EA1BC,YAAY,CA2Bf,EApCC,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;sCAsCX,0TAAC,cAAW;oDACV,6LAAA,GA+EM;gCA/ES,SAAW,EAAX,WAAW;;kDAExB,6LAAA,GAKM;wCALS,SAAmC,EAAnC,mCAAmC;;0DAChD,6LAAA,GAEM;gDAFS,SAA+B,EAA/B,+BAA+B;;oDAAC;kEACvC,6LAAA,IAAgE;wDAAhD,SAAuB,EAAvB,uBAAuB,CAAE;kEAAAA,YAAU,CAAAG,IAAI,CAAE,EAAzD,IAAgE,CACxE,EAFA,GAEM,CACL;;;;;;;;;;;;4CAAAT,cAAc,CAACM,YAAU,EAC5B,EALA,GAKM;;;;;;;kDAGN,6LAAA,GA0BM;wCA1BS,SAAW,EAAX,WAAW;;0DACxB,6LAAA,GAQM;gDARS,SAA2C,EAA3C,2CAA2C;;kEACxD,6LAAA,IAGO;wDAHS,SAAyB,EAAzB,yBAAyB;;0EACvC,qYAAC,QAAK;gEAAW,SAAS,EAAT,SAAS;;;;;;4DAAG,SAE/B,EAHA,IAGO;;;;;;;kEACP,6LAAA,IAEO;wDAFS,SAAa,EAAb,aAAa,CAC1B;;4DAAAA,YAAU,CAAAI,SAAA,CAAAR,MAAgB;4DAAE,GAAI;4DAAAI,YAAU,CAAAK,QAAQ,CACrD,EAFA,IAEO,CACT,EARA,GAQM;;;;;;;;;;;;;0DAEN,8TAAC,WAAQ;gDACA,KAAsB,CAAtB,CAAAL,YAAU,CAAAM,WAAW,CAAC;gDACnB,SAAK,EAAL,KAAK;;;;;;0DAGjB,6LAAA,GASM;gDATS,SAAoD,EAApD,oDAAoD;;kEACjE,6LAAA,IAAiC;kEAA3B,oBAAoB,EAA1B,IAAiC;;;;;;kEACjC,6LAAA,IAMO;wDANU,SAIf,CAJe,CAAA,eAGC,CACjB,CAAC,CACC,IAJDN,YAAU,CAAAO,cAAA,GAAkB,cAAc,GAC1CP,YAAU,CAAAQ,cAAA,GAAkB,iBAAiB,GAC7C,gBAAgB;;4DAEfR,YAAU,CAAAM,WAAW;4DAAE,CAC1B,EANA,IAMO,CACT,EATA,GASM,CACR,EA1BA,GA0BM,CAGL;;;;;;;;;;;;;;;;;;;oCAAAN,YAAU,CAAAI,SAAA,CAAAR,MAAA,GAAA,CAAqB,kBAC9B,6LAAA,GAcM;wCAdS,SAAW,EAAX,WAAW;;0DACxB,6LAAA,GAA4D;gDAA7C,SAAqB,EAArB,qBAAqB;0DAAC,iBAAiB,EAAtD,GAA4D;;;;;;0DAC5D,6LAAA,GAWM;gDAXS,SAAW,EAAX,WAAW,CACvB;;oDAAAI,YAAU,CAAAI,SAAA,CAAAK,KAAA,CAAA,GAAA,CAAqB,CAAC,CAAAC,GAAA,CAAAC,MAIhC,EACA;oDAAAX,YAAU,CAAAI,SAAA,CAAAR,MAAA,GAAA,CAAqB,kBAC9B,6LAAA,GAEM;wDAFS,SAA+B,EAA/B,+BAA+B;;4DAAC,CAC3C;4DAAAI,YAAU,CAAAI,SAAA,CAAAR,MAAA,GAAA,CAAoB;4DAAE,QACpC,EAFA,GAEM,CACR,CACF,EAXA,GAWM,CACR,EAdA,GAcM,CACR;;;;;;;;;;;;;;;;;;;kDAGA,6LAAA,GAoBM;wCApBS,SAAiB,EAAjB,iBAAiB;;0DAC9B,4TAAC,SAAM;gDACA,IAAI,EAAJ,IAAI;gDACD,OAAS,EAAT,SAAS;gDACR,OAAmC,CAAnC,CAAA,IAAMN,iBAAiB,CAACU,YAAU,EAAC;gDAClC,SAAQ,EAAR,QAAQ;;kEAElB,+YAAC,WAAQ;wDAAW,SAAc,EAAd,cAAc;;;;;;oDAAG,YAEvC,EARC,MAAM;;;;;;;0DAUP,4TAAC,SAAM;gDACA,IAAI,EAAJ,IAAI;gDACD,OAAW,EAAX,WAAW;gDACV,OAA4B,CAA5B,CAAA,IAAMd,UAAU,CAACc,YAAU,EAAC;gDAC3B,SAAQ,EAAR,QAAQ;;kEAElB,2YAAC,QAAI;wDAAW,SAAc,EAAd,cAAc;;;;;;oDAAG,IAEnC,EARC,MAAM,CAST,EApBA,GAoBM,CACR,EA/EA,GA+EM,CACR,EAjFC,WAAW,CAkFd,EAzHC,IAAI,CAyHE;;;;;;;;;;;;;;;;;;;;;;;;;mBAzHIA,YAAU,CAAAC,EAAE,CAAC;;;;;YA0HzBpB,CAAA,CAAA,EAAA,GAAAiB,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAAjB,CAAA,CAAA,EAAA;QAAA;QA3HAgB,EAAA,GAAAZ,WAAW,CAAAyB,GAAA,CAAKZ,EA2HhB,CAAC;QAAAjB,CAAA,CAAA,EAAA,GAAAI,WAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAgB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAhB,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAiB,EAAA;IAAA,IAAAjB,CAAA,CAAA,EAAA,KAAAgB,EAAA,EAAA;QA5HJC,EAAA,iBAAA,6LAAA,GA6HM;YA7HS,SAA0C,EAA1C,0CAA0C,CACtD;sBAAAD,EA2HA,CACH,EA7HA,GA6HM;;;;;;QAAAhB,CAAA,CAAA,EAAA,GAAAgB,EAAA;QAAAhB,CAAA,CAAA,EAAA,GAAAiB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAjB,CAAA,CAAA,EAAA;IAAA;IAAA,OA7HNiB,EA6HM;AAAA;KAlLHnB;AAAA,SAAAgC,OAAAC,QAAA;IAAA,qBA2Ie,6LAAA,GAEM,CAFI,GAAW,CAAX;QAAuB,SAA+B,EAA/B,+BAA+B,CAC7D;kBAAAA,QAAQ,CAAAV,IAAI,CACf,EAFA,GAEM;OAFIU,QAAQ,CAAAX,EAAE,CAAC;;;;;AAEf;AA7IrB,SAAAN,OAAAkB,YAAA;IAAA,IAkCCb,YAAU,CAAAO,cAAA,EAAA;QAAA,qBACL,2TAAC,QAAK;YAAS,OAAa,EAAb,aAAa;sBAAC,aAAa,EAAzC,KAAK,CAA4C;;;;;;IAAA;IAAA,IAEvDP,YAAU,CAAAQ,cAAA,EAAA;QAAA,qBACL,0TAAC,SAAK;YAAS,OAAW,EAAX,WAAW;sBAAC,aAAa,EAAvC,KAAK,CAA0C;;;;;;IAAA;IAAA,qBAElD,2TAAC,QAAK;QAAS,OAAS,EAAT,SAAS;kBAAC,OAAO,EAA/B,KAAK,CAAkC;;;;;;AAAA;AAxC5C,SAAAf,OAAAqB,YAAA;IAAA,IAwBCd,YAAU,CAAAO,cAAA,EAAA;QAAA,qBACL,yZAAC,gBAAa;YAAW,SAAsB,EAAtB,sBAAsB,GAAG;;;;;;IAAA;IAAA,IAEvDP,YAAU,CAAAQ,cAAA,EAAA;QAAA,qBACL,yZAAC,gBAAa;YAAW,SAAyB,EAAzB,yBAAyB,GAAG;;;;;;IAAA;IAAA,qBAEvD,qZAAC,cAAW;QAAW,SAAwB,EAAxB,wBAAwB,GAAG;;;;;;AAAA;AA9BtD,SAAAjB,OAAAwB,YAAA;IAaHC,OAAA,CAAAC,GAAA,CAAY,6BAA6B,EAAEjB,YAAU,CAAAC,EAAG,CAAC;AAAA;AAbtD,SAAAZ,OAAA6B,YAAA;IAQHF,OAAA,CAAAC,GAAA,CAAY,oBAAoB,EAAEjB,YAAU,CAAAC,EAAG,CAAC;AAAA;AAR7C,SAAAd,MAAAa,UAAA;IAGHgB,OAAA,CAAAC,GAAA,CAAY,kBAAkB,EAAEjB,UAAU,CAAAC,EAAG,CAAC;AAAA", "ignoreList": [], "debugId": null}}, {"offset": {"line": 864, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/lib/store/hr-store.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { subscribeWithSelector } from 'zustand/middleware'\nimport { Employee, Department, BulkOperation } from '@/lib/types'\n\ninterface HRState {\n  // Data\n  employees: Employee[]\n  departments: Department[]\n  freeBucket: Employee[]\n  \n  // UI State\n  selectedEmployees: string[]\n  isLoading: boolean\n  searchQuery: string\n  \n  // Actions\n  setEmployees: (employees: Employee[]) => void\n  setDepartments: (departments: Department[]) => void\n  addEmployee: (employee: Employee) => void\n  updateEmployee: (id: string, updates: Partial<Employee>) => void\n  removeEmployee: (id: string) => void\n  \n  // Selection\n  toggleEmployeeSelection: (id: string) => void\n  selectAllEmployees: (ids: string[]) => void\n  clearSelection: () => void\n  \n  // Bulk Operations\n  executeBulkOperation: (operation: BulkOperation) => Promise<void>\n  \n  // Search & Filter\n  setSearchQuery: (query: string) => void\n  getFilteredEmployees: () => Employee[]\n  \n  // Free Bucket\n  moveToFreeBucket: (employeeIds: string[]) => void\n  removeFromFreeBucket: (employeeIds: string[]) => void\n}\n\nexport const useHRStore = create<HRState>()(\n  subscribeWithSelector((set, get) => ({\n    // Initial state\n    employees: [],\n    departments: [],\n    freeBucket: [],\n    selectedEmployees: [],\n    isLoading: false,\n    searchQuery: '',\n\n    // Data actions\n    setEmployees: (employees) => set((state) => ({\n      employees,\n      freeBucket: employees.filter(emp => emp.departmentId === null)\n    })),\n    setDepartments: (departments) => set({ departments }),\n    \n    addEmployee: (employee) => set((state) => ({\n      employees: [...state.employees, employee]\n    })),\n    \n    updateEmployee: (id, updates) => set((state) => ({\n      employees: state.employees.map(emp => \n        emp.id === id ? { ...emp, ...updates } : emp\n      )\n    })),\n    \n    removeEmployee: (id) => set((state) => ({\n      employees: state.employees.filter(emp => emp.id !== id),\n      selectedEmployees: state.selectedEmployees.filter(empId => empId !== id)\n    })),\n\n    // Selection actions\n    toggleEmployeeSelection: (id) => set((state) => ({\n      selectedEmployees: state.selectedEmployees.includes(id)\n        ? state.selectedEmployees.filter(empId => empId !== id)\n        : [...state.selectedEmployees, id]\n    })),\n    \n    selectAllEmployees: (ids) => set({ selectedEmployees: ids }),\n    clearSelection: () => set({ selectedEmployees: [] }),\n\n    // Bulk operations\n    executeBulkOperation: async (operation) => {\n      const { employees, selectedEmployees } = get()\n      \n      switch (operation.type) {\n        case 'transfer':\n          set((state) => ({\n            employees: state.employees.map(emp =>\n              operation.employeeIds.includes(emp.id)\n                ? { ...emp, departmentId: operation.targetDepartmentId || null }\n                : emp\n            ),\n            freeBucket: state.employees\n              .map(emp =>\n                operation.employeeIds.includes(emp.id)\n                  ? { ...emp, departmentId: operation.targetDepartmentId || null }\n                  : emp\n              )\n              .filter(emp => emp.departmentId === null)\n          }))\n          break\n          \n        case 'status_update':\n          if (operation.newStatus) {\n            set((state) => ({\n              employees: state.employees.map(emp =>\n                operation.employeeIds.includes(emp.id)\n                  ? { ...emp, status: operation.newStatus! }\n                  : emp\n              )\n            }))\n          }\n          break\n          \n        case 'delete':\n          set((state) => ({\n            employees: state.employees.filter(emp => \n              !operation.employeeIds.includes(emp.id)\n            )\n          }))\n          break\n      }\n      \n      set({ selectedEmployees: [] })\n    },\n\n    // Search & filter\n    setSearchQuery: (query) => set({ searchQuery: query }),\n    \n    getFilteredEmployees: () => {\n      const { employees, searchQuery } = get()\n      if (!searchQuery) return employees\n      \n      return employees.filter(emp =>\n        emp.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        emp.email.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        emp.id.toLowerCase().includes(searchQuery.toLowerCase())\n      )\n    },\n\n    // Free bucket operations\n    moveToFreeBucket: (employeeIds) => set((state) => {\n      const movedEmployees = state.employees\n        .filter(emp => employeeIds.includes(emp.id))\n        .map(emp => ({ ...emp, departmentId: null }))\n      \n      return {\n        employees: state.employees.map(emp =>\n          employeeIds.includes(emp.id) ? { ...emp, departmentId: null } : emp\n        ),\n        freeBucket: [...state.freeBucket, ...movedEmployees]\n      }\n    }),\n    \n    removeFromFreeBucket: (employeeIds) => set((state) => ({\n      freeBucket: state.freeBucket.filter(emp => !employeeIds.includes(emp.id)),\n      employees: state.employees.filter(emp => !employeeIds.includes(emp.id))\n    }))\n  }))\n)"], "names": [], "mappings": ";;;AAAA;AACA;;;AAsCO,MAAM,aAAa,CAAA,GAAA,2JAAA,CAAA,SAAM,AAAD,IAC7B,CAAA,GAAA,gJAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC,KAAK,MAAQ,CAAC;QACnC,gBAAgB;QAChB,WAAW,EAAE;QACb,aAAa,EAAE;QACf,YAAY,EAAE;QACd,mBAAmB,EAAE;QACrB,WAAW;QACX,aAAa;QAEb,eAAe;QACf,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C;oBACA,YAAY,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK;gBAC3D,CAAC;QACD,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QAEnD,aAAa,CAAC,WAAa,IAAI,CAAC,QAAU,CAAC;oBACzC,WAAW;2BAAI,MAAM,SAAS;wBAAE;qBAAS;gBAC3C,CAAC;QAED,gBAAgB,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC/C,WAAW,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,MAC7B,IAAI,EAAE,KAAK,KAAK;4BAAE,GAAG,GAAG;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAE7C,CAAC;QAED,gBAAgB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBACtC,WAAW,MAAM,SAAS,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;oBACpD,mBAAmB,MAAM,iBAAiB,CAAC,MAAM,CAAC,CAAA,QAAS,UAAU;gBACvE,CAAC;QAED,oBAAoB;QACpB,yBAAyB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAC/C,mBAAmB,MAAM,iBAAiB,CAAC,QAAQ,CAAC,MAChD,MAAM,iBAAiB,CAAC,MAAM,CAAC,CAAA,QAAS,UAAU,MAClD;2BAAI,MAAM,iBAAiB;wBAAE;qBAAG;gBACtC,CAAC;QAED,oBAAoB,CAAC,MAAQ,IAAI;gBAAE,mBAAmB;YAAI;QAC1D,gBAAgB,IAAM,IAAI;gBAAE,mBAAmB,EAAE;YAAC;QAElD,kBAAkB;QAClB,sBAAsB,OAAO;YAC3B,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAE,GAAG;YAEzC,OAAQ,UAAU,IAAI;gBACpB,KAAK;oBACH,IAAI,CAAC,QAAU,CAAC;4BACd,WAAW,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,MAC7B,UAAU,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,IACjC;oCAAE,GAAG,GAAG;oCAAE,cAAc,UAAU,kBAAkB,IAAI;gCAAK,IAC7D;4BAEN,YAAY,MAAM,SAAS,CACxB,GAAG,CAAC,CAAA,MACH,UAAU,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,IACjC;oCAAE,GAAG,GAAG;oCAAE,cAAc,UAAU,kBAAkB,IAAI;gCAAK,IAC7D,KAEL,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK;wBACxC,CAAC;oBACD;gBAEF,KAAK;oBACH,IAAI,UAAU,SAAS,EAAE;wBACvB,IAAI,CAAC,QAAU,CAAC;gCACd,WAAW,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,MAC7B,UAAU,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,IACjC;wCAAE,GAAG,GAAG;wCAAE,QAAQ,UAAU,SAAS;oCAAE,IACvC;4BAER,CAAC;oBACH;oBACA;gBAEF,KAAK;oBACH,IAAI,CAAC,QAAU,CAAC;4BACd,WAAW,MAAM,SAAS,CAAC,MAAM,CAAC,CAAA,MAChC,CAAC,UAAU,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE;wBAE1C,CAAC;oBACD;YACJ;YAEA,IAAI;gBAAE,mBAAmB,EAAE;YAAC;QAC9B;QAEA,kBAAkB;QAClB,gBAAgB,CAAC,QAAU,IAAI;gBAAE,aAAa;YAAM;QAEpD,sBAAsB;YACpB,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG;YACnC,IAAI,CAAC,aAAa,OAAO;YAEzB,OAAO,UAAU,MAAM,CAAC,CAAA,MACtB,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACvD,IAAI,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACxD,IAAI,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAEzD;QAEA,yBAAyB;QACzB,kBAAkB,CAAC,cAAgB,IAAI,CAAC;gBACtC,MAAM,iBAAiB,MAAM,SAAS,CACnC,MAAM,CAAC,CAAA,MAAO,YAAY,QAAQ,CAAC,IAAI,EAAE,GACzC,GAAG,CAAC,CAAA,MAAO,CAAC;wBAAE,GAAG,GAAG;wBAAE,cAAc;oBAAK,CAAC;gBAE7C,OAAO;oBACL,WAAW,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,MAC7B,YAAY,QAAQ,CAAC,IAAI,EAAE,IAAI;4BAAE,GAAG,GAAG;4BAAE,cAAc;wBAAK,IAAI;oBAElE,YAAY;2BAAI,MAAM,UAAU;2BAAK;qBAAe;gBACtD;YACF;QAEA,sBAAsB,CAAC,cAAgB,IAAI,CAAC,QAAU,CAAC;oBACrD,YAAY,MAAM,UAAU,CAAC,MAAM,CAAC,CAAA,MAAO,CAAC,YAAY,QAAQ,CAAC,IAAI,EAAE;oBACvE,WAAW,MAAM,SAAS,CAAC,MAAM,CAAC,CAAA,MAAO,CAAC,YAAY,QAAQ,CAAC,IAAI,EAAE;gBACvE,CAAC;IACH,CAAC", "debugId": null}}, {"offset": {"line": 992, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAI5B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1033, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/G%3A/Augment%20code/components/ui/dialog.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": ["React", "DialogPrimitive", "X", "cn", "Dialog", "Root", "DialogTrigger", "<PERSON><PERSON>", "DialogPortal", "Portal", "DialogClose", "Close", "DialogOverlay", "forwardRef", "t0", "ref", "$", "_c", "$i", "Symbol", "for", "className", "props", "t1", "t2", "displayName", "Overlay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "t3", "t4", "Content", "DialogHeader", "<PERSON><PERSON><PERSON><PERSON>er", "DialogTitle", "Title", "DialogDescription", "Description"], "mappings": ";;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,eAAe,MAAM,wBAAwB;AACzD,SAASC,CAAC,QAAQ,cAAc;AAChC,SAASC,EAAE,QAAQ,aAAa;;;;;;;AAEhC,MAAMC,MAAM,yKAAGH,OAAoB,QAAL,CAACI;AAE/B,MAAMC,aAAa,yKAAGL,UAAuB,KAAR,CAACM;AAEtC,MAAMC,YAAY,yKAAGP,SAAsB,MAAP,CAACQ;AAErC,MAAMC,WAAW,yKAAGT,QAAqB,OAAN,CAACU;AAEpC,MAAMC,aAAa,iBAAGZ,KAAK,CAACa,qKAAU,CAGpC,CAAAC,EAAA,EAAAC,GAAA;IAAA,MAAAC,CAAA,mLAAAC,IAAAA,AAAA,EAAA;IAAA,IAAAD,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAE,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAF,CAAA,CAAAE,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAJ,CAAA,CAAA,EAAA,GAAA;IAAA;IAAA,IAAAK,SAAA;IAAA,IAAAC,KAAA;IAAA,IAAAN,CAAA,CAAA,EAAA,KAAAF,EAAA,EAAA;QAAC,CAAA,EAAAO,SAAA,EAAA,GAAAC,OAAA,GAAAR,EAAuB;QAAAE,CAAA,CAAA,EAAA,GAAAF,EAAA;QAAAE,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAM,KAAA;IAAA,OAAA;QAAAD,SAAA,GAAAL,CAAA,CAAA,EAAA;QAAAM,KAAA,GAAAN,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAO,EAAA;IAAA,IAAAP,CAAA,CAAA,EAAA,KAAAK,SAAA,EAAA;QAGXE,EAAA,uHAAApB,KAAAA,AAAA,EACT,8KAA8K,EAC9KkB,SACF,CAAC;QAAAL,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAO,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAP,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAQ,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAM,KAAA,IAAAN,CAAA,CAAA,EAAA,KAAAD,GAAA,IAAAC,CAAA,CAAA,EAAA,KAAAO,EAAA,EAAA;QALHC,EAAA,iBAAA,6LAAA,qKAAA,CAAA,UAAA;YACOT,GAAG,CAAHA,CAAAA,GAAE,CAAC;YACG,SAGV,CAHU,CAAAQ,EAGX,CAAC;YAAA,GACGD,KAAK,IACT;;;;;;QAAAN,CAAA,CAAA,EAAA,GAAAM,KAAA;QAAAN,CAAA,CAAA,EAAA,GAAAD,GAAA;QAAAC,CAAA,CAAA,EAAA,GAAAO,EAAA;QAAAP,CAAA,CAAA,EAAA,GAAAQ,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAR,CAAA,CAAA,EAAA;IAAA;IAAA,OAPFQ,EAOE;AAAA,CACH,CAAC;;AACFZ,aAAa,CAACa,WAAW,yKAAGxB,UAAuB,CAACwB,IAAT,CAACC,MAAmB;AAE/D,MAAMC,aAAa,iBAAG3B,KAAK,CAACa,qKAAU,OAGpC,CAAAC,EAAA,EAAAC,GAAA;IAAA,MAAAC,CAAA,mLAAAC,IAAAA,AAAA,EAAA;IAAA,IAAAD,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAE,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAF,CAAA,CAAAE,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAJ,CAAA,CAAA,EAAA,GAAA;IAAA;IAAA,IAAAY,QAAA;IAAA,IAAAP,SAAA;IAAA,IAAAC,KAAA;IAAA,IAAAN,CAAA,CAAA,EAAA,KAAAF,EAAA,EAAA;QAAC,CAAA,EAAAO,SAAA,EAAAO,QAAA,EAAA,GAAAN,OAAA,GAAAR,EAAiC;QAAAE,CAAA,CAAA,EAAA,GAAAF,EAAA;QAAAE,CAAA,CAAA,EAAA,GAAAY,QAAA;QAAAZ,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAM,KAAA;IAAA,OAAA;QAAAM,QAAA,GAAAZ,CAAA,CAAA,EAAA;QAAAK,SAAA,GAAAL,CAAA,CAAA,EAAA;QAAAM,KAAA,GAAAN,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAO,EAAA;IAAA,IAAAP,CAAA,CAAA,EAAA,KAAAG,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAEhCG,EAAA,iBAAA,6LAAC,aAAa,GAAG;;;;;QAAAP,CAAA,CAAA,EAAA,GAAAO,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAP,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAQ,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAK,SAAA,EAAA;QAGJG,EAAA,GAAArB,yHAAA,AAAAA,EACT,6fAA6f,EAC7fkB,SACF,CAAC;QAAAL,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAQ,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAR,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAa,EAAA;IAAA,IAAAb,CAAA,CAAA,EAAA,KAAAG,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAIDS,EAAA,iBAAA,6LAAA,qKAAA,CAAA,QAAA;YAAiC,SAA+Q,EAA/Q,+QAA+Q;;8BAC9S,6XAAC,IAAC;oBAAW,SAAS,EAAT,SAAS;;;;;;8BACtB,6LAAA,IAAsC;oBAAtB,SAAS,EAAT,SAAS;8BAAC,KAAK,EAA/B,IAAsC,CACxC,wBAAwB;;;;;;;;;;;;QAAAb,CAAA,CAAA,EAAA,GAAAa,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAb,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAc,EAAA;IAAA,IAAAd,CAAA,CAAA,EAAA,KAAAY,QAAA,IAAAZ,CAAA,CAAA,GAAA,KAAAM,KAAA,IAAAN,CAAA,CAAA,GAAA,KAAAD,GAAA,IAAAC,CAAA,CAAA,GAAA,KAAAQ,EAAA,EAAA;QAd5BM,EAAA,iBAAA,6LAAC,YAAY,CACX;;gBAAAP,EAAgB;8BAChB,6LAAA,qKAAA,CAAA,UAAA;oBACOR,GAAG,CAAHA,CAAAA,GAAE,CAAC;oBACG,SAGV,CAHU,CAAAS,EAGX,CAAC;oBAAA,GACGF,KAAK,EAERM;;wBAAAA,QAAO,CACR;wBAAAC,EAGuB,CACzB,0BACF,EAhBC,YAAY,CAgBE;;;;;;;;;;;;;QAAAb,CAAA,CAAA,EAAA,GAAAY,QAAA;QAAAZ,CAAA,CAAA,GAAA,GAAAM,KAAA;QAAAN,CAAA,CAAA,GAAA,GAAAD,GAAA;QAAAC,CAAA,CAAA,GAAA,GAAAQ,EAAA;QAAAR,CAAA,CAAA,GAAA,GAAAc,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAd,CAAA,CAAA,GAAA;IAAA;IAAA,OAhBfc,EAgBe;AAAA,CAChB,CAAC;;AACFH,aAAa,CAACF,WAAW,yKAAGxB,UAAuB,CAACwB,IAAT,CAACM,MAAmB;AAE/D,MAAMC,YAAY,IAAGlB,EAAA;IAAA,MAAAE,CAAA,mLAAAC,IAAAA,AAAA,EAAA;IAAA,IAAAD,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAE,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAF,CAAA,CAAAE,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAJ,CAAA,CAAA,EAAA,GAAA;IAAA;IAAA,IAAAK,SAAA;IAAA,IAAAC,KAAA;IAAA,IAAAN,CAAA,CAAA,EAAA,KAAAF,EAAA,EAAA;QAAC,CAAA,EAAAO,SAAA,EAAA,GAAAC,OAAA,GAAAR,EAGiB;QAAAE,CAAA,CAAA,EAAA,GAAAF,EAAA;QAAAE,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAM,KAAA;IAAA,OAAA;QAAAD,SAAA,GAAAL,CAAA,CAAA,EAAA;QAAAM,KAAA,GAAAN,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAO,EAAA;IAAA,IAAAP,CAAA,CAAA,EAAA,KAAAK,SAAA,EAAA;QAExBE,EAAA,sHAAApB,MAAAA,AAAA,EACT,oDAAoD,EACpDkB,SACF,CAAC;QAAAL,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAO,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAP,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAQ,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAM,KAAA,IAAAN,CAAA,CAAA,EAAA,KAAAO,EAAA,EAAA;QAJHC,EAAA,iBAAA,6LAAA,GAME;YALW,SAGV,CAHU,CAAAD,EAGX,CAAC;YAAA,GACGD,KAAK,IACT;;;;;;QAAAN,CAAA,CAAA,EAAA,GAAAM,KAAA;QAAAN,CAAA,CAAA,EAAA,GAAAO,EAAA;QAAAP,CAAA,CAAA,EAAA,GAAAQ,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAR,CAAA,CAAA,EAAA;IAAA;IAAA,OANFQ,EAME;AAAA,CACH;;AACDQ,YAAY,CAACP,WAAW,GAAG,cAAc;AAEzC,sBAAqBX,EAAA;IAAA,MAAAE,CAAA,mLAAAC,IAAAA,AAAA,EAAA;IAAA,IAAAD,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAE,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAF,CAAA,CAAAE,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAJ,CAAA,CAAA,EAAA,GAAA;IAAA;IAAA,IAAAK,SAAA;IAAA,IAAAC,KAAA;IAAA,IAAAN,CAAA,CAAA,EAAA,KAAAF,EAAA,EAAA;QAAC,CAAA,EAAAO,SAAA,EAAA,GAAAC,OAAA,GAAAR,EAGiB;QAAAE,CAAA,CAAA,EAAA,GAAAF,EAAA;QAAAE,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAM,KAAA;IAAA,OAAA;QAAAD,SAAA,GAAAL,CAAA,CAAA,EAAA;QAAAM,KAAA,GAAAN,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAO,EAAA;IAAA,IAAAP,CAAA,CAAA,EAAA,KAAAK,SAAA,EAAA;QAExBE,EAAA,OAAApB,qHAAAA,AAAA,EACT,+DAA+D,EAC/DkB,SACF,CAAC;QAAAL,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAO,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAP,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAQ,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAM,KAAA,IAAAN,CAAA,CAAA,EAAA,KAAAO,EAAA,EAAA;QAJHC,EAAA,iBAAA,6LAAA,GAME;YALW,SAGV,CAHU,CAAAD,EAGX,CAAC;YAAA,GACGD,KAAK,IACT;;;;;;QAAAN,CAAA,CAAA,EAAA,GAAAM,KAAA;QAAAN,CAAA,CAAA,EAAA,GAAAO,EAAA;QAAAP,CAAA,CAAA,EAAA,GAAAQ,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAR,CAAA,CAAA,EAAA;IAAA;IAAA,OANFQ,EAME;AAAA,CACH;MAXKS,YAAY;AAYlBA,YAAY,CAACR,WAAW,GAAG,cAAc;AAEzC,MAAMS,WAAW,+KAAGlC,KAAK,CAACa,OAAU,OAGlC,CAAAC,EAAA,EAAAC,GAAA;IAAA,MAAAC,CAAA,mLAAAC,IAAAA,AAAA,EAAA;IAAA,IAAAD,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAE,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAF,CAAA,CAAAE,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAJ,CAAA,CAAA,EAAA,GAAA;IAAA;IAAA,IAAAK,SAAA;IAAA,IAAAC,KAAA;IAAA,IAAAN,CAAA,CAAA,EAAA,KAAAF,EAAA,EAAA;QAAC,CAAA,EAAAO,SAAA,EAAA,GAAAC,OAAA,GAAAR,EAAuB;QAAAE,CAAA,CAAA,EAAA,GAAAF,EAAA;QAAAE,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAM,KAAA;IAAA,OAAA;QAAAD,SAAA,GAAAL,CAAA,CAAA,EAAA;QAAAM,KAAA,GAAAN,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAO,EAAA;IAAA,IAAAP,CAAA,CAAA,EAAA,KAAAK,SAAA,EAAA;QAGXE,EAAA,uHAAApB,KAAAA,AAAA,EACT,mDAAmD,EACnDkB,SACF,CAAC;QAAAL,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAO,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAP,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAQ,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAM,KAAA,IAAAN,CAAA,CAAA,EAAA,KAAAD,GAAA,IAAAC,CAAA,CAAA,EAAA,KAAAO,EAAA,EAAA;QALHC,EAAA,iBAAA,6LAAA,qKAAA,CAAA,QAAA;YACOT,GAAG,CAAHA,CAAAA,GAAE,CAAC;YACG,SAGV,CAHU,CAAAQ,EAGX,CAAC;YAAA,GACGD,KAAK,IACT;;;;;;QAAAN,CAAA,CAAA,EAAA,GAAAM,KAAA;QAAAN,CAAA,CAAA,EAAA,GAAAD,GAAA;QAAAC,CAAA,CAAA,EAAA,GAAAO,EAAA;QAAAP,CAAA,CAAA,EAAA,GAAAQ,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAR,CAAA,CAAA,EAAA;IAAA;IAAA,OAPFQ,EAOE;AAAA,CACH,CAAC;;AACFU,WAAW,CAACT,WAAW,yKAAGxB,QAAqB,CAACwB,MAAP,CAACU,IAAiB;AAE3D,MAAMC,iBAAiB,+KAAGpC,KAAK,CAACa,OAAU,OAGxC,CAAAC,EAAA,EAAAC,GAAA;IAAA,MAAAC,CAAA,mLAAAC,IAAAA,AAAA,EAAA;IAAA,IAAAD,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAE,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAF,CAAA,CAAAE,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAJ,CAAA,CAAA,EAAA,GAAA;IAAA;IAAA,IAAAK,SAAA;IAAA,IAAAC,KAAA;IAAA,IAAAN,CAAA,CAAA,EAAA,KAAAF,EAAA,EAAA;QAAC,CAAA,EAAAO,SAAA,EAAA,GAAAC,OAAA,GAAAR,EAAuB;QAAAE,CAAA,CAAA,EAAA,GAAAF,EAAA;QAAAE,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAM,KAAA;IAAA,OAAA;QAAAD,SAAA,GAAAL,CAAA,CAAA,EAAA;QAAAM,KAAA,GAAAN,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAO,EAAA;IAAA,IAAAP,CAAA,CAAA,EAAA,KAAAK,SAAA,EAAA;QAGXE,EAAA,uHAAApB,KAAAA,AAAA,EAAG,+BAA+B,EAAEkB,SAAS,CAAC;QAAAL,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAO,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAP,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAQ,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAM,KAAA,IAAAN,CAAA,CAAA,EAAA,KAAAD,GAAA,IAAAC,CAAA,CAAA,EAAA,KAAAO,EAAA,EAAA;QAF3DC,EAAA,iBAAA,6LAAA,qKAAA,CAAA,cAAA;YACOT,GAAG,CAAHA,CAAAA,GAAE,CAAC;YACG,SAA8C,CAA9C,CAAAQ,EAA6C,CAAC;YAAA,GACrDD,KAAK,IACT;;;;;;QAAAN,CAAA,CAAA,EAAA,GAAAM,KAAA;QAAAN,CAAA,CAAA,EAAA,GAAAD,GAAA;QAAAC,CAAA,CAAA,EAAA,GAAAO,EAAA;QAAAP,CAAA,CAAA,EAAA,GAAAQ,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAR,CAAA,CAAA,EAAA;IAAA;IAAA,OAJFQ,EAIE;AAAA,CACH,CAAC;;AACFY,iBAAiB,CAACX,WAAW,yKAAGxB,cAA2B,CAAZ,AAAawB,CAAZY,UAAuB", "ignoreList": [], "debugId": null}}, {"offset": {"line": 1429, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/departments/G%3A/Augment%20code/components/departments/add-department-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Department } from \"@/lib/types\"\nimport { useHRStore } from \"@/lib/store/hr-store\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\"\n\ninterface AddDepartmentDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n}\n\nexport function AddDepartmentDialog({ \n  open, \n  onOpenChange \n}: AddDepartmentDialogProps) {\n  const { departments, setDepartments } = useHRStore()\n  const [isLoading, setIsLoading] = useState(false)\n  const [formData, setFormData] = useState({\n    name: '',\n    code: '',\n    capacity: ''\n  })\n  const [errors, setErrors] = useState<Record<string, string>>({})\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {}\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'Department name is required'\n    }\n\n    if (!formData.code.trim()) {\n      newErrors.code = 'Department code is required'\n    } else if (formData.code.length > 5) {\n      newErrors.code = 'Department code must be 5 characters or less'\n    } else if (departments.some(dept => dept.code.toLowerCase() === formData.code.toLowerCase())) {\n      newErrors.code = 'Department code already exists'\n    }\n\n    if (!formData.capacity.trim()) {\n      newErrors.capacity = 'Capacity is required'\n    } else {\n      const capacity = parseInt(formData.capacity)\n      if (isNaN(capacity) || capacity <= 0) {\n        newErrors.capacity = 'Capacity must be a positive number'\n      } else if (capacity > 1000) {\n        newErrors.capacity = 'Capacity cannot exceed 1000'\n      }\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) {\n      return\n    }\n\n    setIsLoading(true)\n\n    try {\n      // Generate department ID\n      const existingIds = departments.map(dept => \n        parseInt(dept.id.replace('dept-', '')) || 0\n      )\n      const nextId = Math.max(0, ...existingIds) + 1\n      const departmentId = `dept-${nextId.toString().padStart(3, '0')}`\n\n      const newDepartment: Department = {\n        id: departmentId,\n        name: formData.name.trim(),\n        code: formData.code.trim().toUpperCase(),\n        capacity: parseInt(formData.capacity),\n        employees: [],\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      }\n\n      // Add to departments list\n      setDepartments([...departments, newDepartment])\n      \n      // Reset form\n      setFormData({\n        name: '',\n        code: '',\n        capacity: ''\n      })\n      setErrors({})\n      \n      onOpenChange(false)\n    } catch (error) {\n      console.error('Failed to add department:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleInputChange = (field: string, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }))\n    \n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }))\n    }\n  }\n\n  const handleClose = () => {\n    setFormData({\n      name: '',\n      code: '',\n      capacity: ''\n    })\n    setErrors({})\n    onOpenChange(false)\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"sm:max-w-[425px]\">\n        <DialogHeader>\n          <DialogTitle>Add New Department</DialogTitle>\n          <DialogDescription>\n            Create a new department with a specific capacity for employees.\n          </DialogDescription>\n        </DialogHeader>\n        \n        <form onSubmit={handleSubmit}>\n          <div className=\"grid gap-4 py-4\">\n            <div className=\"grid gap-2\">\n              <Label htmlFor=\"name\">Department Name</Label>\n              <Input\n                id=\"name\"\n                value={formData.name}\n                onChange={(e) => handleInputChange('name', e.target.value)}\n                placeholder=\"Enter department name\"\n                className={errors.name ? 'border-red-500' : ''}\n              />\n              {errors.name && (\n                <p className=\"text-sm text-red-500\">{errors.name}</p>\n              )}\n            </div>\n            \n            <div className=\"grid gap-2\">\n              <Label htmlFor=\"code\">Department Code</Label>\n              <Input\n                id=\"code\"\n                value={formData.code}\n                onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}\n                placeholder=\"Enter department code (e.g., ENG)\"\n                maxLength={5}\n                className={errors.code ? 'border-red-500' : ''}\n              />\n              {errors.code && (\n                <p className=\"text-sm text-red-500\">{errors.code}</p>\n              )}\n              <p className=\"text-xs text-muted-foreground\">\n                Maximum 5 characters, will be converted to uppercase\n              </p>\n            </div>\n            \n            <div className=\"grid gap-2\">\n              <Label htmlFor=\"capacity\">Capacity</Label>\n              <Input\n                id=\"capacity\"\n                type=\"number\"\n                value={formData.capacity}\n                onChange={(e) => handleInputChange('capacity', e.target.value)}\n                placeholder=\"Enter maximum number of employees\"\n                min=\"1\"\n                max=\"1000\"\n                className={errors.capacity ? 'border-red-500' : ''}\n              />\n              {errors.capacity && (\n                <p className=\"text-sm text-red-500\">{errors.capacity}</p>\n              )}\n              <p className=\"text-xs text-muted-foreground\">\n                Maximum number of employees this department can hold\n              </p>\n            </div>\n          </div>\n          \n          <DialogFooter>\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={handleClose}\n              disabled={isLoading}\n            >\n              Cancel\n            </Button>\n            <Button type=\"submit\" disabled={isLoading}>\n              {isLoading ? 'Creating...' : 'Create Department'}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": ["useState", "useHRStore", "<PERSON><PERSON>", "Input", "Label", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogDescription", "<PERSON><PERSON><PERSON><PERSON>er", "DialogHeader", "DialogTitle", "AddDepartmentDialog", "open", "onOpenChange", "departments", "setDepartments", "isLoading", "setIsLoading", "formData", "setFormData", "name", "code", "capacity", "errors", "setErrors", "validateForm", "newErrors", "trim", "length", "some", "dept", "toLowerCase", "parseInt", "isNaN", "Object", "keys", "handleSubmit", "e", "preventDefault", "existingIds", "map", "id", "replace", "nextId", "Math", "max", "departmentId", "toString", "padStart", "newDepartment", "toUpperCase", "employees", "createdAt", "Date", "updatedAt", "error", "console", "handleInputChange", "field", "value", "prev", "handleClose", "target"], "mappings": ";;;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAEhC,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SACEC,MAAM,EACNC,aAAa,EACbC,iBAAiB,EACjBC,YAAY,EACZC,YAAY,EACZC,WAAW,QACN,wBAAwB;;;AAf/B,YAAY;;;;;;;AAsBL,kCAGoB,EAAE;QAHO,EAClCE,IAAI,EACJC,YAAAA;;IAEA,MAAM,EAAEC,WAAW,EAAEC,cAAAA,EAAgB,mJAAGd;IACxC,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,qKAAGjB,WAAAA,AAAQ,EAAC,KAAK,CAAC;IACjD,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,qKAAGnB,WAAAA,AAAQ,EAAC;QACvCoB,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,EAAE;QACRC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,qKAAGxB,WAAAA,AAAQ,EAAyB,CAAC,CAAC,CAAC;IAEhE,MAAMyB,YAAY,GAAGA,CAAA,KAAM;QACzB,MAAMC,SAAiC,GAAG,CAAC,CAAC;QAE5C,IAAI,CAACR,QAAQ,CAACE,IAAI,CAACO,IAAI,CAAC,CAAC,EAAE;YACzBD,SAAS,CAACN,IAAI,GAAG,6BAA6B;QAChD;QAEA,IAAI,CAACF,QAAQ,CAACG,IAAI,CAACM,IAAI,CAAC,CAAC,EAAE;YACzBD,SAAS,CAACL,IAAI,GAAG,6BAA6B;QAChD,CAAC,MAAM,IAAIH,QAAQ,CAACG,IAAI,CAACO,MAAM,GAAG,CAAC,EAAE;YACnCF,SAAS,CAACL,IAAI,GAAG,8CAA8C;QACjE,CAAC,MAAM,IAAIP,WAAW,CAACe,IAAI,EAACC,IAAI,GAAIA,IAAI,CAACT,IAAI,CAACU,WAAW,CAAC,CAAC,KAAKb,QAAQ,CAACG,IAAI,CAACU,WAAW,CAAC,CAAC,CAAC,EAAE;YAC5FL,SAAS,CAACL,IAAI,GAAG,gCAAgC;QACnD;QAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,CAACK,IAAI,CAAC,CAAC,EAAE;YAC7BD,SAAS,CAACJ,QAAQ,GAAG,sBAAsB;QAC7C,CAAC,MAAM;YACL,MAAMA,QAAQ,GAAGU,QAAQ,CAACd,QAAQ,CAACI,QAAQ,CAAC;YAC5C,IAAIW,KAAK,CAACX,QAAQ,CAAC,IAAIA,QAAQ,IAAI,CAAC,EAAE;gBACpCI,SAAS,CAACJ,QAAQ,GAAG,oCAAoC;YAC3D,CAAC,MAAM,IAAIA,QAAQ,GAAG,IAAI,EAAE;gBAC1BI,SAAS,CAACJ,QAAQ,GAAG,6BAA6B;YACpD;QACF;QAEAE,SAAS,CAACE,SAAS,CAAC;QACpB,OAAOQ,MAAM,CAACC,IAAI,CAACT,SAAS,CAAC,CAACE,MAAM,KAAK,CAAC;IAC5C,CAAC;IAED,MAAMQ,YAAY,GAAG,OAAOC,CAAkB,IAAK;QACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;QAElB,IAAI,CAACb,YAAY,CAAC,CAAC,EAAE;YACnB;QACF;QAEAR,YAAY,CAAC,IAAI,CAAC;QAElB,IAAI;YACF,yBAAA;YACA,MAAMsB,WAAW,GAAGzB,WAAW,CAAC0B,GAAG,EAACV,MAAI,GACtCE,QAAQ,CAACF,MAAI,CAACW,EAAE,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,CAC5C,CAAC;YACD,MAAMC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,GAAGN,WAAW,CAAC,GAAG,CAAC;YAC9C,MAAMO,YAAY,GAAG,QAA0C,CAAE,MAApCH,MAAM,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;YAE/D,MAAMC,aAAyB,GAAG;gBAChCR,EAAE,EAAEK,YAAY;gBAChB1B,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAACO,IAAI,CAAC,CAAC;gBAC1BN,IAAI,EAAEH,QAAQ,CAACG,IAAI,CAACM,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC,CAAC;gBACxC5B,QAAQ,EAAEU,QAAQ,CAACd,QAAQ,CAACI,QAAQ,CAAC;gBACrC6B,SAAS,EAAE,EAAE;gBACbC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;gBACrBC,SAAS,EAAE,IAAID,IAAI,CAAC;YACtB,CAAC;YAED,0BAAA;YACAtC,cAAc,CAAC,CAAC;mBAAGD,WAAW;gBAAEmC,aAAa;aAAC,CAAC;YAE/C,aAAA;YACA9B,WAAW,CAAC;gBACVC,IAAI,EAAE,EAAE;gBACRC,IAAI,EAAE,EAAE;gBACRC,QAAQ,EAAE;YACZ,CAAC,CAAC;YACFE,SAAS,CAAC,CAAC,CAAC,CAAC;YAEbX,YAAY,CAAC,KAAK,CAAC;QACrB,CAAC,CAAC,OAAO0C,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACnD,CAAC,QAAS;YACRtC,YAAY,CAAC,KAAK,CAAC;QACrB;IACF,CAAC;IAED,MAAMwC,iBAAiB,GAAGA,CAACC,KAAa,EAAEC,KAAa,KAAK;QAC1DxC,WAAW,EAACyC,IAAI,GAAA,CAAK;gBACnB,GAAGA,IAAI;gBACP,CAACF,KAAK,CAAA,EAAGC;YACX,CAAC,CAAC,CAAC;QAEH,sCAAA;QACA,IAAIpC,MAAM,CAACmC,KAAK,CAAC,EAAE;YACjBlC,SAAS,EAACoC,MAAI,GAAA,CAAK;oBACjB,GAAGA,MAAI;oBACP,CAACF,KAAK,CAAA,EAAG;gBACX,CAAC,CAAC,CAAC;QACL;IACF,CAAC;IAED,MAAMG,WAAW,GAAGA,CAAA,KAAM;QACxB1C,WAAW,CAAC;YACVC,IAAI,EAAE,EAAE;YACRC,IAAI,EAAE,EAAE;YACRC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFE,SAAS,CAAC,CAAC,CAAC,CAAC;QACbX,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC;IAED,qBACE,4TAAC,SAAM;QAAC,IAAI,CAAC,CAACD,IAAI,CAAC;QAAC,YAAY,CAAC,CAACiD,WAAW,CAAC;gCAC5C,4TAAC,gBAAa;YAAC,SAAS,EAAC,kBAAkB;;8BACzC,4TAAC,eAAY;;sCACX,4TAAC,cAAW;sCAAC,kBAAkB,EAAE,WAAW;;;;;;sCAC5C,4TAAC,oBAAiB;sCAAA;;;;;;;;;;;;8BAKpB,6LAAC,IAAI;oBAAC,QAAQ,CAAC,CAACzB,YAAY,CAAC;;sCAC3B,6LAAC,GAAG;4BAAC,SAAS,EAAC,iBAAiB;;8CAC9B,6LAAC,GAAG;oCAAC,SAAS,EAAC,YAAY;;sDACzB,2TAAC,QAAK;4CAAC,OAAO,EAAC,MAAM;sDAAC,eAAe,EAAE,KAAK;;;;;;sDAC5C,2TAAC,QAAK;4CACJ,EAAE,EAAC,MAAM;4CACT,KAAK,CAAC,CAAClB,QAAQ,CAACE,IAAI,CAAC;4CACrB,QAAQ,CAAC,EAAEiB,GAAC,GAAKoB,iBAAiB,CAAC,MAAM,EAAEpB,GAAC,CAACyB,MAAM,CAACH,KAAK,CAAC,CAAC;4CAC3D,WAAW,EAAC,uBAAuB;4CACnC,SAAS,CAAC,CAACpC,MAAM,CAACH,IAAI,GAAG,gBAAgB,GAAG,EAAE,CAAC;;;;;;wCAEhDG,MAAM,CAACH,IAAI,kBACV,6LAAC,CAAC;4CAAC,SAAS,EAAC,sBAAsB,CAAC;sDAACG,MAAM,CAACH,IAAI,CAAC,EAAE,CAAC,CACrD;;;;;;;;;;;;8CAGH,6LAAC,GAAG;oCAAC,SAAS,EAAC,YAAY;;sDACzB,2TAAC,QAAK;4CAAC,OAAO,EAAC,MAAM;sDAAC,eAAe,EAAE,KAAK;;;;;;sDAC5C,2TAAC,QAAK;4CACJ,EAAE,EAAC,MAAM;4CACT,KAAK,CAAC,CAACF,QAAQ,CAACG,IAAI,CAAC;4CACrB,QAAQ,CAAC,EAAEgB,GAAC,GAAKoB,iBAAiB,CAAC,MAAM,EAAEpB,GAAC,CAACyB,MAAM,CAACH,KAAK,CAACT,WAAW,CAAC,CAAC,CAAC,CAAC;4CACzE,WAAW,EAAC,mCAAmC;4CAC/C,SAAS,CAAC,CAAC,CAAC,CAAC;4CACb,SAAS,CAAC,CAAC3B,MAAM,CAACF,IAAI,GAAG,gBAAgB,GAAG,EAAE,CAAC;;;;;;wCAEhDE,MAAM,CAACF,IAAI,kBACV,6LAAC,CAAC;4CAAC,SAAS,EAAC,sBAAsB,CAAC;sDAACE,MAAM,CAACF,IAAI,CAAC,EAAE,CAAC,CACrD;;;;;;sDACD,6LAAC,CAAC;4CAAC,SAAS,EAAC,+BAA+B;sDAAA;;;;;;;;;;;;8CAK9C,6LAAC,GAAG;oCAAC,SAAS,EAAC,YAAY;;sDACzB,2TAAC,QAAK;4CAAC,OAAO,EAAC,UAAU;sDAAC,QAAQ,EAAE,KAAK;;;;;;sDACzC,2TAAC,QAAK;4CACJ,EAAE,EAAC,UAAU;4CACb,IAAI,EAAC,QAAQ;4CACb,KAAK,CAAC,CAACH,QAAQ,CAACI,QAAQ,CAAC;4CACzB,QAAQ,CAAC,EAAEe,GAAC,GAAKoB,iBAAiB,CAAC,UAAU,EAAEpB,GAAC,CAACyB,MAAM,CAACH,KAAK,CAAC,CAAC;4CAC/D,WAAW,EAAC,mCAAmC;4CAC/C,GAAG,EAAC,GAAG;4CACP,GAAG,EAAC,MAAM;4CACV,SAAS,CAAC,CAACpC,MAAM,CAACD,QAAQ,GAAG,gBAAgB,GAAG,EAAE,CAAC;;;;;;wCAEpDC,MAAM,CAACD,QAAQ,kBACd,6LAAC,CAAC;4CAAC,SAAS,EAAC,sBAAsB,CAAC;sDAACC,MAAM,CAACD,QAAQ,CAAC,EAAE,CAAC,CACzD;;;;;;sDACD,6LAAC,CAAC;4CAAC,SAAS,EAAC,+BAA+B;sDAAA;;;;;;;;;;;;;;;;;;sCAMhD,4TAAC,eAAY;;8CACX,4TAAC,SAAM;oCACL,IAAI,EAAC,QAAQ;oCACb,OAAO,EAAC,SAAS;oCACjB,OAAO,CAAC,CAACuC,WAAW,CAAC;oCACrB,QAAQ,CAAC,CAAC7C,SAAS,CAAC;8CAAA;;;;;;8CAItB,2TAAC,UAAM;oCAAC,IAAI,EAAC,QAAQ;oCAAC,QAAQ,CAAC,CAACA,SAAS,CAAC;8CACvCA,SAAS,GAAG,aAAa,GAAG,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9D;GApMgBL,mBAAmBA;;uIAIOV,aAAU,CAAC,CAAC", "ignoreList": [], "debugId": null}}, {"offset": {"line": 1776, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/lib/sample-data.ts"], "sourcesContent": ["import { Department, Employee } from './types'\n\nexport const sampleDepartments: Department[] = [\n  {\n    id: 'dept-001',\n    name: 'Engineering',\n    code: 'ENG',\n    capacity: 25,\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-01-15'),\n  },\n  {\n    id: 'dept-002',\n    name: 'Marketing',\n    code: 'MKT',\n    capacity: 15,\n    createdAt: new Date('2024-01-16'),\n    updatedAt: new Date('2024-01-16'),\n  },\n  {\n    id: 'dept-003',\n    name: 'Sales',\n    code: 'SAL',\n    capacity: 20,\n    createdAt: new Date('2024-01-17'),\n    updatedAt: new Date('2024-01-17'),\n  },\n  {\n    id: 'dept-004',\n    name: 'Human Resources',\n    code: 'HR',\n    capacity: 8,\n    createdAt: new Date('2024-01-18'),\n    updatedAt: new Date('2024-01-18'),\n  },\n  {\n    id: 'dept-005',\n    name: 'Finance',\n    code: 'FIN',\n    capacity: 12,\n    createdAt: new Date('2024-01-19'),\n    updatedAt: new Date('2024-01-19'),\n  },\n  {\n    id: 'dept-006',\n    name: 'Operations',\n    code: 'OPS',\n    capacity: 18,\n    createdAt: new Date('2024-01-20'),\n    updatedAt: new Date('2024-01-20'),\n  },\n]\n\nexport const sampleEmployees: Employee[] = [\n  // Engineering Department\n  {\n    id: 'ENG-001',\n    name: 'John Smith',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-03-15'),\n    transferHistory: [],\n    createdAt: new Date('2023-03-15'),\n    updatedAt: new Date('2023-03-15'),\n  },\n  {\n    id: 'ENG-002',\n    name: 'Sarah Johnson',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-05-20'),\n    transferHistory: [],\n    createdAt: new Date('2023-05-20'),\n    updatedAt: new Date('2023-05-20'),\n  },\n  {\n    id: 'ENG-003',\n    name: 'Michael Chen',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-07-10'),\n    transferHistory: [],\n    createdAt: new Date('2023-07-10'),\n    updatedAt: new Date('2023-07-10'),\n  },\n  {\n    id: 'ENG-004',\n    name: 'Emily Davis',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-09-05'),\n    transferHistory: [],\n    createdAt: new Date('2023-09-05'),\n    updatedAt: new Date('2023-09-05'),\n  },\n  {\n    id: 'ENG-005',\n    name: 'David Wilson',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-11-12'),\n    transferHistory: [],\n    createdAt: new Date('2023-11-12'),\n    updatedAt: new Date('2023-11-12'),\n  },\n  {\n    id: 'ENG-006',\n    name: 'Lisa Anderson',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-01-08'),\n    transferHistory: [],\n    createdAt: new Date('2024-01-08'),\n    updatedAt: new Date('2024-01-08'),\n  },\n  {\n    id: 'ENG-007',\n    name: 'Robert Taylor',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-02-14'),\n    transferHistory: [],\n    createdAt: new Date('2024-02-14'),\n    updatedAt: new Date('2024-02-14'),\n  },\n  {\n    id: 'ENG-008',\n    name: 'Jennifer Brown',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-03-22'),\n    transferHistory: [],\n    createdAt: new Date('2024-03-22'),\n    updatedAt: new Date('2024-03-22'),\n  },\n  {\n    id: 'ENG-009',\n    name: 'Christopher Lee',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-04-18'),\n    transferHistory: [],\n    createdAt: new Date('2024-04-18'),\n    updatedAt: new Date('2024-04-18'),\n  },\n  {\n    id: 'ENG-010',\n    name: 'Amanda White',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-05-25'),\n    transferHistory: [],\n    createdAt: new Date('2024-05-25'),\n    updatedAt: new Date('2024-05-25'),\n  },\n\n  // Marketing Department\n  {\n    id: 'MKT-001',\n    name: 'Jessica Garcia',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-04-10'),\n    transferHistory: [],\n    createdAt: new Date('2023-04-10'),\n    updatedAt: new Date('2023-04-10'),\n  },\n  {\n    id: 'MKT-002',\n    name: 'Daniel Martinez',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-06-15'),\n    transferHistory: [],\n    createdAt: new Date('2023-06-15'),\n    updatedAt: new Date('2023-06-15'),\n  },\n  {\n    id: 'MKT-003',\n    name: 'Ashley Rodriguez',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-08-20'),\n    transferHistory: [],\n    createdAt: new Date('2023-08-20'),\n    updatedAt: new Date('2023-08-20'),\n  },\n  {\n    id: 'MKT-004',\n    name: 'Matthew Thompson',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-10-12'),\n    transferHistory: [],\n    createdAt: new Date('2023-10-12'),\n    updatedAt: new Date('2023-10-12'),\n  },\n  {\n    id: 'MKT-005',\n    name: 'Stephanie Clark',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-01-30'),\n    transferHistory: [],\n    createdAt: new Date('2024-01-30'),\n    updatedAt: new Date('2024-01-30'),\n  },\n  {\n    id: 'MKT-006',\n    name: 'Kevin Lewis',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-03-15'),\n    transferHistory: [],\n    createdAt: new Date('2024-03-15'),\n    updatedAt: new Date('2024-03-15'),\n  },\n\n  // Sales Department\n  {\n    id: 'SAL-001',\n    name: 'Ryan Walker',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-02-28'),\n    transferHistory: [],\n    createdAt: new Date('2023-02-28'),\n    updatedAt: new Date('2023-02-28'),\n  },\n  {\n    id: 'SAL-002',\n    name: 'Nicole Hall',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-05-10'),\n    transferHistory: [],\n    createdAt: new Date('2023-05-10'),\n    updatedAt: new Date('2023-05-10'),\n  },\n  {\n    id: 'SAL-003',\n    name: 'Brandon Allen',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-07-25'),\n    transferHistory: [],\n    createdAt: new Date('2023-07-25'),\n    updatedAt: new Date('2023-07-25'),\n  },\n  {\n    id: 'SAL-004',\n    name: 'Megan Young',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-09-18'),\n    transferHistory: [],\n    createdAt: new Date('2023-09-18'),\n    updatedAt: new Date('2023-09-18'),\n  },\n  {\n    id: 'SAL-005',\n    name: 'Tyler King',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-11-30'),\n    transferHistory: [],\n    createdAt: new Date('2023-11-30'),\n    updatedAt: new Date('2023-11-30'),\n  },\n  {\n    id: 'SAL-006',\n    name: 'Rachel Wright',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-02-05'),\n    transferHistory: [],\n    createdAt: new Date('2024-02-05'),\n    updatedAt: new Date('2024-02-05'),\n  },\n  {\n    id: 'SAL-007',\n    name: 'Justin Lopez',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-04-12'),\n    transferHistory: [],\n    createdAt: new Date('2024-04-12'),\n    updatedAt: new Date('2024-04-12'),\n  },\n\n  // HR Department\n  {\n    id: 'HR-001',\n    name: 'Laura Hill',\n    email: '<EMAIL>',\n    departmentId: 'dept-004',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-01-20'),\n    transferHistory: [],\n    createdAt: new Date('2023-01-20'),\n    updatedAt: new Date('2023-01-20'),\n  },\n  {\n    id: 'HR-002',\n    name: 'Steven Green',\n    email: '<EMAIL>',\n    departmentId: 'dept-004',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-06-08'),\n    transferHistory: [],\n    createdAt: new Date('2023-06-08'),\n    updatedAt: new Date('2023-06-08'),\n  },\n  {\n    id: 'HR-003',\n    name: 'Kimberly Adams',\n    email: '<EMAIL>',\n    departmentId: 'dept-004',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-10-25'),\n    transferHistory: [],\n    createdAt: new Date('2023-10-25'),\n    updatedAt: new Date('2023-10-25'),\n  },\n\n  // Finance Department\n  {\n    id: 'FIN-001',\n    name: 'Andrew Baker',\n    email: '<EMAIL>',\n    departmentId: 'dept-005',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-03-08'),\n    transferHistory: [],\n    createdAt: new Date('2023-03-08'),\n    updatedAt: new Date('2023-03-08'),\n  },\n  {\n    id: 'FIN-002',\n    name: 'Michelle Nelson',\n    email: '<EMAIL>',\n    departmentId: 'dept-005',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-07-15'),\n    transferHistory: [],\n    createdAt: new Date('2023-07-15'),\n    updatedAt: new Date('2023-07-15'),\n  },\n  {\n    id: 'FIN-003',\n    name: 'Joshua Carter',\n    email: '<EMAIL>',\n    departmentId: 'dept-005',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-11-20'),\n    transferHistory: [],\n    createdAt: new Date('2023-11-20'),\n    updatedAt: new Date('2023-11-20'),\n  },\n  {\n    id: 'FIN-004',\n    name: 'Samantha Mitchell',\n    email: '<EMAIL>',\n    departmentId: 'dept-005',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-02-28'),\n    transferHistory: [],\n    createdAt: new Date('2024-02-28'),\n    updatedAt: new Date('2024-02-28'),\n  },\n\n  // Operations Department\n  {\n    id: 'OPS-001',\n    name: 'Gregory Perez',\n    email: '<EMAIL>',\n    departmentId: 'dept-006',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-04-05'),\n    transferHistory: [],\n    createdAt: new Date('2023-04-05'),\n    updatedAt: new Date('2023-04-05'),\n  },\n  {\n    id: 'OPS-002',\n    name: 'Heather Roberts',\n    email: '<EMAIL>',\n    departmentId: 'dept-006',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-08-12'),\n    transferHistory: [],\n    createdAt: new Date('2023-08-12'),\n    updatedAt: new Date('2023-08-12'),\n  },\n  {\n    id: 'OPS-003',\n    name: 'Nathan Turner',\n    email: '<EMAIL>',\n    departmentId: 'dept-006',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-12-01'),\n    transferHistory: [],\n    createdAt: new Date('2023-12-01'),\n    updatedAt: new Date('2023-12-01'),\n  },\n  {\n    id: 'OPS-004',\n    name: 'Brittany Phillips',\n    email: '<EMAIL>',\n    departmentId: 'dept-006',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-03-10'),\n    transferHistory: [],\n    createdAt: new Date('2024-03-10'),\n    updatedAt: new Date('2024-03-10'),\n  },\n\n  // Free Bucket (Unassigned) Employees\n  {\n    id: 'FB-001',\n    name: 'Alex Campbell',\n    email: '<EMAIL>',\n    departmentId: null,\n    status: 'ACTIVE',\n    hireDate: new Date('2024-06-01'),\n    transferHistory: [],\n    createdAt: new Date('2024-06-01'),\n    updatedAt: new Date('2024-06-01'),\n  },\n  {\n    id: 'FB-002',\n    name: 'Morgan Parker',\n    email: '<EMAIL>',\n    departmentId: null,\n    status: 'ACTIVE',\n    hireDate: new Date('2024-06-15'),\n    transferHistory: [],\n    createdAt: new Date('2024-06-15'),\n    updatedAt: new Date('2024-06-15'),\n  },\n]\n\n// Helper function to populate departments with their employees\nexport const getDepartmentsWithEmployees = (): Department[] => {\n  return sampleDepartments.map(dept => ({\n    ...dept,\n    employees: sampleEmployees.filter(emp => emp.departmentId === dept.id)\n  }))\n}\n\n// Helper function to get free bucket employees\nexport const getFreeBucketEmployees = (): Employee[] => {\n  return sampleEmployees.filter(emp => emp.departmentId === null)\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,oBAAkC;IAC7C;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAEM,MAAM,kBAA8B;IACzC,yBAAyB;IACzB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,uBAAuB;IACvB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,qBAAqB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,wBAAwB;IACxB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,qCAAqC;IACrC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,8BAA8B;IACzC,OAAO,kBAAkB,GAAG,CAAC,CAAA,OAAQ,CAAC;YACpC,GAAG,IAAI;YACP,WAAW,gBAAgB,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK,KAAK,EAAE;QACvE,CAAC;AACH;AAGO,MAAM,yBAAyB;IACpC,OAAO,gBAAgB,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK;AAC5D", "debugId": null}}, {"offset": {"line": 2255, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/lib/hooks/G%3A/Augment%20code/lib/hooks/use-sample-data.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { useHRStore } from '@/lib/store/hr-store'\nimport { \n  sampleEmployees, \n  getDepartmentsWithEmployees, \n  getFreeBucketEmployees \n} from '@/lib/sample-data'\n\nexport function useSampleData() {\n  const { setEmployees, setDepartments } = useHRStore()\n\n  useEffect(() => {\n    // Initialize with sample data\n    const departmentsWithEmployees = getDepartmentsWithEmployees()\n    const freeBucketEmployees = getFreeBucketEmployees()\n    \n    setEmployees(sampleEmployees)\n    setDepartments(departmentsWithEmployees)\n    \n    // Note: Free bucket employees are automatically calculated in the store\n    // based on employees with departmentId === null\n    \n    console.log('Sample data loaded:', {\n      employees: sampleEmployees.length,\n      departments: departmentsWithEmployees.length,\n      freeBucket: freeBucketEmployees.length\n    })\n  }, [setEmployees, setDepartments])\n}\n"], "names": ["useEffect", "useHRStore", "sampleEmployees", "getDepartmentsWithEmployees", "getFreeBucketEmployees", "useSampleData", "$", "_c", "$i", "Symbol", "for", "setEmployees", "setDepartments", "t0", "t1", "departmentsWithEmployees", "freeBucketEmployees", "console", "log", "employees", "length", "departments", "freeBucket"], "mappings": ";;;;AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SACEC,eAAe,EACfC,2BAA2B,EAC3BC,sBAAsB,QACjB,mBAAmB;;;;;;AAEnB;;IAAA,MAAAE,CAAA,mLAAAC,IAAAA,AAAA,EAAA;IAAA,IAAAD,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAE,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAF,CAAA,CAAAE,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAJ,CAAA,CAAA,EAAA,GAAA;IAAA;IACL,MAAA,EAAAK,YAAA,EAAAC,cAAAA,EAAA,mJAAyCX;IAAY,IAAAY,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAM,cAAA,IAAAN,CAAA,CAAA,EAAA,KAAAK,YAAA,EAAA;QAE3CE,EAAA,GAAAA,CAAA;YAER,MAAAE,wBAAA,IAAiCZ,0JAAAA,AAAA,CAA4B,CAAC;YAC9D,MAAAa,mBAAA,gIAA4BZ,yBAAAA,AAAA,CAAuB,CAAC;YAEpDO,YAAY,0HAAAT,kBAAgB,CAAC;YAC7BU,cAAc,CAACG,wBAAwB,CAAC;YAKxCE,OAAA,CAAAC,GAAA,CAAY,qBAAqB,EAAA;gBAAAC,SAAA,2HAAAjB,kBAAA,CAAAkB,MAAA;gBAAAC,WAAA,EAElBN,wBAAwB,CAAAK,MAAA;gBAAAE,UAAA,EACzBN,mBAAmB,CAAAI,MAAAA;YAAA,CAChC,CAAC;QAAA;QACDN,EAAA,GAAA;YAACH,YAAY;YAAEC,cAAc;SAAA;QAACN,CAAA,CAAA,EAAA,GAAAM,cAAA;QAAAN,CAAA,CAAA,EAAA,GAAAK,YAAA;QAAAL,CAAA,CAAA,EAAA,GAAAO,EAAA;QAAAP,CAAA,CAAA,EAAA,GAAAQ,EAAA;IAAA,OAAA;QAAAD,EAAA,GAAAP,CAAA,CAAA,EAAA;QAAAQ,EAAA,GAAAR,CAAA,CAAA,EAAA;IAAA;sKAhBjCN,YAAAA,AAAA,EAAUa,EAgBT,EAAEC,EAA8B,CAAC;AAAA;GAnB7BT,cAAA;;uIACoCJ,aAAA,CAAW,CAAC", "ignoreList": [], "debugId": null}}, {"offset": {"line": 2319, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/app/%28dashboard%29/departments/G%3A/Augment%20code/app/%28dashboard%29/departments/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useSession } from \"next-auth/react\"\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { DepartmentGrid } from \"@/components/departments/department-grid\"\nimport { AddDepartmentDialog } from \"@/components/departments/add-department-dialog\"\nimport { useHRStore } from \"@/lib/store/hr-store\"\nimport { useSampleData } from \"@/lib/hooks/use-sample-data\"\nimport { calculateCapacityUtilization } from \"@/lib/utils\"\nimport { \n  Building2, \n  Plus, \n  Search,\n  Users,\n  TrendingUp,\n  AlertTriangle\n} from \"lucide-react\"\n\nexport default function DepartmentsPage() {\n  const { data: session } = useSession()\n  const { departments, employees } = useHRStore()\n  const [showAddDialog, setShowAddDialog] = useState(false)\n  const [searchQuery, setSearchQuery] = useState(\"\")\n\n  // Load sample data\n  useSampleData()\n\n  // Calculate department statistics\n  const totalDepartments = departments.length\n  const totalCapacity = departments.reduce((sum, dept) => sum + dept.capacity, 0)\n  const totalAssigned = employees.filter(emp => emp.departmentId !== null).length\n  const averageUtilization = totalCapacity > 0 \n    ? Math.round((totalAssigned / totalCapacity) * 100)\n    : 0\n\n  // Get departments with utilization data\n  const departmentsWithStats = departments.map(dept => {\n    const deptEmployees = employees.filter(emp => emp.departmentId === dept.id)\n    const utilization = calculateCapacityUtilization(deptEmployees.length, dept.capacity)\n    \n    return {\n      ...dept,\n      employees: deptEmployees,\n      utilization,\n      isOverCapacity: deptEmployees.length > dept.capacity,\n      isNearCapacity: utilization >= 80 && utilization < 100\n    }\n  })\n\n  // Filter departments based on search\n  const filteredDepartments = departmentsWithStats.filter(dept =>\n    dept.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    dept.code.toLowerCase().includes(searchQuery.toLowerCase())\n  )\n\n  // Get departments that need attention\n  const overCapacityDepts = departmentsWithStats.filter(dept => dept.isOverCapacity)\n  const nearCapacityDepts = departmentsWithStats.filter(dept => dept.isNearCapacity)\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Departments</h1>\n          <p className=\"text-muted-foreground\">\n            Manage your organization's departments and their capacity.\n          </p>\n        </div>\n        \n        <Button onClick={() => setShowAddDialog(true)}>\n          <Plus className=\"h-4 w-4 mr-2\" />\n          Add Department\n        </Button>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid gap-4 md:grid-cols-4\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total Departments</CardTitle>\n            <Building2 className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{totalDepartments}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Active departments\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total Capacity</CardTitle>\n            <Users className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{totalCapacity}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Maximum employees\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Avg. Utilization</CardTitle>\n            <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{averageUtilization}%</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Capacity utilization\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Needs Attention</CardTitle>\n            <AlertTriangle className=\"h-4 w-4 text-orange-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">\n              {overCapacityDepts.length + nearCapacityDepts.length}\n            </div>\n            <p className=\"text-xs text-muted-foreground\">\n              Over/near capacity\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Alerts for departments needing attention */}\n      {(overCapacityDepts.length > 0 || nearCapacityDepts.length > 0) && (\n        <Card className=\"border-orange-200 bg-orange-50\">\n          <CardHeader>\n            <CardTitle className=\"text-orange-800 flex items-center gap-2\">\n              <AlertTriangle className=\"h-5 w-5\" />\n              Departments Needing Attention\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            {overCapacityDepts.length > 0 && (\n              <div className=\"mb-2\">\n                <p className=\"text-sm font-medium text-red-800\">Over Capacity:</p>\n                <p className=\"text-sm text-red-700\">\n                  {overCapacityDepts.map(dept => dept.name).join(', ')}\n                </p>\n              </div>\n            )}\n            {nearCapacityDepts.length > 0 && (\n              <div>\n                <p className=\"text-sm font-medium text-orange-800\">Near Capacity (80%+):</p>\n                <p className=\"text-sm text-orange-700\">\n                  {nearCapacityDepts.map(dept => dept.name).join(', ')}\n                </p>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Search and Department Grid */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <CardTitle>Department Management</CardTitle>\n            <div className=\"relative\">\n              <Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\n              <Input\n                placeholder=\"Search departments...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-8 w-[300px]\"\n              />\n            </div>\n          </div>\n        </CardHeader>\n        \n        <CardContent>\n          <DepartmentGrid departments={filteredDepartments} />\n        </CardContent>\n      </Card>\n\n      {/* Add Department Dialog */}\n      <AddDepartmentDialog\n        open={showAddDialog}\n        onOpenChange={setShowAddDialog}\n      />\n    </div>\n  )\n}\n"], "names": ["c", "_c", "useState", "useSession", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON>", "Input", "DepartmentGrid", "AddDepartmentDialog", "useHRStore", "useSampleData", "calculateCapacityUtilization", "Building2", "Plus", "Search", "Users", "TrendingUp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DepartmentsPage", "$", "$i", "Symbol", "for", "departments", "employees", "showAddDialog", "setShowAddDialog", "searchQuery", "setSearch<PERSON>uery", "totalDepartments", "length", "totalCapacity", "reduce", "_temp", "t0", "filter", "_temp2", "totalAssigned", "t1", "Math", "round", "averageUtilization", "filteredDepartments", "t2", "t3", "t4", "t5", "t6", "dept_0", "deptEmployees", "emp_0", "emp", "departmentId", "dept", "id", "utilization", "capacity", "isOverCapacity", "isNearCapacity", "departmentsWithStats", "map", "t7", "dept_1", "name", "toLowerCase", "includes", "code", "overCapacityDepts", "_temp3", "nearCapacityDepts", "_temp4", "t8", "t9", "t10", "t11", "t12", "t13", "t14", "t15", "t16", "t17", "t18", "t19", "t20", "t21", "t22", "t23", "t24", "t25", "_temp5", "join", "_temp6", "e", "target", "value", "dept_5", "dept_4", "dept_3", "dept_2", "sum"], "mappings": ";;;;AAAY,SAAAA,CAAA,IAAAC,EAAA;AAEZ,SAASC,QAAQ,QAAQ,OAAO;AAChC,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,sBAAsB;AAC/E,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,cAAc,QAAQ,0CAA0C;AACzE,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,4BAA4B,QAAQ,aAAa;;;;;;AAC1D,SACEC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,aAAa,QACR,cAAc;;;AAnBrB,YAAY;;;;;;;;;;;;;AAqBG,SAAAC,gBAAA;;IAAA,MAAAC,CAAA,OAAArB,gLAAAA,AAAA,EAAA;IAAA,IAAAqB,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;uKACanB;IAC1B,MAAA,EAAAuB,WAAA,EAAAC,SAAAA,EAAA,mJAAmCf;IACnC,MAAA,CAAAgB,aAAA,EAAAC,gBAAA,CAAA,qKAA0C3B,WAAA,AAAAA,EAAA,KAAc,CAAC;IACzD,MAAA,CAAA4B,WAAA,EAAAC,cAAA,CAAA,oKAAsC7B,YAAAA,AAAA,EAAS,EAAE,CAAC;iKAGlDW;IAGA,MAAAmB,gBAAA,GAAyBN,WAAW,CAAAO,MAAA;IACpC,MAAAC,aAAA,GAAsBR,WAAW,CAAAS,MAAA,CAAAC,KAAA,EAAA,CAA6C,CAAC;IAAA,IAAAC,EAAA;IAAA,IAAAf,CAAA,CAAA,EAAA,KAAAK,SAAA,EAAA;QACzDU,EAAA,GAAAV,SAAS,CAAAW,MAAA,CAAAC,MAAwC,CAAC;QAAAjB,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAe,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAf,CAAA,CAAA,EAAA;IAAA;IAAxE,MAAAkB,aAAA,GAAsBH,EAAkD,CAAAJ,MAAA;IAAO,IAAAQ,EAAA;IAAA,IAAAnB,CAAA,CAAA,EAAA,KAAAkB,aAAA,IAAAlB,CAAA,CAAA,EAAA,KAAAY,aAAA,EAAA;QACpDO,EAAA,GAAAP,aAAa,GAAA,CAAI,GACxCQ,IAAA,CAAAC,KAAA,CAAYH,aAAa,GAAGN,aAAa,GAAA,GAAO,CAAC,GAAA,CAChD;QAAAZ,CAAA,CAAA,EAAA,GAAAkB,aAAA;QAAAlB,CAAA,CAAA,EAAA,GAAAY,aAAA;QAAAZ,CAAA,CAAA,EAAA,GAAAmB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAnB,CAAA,CAAA,EAAA;IAAA;IAFL,MAAAsB,kBAAA,GAA2BH,EAEtB;IAAA,IAAAI,mBAAA;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAA3B,CAAA,CAAA,EAAA,KAAAsB,kBAAA,IAAAtB,CAAA,CAAA,EAAA,KAAAI,WAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAK,SAAA,IAAAL,CAAA,CAAA,EAAA,KAAAQ,WAAA,IAAAR,CAAA,CAAA,GAAA,KAAAY,aAAA,IAAAZ,CAAA,CAAA,GAAA,KAAAU,gBAAA,EAAA;QAAA,IAAAkB,EAAA;QAAA,IAAA5B,CAAA,CAAA,GAAA,KAAAK,SAAA,EAAA;YAGwCuB,EAAA,IAAAC,MAAA;gBAC3C,MAAAC,aAAA,GAAsBzB,SAAS,CAAAW,MAAA,EAAAe,KAAA,GAAeC,KAAG,CAAAC,YAAA,KAAkBC,MAAI,CAAAC,EAAG,CAAC;gBAC3E,MAAAC,WAAA,sHAAoB5C,gCAAAA,AAAA,EAA6BsC,aAAa,CAAAnB,MAAA,EAASuB,MAAI,CAAAG,QAAS,CAAC;gBAAA,OAAA;oBAAA,GAGhFH,MAAI;oBAAA7B,SAAA,EACIyB,aAAa;oBAAAM,WAAA;oBAAAE,cAAA,EAERR,aAAa,CAAAnB,MAAA,GAAUuB,MAAI,CAAAG,QAAS;oBAAAE,cAAA,EACpCH,WAAW,IAAA,EAAM,IAAIA,WAAW,GAAA;gBAAM;YAAA;YAEzDpC,CAAA,CAAA,GAAA,GAAAK,SAAA;YAAAL,CAAA,CAAA,GAAA,GAAA4B,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAA5B,CAAA,CAAA,GAAA;QAAA;QAXD,MAAAwC,oBAAA,GAA6BpC,WAAW,CAAAqC,GAAA,CAAKb,EAW5C,CAAC;QAAA,IAAAc,EAAA;QAAA,IAAA1C,CAAA,CAAA,GAAA,KAAAQ,WAAA,EAAA;YAGsDkC,EAAA,IAAAC,MAAA,GACtDT,MAAI,CAAAU,IAAA,CAAAC,WAAA,CAAkB,CAAC,CAAAC,QAAA,CAAUtC,WAAW,CAAAqC,WAAA,CAAa,CAAC,CAAC,IAC3DX,MAAI,CAAAa,IAAA,CAAAF,WAAA,CAAkB,CAAC,CAAAC,QAAA,CAAUtC,WAAW,CAAAqC,WAAA,CAAa,CAAC,CAAC;YAAA7C,CAAA,CAAA,GAAA,GAAAQ,WAAA;YAAAR,CAAA,CAAA,GAAA,GAAA0C,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAA1C,CAAA,CAAA,GAAA;QAAA;QAF7DuB,mBAAA,GAA4BiB,oBAAoB,CAAAxB,MAAA,CAAQ0B,EAGxD,CAAC;QAGD,MAAAM,iBAAA,GAA0BR,oBAAoB,CAAAxB,MAAA,CAAAiC,MAAmC,CAAC;QAClF,MAAAC,iBAAA,GAA0BV,oBAAoB,CAAAxB,MAAA,CAAAmC,MAAmC,CAAC;QAGjE3B,EAAA,GAAA,WAAW;QAAA,IAAA4B,EAAA;QAAA,IAAApD,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;YAGtBiD,EAAA,iBAAA,6LAAA,GAKM;;kCAJJ,6LAAA,EAAkE;wBAApD,SAAmC,EAAnC,mCAAmC;kCAAC,WAAW,EAA7D,EAAkE;;;;;;kCAClE,6LAAA,CAEI;wBAFS,SAAuB,EAAvB,uBAAuB;kCAAC,0DAErC,EAFA,CAEI,CACN,EALA,GAKM;;;;;;;;;;;;YAAApD,CAAA,CAAA,GAAA,GAAAoD,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAApD,CAAA,CAAA,GAAA;QAAA;QAAA,IAAAA,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;YANRsB,EAAA,iBAAA,6LAAA,GAYM;gBAZS,SAAmC,EAAnC,mCAAmC,CAChD;;oBAAA2B,EAKK;kCAEL,4TAAC,SAAM;wBAAU,OAA4B,CAA5B,CAAA,IAAM7C,gBAAgB,CAAA,IAAK,EAAC;;0CAC3C,mYAAC,OAAI;gCAAW,SAAc,EAAd,cAAc;;;;;;4BAAG,cAEnC,EAHC,MAAM,CAIT,EAZA,GAYM;;;;;;;;;;;;;YAAAP,CAAA,CAAA,GAAA,GAAAyB,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAAzB,CAAA,CAAA,GAAA;QAAA;QAAA,IAAAqD,EAAA;QAAA,IAAArD,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;YAKFkD,EAAA,iBAAA,0TAAC,aAAU;gBAAW,SAA2D,EAA3D,2DAA2D;;kCAC/E,0TAAC,YAAS;wBAAW,SAAqB,EAArB,qBAAqB;kCAAC,iBAAiB,EAA3D,SAAS;;;;;;kCACV,iZAAC,YAAS;wBAAW,SAA+B,EAA/B,+BAA+B,GACtD,EAHC,UAAU,CAGE;;;;;;;;;;;;YAAArD,CAAA,CAAA,GAAA,GAAAqD,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAArD,CAAA,CAAA,GAAA;QAAA;QAAA,IAAAsD,GAAA;QAAA,IAAAtD,CAAA,CAAA,GAAA,KAAAU,gBAAA,EAAA;YAEX4C,GAAA,iBAAA,6LAAA,GAA4D;gBAA7C,SAAoB,EAApB,oBAAoB,CAAE5C;0BAAAA,gBAAe,CAAE,EAAtD,GAA4D;;;;;;YAAAV,CAAA,CAAA,GAAA,GAAAU,gBAAA;YAAAV,CAAA,CAAA,GAAA,GAAAsD,GAAA;QAAA,OAAA;YAAAA,GAAA,GAAAtD,CAAA,CAAA,GAAA;QAAA;QAAA,IAAAuD,GAAA;QAAA,IAAAvD,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;YAC5DoD,GAAA,iBAAA,6LAAA,CAEI;gBAFS,SAA+B,EAA/B,+BAA+B;0BAAC,kBAE7C,EAFA,CAEI;;;;;;YAAAvD,CAAA,CAAA,GAAA,GAAAuD,GAAA;QAAA,OAAA;YAAAA,GAAA,GAAAvD,CAAA,CAAA,GAAA;QAAA;QAAA,IAAAwD,GAAA;QAAA,IAAAxD,CAAA,CAAA,GAAA,KAAAsD,GAAA,EAAA;YATRE,GAAA,iBAAA,0TAAC,OAAI,CACH;;oBAAAH,EAGY;kCACZ,0TAAC,cAAW,CACV;;4BAAAC,GAA2D,CAC3D;4BAAAC,GAEG,CACL,EALC,WAAW,CAMd,EAXC,IAAI,CAWE;;;;;;;;;;;;;YAAAvD,CAAA,CAAA,GAAA,GAAAsD,GAAA;YAAAtD,CAAA,CAAA,GAAA,GAAAwD,GAAA;QAAA,OAAA;YAAAA,GAAA,GAAAxD,CAAA,CAAA,GAAA;QAAA;QAAA,IAAAyD,GAAA;QAAA,IAAAzD,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;YAGLsD,GAAA,iBAAA,0TAAC,aAAU;gBAAW,SAA2D,EAA3D,2DAA2D;;kCAC/E,0TAAC,YAAS;wBAAW,SAAqB,EAArB,qBAAqB;kCAAC,cAAc,EAAxD,SAAS;;;;;;kCACV,6LAAC,gNAAK;wBAAW,SAA+B,EAA/B,+BAA+B,GAClD,EAHC,UAAU,CAGE;;;;;;;;;;;;YAAAzD,CAAA,CAAA,GAAA,GAAAyD,GAAA;QAAA,OAAA;YAAAA,GAAA,GAAAzD,CAAA,CAAA,GAAA;QAAA;QAAA,IAAA0D,GAAA;QAAA,IAAA1D,CAAA,CAAA,GAAA,KAAAY,aAAA,EAAA;YAEX8C,GAAA,iBAAA,6LAAA,GAAyD;gBAA1C,SAAoB,EAApB,oBAAoB,CAAE9C;0BAAAA,aAAY,CAAE,EAAnD,GAAyD;;;;;;YAAAZ,CAAA,CAAA,GAAA,GAAAY,aAAA;YAAAZ,CAAA,CAAA,GAAA,GAAA0D,GAAA;QAAA,OAAA;YAAAA,GAAA,GAAA1D,CAAA,CAAA,GAAA;QAAA;QAAA,IAAA2D,GAAA;QAAA,IAAA3D,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;YACzDwD,GAAA,iBAAA,6LAAA,CAEI;gBAFS,SAA+B,EAA/B,+BAA+B;0BAAC,iBAE7C,EAFA,CAEI;;;;;;YAAA3D,CAAA,CAAA,GAAA,GAAA2D,GAAA;QAAA,OAAA;YAAAA,GAAA,GAAA3D,CAAA,CAAA,GAAA;QAAA;QAAA,IAAA4D,GAAA;QAAA,IAAA5D,CAAA,CAAA,GAAA,KAAA0D,GAAA,EAAA;YATRE,GAAA,iBAAA,0TAAC,OAAI,CACH;;oBAAAH,GAGY;kCACZ,0TAAC,cAAW,CACV;;4BAAAC,GAAwD,CACxD;4BAAAC,GAEG,CACL,EALC,WAAW,CAMd,EAXC,IAAI,CAWE;;;;;;;;;;;;;YAAA3D,CAAA,CAAA,GAAA,GAAA0D,GAAA;YAAA1D,CAAA,CAAA,GAAA,GAAA4D,GAAA;QAAA,OAAA;YAAAA,GAAA,GAAA5D,CAAA,CAAA,GAAA;QAAA;QAAA,IAAA6D,GAAA;QAAA,IAAA7D,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;YAGL0D,GAAA,iBAAA,0TAAC,aAAU;gBAAW,SAA2D,EAA3D,2DAA2D;;kCAC/E,0TAAC,YAAS;wBAAW,SAAqB,EAArB,qBAAqB;kCAAC,gBAAgB,EAA1D,SAAS;;;;;;kCACV,mZAAC,aAAU;wBAAW,SAA+B,EAA/B,+BAA+B,GACvD,EAHC,UAAU,CAGE;;;;;;;;;;;;YAAA7D,CAAA,CAAA,GAAA,GAAA6D,GAAA;QAAA,OAAA;YAAAA,GAAA,GAAA7D,CAAA,CAAA,GAAA;QAAA;QAAA,IAAA8D,GAAA;QAAA,IAAA9D,CAAA,CAAA,GAAA,KAAAsB,kBAAA,EAAA;YAEXwC,GAAA,iBAAA,6LAAA,GAA+D;gBAAhD,SAAoB,EAApB,oBAAoB,CAAExC;;oBAAAA,kBAAiB;oBAAE,CAAC,EAAzD,GAA+D;;;;;;;YAAAtB,CAAA,CAAA,GAAA,GAAAsB,kBAAA;YAAAtB,CAAA,CAAA,GAAA,GAAA8D,GAAA;QAAA,OAAA;YAAAA,GAAA,GAAA9D,CAAA,CAAA,GAAA;QAAA;QAAA,IAAA+D,GAAA;QAAA,IAAA/D,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;YAC/D4D,GAAA,iBAAA,6LAAA,CAEI;gBAFS,SAA+B,EAA/B,+BAA+B;0BAAC,oBAE7C,EAFA,CAEI;;;;;;YAAA/D,CAAA,CAAA,GAAA,GAAA+D,GAAA;QAAA,OAAA;YAAAA,GAAA,GAAA/D,CAAA,CAAA,GAAA;QAAA;QAAA,IAAAgE,GAAA;QAAA,IAAAhE,CAAA,CAAA,GAAA,KAAA8D,GAAA,EAAA;YATRE,GAAA,iBAAA,0TAAC,OAAI,CACH;;oBAAAH,GAGY;kCACZ,0TAAC,cAAW,CACV;;4BAAAC,GAA8D,CAC9D;4BAAAC,GAEG,CACL,EALC,WAAW,CAMd,EAXC,IAAI,CAWE;;;;;;;;;;;;;YAAA/D,CAAA,CAAA,GAAA,GAAA8D,GAAA;YAAA9D,CAAA,CAAA,GAAA,GAAAgE,GAAA;QAAA,OAAA;YAAAA,GAAA,GAAAhE,CAAA,CAAA,GAAA;QAAA;QAAA,IAAAiE,GAAA;QAAA,IAAAjE,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;YAGL8D,GAAA,iBAAA,6LAAC,0IAAU;gBAAW,SAA2D,EAA3D,2DAA2D;;kCAC/E,0TAAC,YAAS;wBAAW,SAAqB,EAArB,qBAAqB;kCAAC,eAAe,EAAzD,SAAS;;;;;;kCACV,yZAAC,gBAAa;wBAAW,SAAyB,EAAzB,yBAAyB,GACpD,EAHC,UAAU,CAGE;;;;;;;;;;;;YAAAjE,CAAA,CAAA,GAAA,GAAAiE,GAAA;QAAA,OAAA;YAAAA,GAAA,GAAAjE,CAAA,CAAA,GAAA;QAAA;QAGR,MAAAkE,GAAA,GAAAlB,iBAAiB,CAAArC,MAAA,GAAUuC,iBAAiB,CAAAvC,MAAO;QAAA,IAAAwD,GAAA;QAAA,IAAAnE,CAAA,CAAA,GAAA,KAAAkE,GAAA,EAAA;YADtDC,GAAA,iBAAA,6LAAA,GAEM;gBAFS,SAAoB,EAApB,oBAAoB,CAChC;0BAAAD,GAAkD,CACrD,EAFA,GAEM;;;;;;YAAAlE,CAAA,CAAA,GAAA,GAAAkE,GAAA;YAAAlE,CAAA,CAAA,GAAA,GAAAmE,GAAA;QAAA,OAAA;YAAAA,GAAA,GAAAnE,CAAA,CAAA,GAAA;QAAA;QAAA,IAAAoE,GAAA;QAAA,IAAApE,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;YACNiE,GAAA,iBAAA,6LAAA,CAEI;gBAFS,SAA+B,EAA/B,+BAA+B;0BAAC,kBAE7C,EAFA,CAEI;;;;;;YAAApE,CAAA,CAAA,GAAA,GAAAoE,GAAA;QAAA,OAAA;YAAAA,GAAA,GAAApE,CAAA,CAAA,GAAA;QAAA;QAAA,IAAAqE,GAAA;QAAA,IAAArE,CAAA,CAAA,GAAA,KAAAmE,GAAA,EAAA;YAXRE,GAAA,iBAAA,6LAAC,oIAAI,CACH;;oBAAAJ,GAGY;kCACZ,0TAAC,cAAW,CACV;;4BAAAE,GAEK,CACL;4BAAAC,GAEG,CACL,EAPC,WAAW,CAQd,EAbC,IAAI,CAaE;;;;;;;;;;;;;YAAApE,CAAA,CAAA,GAAA,GAAAmE,GAAA;YAAAnE,CAAA,CAAA,GAAA,GAAAqE,GAAA;QAAA,OAAA;YAAAA,GAAA,GAAArE,CAAA,CAAA,GAAA;QAAA;QAAA,IAAAA,CAAA,CAAA,GAAA,KAAAwD,GAAA,IAAAxD,CAAA,CAAA,GAAA,KAAA4D,GAAA,IAAA5D,CAAA,CAAA,GAAA,KAAAgE,GAAA,IAAAhE,CAAA,CAAA,GAAA,KAAAqE,GAAA,EAAA;YArDT3C,EAAA,iBAAA,6LAAA,GAsDM;gBAtDS,SAA2B,EAA3B,2BAA2B,CACxC;;oBAAA8B,GAWM,CAEN;oBAAAI,GAWM,CAEN;oBAAAI,GAWM,CAEN;oBAAAK,GAaM,CACR,EAtDA,GAsDM;;;;;;;YAAArE,CAAA,CAAA,GAAA,GAAAwD,GAAA;YAAAxD,CAAA,CAAA,GAAA,GAAA4D,GAAA;YAAA5D,CAAA,CAAA,GAAA,GAAAgE,GAAA;YAAAhE,CAAA,CAAA,GAAA,GAAAqE,GAAA;YAAArE,CAAA,CAAA,GAAA,GAAA0B,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAA1B,CAAA,CAAA,GAAA;QAAA;QAGL2B,EAAA,GAAA,CAACqB,iBAAiB,CAAArC,MAAA,GAAA,CAAW,IAAIuC,iBAAiB,CAAAvC,MAAA,GAAA,CAAW,mBAC5D,6LAAC,oIAAI;YAAW,SAAgC,EAAhC,gCAAgC;;8BAC9C,0TAAC,aAAU;4CACT,0TAAC,YAAS;wBAAW,SAAyC,EAAzC,yCAAyC;;0CAC5D,yZAAC,gBAAa;gCAAW,SAAS,EAAT,SAAS;;;;;;4BAAG,6BAEvC,EAHC,SAAS,CAIZ,EALC,UAAU;;;;;;;;;;;;8BAMX,0TAAC,cAAW,CACT;;wBAAAqC,iBAAiB,CAAArC,MAAA,GAAA,CAAW,kBAC3B,6LAAA,GAKM;4BALS,SAAM,EAAN,MAAM;;8CACnB,6LAAA,CAAkE;oCAArD,SAAkC,EAAlC,kCAAkC;8CAAC,cAAc,EAA9D,CAAkE;;;;;;8CAClE,6LAAA,CAEI;oCAFS,SAAsB,EAAtB,sBAAsB,CAChC;8CAAAqC,iBAAiB,CAAAP,GAAA,CAAA6B,MAAsB,CAAC,CAAAC,IAAA,CAAM,IAAI,EACrD,EAFA,CAEI,CACN,EALA,GAKM,CACR,CACC;;;;;;;;;;;;wBAAArB,iBAAiB,CAAAvC,MAAA,GAAA,CAAW,kBAC3B,6LAAA,GAKM;;8CAJJ,6LAAA,CAA4E;oCAA/D,SAAqC,EAArC,qCAAqC;8CAAC,qBAAqB,EAAxE,CAA4E;;;;;;8CAC5E,6LAAA,CAEI;oCAFS,SAAyB,EAAzB,yBAAyB,CACnC;8CAAAuC,iBAAiB,CAAAT,GAAA,CAAA+B,MAAsB,CAAC,CAAAD,IAAA,CAAM,IAAI,EACrD,EAFA,CAEI,CACN,EALA,GAKM,CACR,CACF,EAjBC,WAAW,CAkBd,EAzBC,IAAI,CA0BN;;;;;;;;;;;;;;;;;;;;;;;;QAAAvE,CAAA,CAAA,EAAA,GAAAsB,kBAAA;QAAAtB,CAAA,CAAA,EAAA,GAAAI,WAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAQ,WAAA;QAAAR,CAAA,CAAA,GAAA,GAAAY,aAAA;QAAAZ,CAAA,CAAA,GAAA,GAAAU,gBAAA;QAAAV,CAAA,CAAA,GAAA,GAAAuB,mBAAA;QAAAvB,CAAA,CAAA,GAAA,GAAAwB,EAAA;QAAAxB,CAAA,CAAA,GAAA,GAAAyB,EAAA;QAAAzB,CAAA,CAAA,GAAA,GAAA0B,EAAA;QAAA1B,CAAA,CAAA,GAAA,GAAA2B,EAAA;IAAA,OAAA;QAAAJ,mBAAA,GAAAvB,CAAA,CAAA,GAAA;QAAAwB,EAAA,GAAAxB,CAAA,CAAA,GAAA;QAAAyB,EAAA,GAAAzB,CAAA,CAAA,GAAA;QAAA0B,EAAA,GAAA1B,CAAA,CAAA,GAAA;QAAA2B,EAAA,GAAA3B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA4B,EAAA;IAAA,IAAA5B,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAMKyB,EAAA,iBAAA,0TAAC,YAAS;sBAAC,qBAAqB,EAA/B,SAAS,CAAkC;;;;;;QAAA5B,CAAA,CAAA,GAAA,GAAA4B,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAA5B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA0C,EAAA;IAAA,IAAA1C,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAE1CuC,EAAA,iBAAA,uYAAC,SAAM;YAAW,SAAuD,EAAvD,uDAAuD,GAAG;;;;;;QAAA1C,CAAA,CAAA,GAAA,GAAA0C,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAA1C,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAoD,EAAA;IAAA,IAAApD,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAIhEiD,EAAA,IAAAqB,CAAA,GAAOhE,cAAc,CAACgE,CAAC,CAAAC,MAAA,CAAAC,KAAa,CAAC;QAAA3E,CAAA,CAAA,GAAA,GAAAoD,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAApD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAqD,EAAA;IAAA,IAAArD,CAAA,CAAA,GAAA,KAAAQ,WAAA,EAAA;QARvD6C,EAAA,iBAAA,0TAAC,aAAU;oCACT,6LAAA,GAWM;gBAXS,SAAmC,EAAnC,mCAAmC,CAChD;;oBAAAzB,EAA2C;kCAC3C,6LAAA,GAQM;wBARS,SAAU,EAAV,UAAU,CACvB;;4BAAAc,EAA2E;0CAC3E,2TAAC,QAAK;gCACQ,WAAuB,EAAvB,uBAAuB;gCAC5BlC,KAAW,CAAXA,CAAAA,WAAU,CAAC;gCACR,QAAqC,CAArC,CAAA4C,EAAoC,CAAC;gCACrC,SAAgB,EAAhB,gBAAgB,GAE9B,EARA,GAQM,CACR,EAXA,GAWM,CACR,EAbC,UAAU,CAaE;;;;;;;;;;;;;;;;;;;;;;;QAAApD,CAAA,CAAA,GAAA,GAAAQ,WAAA;QAAAR,CAAA,CAAA,GAAA,GAAAqD,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAArD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAsD,GAAA;IAAA,IAAAtD,CAAA,CAAA,GAAA,KAAAuB,mBAAA,EAAA;QAEb+B,GAAA,iBAAA,0TAAC,cAAW;sBACV,+VAAC,iBAAc;gBAAc/B,WAAmB,CAAnBA,CAAAA,mBAAkB,CAAC,GAClD,EAFC,WAAW,CAEE;;;;;;;;;;;QAAAvB,CAAA,CAAA,GAAA,GAAAuB,mBAAA;QAAAvB,CAAA,CAAA,GAAA,GAAAsD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAtD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAuD,GAAA;IAAA,IAAAvD,CAAA,CAAA,GAAA,KAAAsD,GAAA,IAAAtD,CAAA,CAAA,GAAA,KAAAqD,EAAA,EAAA;QAlBhBE,GAAA,iBAAA,0TAAC,OAAI,CACH;;gBAAAF,EAaY,CAEZ;gBAAAC,GAEa,CACf,EAnBC,IAAI,CAmBE;;;;;;;QAAAtD,CAAA,CAAA,GAAA,GAAAsD,GAAA;QAAAtD,CAAA,CAAA,GAAA,GAAAqD,EAAA;QAAArD,CAAA,CAAA,GAAA,GAAAuD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAvD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAwD,GAAA;IAAA,IAAAxD,CAAA,CAAA,GAAA,KAAAM,aAAA,EAAA;QAGPkD,GAAA,iBAAA,0VAAC,sBAAmB;YACZlD,IAAa,CAAbA,CAAAA,aAAY,CAAC;YACLC,YAAgB,CAAhBA,CAAAA,gBAAe,CAAC,GAC9B;;;;;;QAAAP,CAAA,CAAA,GAAA,GAAAM,aAAA;QAAAN,CAAA,CAAA,GAAA,GAAAwD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAxD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAyD,GAAA;IAAA,IAAAzD,CAAA,CAAA,GAAA,KAAAuD,GAAA,IAAAvD,CAAA,CAAA,GAAA,KAAAwD,GAAA,IAAAxD,CAAA,CAAA,GAAA,KAAAwB,EAAA,IAAAxB,CAAA,CAAA,GAAA,KAAAyB,EAAA,IAAAzB,CAAA,CAAA,GAAA,KAAA0B,EAAA,IAAA1B,CAAA,CAAA,GAAA,KAAA2B,EAAA,EAAA;QAjIJ8B,GAAA,iBAAA,6LAAA,GAkIM;YAlIS,SAAW,CAAX,CAAAjC,EAAU,CAAC,CAExB;;gBAAAC,EAYK,CAGL;gBAAAC,EAsDK,CAGJ;gBAAAC,EA2BD,CAGA;gBAAA4B,GAmBM,CAGN;gBAAAC,GAGC,CACH,EAlIA,GAkIM;;;;;;;QAAAxD,CAAA,CAAA,GAAA,GAAAuD,GAAA;QAAAvD,CAAA,CAAA,GAAA,GAAAwD,GAAA;QAAAxD,CAAA,CAAA,GAAA,GAAAwB,EAAA;QAAAxB,CAAA,CAAA,GAAA,GAAAyB,EAAA;QAAAzB,CAAA,CAAA,GAAA,GAAA0B,EAAA;QAAA1B,CAAA,CAAA,GAAA,GAAA2B,EAAA;QAAA3B,CAAA,CAAA,GAAA,GAAAyD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAzD,CAAA,CAAA,GAAA;IAAA;IAAA,OAlINyD,GAkIM;AAAA;;;0JA3KkB5E,aAAA,CAAW,CAAC;uIACHS,aAAA,CAAW,CAAC;iJAK/CC,gBAAA,CAAc,CAAC;;;;AAPF,SAAAiF,OAAAI,MAAA;IAAA,OAyIkC1C,MAAI,CAAAU,IAAA;AAAA;AAzItC,SAAA0B,OAAAO,MAAA;IAAA,OAiIkC3C,MAAI,CAAAU,IAAA;AAAA;AAjItC,SAAAO,OAAA2B,MAAA;IAAA,OAuCiD5C,MAAI,CAAAK,cAAA;AAAA;AAvCrD,SAAAU,OAAA8B,MAAA;IAAA,OAsCiD7C,MAAI,CAAAI,cAAA;AAAA;AAtCrD,SAAArB,OAAAe,GAAA;IAAA,OAYiCA,GAAG,CAAAC,YAAA,KAAA,IAAsB;AAAA;AAZ1D,SAAAnB,MAAAkE,GAAA,EAAA9C,IAAA;IAAA,OAW2C8C,GAAG,GAAG9C,IAAI,CAAAG,QAAS;AAAA", "ignoreList": [], "debugId": null}}]}