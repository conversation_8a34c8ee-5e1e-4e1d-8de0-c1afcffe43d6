"use client"

import { useState } from "react"
import { Employee, Department } from "@/lib/types"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { formatDate } from "@/lib/utils"
import { 
  Download, 
  FileText,
  Filter
} from "lucide-react"

interface CSVExportSectionProps {
  employees: Employee[]
  departments: Department[]
}

export function CSVExportSection({ employees, departments }: CSVExportSectionProps) {
  const [filters, setFilters] = useState({
    department: 'all',
    status: 'all',
    includeTransferHistory: false
  })
  const [selectedColumns, setSelectedColumns] = useState({
    id: true,
    name: true,
    email: true,
    department: true,
    status: true,
    hireDate: true,
    createdAt: false,
    updatedAt: false
  })

  const availableColumns = [
    { key: 'id', label: 'Employee ID', required: true },
    { key: 'name', label: 'Name', required: true },
    { key: 'email', label: 'Email', required: true },
    { key: 'department', label: 'Department', required: false },
    { key: 'status', label: 'Status', required: false },
    { key: 'hireDate', label: 'Hire Date', required: false },
    { key: 'createdAt', label: 'Created Date', required: false },
    { key: 'updatedAt', label: 'Updated Date', required: false }
  ]

  const getFilteredEmployees = () => {
    return employees.filter(employee => {
      // Department filter
      if (filters.department !== 'all') {
        if (filters.department === 'unassigned') {
          if (employee.departmentId !== null) return false
        } else {
          if (employee.departmentId !== filters.department) return false
        }
      }

      // Status filter
      if (filters.status !== 'all' && employee.status !== filters.status) {
        return false
      }

      return true
    })
  }

  const getDepartmentName = (departmentId: string | null) => {
    if (!departmentId) return 'Unassigned'
    const department = departments.find(dept => dept.id === departmentId)
    return department?.name || 'Unknown'
  }

  const generateCSV = () => {
    const filteredEmployees = getFilteredEmployees()
    
    if (filteredEmployees.length === 0) {
      alert('No employees match the current filters')
      return
    }

    // Generate headers
    const headers: string[] = []
    if (selectedColumns.id) headers.push('Employee ID')
    if (selectedColumns.name) headers.push('Name')
    if (selectedColumns.email) headers.push('Email')
    if (selectedColumns.department) headers.push('Department')
    if (selectedColumns.status) headers.push('Status')
    if (selectedColumns.hireDate) headers.push('Hire Date')
    if (selectedColumns.createdAt) headers.push('Created Date')
    if (selectedColumns.updatedAt) headers.push('Updated Date')

    // Generate rows
    const rows = filteredEmployees.map(employee => {
      const row: string[] = []
      if (selectedColumns.id) row.push(employee.id)
      if (selectedColumns.name) row.push(employee.name)
      if (selectedColumns.email) row.push(employee.email)
      if (selectedColumns.department) row.push(getDepartmentName(employee.departmentId))
      if (selectedColumns.status) row.push(employee.status)
      if (selectedColumns.hireDate) row.push(formatDate(employee.hireDate))
      if (selectedColumns.createdAt) row.push(formatDate(employee.createdAt))
      if (selectedColumns.updatedAt) row.push(formatDate(employee.updatedAt))
      return row
    })

    // Create CSV content
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n')

    // Download file
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `employees-export-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  const handleColumnToggle = (column: string, checked: boolean) => {
    setSelectedColumns(prev => ({
      ...prev,
      [column]: checked
    }))
  }

  const filteredEmployees = getFilteredEmployees()

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Export Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label>Department</Label>
              <Select
                value={filters.department}
                onValueChange={(value) => setFilters(prev => ({ ...prev, department: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  <SelectItem value="unassigned">Unassigned</SelectItem>
                  {departments.map((dept) => (
                    <SelectItem key={dept.id} value={dept.id}>
                      {dept.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Status</Label>
              <Select
                value={filters.status}
                onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="TRANSFERRED">Transferred</SelectItem>
                  <SelectItem value="PENDING_REMOVAL">Pending Removal</SelectItem>
                  <SelectItem value="ARCHIVED">Archived</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Column Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Columns to Export</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-2">
            {availableColumns.map((column) => (
              <div key={column.key} className="flex items-center space-x-2">
                <Checkbox
                  id={column.key}
                  checked={selectedColumns[column.key as keyof typeof selectedColumns]}
                  onCheckedChange={(checked) => handleColumnToggle(column.key, checked as boolean)}
                  disabled={column.required}
                />
                <Label 
                  htmlFor={column.key}
                  className={column.required ? 'text-muted-foreground' : ''}
                >
                  {column.label}
                  {column.required && ' (Required)'}
                </Label>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Export Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Export Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-2 md:grid-cols-3">
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold">{filteredEmployees.length}</div>
                <div className="text-sm text-muted-foreground">Employees to export</div>
              </div>
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold">
                  {Object.values(selectedColumns).filter(Boolean).length}
                </div>
                <div className="text-sm text-muted-foreground">Columns selected</div>
              </div>
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold">CSV</div>
                <div className="text-sm text-muted-foreground">Export format</div>
              </div>
            </div>

            <Button 
              onClick={generateCSV} 
              disabled={filteredEmployees.length === 0}
              className="w-full"
            >
              <Download className="h-4 w-4 mr-2" />
              Export {filteredEmployees.length} Employees to CSV
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
