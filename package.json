{"name": "hr-synergy", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^15.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "@next/font": "^15.0.0", "next-auth": "^4.24.0", "zustand": "^4.4.0", "localforage": "^1.10.0", "yjs": "^13.6.0", "y-websocket": "^1.5.0", "socket.io-client": "^4.7.0", "prisma": "^5.6.0", "@prisma/client": "^5.6.0", "aws-sdk": "^2.1500.0", "papaparse": "^5.4.0", "react-virtuoso": "^4.6.0", "sonner": "^1.2.0", "lucide-react": "^0.294.0", "tailwindcss": "^3.3.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/papaparse": "^5.3.0", "eslint": "^8.0.0", "eslint-config-next": "^15.0.0", "autoprefixer": "^10.0.0", "postcss": "^8.0.0"}}