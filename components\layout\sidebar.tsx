"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useSession } from "next-auth/react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  LayoutDashboard,
  Users,
  Building2,
  Upload,
  Settings,
  Archive,
  UserCheck
} from "lucide-react"

const navigation = [
  {
    name: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
    roles: ["ADMIN", "HR_MANAGER", "VIEWER"]
  },
  {
    name: "Employees",
    href: "/employees",
    icon: Users,
    roles: ["ADMIN", "HR_MANAGER", "VIEWER"]
  },
  {
    name: "Departments",
    href: "/departments",
    icon: Building2,
    roles: ["ADMIN", "HR_MANAGER", "VIEWER"]
  },
  {
    name: "Bulk Operations",
    href: "/bulk",
    icon: Upload,
    roles: ["ADMIN", "HR_MANAGER"]
  },
  {
    name: "Free Bucket",
    href: "/free-bucket",
    icon: Archive,
    roles: ["ADMIN", "HR_MANAGER"]
  },
  {
    name: "User Management",
    href: "/admin/users",
    icon: UserCheck,
    roles: ["ADMIN"]
  },
  {
    name: "Settings",
    href: "/settings",
    icon: Settings,
    roles: ["ADMIN", "HR_MANAGER"]
  }
]

export function Sidebar() {
  const pathname = usePathname()
  const { data: session } = useSession()
  const userRole = session?.user?.role

  const filteredNavigation = navigation.filter(item =>
    item.roles.includes(userRole as string)
  )

  return (
    <div className="flex h-full w-64 flex-col bg-card border-r">
      <div className="flex h-16 items-center px-6 border-b">
        <h1 className="text-xl font-bold">HR Synergy</h1>
      </div>
      
      <nav className="flex-1 space-y-1 p-4">
        {filteredNavigation.map((item) => {
          const isActive = pathname === item.href || pathname.startsWith(item.href + "/")
          
          return (
            <Link key={item.name} href={item.href}>
              <Button
                variant={isActive ? "secondary" : "ghost"}
                className={cn(
                  "w-full justify-start",
                  isActive && "bg-secondary"
                )}
              >
                <item.icon className="mr-2 h-4 w-4" />
                {item.name}
              </Button>
            </Link>
          )
        })}
      </nav>
    </div>
  )
}