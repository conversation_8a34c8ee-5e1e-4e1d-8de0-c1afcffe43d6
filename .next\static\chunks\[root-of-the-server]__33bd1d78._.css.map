{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.module.css"], "sourcesContent": ["/* cyrillic-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Inter Fallback';\n    src: local(\"Arial\");\n    ascent-override: 90.44%;\ndescent-override: 22.52%;\nline-gap-override: 0.00%;\nsize-adjust: 107.12%;\n\n}\n.className {\n    font-family: 'Inter', 'Inter Fallback';\n    font-style: normal;\n\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/app/globals.css"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n@layer base {\n  :root {\n    /* Modern Enterprise Color Palette */\n    --background: 240 10% 98%;\n    --foreground: 240 10% 3.9%;\n    --card: 0 0% 100%;\n    --card-foreground: 240 10% 3.9%;\n    --popover: 0 0% 100%;\n    --popover-foreground: 240 10% 3.9%;\n\n    /* Primary: Professional Blue */\n    --primary: 217 91% 60%;\n    --primary-foreground: 0 0% 98%;\n    --primary-hover: 217 91% 55%;\n\n    /* Secondary: Sophisticated Purple */\n    --secondary: 262 83% 58%;\n    --secondary-foreground: 0 0% 98%;\n\n    /* Accent: Modern Teal */\n    --accent: 173 80% 40%;\n    --accent-foreground: 0 0% 98%;\n\n    /* Neutral Grays */\n    --muted: 240 4.8% 95.9%;\n    --muted-foreground: 240 3.8% 46.1%;\n    --border: 240 5.9% 90%;\n    --input: 240 5.9% 90%;\n    --ring: 217 91% 60%;\n\n    /* Status Colors */\n    --destructive: 0 84.2% 60.2%;\n    --destructive-foreground: 0 0% 98%;\n    --success: 142 76% 36%;\n    --success-foreground: 0 0% 98%;\n    --warning: 38 92% 50%;\n    --warning-foreground: 0 0% 98%;\n\n    /* Enhanced Radius */\n    --radius: 0.75rem;\n\n    /* Chart Colors - Modern Palette */\n    --chart-1: 217 91% 60%;\n    --chart-2: 262 83% 58%;\n    --chart-3: 173 80% 40%;\n    --chart-4: 38 92% 50%;\n    --chart-5: 142 76% 36%;\n  }\n\n  .dark {\n    --background: 240 10% 3.9%;\n    --foreground: 0 0% 98%;\n    --card: 240 10% 3.9%;\n    --card-foreground: 0 0% 98%;\n    --popover: 240 10% 3.9%;\n    --popover-foreground: 0 0% 98%;\n\n    --primary: 217 91% 60%;\n    --primary-foreground: 240 10% 3.9%;\n    --primary-hover: 217 91% 65%;\n\n    --secondary: 262 83% 58%;\n    --secondary-foreground: 240 10% 3.9%;\n\n    --accent: 173 80% 40%;\n    --accent-foreground: 240 10% 3.9%;\n\n    --muted: 240 3.7% 15.9%;\n    --muted-foreground: 240 5% 64.9%;\n    --border: 240 3.7% 15.9%;\n    --input: 240 3.7% 15.9%;\n    --ring: 217 91% 60%;\n\n    --destructive: 0 62.8% 30.6%;\n    --destructive-foreground: 0 0% 98%;\n    --success: 142 76% 36%;\n    --success-foreground: 0 0% 98%;\n    --warning: 38 92% 50%;\n    --warning-foreground: 240 10% 3.9%;\n\n    --chart-1: 217 91% 60%;\n    --chart-2: 262 83% 58%;\n    --chart-3: 173 80% 40%;\n    --chart-4: 38 92% 50%;\n    --chart-5: 142 76% 36%;\n  }\n}\n\n@layer base {\n  * {\n    @apply border-border;\n  }\n\n  body {\n    @apply bg-background text-foreground font-sans antialiased;\n    font-feature-settings: \"rlig\" 1, \"calt\" 1;\n  }\n\n  /* Enhanced Typography */\n  h1, h2, h3, h4, h5, h6 {\n    @apply font-semibold tracking-tight;\n  }\n\n  h1 {\n    @apply text-4xl lg:text-5xl;\n  }\n\n  h2 {\n    @apply text-3xl lg:text-4xl;\n  }\n\n  h3 {\n    @apply text-2xl lg:text-3xl;\n  }\n\n  h4 {\n    @apply text-xl lg:text-2xl;\n  }\n}\n\n@layer components {\n  /* Modern Gradient Backgrounds */\n  .gradient-primary {\n    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 100%);\n  }\n\n  .gradient-card {\n    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);\n  }\n\n  .gradient-accent {\n    background: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(var(--primary)) 100%);\n  }\n\n  /* Glass Morphism Effect */\n  .glass {\n    background: rgba(255, 255, 255, 0.1);\n    backdrop-filter: blur(10px);\n    border: 1px solid rgba(255, 255, 255, 0.2);\n  }\n\n  /* Enhanced Shadows */\n  .shadow-soft {\n    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\n  }\n\n  .shadow-medium {\n    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  }\n\n  .shadow-strong {\n    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 2px 10px -2px rgba(0, 0, 0, 0.05);\n  }\n\n  /* Hover Animations */\n  .hover-lift {\n    transition: all 0.2s ease-in-out;\n  }\n\n  .hover-lift:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 30px -5px rgba(0, 0, 0, 0.15);\n  }\n\n  /* Status Indicators */\n  .status-success {\n    @apply bg-green-50 text-green-700 border-green-200;\n  }\n\n  .status-warning {\n    @apply bg-yellow-50 text-yellow-700 border-yellow-200;\n  }\n\n  .status-error {\n    @apply bg-red-50 text-red-700 border-red-200;\n  }\n\n  .status-info {\n    @apply bg-blue-50 text-blue-700 border-blue-200;\n  }\n\n  /* Animations */\n  @keyframes fadeIn {\n    from { opacity: 0; transform: translateY(10px); }\n    to { opacity: 1; transform: translateY(0); }\n  }\n\n  @keyframes slideIn {\n    from { opacity: 0; transform: translateX(-20px); }\n    to { opacity: 1; transform: translateX(0); }\n  }\n\n  @keyframes pulse-soft {\n    0%, 100% { opacity: 1; }\n    50% { opacity: 0.8; }\n  }\n\n  .animate-fade-in {\n    animation: fadeIn 0.5s ease-out;\n  }\n\n  .animate-slide-in {\n    animation: slideIn 0.3s ease-out;\n  }\n\n  .animate-pulse-soft {\n    animation: pulse-soft 2s infinite;\n  }\n\n  /* Responsive Grid Background */\n  .bg-grid-slate-100 {\n    background-image:\n      linear-gradient(to right, rgb(241 245 249 / 0.5) 1px, transparent 1px),\n      linear-gradient(to bottom, rgb(241 245 249 / 0.5) 1px, transparent 1px);\n    background-size: 20px 20px;\n  }\n\n  /* Smooth Scrolling */\n  html {\n    scroll-behavior: smooth;\n  }\n\n  /* Focus Styles */\n  .focus-ring {\n    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background;\n  }\n}"], "names": [], "mappings": "AAAA;;AACA;;AACA;;AAEA;EACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAgDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAyCE;;EAIA;;EACsB;;;;EAKtB;;EAIA;;EAIA;;EAIA;;EAIA;;;AAIJ;EAEE;;;;EAIA;;;;EAIA;;;;EAKA;;;;;;;EAOA;;;;EAIA;;;;EAIA;;;;EAKA;;;;EAIA;;;;;EAOE;;EAIA;;EAIA;;EAIA;;EAIF;;;;;;;;;;;;EAKA;;;;;;;;;;;;EAKA;;;;;;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAKA;;;;;EAQA;;;;EAME", "debugId": null}}]}