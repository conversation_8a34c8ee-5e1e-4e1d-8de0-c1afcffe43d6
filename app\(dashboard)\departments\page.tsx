"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DepartmentGrid } from "@/components/departments/department-grid"
import { AddDepartmentDialog } from "@/components/departments/add-department-dialog"
import { useHRStore } from "@/lib/store/hr-store"
import { useSampleData } from "@/lib/hooks/use-sample-data"
import { calculateCapacityUtilization } from "@/lib/utils"
import { 
  Building2, 
  Plus, 
  Search,
  Users,
  TrendingUp,
  AlertTriangle
} from "lucide-react"

export default function DepartmentsPage() {
  const { data: session } = useSession()
  const { departments, employees } = useHRStore()
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  // Load sample data
  useSampleData()

  // Calculate department statistics
  const totalDepartments = departments.length
  const totalCapacity = departments.reduce((sum, dept) => sum + dept.capacity, 0)
  const totalAssigned = employees.filter(emp => emp.departmentId !== null).length
  const averageUtilization = totalCapacity > 0 
    ? Math.round((totalAssigned / totalCapacity) * 100)
    : 0

  // Get departments with utilization data
  const departmentsWithStats = departments.map(dept => {
    const deptEmployees = employees.filter(emp => emp.departmentId === dept.id)
    const utilization = calculateCapacityUtilization(deptEmployees.length, dept.capacity)
    
    return {
      ...dept,
      employees: deptEmployees,
      utilization,
      isOverCapacity: deptEmployees.length > dept.capacity,
      isNearCapacity: utilization >= 80 && utilization < 100
    }
  })

  // Filter departments based on search
  const filteredDepartments = departmentsWithStats.filter(dept =>
    dept.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    dept.code.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Get departments that need attention
  const overCapacityDepts = departmentsWithStats.filter(dept => dept.isOverCapacity)
  const nearCapacityDepts = departmentsWithStats.filter(dept => dept.isNearCapacity)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Departments</h1>
          <p className="text-muted-foreground">
            Manage your organization's departments and their capacity.
          </p>
        </div>
        
        <Button onClick={() => setShowAddDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Department
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Departments</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalDepartments}</div>
            <p className="text-xs text-muted-foreground">
              Active departments
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Capacity</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCapacity}</div>
            <p className="text-xs text-muted-foreground">
              Maximum employees
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Utilization</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageUtilization}%</div>
            <p className="text-xs text-muted-foreground">
              Capacity utilization
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Needs Attention</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {overCapacityDepts.length + nearCapacityDepts.length}
            </div>
            <p className="text-xs text-muted-foreground">
              Over/near capacity
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Alerts for departments needing attention */}
      {(overCapacityDepts.length > 0 || nearCapacityDepts.length > 0) && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="text-orange-800 flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Departments Needing Attention
            </CardTitle>
          </CardHeader>
          <CardContent>
            {overCapacityDepts.length > 0 && (
              <div className="mb-2">
                <p className="text-sm font-medium text-red-800">Over Capacity:</p>
                <p className="text-sm text-red-700">
                  {overCapacityDepts.map(dept => dept.name).join(', ')}
                </p>
              </div>
            )}
            {nearCapacityDepts.length > 0 && (
              <div>
                <p className="text-sm font-medium text-orange-800">Near Capacity (80%+):</p>
                <p className="text-sm text-orange-700">
                  {nearCapacityDepts.map(dept => dept.name).join(', ')}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Search and Department Grid */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Department Management</CardTitle>
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search departments..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8 w-[300px]"
              />
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <DepartmentGrid departments={filteredDepartments} />
        </CardContent>
      </Card>

      {/* Add Department Dialog */}
      <AddDepartmentDialog
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
      />
    </div>
  )
}
