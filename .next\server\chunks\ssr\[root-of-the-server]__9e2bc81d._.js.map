{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/providers.tsx"], "sourcesContent": ["\"use client\"\n\nimport { SessionProvider } from \"next-auth/react\"\nimport { Toaster } from \"sonner\"\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  return (\n    <SessionProvider>\n      {children}\n      <Toaster position=\"top-right\" />\n    </SessionProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKO,SAAS,UAAU,EAAE,QAAQ,EAAiC;IACnE,qBACE,8OAAC,8IAAA,CAAA,kBAAe;;YACb;0BACD,8OAAC,wIAAA,CAAA,UAAO;gBAAC,UAAS;;;;;;;;;;;;AAGxB", "debugId": null}}]}