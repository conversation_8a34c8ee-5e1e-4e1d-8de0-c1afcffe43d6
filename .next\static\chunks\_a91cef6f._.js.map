{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/progress.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAG/B,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,AAAC,eAAiC,OAAnB,MAAM,CAAC,SAAS,CAAC,GAAE;YAAI;;;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/dashboard/G%3A/Augment%20code/components/dashboard/department-card.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from \"@/components/ui/card\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { Button } from \"@/components/ui/button\"\nimport { Department } from \"@/lib/types\"\nimport { calculateCapacityUtilization } from \"@/lib/utils\"\nimport { Building2, Users, Plus } from \"lucide-react\"\n\ninterface DepartmentCardProps {\n  department: Department\n  onAddEmployee?: () => void\n  onViewDetails?: () => void\n}\n\nexport function DepartmentCard({ \n  department, \n  onAddEmployee, \n  onViewDetails \n}: DepartmentCardProps) {\n  const employeeCount = department.employees?.length || 0\n  const utilization = calculateCapacityUtilization(employeeCount, department.capacity)\n  \n  const getUtilizationColor = (percentage: number) => {\n    if (percentage >= 90) return \"bg-red-500\"\n    if (percentage >= 75) return \"bg-yellow-500\"\n    return \"bg-green-500\"\n  }\n\n  return (\n    <Card className=\"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 bg-gradient-to-br from-white to-slate-50/50\">\n      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-4\">\n        <CardTitle className=\"text-lg font-semibold text-foreground\">\n          {department.name}\n        </CardTitle>\n        <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg\">\n          <Building2 className=\"h-5 w-5 text-white\" />\n        </div>\n      </CardHeader>\n      \n      <CardContent>\n        <div className=\"space-y-5\">\n          <div className=\"flex items-center justify-between p-3 bg-gradient-to-r from-slate-50 to-white rounded-lg border border-border/50\">\n            <span className=\"text-sm font-medium text-muted-foreground\">Department Code:</span>\n            <span className=\"font-mono text-sm font-semibold bg-primary/10 text-primary px-2 py-1 rounded\">{department.code}</span>\n          </div>\n\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"flex items-center gap-2 text-sm font-medium text-muted-foreground\">\n                <Users className=\"h-4 w-4\" />\n                Employee Count\n              </span>\n              <span className=\"font-semibold text-lg\">\n                {employeeCount} / {department.capacity}\n              </span>\n            </div>\n\n            <div className=\"space-y-2\">\n              <div className=\"relative\">\n                <Progress\n                  value={utilization}\n                  className=\"h-3 bg-slate-200\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-full\"></div>\n              </div>\n\n              <div className=\"flex justify-between text-sm\">\n                <span className=\"font-medium text-muted-foreground\">Capacity Utilization</span>\n                <span className={`font-bold ${\n                  utilization >= 90 ? 'text-red-600' :\n                  utilization >= 75 ? 'text-orange-600' :\n                  'text-green-600'\n                }`}>\n                  {utilization}%\n                </span>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"flex gap-3 pt-3\">\n            {onAddEmployee && (\n              <Button\n                size=\"sm\"\n                variant=\"outline\"\n                onClick={onAddEmployee}\n                className=\"flex-1 h-9 border-2 border-border/50 hover:border-primary hover:bg-primary/5 transition-all\"\n              >\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Add Employee\n              </Button>\n            )}\n\n            {onViewDetails && (\n              <Button\n                size=\"sm\"\n                variant=\"secondary\"\n                onClick={onViewDetails}\n                className=\"flex-1 h-9 bg-gradient-to-r from-slate-100 to-slate-200 hover:from-slate-200 hover:to-slate-300 transition-all\"\n              >\n                View Details\n              </Button>\n            )}\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}"], "names": ["c", "_c", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "Progress", "<PERSON><PERSON>", "calculateCapacityUtilization", "Building2", "Users", "Plus", "DepartmentCard", "t0", "$", "$i", "Symbol", "for", "department", "onAddEmployee", "onViewDetails", "employeeCount", "employees", "length", "utilization", "capacity", "t1", "name", "t2", "t3", "t4", "t5", "code", "t6", "t7", "t8", "t9", "t10", "t11", "t12", "t13", "t14", "t15", "t16", "t17", "t18", "t19", "t20"], "mappings": ";;;;AAAY,SAAAA,CAAA,IAAAC,EAAA;AAEZ,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,sBAAsB;AAC/E,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,MAAM,QAAQ,wBAAwB;AAE/C,SAASC,4BAA4B,QAAQ,aAAa;AAC1D,SAASC,SAAS,EAAEC,KAAK,EAAEC,IAAI,QAAQ,cAAc;;;AAPrD,YAAY;;;;;;;;AAeL,wBAAAE,EAAA;;IAAA,MAAAC,CAAA,mLAAAb,IAAAA,AAAA,EAAA;IAAA,IAAAa,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IAAwB,MAAA,EAAAI,UAAA,EAAAC,aAAA,EAAAC,aAAAA,EAAA,GAAAP,EAIT;IACpB,MAAAQ,aAAA,6BAAsBH,UAAU,CAAAI,SAAA,gFAAAC,MAAA,KAAA,CAAuB;IACvD,MAAAC,WAAA,uHAAoBhB,+BAAAA,AAAA,EAA6Ba,aAAa,EAAEH,UAAU,CAAAO,QAAS,CAAC;IAAA,IAAAC,EAAA;IAAA,IAAAZ,CAAA,CAAA,EAAA,KAAAI,UAAA,CAAAS,IAAA,EAAA;QAW9ED,EAAA,iBAAA,6LAAC,yIAAS;YAAW,SAAuC,EAAvC,uCAAuC,CACzD;sBAAAR,UAAU,CAAAS,IAAI,CACjB,EAFC,SAAS,CAEE;;;;;;QAAAb,CAAA,CAAA,EAAA,GAAAI,UAAA,CAAAS,IAAA;QAAAb,CAAA,CAAA,EAAA,GAAAY,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAZ,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAc,EAAA;IAAA,IAAAd,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QACZW,EAAA,iBAAA,6LAAA,GAEM;YAFS,SAA+G,EAA/G,+GAA+G;oCAC5H,iZAAC,YAAS;gBAAW,SAAoB,EAApB,oBAAoB,GAC3C,EAFA,GAEM;;;;;;;;;;;QAAAd,CAAA,CAAA,EAAA,GAAAc,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAd,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAe,EAAA;IAAA,IAAAf,CAAA,CAAA,EAAA,KAAAY,EAAA,EAAA;QANRG,EAAA,iBAAA,0TAAC,aAAU;YAAW,SAA2D,EAA3D,2DAA2D,CAC/E;;gBAAAH,EAEW,CACX;gBAAAE,EAEK,CACP,EAPC,UAAU,CAOE;;;;;;;QAAAd,CAAA,CAAA,EAAA,GAAAY,EAAA;QAAAZ,CAAA,CAAA,EAAA,GAAAe,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAf,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAgB,EAAA;IAAA,IAAAhB,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAKPa,EAAA,iBAAA,6LAAA,IAAmF;YAAnE,SAA2C,EAA3C,2CAA2C;sBAAC,gBAAgB,EAA5E,IAAmF;;;;;;QAAAhB,CAAA,CAAA,EAAA,GAAAgB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAhB,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAiB,EAAA;IAAA,IAAAjB,CAAA,CAAA,EAAA,KAAAI,UAAA,CAAAc,IAAA,EAAA;QADrFD,EAAA,iBAAA,6LAAA,GAGM;YAHS,SAAkH,EAAlH,kHAAkH,CAC/H;;gBAAAD,EAAkF;8BAClF,6LAAA,IAAuH;oBAAvG,SAA8E,EAA9E,8EAA8E,CAAE;8BAAAZ,UAAU,CAAAc,IAAI,CAAE,EAAhH,IAAuH,CACzH,EAHA,GAGM;;;;;;;;;;;;QAAAlB,CAAA,CAAA,EAAA,GAAAI,UAAA,CAAAc,IAAA;QAAAlB,CAAA,CAAA,EAAA,GAAAiB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAjB,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAmB,EAAA;IAAA,IAAAnB,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAIFgB,EAAA,iBAAA,6LAAA,IAGO;YAHS,SAAmE,EAAnE,mEAAmE;;8BACjF,qYAAC,QAAK;oBAAW,SAAS,EAAT,SAAS;;;;;;gBAAG,cAE/B,EAHA,IAGO;;;;;;;QAAAnB,CAAA,CAAA,EAAA,GAAAmB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAnB,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAoB,EAAA;IAAA,IAAApB,CAAA,CAAA,GAAA,KAAAI,UAAA,CAAAO,QAAA,IAAAX,CAAA,CAAA,GAAA,KAAAO,aAAA,EAAA;QAJTa,EAAA,iBAAA,6LAAA,GAQM;YARS,SAAmC,EAAnC,mCAAmC,CAChD;;gBAAAD,EAGM;8BACN,6LAAA,IAEO;oBAFS,SAAuB,EAAvB,uBAAuB,CACpCZ;;wBAAAA,aAAY;wBAAE,GAAI;wBAAAH,UAAU,CAAAO,QAAQ,CACvC,EAFA,IAEO,CACT,EARA,GAQM;;;;;;;;;;;;;QAAAX,CAAA,CAAA,GAAA,GAAAI,UAAA,CAAAO,QAAA;QAAAX,CAAA,CAAA,GAAA,GAAAO,aAAA;QAAAP,CAAA,CAAA,GAAA,GAAAoB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAApB,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAqB,EAAA;IAAA,IAAArB,CAAA,CAAA,GAAA,KAAAU,WAAA,EAAA;QAIFW,EAAA,iBAAA,6LAAC,4IAAQ;YACAX,KAAW,CAAXA,CAAAA,WAAU,CAAC;YACR,SAAkB,EAAlB,kBAAkB,GAC5B;;;;;;QAAAV,CAAA,CAAA,GAAA,GAAAU,WAAA;QAAAV,CAAA,CAAA,GAAA,GAAAqB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAArB,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAsB,EAAA;IAAA,IAAAtB,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QACFmB,EAAA,iBAAA,6LAAA,GAAmH;YAApG,SAA6F,EAA7F,6FAA6F,GAAO;;;;;;QAAAtB,CAAA,CAAA,GAAA,GAAAsB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAtB,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAuB,GAAA;IAAA,IAAAvB,CAAA,CAAA,GAAA,KAAAqB,EAAA,EAAA;QALrHE,GAAA,iBAAA,6LAAA,GAMM;YANS,SAAU,EAAV,UAAU,CACvB;;gBAAAF,EAGC,CACD;gBAAAC,EAAkH,CACpH,EANA,GAMM;;;;;;;QAAAtB,CAAA,CAAA,GAAA,GAAAqB,EAAA;QAAArB,CAAA,CAAA,GAAA,GAAAuB,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAvB,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAwB,GAAA;IAAA,IAAAxB,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAGJqB,GAAA,iBAAA,6LAAA,IAA+E;YAA/D,SAAmC,EAAnC,mCAAmC;sBAAC,oBAAoB,EAAxE,IAA+E;;;;;;QAAAxB,CAAA,CAAA,GAAA,GAAAwB,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAxB,CAAA,CAAA,GAAA;IAAA;IAC9D,MAAAyB,GAAA,GAAA,aAGC,CAChB,MAHAf,WAAW,IAAA,EAAM,GAAG,cAAc,GAClCA,WAAW,IAAA,EAAM,GAAG,iBAAiB,GACrC,gBAAgB;IAChB,IAAAgB,GAAA;IAAA,IAAA1B,CAAA,CAAA,GAAA,KAAAyB,GAAA,IAAAzB,CAAA,CAAA,GAAA,KAAAU,WAAA,EAAA;QANJgB,GAAA,iBAAA,6LAAA,GASM;YATS,SAA8B,EAA9B,8BAA8B,CAC3C;;gBAAAF,GAA8E;8BAC9E,6LAAA,IAMO;oBANU,SAIf,CAJe,CAAAC,GAIhB,CAAC,CACCf;;wBAAAA,WAAU;wBAAE,CACf,EANA,IAMO,CACT,EATA,GASM;;;;;;;;;;;;;QAAAV,CAAA,CAAA,GAAA,GAAAyB,GAAA;QAAAzB,CAAA,CAAA,GAAA,GAAAU,WAAA;QAAAV,CAAA,CAAA,GAAA,GAAA0B,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA1B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA2B,GAAA;IAAA,IAAA3B,CAAA,CAAA,GAAA,KAAAuB,GAAA,IAAAvB,CAAA,CAAA,GAAA,KAAA0B,GAAA,EAAA;QAlBRC,GAAA,iBAAA,6LAAA,GAmBM;YAnBS,SAAW,EAAX,WAAW,CACxB;;gBAAAJ,GAMK,CAEL;gBAAAG,GASK,CACP,EAnBA,GAmBM;;;;;;;QAAA1B,CAAA,CAAA,GAAA,GAAAuB,GAAA;QAAAvB,CAAA,CAAA,GAAA,GAAA0B,GAAA;QAAA1B,CAAA,CAAA,GAAA,GAAA2B,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA3B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA4B,GAAA;IAAA,IAAA5B,CAAA,CAAA,GAAA,KAAA2B,GAAA,IAAA3B,CAAA,CAAA,GAAA,KAAAoB,EAAA,EAAA;QA9BRQ,GAAA,iBAAA,6LAAA,GA+BM;YA/BS,SAAW,EAAX,WAAW,CACxB;;gBAAAR,EAQK,CAEL;gBAAAO,GAmBK,CACP,EA/BA,GA+BM;;;;;;;QAAA3B,CAAA,CAAA,GAAA,GAAA2B,GAAA;QAAA3B,CAAA,CAAA,GAAA,GAAAoB,EAAA;QAAApB,CAAA,CAAA,GAAA,GAAA4B,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA5B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA6B,GAAA;IAAA,IAAA7B,CAAA,CAAA,GAAA,KAAAK,aAAA,EAAA;QAGHwB,GAAA,GAAAxB,aAAa,kBACZ,4TAAC,SAAM;YACA,IAAI,EAAJ,IAAI;YACD,OAAS,EAAT,SAAS;YACRA,OAAa,CAAbA,CAAAA,aAAY,CAAC;YACZ,SAA6F,EAA7F,6FAA6F;;8BAEvG,mYAAC,OAAI;oBAAW,SAAc,EAAd,cAAc;;;;;;gBAAG,YAEnC,EARC,MAAM,CASR;;;;;;;QAAAL,CAAA,CAAA,GAAA,GAAAK,aAAA;QAAAL,CAAA,CAAA,GAAA,GAAA6B,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA7B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA8B,GAAA;IAAA,IAAA9B,CAAA,CAAA,GAAA,KAAAM,aAAA,EAAA;QAEAwB,GAAA,GAAAxB,aAAa,kBACZ,4TAAC,SAAM;YACA,IAAI,EAAJ,IAAI;YACD,OAAW,EAAX,WAAW;YACVA,OAAa,CAAbA,CAAAA,aAAY,CAAC;YACZ,SAAgH,EAAhH,gHAAgH;sBAC3H,YAED,EAPC,MAAM,CAQR;;;;;;QAAAN,CAAA,CAAA,GAAA,GAAAM,aAAA;QAAAN,CAAA,CAAA,GAAA,GAAA8B,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA9B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA+B,GAAA;IAAA,IAAA/B,CAAA,CAAA,GAAA,KAAA6B,GAAA,IAAA7B,CAAA,CAAA,GAAA,KAAA8B,GAAA,EAAA;QAtBHC,GAAA,iBAAA,6LAAA,GAuBM;YAvBS,SAAiB,EAAjB,iBAAiB,CAC7B;;gBAAAF,GAUD,CAEC;gBAAAC,GASD,CACF,EAvBA,GAuBM;;;;;;;QAAA9B,CAAA,CAAA,GAAA,GAAA6B,GAAA;QAAA7B,CAAA,CAAA,GAAA,GAAA8B,GAAA;QAAA9B,CAAA,CAAA,GAAA,GAAA+B,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA/B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAgC,GAAA;IAAA,IAAAhC,CAAA,CAAA,GAAA,KAAA4B,GAAA,IAAA5B,CAAA,CAAA,GAAA,KAAA+B,GAAA,IAAA/B,CAAA,CAAA,GAAA,KAAAiB,EAAA,EAAA;QA/DVe,GAAA,iBAAA,0TAAC,cAAW;oCACV,6LAAA,GA+DM;gBA/DS,SAAW,EAAX,WAAW,CACxB;;oBAAAf,EAGK,CAEL;oBAAAW,GA+BK,CAEL;oBAAAG,GAuBK,CACP,EA/DA,GA+DM,CACR,EAjEC,WAAW,CAiEE;;;;;;;;;;;;QAAA/B,CAAA,CAAA,GAAA,GAAA4B,GAAA;QAAA5B,CAAA,CAAA,GAAA,GAAA+B,GAAA;QAAA/B,CAAA,CAAA,GAAA,GAAAiB,EAAA;QAAAjB,CAAA,CAAA,GAAA,GAAAgC,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAhC,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAiC,GAAA;IAAA,IAAAjC,CAAA,CAAA,GAAA,KAAAgC,GAAA,IAAAhC,CAAA,CAAA,GAAA,KAAAe,EAAA,EAAA;QA3EhBkB,GAAA,iBAAA,0TAAC,OAAI;YAAW,SAAiI,EAAjI,iIAAiI,CAC/I;;gBAAAlB,EAOY,CAEZ;gBAAAiB,GAiEa,CACf,EA5EC,IAAI,CA4EE;;;;;;;QAAAhC,CAAA,CAAA,GAAA,GAAAgC,GAAA;QAAAhC,CAAA,CAAA,GAAA,GAAAe,EAAA;QAAAf,CAAA,CAAA,GAAA,GAAAiC,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAjC,CAAA,CAAA,GAAA;IAAA;IAAA,OA5EPiC,GA4EO;AAAA;KA3FJnC", "ignoreList": [], "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/lib/store/hr-store.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { subscribeWithSelector } from 'zustand/middleware'\nimport { Employee, Department, BulkOperation } from '@/lib/types'\n\ninterface HRState {\n  // Data\n  employees: Employee[]\n  departments: Department[]\n  freeBucket: Employee[]\n  \n  // UI State\n  selectedEmployees: string[]\n  isLoading: boolean\n  searchQuery: string\n  \n  // Actions\n  setEmployees: (employees: Employee[]) => void\n  setDepartments: (departments: Department[]) => void\n  addEmployee: (employee: Employee) => void\n  updateEmployee: (id: string, updates: Partial<Employee>) => void\n  removeEmployee: (id: string) => void\n  \n  // Selection\n  toggleEmployeeSelection: (id: string) => void\n  selectAllEmployees: (ids: string[]) => void\n  clearSelection: () => void\n  \n  // Bulk Operations\n  executeBulkOperation: (operation: BulkOperation) => Promise<void>\n  \n  // Search & Filter\n  setSearchQuery: (query: string) => void\n  getFilteredEmployees: () => Employee[]\n  \n  // Free Bucket\n  moveToFreeBucket: (employeeIds: string[]) => void\n  removeFromFreeBucket: (employeeIds: string[]) => void\n}\n\nexport const useHRStore = create<HRState>()(\n  subscribeWithSelector((set, get) => ({\n    // Initial state\n    employees: [],\n    departments: [],\n    freeBucket: [],\n    selectedEmployees: [],\n    isLoading: false,\n    searchQuery: '',\n\n    // Data actions\n    setEmployees: (employees) => set((state) => ({\n      employees,\n      freeBucket: employees.filter(emp => emp.departmentId === null)\n    })),\n    setDepartments: (departments) => set({ departments }),\n    \n    addEmployee: (employee) => set((state) => ({\n      employees: [...state.employees, employee]\n    })),\n    \n    updateEmployee: (id, updates) => set((state) => ({\n      employees: state.employees.map(emp => \n        emp.id === id ? { ...emp, ...updates } : emp\n      )\n    })),\n    \n    removeEmployee: (id) => set((state) => ({\n      employees: state.employees.filter(emp => emp.id !== id),\n      selectedEmployees: state.selectedEmployees.filter(empId => empId !== id)\n    })),\n\n    // Selection actions\n    toggleEmployeeSelection: (id) => set((state) => ({\n      selectedEmployees: state.selectedEmployees.includes(id)\n        ? state.selectedEmployees.filter(empId => empId !== id)\n        : [...state.selectedEmployees, id]\n    })),\n    \n    selectAllEmployees: (ids) => set({ selectedEmployees: ids }),\n    clearSelection: () => set({ selectedEmployees: [] }),\n\n    // Bulk operations\n    executeBulkOperation: async (operation) => {\n      const { employees, selectedEmployees } = get()\n      \n      switch (operation.type) {\n        case 'transfer':\n          set((state) => ({\n            employees: state.employees.map(emp =>\n              operation.employeeIds.includes(emp.id)\n                ? { ...emp, departmentId: operation.targetDepartmentId || null }\n                : emp\n            ),\n            freeBucket: state.employees\n              .map(emp =>\n                operation.employeeIds.includes(emp.id)\n                  ? { ...emp, departmentId: operation.targetDepartmentId || null }\n                  : emp\n              )\n              .filter(emp => emp.departmentId === null)\n          }))\n          break\n          \n        case 'status_update':\n          if (operation.newStatus) {\n            set((state) => ({\n              employees: state.employees.map(emp =>\n                operation.employeeIds.includes(emp.id)\n                  ? { ...emp, status: operation.newStatus! }\n                  : emp\n              )\n            }))\n          }\n          break\n          \n        case 'delete':\n          set((state) => ({\n            employees: state.employees.filter(emp => \n              !operation.employeeIds.includes(emp.id)\n            )\n          }))\n          break\n      }\n      \n      set({ selectedEmployees: [] })\n    },\n\n    // Search & filter\n    setSearchQuery: (query) => set({ searchQuery: query }),\n    \n    getFilteredEmployees: () => {\n      const { employees, searchQuery } = get()\n      if (!searchQuery) return employees\n      \n      return employees.filter(emp =>\n        emp.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        emp.email.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        emp.id.toLowerCase().includes(searchQuery.toLowerCase())\n      )\n    },\n\n    // Free bucket operations\n    moveToFreeBucket: (employeeIds) => set((state) => {\n      const movedEmployees = state.employees\n        .filter(emp => employeeIds.includes(emp.id))\n        .map(emp => ({ ...emp, departmentId: null }))\n      \n      return {\n        employees: state.employees.map(emp =>\n          employeeIds.includes(emp.id) ? { ...emp, departmentId: null } : emp\n        ),\n        freeBucket: [...state.freeBucket, ...movedEmployees]\n      }\n    }),\n    \n    removeFromFreeBucket: (employeeIds) => set((state) => ({\n      freeBucket: state.freeBucket.filter(emp => !employeeIds.includes(emp.id)),\n      employees: state.employees.filter(emp => !employeeIds.includes(emp.id))\n    }))\n  }))\n)"], "names": [], "mappings": ";;;AAAA;AACA;;;AAsCO,MAAM,aAAa,CAAA,GAAA,2JAAA,CAAA,SAAM,AAAD,IAC7B,CAAA,GAAA,gJAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC,KAAK,MAAQ,CAAC;QACnC,gBAAgB;QAChB,WAAW,EAAE;QACb,aAAa,EAAE;QACf,YAAY,EAAE;QACd,mBAAmB,EAAE;QACrB,WAAW;QACX,aAAa;QAEb,eAAe;QACf,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C;oBACA,YAAY,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK;gBAC3D,CAAC;QACD,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QAEnD,aAAa,CAAC,WAAa,IAAI,CAAC,QAAU,CAAC;oBACzC,WAAW;2BAAI,MAAM,SAAS;wBAAE;qBAAS;gBAC3C,CAAC;QAED,gBAAgB,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC/C,WAAW,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,MAC7B,IAAI,EAAE,KAAK,KAAK;4BAAE,GAAG,GAAG;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAE7C,CAAC;QAED,gBAAgB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBACtC,WAAW,MAAM,SAAS,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;oBACpD,mBAAmB,MAAM,iBAAiB,CAAC,MAAM,CAAC,CAAA,QAAS,UAAU;gBACvE,CAAC;QAED,oBAAoB;QACpB,yBAAyB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAC/C,mBAAmB,MAAM,iBAAiB,CAAC,QAAQ,CAAC,MAChD,MAAM,iBAAiB,CAAC,MAAM,CAAC,CAAA,QAAS,UAAU,MAClD;2BAAI,MAAM,iBAAiB;wBAAE;qBAAG;gBACtC,CAAC;QAED,oBAAoB,CAAC,MAAQ,IAAI;gBAAE,mBAAmB;YAAI;QAC1D,gBAAgB,IAAM,IAAI;gBAAE,mBAAmB,EAAE;YAAC;QAElD,kBAAkB;QAClB,sBAAsB,OAAO;YAC3B,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAE,GAAG;YAEzC,OAAQ,UAAU,IAAI;gBACpB,KAAK;oBACH,IAAI,CAAC,QAAU,CAAC;4BACd,WAAW,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,MAC7B,UAAU,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,IACjC;oCAAE,GAAG,GAAG;oCAAE,cAAc,UAAU,kBAAkB,IAAI;gCAAK,IAC7D;4BAEN,YAAY,MAAM,SAAS,CACxB,GAAG,CAAC,CAAA,MACH,UAAU,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,IACjC;oCAAE,GAAG,GAAG;oCAAE,cAAc,UAAU,kBAAkB,IAAI;gCAAK,IAC7D,KAEL,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK;wBACxC,CAAC;oBACD;gBAEF,KAAK;oBACH,IAAI,UAAU,SAAS,EAAE;wBACvB,IAAI,CAAC,QAAU,CAAC;gCACd,WAAW,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,MAC7B,UAAU,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,IACjC;wCAAE,GAAG,GAAG;wCAAE,QAAQ,UAAU,SAAS;oCAAE,IACvC;4BAER,CAAC;oBACH;oBACA;gBAEF,KAAK;oBACH,IAAI,CAAC,QAAU,CAAC;4BACd,WAAW,MAAM,SAAS,CAAC,MAAM,CAAC,CAAA,MAChC,CAAC,UAAU,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE;wBAE1C,CAAC;oBACD;YACJ;YAEA,IAAI;gBAAE,mBAAmB,EAAE;YAAC;QAC9B;QAEA,kBAAkB;QAClB,gBAAgB,CAAC,QAAU,IAAI;gBAAE,aAAa;YAAM;QAEpD,sBAAsB;YACpB,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG;YACnC,IAAI,CAAC,aAAa,OAAO;YAEzB,OAAO,UAAU,MAAM,CAAC,CAAA,MACtB,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACvD,IAAI,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACxD,IAAI,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAEzD;QAEA,yBAAyB;QACzB,kBAAkB,CAAC,cAAgB,IAAI,CAAC;gBACtC,MAAM,iBAAiB,MAAM,SAAS,CACnC,MAAM,CAAC,CAAA,MAAO,YAAY,QAAQ,CAAC,IAAI,EAAE,GACzC,GAAG,CAAC,CAAA,MAAO,CAAC;wBAAE,GAAG,GAAG;wBAAE,cAAc;oBAAK,CAAC;gBAE7C,OAAO;oBACL,WAAW,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,MAC7B,YAAY,QAAQ,CAAC,IAAI,EAAE,IAAI;4BAAE,GAAG,GAAG;4BAAE,cAAc;wBAAK,IAAI;oBAElE,YAAY;2BAAI,MAAM,UAAU;2BAAK;qBAAe;gBACtD;YACF;QAEA,sBAAsB,CAAC,cAAgB,IAAI,CAAC,QAAU,CAAC;oBACrD,YAAY,MAAM,UAAU,CAAC,MAAM,CAAC,CAAA,MAAO,CAAC,YAAY,QAAQ,CAAC,IAAI,EAAE;oBACvE,WAAW,MAAM,SAAS,CAAC,MAAM,CAAC,CAAA,MAAO,CAAC,YAAY,QAAQ,CAAC,IAAI,EAAE;gBACvE,CAAC;IACH,CAAC", "debugId": null}}, {"offset": {"line": 733, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/lib/sample-data.ts"], "sourcesContent": ["import { Department, Employee } from './types'\n\nexport const sampleDepartments: Department[] = [\n  {\n    id: 'dept-001',\n    name: 'Engineering',\n    code: 'ENG',\n    capacity: 25,\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-01-15'),\n  },\n  {\n    id: 'dept-002',\n    name: 'Marketing',\n    code: 'MKT',\n    capacity: 15,\n    createdAt: new Date('2024-01-16'),\n    updatedAt: new Date('2024-01-16'),\n  },\n  {\n    id: 'dept-003',\n    name: 'Sales',\n    code: 'SAL',\n    capacity: 20,\n    createdAt: new Date('2024-01-17'),\n    updatedAt: new Date('2024-01-17'),\n  },\n  {\n    id: 'dept-004',\n    name: 'Human Resources',\n    code: 'HR',\n    capacity: 8,\n    createdAt: new Date('2024-01-18'),\n    updatedAt: new Date('2024-01-18'),\n  },\n  {\n    id: 'dept-005',\n    name: 'Finance',\n    code: 'FIN',\n    capacity: 12,\n    createdAt: new Date('2024-01-19'),\n    updatedAt: new Date('2024-01-19'),\n  },\n  {\n    id: 'dept-006',\n    name: 'Operations',\n    code: 'OPS',\n    capacity: 18,\n    createdAt: new Date('2024-01-20'),\n    updatedAt: new Date('2024-01-20'),\n  },\n]\n\nexport const sampleEmployees: Employee[] = [\n  // Engineering Department\n  {\n    id: 'ENG-001',\n    name: 'John Smith',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-03-15'),\n    transferHistory: [],\n    createdAt: new Date('2023-03-15'),\n    updatedAt: new Date('2023-03-15'),\n  },\n  {\n    id: 'ENG-002',\n    name: 'Sarah Johnson',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-05-20'),\n    transferHistory: [],\n    createdAt: new Date('2023-05-20'),\n    updatedAt: new Date('2023-05-20'),\n  },\n  {\n    id: 'ENG-003',\n    name: 'Michael Chen',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-07-10'),\n    transferHistory: [],\n    createdAt: new Date('2023-07-10'),\n    updatedAt: new Date('2023-07-10'),\n  },\n  {\n    id: 'ENG-004',\n    name: 'Emily Davis',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-09-05'),\n    transferHistory: [],\n    createdAt: new Date('2023-09-05'),\n    updatedAt: new Date('2023-09-05'),\n  },\n  {\n    id: 'ENG-005',\n    name: 'David Wilson',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-11-12'),\n    transferHistory: [],\n    createdAt: new Date('2023-11-12'),\n    updatedAt: new Date('2023-11-12'),\n  },\n  {\n    id: 'ENG-006',\n    name: 'Lisa Anderson',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-01-08'),\n    transferHistory: [],\n    createdAt: new Date('2024-01-08'),\n    updatedAt: new Date('2024-01-08'),\n  },\n  {\n    id: 'ENG-007',\n    name: 'Robert Taylor',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-02-14'),\n    transferHistory: [],\n    createdAt: new Date('2024-02-14'),\n    updatedAt: new Date('2024-02-14'),\n  },\n  {\n    id: 'ENG-008',\n    name: 'Jennifer Brown',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-03-22'),\n    transferHistory: [],\n    createdAt: new Date('2024-03-22'),\n    updatedAt: new Date('2024-03-22'),\n  },\n  {\n    id: 'ENG-009',\n    name: 'Christopher Lee',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-04-18'),\n    transferHistory: [],\n    createdAt: new Date('2024-04-18'),\n    updatedAt: new Date('2024-04-18'),\n  },\n  {\n    id: 'ENG-010',\n    name: 'Amanda White',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-05-25'),\n    transferHistory: [],\n    createdAt: new Date('2024-05-25'),\n    updatedAt: new Date('2024-05-25'),\n  },\n\n  // Marketing Department\n  {\n    id: 'MKT-001',\n    name: 'Jessica Garcia',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-04-10'),\n    transferHistory: [],\n    createdAt: new Date('2023-04-10'),\n    updatedAt: new Date('2023-04-10'),\n  },\n  {\n    id: 'MKT-002',\n    name: 'Daniel Martinez',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-06-15'),\n    transferHistory: [],\n    createdAt: new Date('2023-06-15'),\n    updatedAt: new Date('2023-06-15'),\n  },\n  {\n    id: 'MKT-003',\n    name: 'Ashley Rodriguez',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-08-20'),\n    transferHistory: [],\n    createdAt: new Date('2023-08-20'),\n    updatedAt: new Date('2023-08-20'),\n  },\n  {\n    id: 'MKT-004',\n    name: 'Matthew Thompson',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-10-12'),\n    transferHistory: [],\n    createdAt: new Date('2023-10-12'),\n    updatedAt: new Date('2023-10-12'),\n  },\n  {\n    id: 'MKT-005',\n    name: 'Stephanie Clark',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-01-30'),\n    transferHistory: [],\n    createdAt: new Date('2024-01-30'),\n    updatedAt: new Date('2024-01-30'),\n  },\n  {\n    id: 'MKT-006',\n    name: 'Kevin Lewis',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-03-15'),\n    transferHistory: [],\n    createdAt: new Date('2024-03-15'),\n    updatedAt: new Date('2024-03-15'),\n  },\n\n  // Sales Department\n  {\n    id: 'SAL-001',\n    name: 'Ryan Walker',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-02-28'),\n    transferHistory: [],\n    createdAt: new Date('2023-02-28'),\n    updatedAt: new Date('2023-02-28'),\n  },\n  {\n    id: 'SAL-002',\n    name: 'Nicole Hall',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-05-10'),\n    transferHistory: [],\n    createdAt: new Date('2023-05-10'),\n    updatedAt: new Date('2023-05-10'),\n  },\n  {\n    id: 'SAL-003',\n    name: 'Brandon Allen',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-07-25'),\n    transferHistory: [],\n    createdAt: new Date('2023-07-25'),\n    updatedAt: new Date('2023-07-25'),\n  },\n  {\n    id: 'SAL-004',\n    name: 'Megan Young',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-09-18'),\n    transferHistory: [],\n    createdAt: new Date('2023-09-18'),\n    updatedAt: new Date('2023-09-18'),\n  },\n  {\n    id: 'SAL-005',\n    name: 'Tyler King',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-11-30'),\n    transferHistory: [],\n    createdAt: new Date('2023-11-30'),\n    updatedAt: new Date('2023-11-30'),\n  },\n  {\n    id: 'SAL-006',\n    name: 'Rachel Wright',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-02-05'),\n    transferHistory: [],\n    createdAt: new Date('2024-02-05'),\n    updatedAt: new Date('2024-02-05'),\n  },\n  {\n    id: 'SAL-007',\n    name: 'Justin Lopez',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-04-12'),\n    transferHistory: [],\n    createdAt: new Date('2024-04-12'),\n    updatedAt: new Date('2024-04-12'),\n  },\n\n  // HR Department\n  {\n    id: 'HR-001',\n    name: 'Laura Hill',\n    email: '<EMAIL>',\n    departmentId: 'dept-004',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-01-20'),\n    transferHistory: [],\n    createdAt: new Date('2023-01-20'),\n    updatedAt: new Date('2023-01-20'),\n  },\n  {\n    id: 'HR-002',\n    name: 'Steven Green',\n    email: '<EMAIL>',\n    departmentId: 'dept-004',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-06-08'),\n    transferHistory: [],\n    createdAt: new Date('2023-06-08'),\n    updatedAt: new Date('2023-06-08'),\n  },\n  {\n    id: 'HR-003',\n    name: 'Kimberly Adams',\n    email: '<EMAIL>',\n    departmentId: 'dept-004',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-10-25'),\n    transferHistory: [],\n    createdAt: new Date('2023-10-25'),\n    updatedAt: new Date('2023-10-25'),\n  },\n\n  // Finance Department\n  {\n    id: 'FIN-001',\n    name: 'Andrew Baker',\n    email: '<EMAIL>',\n    departmentId: 'dept-005',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-03-08'),\n    transferHistory: [],\n    createdAt: new Date('2023-03-08'),\n    updatedAt: new Date('2023-03-08'),\n  },\n  {\n    id: 'FIN-002',\n    name: 'Michelle Nelson',\n    email: '<EMAIL>',\n    departmentId: 'dept-005',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-07-15'),\n    transferHistory: [],\n    createdAt: new Date('2023-07-15'),\n    updatedAt: new Date('2023-07-15'),\n  },\n  {\n    id: 'FIN-003',\n    name: 'Joshua Carter',\n    email: '<EMAIL>',\n    departmentId: 'dept-005',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-11-20'),\n    transferHistory: [],\n    createdAt: new Date('2023-11-20'),\n    updatedAt: new Date('2023-11-20'),\n  },\n  {\n    id: 'FIN-004',\n    name: 'Samantha Mitchell',\n    email: '<EMAIL>',\n    departmentId: 'dept-005',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-02-28'),\n    transferHistory: [],\n    createdAt: new Date('2024-02-28'),\n    updatedAt: new Date('2024-02-28'),\n  },\n\n  // Operations Department\n  {\n    id: 'OPS-001',\n    name: 'Gregory Perez',\n    email: '<EMAIL>',\n    departmentId: 'dept-006',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-04-05'),\n    transferHistory: [],\n    createdAt: new Date('2023-04-05'),\n    updatedAt: new Date('2023-04-05'),\n  },\n  {\n    id: 'OPS-002',\n    name: 'Heather Roberts',\n    email: '<EMAIL>',\n    departmentId: 'dept-006',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-08-12'),\n    transferHistory: [],\n    createdAt: new Date('2023-08-12'),\n    updatedAt: new Date('2023-08-12'),\n  },\n  {\n    id: 'OPS-003',\n    name: 'Nathan Turner',\n    email: '<EMAIL>',\n    departmentId: 'dept-006',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-12-01'),\n    transferHistory: [],\n    createdAt: new Date('2023-12-01'),\n    updatedAt: new Date('2023-12-01'),\n  },\n  {\n    id: 'OPS-004',\n    name: 'Brittany Phillips',\n    email: '<EMAIL>',\n    departmentId: 'dept-006',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-03-10'),\n    transferHistory: [],\n    createdAt: new Date('2024-03-10'),\n    updatedAt: new Date('2024-03-10'),\n  },\n\n  // Free Bucket (Unassigned) Employees\n  {\n    id: 'FB-001',\n    name: 'Alex Campbell',\n    email: '<EMAIL>',\n    departmentId: null,\n    status: 'ACTIVE',\n    hireDate: new Date('2024-06-01'),\n    transferHistory: [],\n    createdAt: new Date('2024-06-01'),\n    updatedAt: new Date('2024-06-01'),\n  },\n  {\n    id: 'FB-002',\n    name: 'Morgan Parker',\n    email: '<EMAIL>',\n    departmentId: null,\n    status: 'ACTIVE',\n    hireDate: new Date('2024-06-15'),\n    transferHistory: [],\n    createdAt: new Date('2024-06-15'),\n    updatedAt: new Date('2024-06-15'),\n  },\n]\n\n// Helper function to populate departments with their employees\nexport const getDepartmentsWithEmployees = (): Department[] => {\n  return sampleDepartments.map(dept => ({\n    ...dept,\n    employees: sampleEmployees.filter(emp => emp.departmentId === dept.id)\n  }))\n}\n\n// Helper function to get free bucket employees\nexport const getFreeBucketEmployees = (): Employee[] => {\n  return sampleEmployees.filter(emp => emp.departmentId === null)\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,oBAAkC;IAC7C;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAEM,MAAM,kBAA8B;IACzC,yBAAyB;IACzB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,uBAAuB;IACvB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,qBAAqB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,wBAAwB;IACxB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,qCAAqC;IACrC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,8BAA8B;IACzC,OAAO,kBAAkB,GAAG,CAAC,CAAA,OAAQ,CAAC;YACpC,GAAG,IAAI;YACP,WAAW,gBAAgB,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK,KAAK,EAAE;QACvE,CAAC;AACH;AAGO,MAAM,yBAAyB;IACpC,OAAO,gBAAgB,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK;AAC5D", "debugId": null}}, {"offset": {"line": 1212, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/lib/hooks/G%3A/Augment%20code/lib/hooks/use-sample-data.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { useHRStore } from '@/lib/store/hr-store'\nimport { \n  sampleEmployees, \n  getDepartmentsWithEmployees, \n  getFreeBucketEmployees \n} from '@/lib/sample-data'\n\nexport function useSampleData() {\n  const { setEmployees, setDepartments } = useHRStore()\n\n  useEffect(() => {\n    // Initialize with sample data\n    const departmentsWithEmployees = getDepartmentsWithEmployees()\n    const freeBucketEmployees = getFreeBucketEmployees()\n    \n    setEmployees(sampleEmployees)\n    setDepartments(departmentsWithEmployees)\n    \n    // Note: Free bucket employees are automatically calculated in the store\n    // based on employees with departmentId === null\n    \n    console.log('Sample data loaded:', {\n      employees: sampleEmployees.length,\n      departments: departmentsWithEmployees.length,\n      freeBucket: freeBucketEmployees.length\n    })\n  }, [setEmployees, setDepartments])\n}\n"], "names": ["useEffect", "useHRStore", "sampleEmployees", "getDepartmentsWithEmployees", "getFreeBucketEmployees", "useSampleData", "$", "_c", "$i", "Symbol", "for", "setEmployees", "setDepartments", "t0", "t1", "departmentsWithEmployees", "freeBucketEmployees", "console", "log", "employees", "length", "departments", "freeBucket"], "mappings": ";;;;AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SACEC,eAAe,EACfC,2BAA2B,EAC3BC,sBAAsB,QACjB,mBAAmB;;;;;;AAEnB;;IAAA,MAAAE,CAAA,mLAAAC,IAAAA,AAAA,EAAA;IAAA,IAAAD,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAE,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAF,CAAA,CAAAE,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAJ,CAAA,CAAA,EAAA,GAAA;IAAA;IACL,MAAA,EAAAK,YAAA,EAAAC,cAAAA,EAAA,mJAAyCX;IAAY,IAAAY,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAM,cAAA,IAAAN,CAAA,CAAA,EAAA,KAAAK,YAAA,EAAA;QAE3CE,EAAA,GAAAA,CAAA;YAER,MAAAE,wBAAA,IAAiCZ,0JAAAA,AAAA,CAA4B,CAAC;YAC9D,MAAAa,mBAAA,gIAA4BZ,yBAAAA,AAAA,CAAuB,CAAC;YAEpDO,YAAY,0HAAAT,kBAAgB,CAAC;YAC7BU,cAAc,CAACG,wBAAwB,CAAC;YAKxCE,OAAA,CAAAC,GAAA,CAAY,qBAAqB,EAAA;gBAAAC,SAAA,2HAAAjB,kBAAA,CAAAkB,MAAA;gBAAAC,WAAA,EAElBN,wBAAwB,CAAAK,MAAA;gBAAAE,UAAA,EACzBN,mBAAmB,CAAAI,MAAAA;YAAA,CAChC,CAAC;QAAA;QACDN,EAAA,GAAA;YAACH,YAAY;YAAEC,cAAc;SAAA;QAACN,CAAA,CAAA,EAAA,GAAAM,cAAA;QAAAN,CAAA,CAAA,EAAA,GAAAK,YAAA;QAAAL,CAAA,CAAA,EAAA,GAAAO,EAAA;QAAAP,CAAA,CAAA,EAAA,GAAAQ,EAAA;IAAA,OAAA;QAAAD,EAAA,GAAAP,CAAA,CAAA,EAAA;QAAAQ,EAAA,GAAAR,CAAA,CAAA,EAAA;IAAA;sKAhBjCN,YAAAA,AAAA,EAAUa,EAgBT,EAAEC,EAA8B,CAAC;AAAA;GAnB7BT,cAAA;;uIACoCJ,aAAA,CAAW,CAAC", "ignoreList": [], "debugId": null}}, {"offset": {"line": 1276, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/app/%28dashboard%29/dashboard/G%3A/Augment%20code/app/%28dashboard%29/dashboard/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState } from \"react\"\nimport { useSession } from \"next-auth/react\"\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { DepartmentCard } from \"@/components/dashboard/department-card\"\nimport { Department, Employee } from \"@/lib/types\"\nimport { useHRStore } from \"@/lib/store/hr-store\"\nimport { useSampleData } from \"@/lib/hooks/use-sample-data\"\nimport {\n  Users,\n  Building2,\n  UserPlus,\n  Archive,\n  TrendingUp,\n  Download,\n  Activity\n} from \"lucide-react\"\n\nexport default function DashboardPage() {\n  const { data: session } = useSession()\n  const { departments, employees, freeBucket } = useHRStore()\n  const [isLoading, setIsLoading] = useState(true)\n\n  // Load sample data\n  useSampleData()\n\n  useEffect(() => {\n    // Simulate loading data\n    const loadData = async () => {\n      try {\n        // Sample data is loaded via useSampleData hook\n        await new Promise(resolve => setTimeout(resolve, 500))\n        setIsLoading(false)\n      } catch (error) {\n        console.error(\"Failed to load dashboard data:\", error)\n        setIsLoading(false)\n      }\n    }\n\n    loadData()\n  }, [])\n\n  const totalEmployees = employees.length\n  const totalDepartments = departments.length\n  const freeBucketCount = freeBucket.length\n  const averageUtilization = departments.length > 0 \n    ? Math.round(\n        departments.reduce((sum, dept) => {\n          const employeeCount = dept.employees?.length || 0\n          return sum + (employeeCount / dept.capacity) * 100\n        }, 0) / departments.length\n      )\n    : 0\n\n  const recentActivity = [\n    { action: \"Employee transferred\", details: \"John Doe moved to Engineering\", time: \"2 hours ago\" },\n    { action: \"New employee added\", details: \"Jane Smith joined Marketing\", time: \"4 hours ago\" },\n    { action: \"Department created\", details: \"Research & Development\", time: \"1 day ago\" },\n  ]\n\n  if (isLoading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n          {[...Array(4)].map((_, i) => (\n            <Card key={i} className=\"animate-pulse\">\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <div className=\"h-4 bg-muted rounded w-20\"></div>\n                <div className=\"h-4 w-4 bg-muted rounded\"></div>\n              </CardHeader>\n              <CardContent>\n                <div className=\"h-8 bg-muted rounded w-16 mb-2\"></div>\n                <div className=\"h-3 bg-muted rounded w-24\"></div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-8 p-8 bg-gradient-to-br from-slate-50/50 to-white min-h-screen\">\n      {/* Welcome Section */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"space-y-2\">\n          <h1 className=\"text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n            Dashboard\n          </h1>\n          <p className=\"text-lg text-muted-foreground\">\n            Welcome back, {session?.user?.name}. Here's what's happening with your organization.\n          </p>\n        </div>\n\n        <div className=\"flex gap-3\">\n          <Button variant=\"outline\" className=\"shadow-soft hover-lift border-2 border-border/50\">\n            <Download className=\"h-2 w-2 mr-2\" />\n            Export Report\n          </Button>\n          <Button className=\"bg-gradient-primary hover:opacity-90 shadow-medium hover-lift\">\n            <UserPlus className=\"h-4 w-4 mr-2\" />\n            Add Employee\n          </Button>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-4\">\n        <Card className=\"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 bg-gradient-to-br from-blue-50 to-blue-100/50 border-l-4 border-l-blue-500\">\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3\">\n            <CardTitle className=\"text-sm font-semibold text-blue-700\">Total Employees</CardTitle>\n            <div className=\"w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center shadow-lg\">\n              <Users className=\"h-5 w-5 text-white\" />\n            </div>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-3xl font-bold text-blue-900\">{totalEmployees}</div>\n            <p className=\"text-sm text-blue-600 font-medium mt-1\">\n              +12% from last month\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card className=\"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 bg-gradient-to-br from-purple-50 to-purple-100/50 border-l-4 border-l-purple-500\">\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3\">\n            <CardTitle className=\"text-sm font-semibold text-purple-700\">Departments</CardTitle>\n            <div className=\"w-10 h-10 bg-purple-500 rounded-xl flex items-center justify-center shadow-lg\">\n              <Building2 className=\"h-5 w-5 text-white\" />\n            </div>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-3xl font-bold text-purple-900\">{totalDepartments}</div>\n            <p className=\"text-sm text-purple-600 font-medium mt-1\">\n              Active departments\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card className=\"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 bg-gradient-to-br from-orange-50 to-orange-100/50 border-l-4 border-l-orange-500\">\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3\">\n            <CardTitle className=\"text-sm font-semibold text-orange-700\">Free Bucket</CardTitle>\n            <div className=\"w-10 h-10 bg-orange-500 rounded-xl flex items-center justify-center shadow-lg\">\n              <Archive className=\"h-5 w-5 text-white\" />\n            </div>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-3xl font-bold text-orange-900\">{freeBucketCount}</div>\n            <p className=\"text-sm text-orange-600 font-medium mt-1\">\n              Unassigned employees\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card className=\"shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-0 bg-gradient-to-br from-green-50 to-green-100/50 border-l-4 border-l-green-500\">\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-3\">\n            <CardTitle className=\"text-sm font-semibold text-green-700\">Avg. Utilization</CardTitle>\n            <div className=\"w-10 h-10 bg-green-500 rounded-xl flex items-center justify-center shadow-lg\">\n              <TrendingUp className=\"h-5 w-5 text-white\" />\n            </div>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-3xl font-bold text-green-900\">{averageUtilization}%</div>\n            <p className=\"text-sm text-green-600 font-medium mt-1\">\n              Department capacity\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Department Overview */}\n      <div className=\"grid gap-8 lg:grid-cols-3\">\n        <div className=\"lg:col-span-2\">\n          <Card className=\"shadow-xl border-0 bg-white/80 backdrop-blur-sm\">\n            <CardHeader className=\"pb-6\">\n              <CardTitle className=\"text-2xl font-bold text-foreground flex items-center gap-3\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg\">\n                  <Building2 className=\"h-4 w-4 text-white\" />\n                </div>\n                Department Overview\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"grid gap-6 md:grid-cols-2\">\n                {departments.slice(0, 6).map((department) => (\n                  <DepartmentCard\n                    key={department.id}\n                    department={department}\n                    onAddEmployee={() => {\n                      // TODO: Implement add employee to department\n                      console.log(\"Add employee to\", department.name)\n                    }}\n                    onViewDetails={() => {\n                      // TODO: Navigate to department details\n                      console.log(\"View details for\", department.name)\n                    }}\n                  />\n                ))}\n              </div>\n              \n              {departments.length > 6 && (\n                <div className=\"mt-6 text-center\">\n                  <Button variant=\"outline\" className=\"shadow-soft hover-lift border-2 border-border/50\">\n                    View All Departments ({departments.length})\n                  </Button>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Recent Activity */}\n        <div>\n          <Card className=\"shadow-xl border-0 bg-white/80 backdrop-blur-sm\">\n            <CardHeader className=\"pb-6\">\n              <CardTitle className=\"text-xl font-bold text-foreground flex items-center gap-3\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-lg flex items-center justify-center shadow-lg\">\n                  <Activity className=\"h-4 w-4 text-white\" />\n                </div>\n                Recent Activity\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                {recentActivity.map((activity, index) => (\n                  <div key={index} className=\"flex items-start space-x-4 p-4 rounded-xl bg-gradient-to-r from-slate-50 to-white border border-border/50 hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5\">\n                    <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg flex-shrink-0\">\n                      <div className=\"w-2 h-2 bg-white rounded-full\"></div>\n                    </div>\n                    <div className=\"flex-1 space-y-2\">\n                      <div className=\"text-sm font-semibold text-foreground\">{activity.action}</div>\n                      <div className=\"text-sm text-muted-foreground\">\n                        {activity.details}\n                      </div>\n                      <div className=\"text-xs text-muted-foreground font-medium\">\n                        {activity.time}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": ["c", "_c", "useEffect", "useState", "useSession", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON>", "DepartmentCard", "useHRStore", "useSampleData", "Users", "Building2", "UserPlus", "Archive", "TrendingUp", "Download", "Activity", "DashboardPage", "$", "$i", "Symbol", "for", "data", "session", "departments", "employees", "freeBucket", "isLoading", "setIsLoading", "t0", "t1", "loadData", "Promise", "_temp", "t2", "error", "console", "totalEmployees", "length", "totalDepartments", "freeBucketCount", "Math", "round", "reduce", "_temp2", "averageUtilization", "t3", "action", "details", "time", "recentActivity", "t4", "Array", "map", "_temp3", "t5", "user", "name", "t6", "t7", "t8", "t9", "t10", "t11", "t12", "t13", "t14", "t15", "t16", "t17", "t18", "t19", "t20", "t21", "t22", "t23", "t24", "t25", "t26", "t27", "t28", "t29", "t30", "t31", "t32", "slice", "_temp4", "t33", "t34", "t35", "t36", "t37", "_temp5", "t38", "t39", "activity", "index", "department", "id", "log", "_", "i", "sum", "dept", "employeeCount", "capacity", "resolve", "setTimeout"], "mappings": ";;;;AAAY,SAAAA,CAAA,IAAAC,EAAA;AAEZ,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,sBAAsB;AAC/E,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SAASC,cAAc,QAAQ,wCAAwC;AAEvE,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,aAAa,QAAQ,6BAA6B;;;;;;;AAC3D,SACEC,KAAK,EACLC,SAAS,EACTC,QAAQ,EACRC,OAAO,EACPC,UAAU,EACVC,QAAQ,EACRC,QAAQ,QACH,cAAc;;;AAlBrB,YAAY;;;;;;;;;;AAoBG;;;IAAA,MAAAE,CAAA,mLAAApB,IAAAA,AAAA,EAAA;IAAA,IAAAoB,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IACb,MAAA,EAAAI,IAAA,EAAAC,OAAAA,EAAA,GAA0BtB,mKAAAA;IAC1B,MAAA,EAAAuB,WAAA,EAAAC,SAAA,EAAAC,UAAAA,EAAA,mJAA+ClB;IAC/C,MAAA,CAAAmB,SAAA,EAAAC,YAAA,CAAA,qKAAkC5B,WAAAA,AAAA,EAAA,IAAa,CAAC;iKAGhDS;IAAe,IAAAoB,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAZ,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAELQ,EAAA,GAAAA,CAAA;YAER,MAAAE,QAAA,GAAA,MAAAA,CAAA;;gBAAA,IAAA;oBAAA,MAAA,IAAAC,OAAA,CAAAC,KAAA;oBAIIL,YAAY,CAAA,KAAM,CAAC;gBAAA,EAAA,OAAAM,EAAA,EAAA;oBACZC,KAAA,CAAAA,KAAA,CAAAA,CAAA,CAAAA,EAAK;oBACZC,OAAA,CAAAD,KAAA,CAAc,gCAAgC,EAAEA,KAAK,CAAC;oBACtDP,YAAY,CAAA,KAAM,CAAC;gBAAA;YAAA;YAIvBG,QAAQ,CAAC,CAAC;QAAA;QACTD,EAAA,GAAA,EAAA;QAAEZ,CAAA,CAAA,EAAA,GAAAW,EAAA;QAAAX,CAAA,CAAA,EAAA,GAAAY,EAAA;IAAA,OAAA;QAAAD,EAAA,GAAAX,CAAA,CAAA,EAAA;QAAAY,EAAA,GAAAZ,CAAA,CAAA,EAAA;IAAA;qKAdLnB,aAAAA,AAAA,EAAU8B,EAcT,EAAEC,EAAE,CAAC;IAEN,MAAAO,cAAA,GAAuBZ,SAAS,CAAAa,MAAA;IAChC,MAAAC,gBAAA,GAAyBf,WAAW,CAAAc,MAAA;IACpC,MAAAE,eAAA,GAAwBd,UAAU,CAAAY,MAAA;IAAO,IAAAJ,EAAA;IAAA,IAAAhB,CAAA,CAAA,EAAA,KAAAM,WAAA,EAAA;QACdU,EAAA,GAAAV,WAAW,CAAAc,MAAA,GAAA,CAAW,GAC7CG,IAAA,CAAAC,KAAA,CACElB,WAAW,CAAAmB,MAAA,CAAAC,MAAA,EAAA,CAGP,CAAC,GAAGpB,WAAW,CAAAc,MACrB,CAAC,GAAA,CACA;QAAApB,CAAA,CAAA,EAAA,GAAAM,WAAA;QAAAN,CAAA,CAAA,EAAA,GAAAgB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAhB,CAAA,CAAA,EAAA;IAAA;IAPL,MAAA2B,kBAAA,GAA2BX,EAOtB;IAAA,IAAAY,EAAA;IAAA,IAAA5B,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAEkByB,EAAA,GAAA;YAAA;gBAAAC,MAAA,EACX,sBAAsB;gBAAAC,OAAA,EAAW,+BAA+B;gBAAAC,IAAA,EAAQ;YAAa;YAAA;gBAAAF,MAAA,EACrF,oBAAoB;gBAAAC,OAAA,EAAW,6BAA6B;gBAAAC,IAAA,EAAQ;YAAa;YAAA;gBAAAF,MAAA,EACjF,oBAAoB;gBAAAC,OAAA,EAAW,wBAAwB;gBAAAC,IAAA,EAAQ;YAAW;SAAA;QACrF/B,CAAA,CAAA,EAAA,GAAA4B,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAA5B,CAAA,CAAA,EAAA;IAAA;IAJD,MAAAgC,cAAA,GAAuBJ,EAItB;IAAA,IAEGnB,SAAS,EAAA;QAAA,IAAAwB,EAAA;QAAA,IAAAjC,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;YAET8B,EAAA,iBAAA,6LAAA,GAeM;gBAfS,SAAW,EAAX,WAAW;wCACxB,6LAAA,GAaM;oBAbS,SAA0C,EAA1C,0CAA0C,CACtD;8BAAA;2BAAIC,KAAA,CAAA,CAAO,CAAC;qBAAA,CAAAC,GAAA,CAAAC,MAWZ,EACH,EAbA,GAaM,CACR,EAfA,GAeM;;;;;;;;;;;YAAApC,CAAA,CAAA,EAAA,GAAAiC,EAAA;QAAA,OAAA;YAAAA,EAAA,GAAAjC,CAAA,CAAA,EAAA;QAAA;QAAA,OAfNiC,EAeM;IAAA;IAAA,IAAAA,EAAA;IAAA,IAAAjC,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QASF8B,EAAA,iBAAA,6LAAA,EAEK;YAFS,SAA+F,EAA/F,+FAA+F;sBAAC,SAE9G,EAFA,EAEK;;;;;;QAAAjC,CAAA,CAAA,EAAA,GAAAiC,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAjC,CAAA,CAAA,EAAA;IAAA;IAEY,MAAAqC,EAAA,sDAAAhC,OAAO,iBAAAiC,IAAA,gEAAAC,IAAA;IAAY,IAAAC,EAAA;IAAA,IAAAxC,CAAA,CAAA,EAAA,KAAAqC,EAAA,EAAA;QALtCG,EAAA,iBAAA,6LAAA,GAOM;YAPS,SAAW,EAAX,WAAW,CACxB;;gBAAAP,EAEI;8BACJ,6LAAA,CAEI;oBAFS,SAA+B,EAA/B,+BAA+B;;wBAAC,cAC5B;wBAAAI,EAAkB;wBAAE,iDACrC,EAFA,CAEI,CACN,EAPA,GAOM;;;;;;;;;;;;;QAAArC,CAAA,CAAA,EAAA,GAAAqC,EAAA;QAAArC,CAAA,CAAA,EAAA,GAAAwC,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAxC,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAyC,EAAA;IAAA,IAAAzC,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAGJsC,EAAA,iBAAA,4TAAC,SAAM;YAAS,OAAS,EAAT,SAAS;YAAW,SAAkD,EAAlD,kDAAkD;;8BACpF,2YAAC,WAAQ;oBAAW,SAAc,EAAd,cAAc;;;;;;gBAAG,aAEvC,EAHC,MAAM,CAGE;;;;;;;QAAAzC,CAAA,CAAA,GAAA,GAAAyC,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAzC,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA0C,EAAA;IAAA,IAAA1C,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAJXuC,EAAA,iBAAA,6LAAA,GASM;YATS,SAAY,EAAZ,YAAY,CACzB;;gBAAAD,EAGQ;8BACR,4TAAC,SAAM;oBAAW,SAA+D,EAA/D,+DAA+D;;sCAC/E,8YAAC,YAAQ;4BAAW,SAAc,EAAd,cAAc;;;;;;wBAAG,YAEvC,EAHC,MAAM,CAIT,EATA,GASM;;;;;;;;;;;;;QAAAzC,CAAA,CAAA,GAAA,GAAA0C,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAA1C,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA2C,EAAA;IAAA,IAAA3C,CAAA,CAAA,GAAA,KAAAwC,EAAA,EAAA;QAnBRG,EAAA,iBAAA,6LAAA,GAoBM;YApBS,SAAmC,EAAnC,mCAAmC,CAChD;;gBAAAH,EAOK,CAEL;gBAAAE,EASK,CACP,EApBA,GAoBM;;;;;;;QAAA1C,CAAA,CAAA,GAAA,GAAAwC,EAAA;QAAAxC,CAAA,CAAA,GAAA,GAAA2C,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAA3C,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA4C,GAAA;IAAA,IAAA5C,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAMAyC,GAAA,iBAAA,yTAAC,aAAS;YAAW,SAAqC,EAArC,qCAAqC;sBAAC,eAAe,EAAzE,SAAS,CAA4E;;;;;;QAAA5C,CAAA,CAAA,GAAA,GAAA4C,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA5C,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA6C,GAAA;IAAA,IAAA7C,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QADxF0C,GAAA,iBAAA,0TAAC,aAAU;YAAW,SAA2D,EAA3D,2DAA2D,CAC/E;;gBAAAD,GAAqF;8BACrF,6LAAA,GAEM;oBAFS,SAA6E,EAA7E,6EAA6E;4CAC1F,qYAAC,QAAK;wBAAW,SAAoB,EAApB,oBAAoB,GACvC,EAFA,GAEM,CACR,EALC,UAAU,CAKE;;;;;;;;;;;;;;;;;QAAA5C,CAAA,CAAA,GAAA,GAAA6C,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA7C,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA8C,GAAA;IAAA,IAAA9C,CAAA,CAAA,GAAA,KAAAmB,cAAA,EAAA;QAEX2B,GAAA,iBAAA,6LAAA,GAAwE;YAAzD,SAAkC,EAAlC,kCAAkC,CAAE3B;sBAAAA,cAAa,CAAE,EAAlE,GAAwE;;;;;;QAAAnB,CAAA,CAAA,GAAA,GAAAmB,cAAA;QAAAnB,CAAA,CAAA,GAAA,GAAA8C,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA9C,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA+C,GAAA;IAAA,IAAA/C,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QACxE4C,GAAA,iBAAA,6LAAA,CAEI;YAFS,SAAwC,EAAxC,wCAAwC;sBAAC,oBAEtD,EAFA,CAEI;;;;;;QAAA/C,CAAA,CAAA,GAAA,GAAA+C,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA/C,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAgD,GAAA;IAAA,IAAAhD,CAAA,CAAA,GAAA,KAAA8C,GAAA,EAAA;QAXRE,GAAA,iBAAA,6LAAC,oIAAI;YAAW,SAAgK,EAAhK,gKAAgK,CAC9K;;gBAAAH,GAKY;8BACZ,0TAAC,cAAW,CACV;;wBAAAC,GAAuE,CACvE;wBAAAC,GAEG,CACL,EALC,WAAW,CAMd,EAbC,IAAI,CAaE;;;;;;;;;;;;;QAAA/C,CAAA,CAAA,GAAA,GAAA8C,GAAA;QAAA9C,CAAA,CAAA,GAAA,GAAAgD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAhD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAiD,GAAA;IAAA,IAAAjD,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAIH8C,GAAA,iBAAA,6LAAC,yIAAS;YAAW,SAAuC,EAAvC,uCAAuC;sBAAC,WAAW,EAAvE,SAAS,CAA0E;;;;;;QAAAjD,CAAA,CAAA,GAAA,GAAAiD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAjD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAkD,GAAA;IAAA,IAAAlD,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QADtF+C,GAAA,iBAAA,0TAAC,aAAU;YAAW,SAA2D,EAA3D,2DAA2D,CAC/E;;gBAAAD,GAAmF;8BACnF,6LAAA,GAEM;oBAFS,SAA+E,EAA/E,+EAA+E;8BAC5F,+ZAAC,YAAS;wBAAW,SAAoB,EAApB,oBAAoB,GAC3C,EAFA,GAEM,CACR,EALC,UAAU,CAKE;;;;;;;;;;;;;;;;;QAAAjD,CAAA,CAAA,GAAA,GAAAkD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAlD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAmD,GAAA;IAAA,IAAAnD,CAAA,CAAA,GAAA,KAAAqB,gBAAA,EAAA;QAEX8B,GAAA,iBAAA,6LAAA,GAA4E;YAA7D,SAAoC,EAApC,oCAAoC,CAAE9B;sBAAAA,gBAAe,CAAE,EAAtE,GAA4E;;;;;;QAAArB,CAAA,CAAA,GAAA,GAAAqB,gBAAA;QAAArB,CAAA,CAAA,GAAA,GAAAmD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAnD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAoD,GAAA;IAAA,IAAApD,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAC5EiD,GAAA,iBAAA,6LAAA,CAEI;YAFS,SAA0C,EAA1C,0CAA0C;sBAAC,kBAExD,EAFA,CAEI;;;;;;QAAApD,CAAA,CAAA,GAAA,GAAAoD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAApD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAqD,GAAA;IAAA,IAAArD,CAAA,CAAA,GAAA,KAAAmD,GAAA,EAAA;QAXRE,GAAA,iBAAA,0TAAC,OAAI;YAAW,SAAsK,EAAtK,sKAAsK,CACpL;;gBAAAH,GAKY;8BACZ,0TAAC,cAAW,CACV;;wBAAAC,GAA2E,CAC3E;wBAAAC,GAEG,CACL,EALC,WAAW,CAMd,EAbC,IAAI,CAaE;;;;;;;;;;;;;QAAApD,CAAA,CAAA,GAAA,GAAAmD,GAAA;QAAAnD,CAAA,CAAA,GAAA,GAAAqD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAArD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAsD,GAAA;IAAA,IAAAtD,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAIHmD,GAAA,iBAAA,6LAAC,yIAAS;YAAW,SAAuC,EAAvC,uCAAuC;sBAAC,WAAW,EAAvE,SAAS,CAA0E;;;;;;QAAAtD,CAAA,CAAA,GAAA,GAAAsD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAtD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAuD,GAAA;IAAA,IAAAvD,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QADtFoD,GAAA,iBAAA,0TAAC,aAAU;YAAW,SAA2D,EAA3D,2DAA2D,CAC/E;;gBAAAD,GAAmF;8BACnF,6LAAA,GAEM;oBAFS,SAA+E,EAA/E,+EAA+E;4CAC5F,yYAAC,UAAO;wBAAW,SAAoB,EAApB,oBAAoB,GACzC,EAFA,GAEM,CACR,EALC,UAAU,CAKE;;;;;;;;;;;;;;;;;QAAAtD,CAAA,CAAA,GAAA,GAAAuD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAvD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAwD,GAAA;IAAA,IAAAxD,CAAA,CAAA,GAAA,KAAAsB,eAAA,EAAA;QAEXkC,GAAA,iBAAA,6LAAA,GAA2E;YAA5D,SAAoC,EAApC,oCAAoC,CAAElC;sBAAAA,eAAc,CAAE,EAArE,GAA2E;;;;;;QAAAtB,CAAA,CAAA,GAAA,GAAAsB,eAAA;QAAAtB,CAAA,CAAA,GAAA,GAAAwD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAxD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAyD,GAAA;IAAA,IAAAzD,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAC3EsD,GAAA,iBAAA,6LAAA,CAEI;YAFS,SAA0C,EAA1C,0CAA0C;sBAAC,oBAExD,EAFA,CAEI;;;;;;QAAAzD,CAAA,CAAA,GAAA,GAAAyD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAzD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA0D,GAAA;IAAA,IAAA1D,CAAA,CAAA,GAAA,KAAAwD,GAAA,EAAA;QAXRE,GAAA,iBAAA,0TAAC,OAAI;YAAW,SAAsK,EAAtK,sKAAsK,CACpL;;gBAAAH,GAKY;8BACZ,0TAAC,cAAW,CACV;;wBAAAC,GAA0E,CAC1E;wBAAAC,GAEG,CACL,EALC,WAAW,CAMd,EAbC,IAAI,CAaE;;;;;;;;;;;;;QAAAzD,CAAA,CAAA,GAAA,GAAAwD,GAAA;QAAAxD,CAAA,CAAA,GAAA,GAAA0D,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA1D,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA2D,GAAA;IAAA,IAAA3D,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAIHwD,GAAA,iBAAA,0TAAC,YAAS;YAAW,SAAsC,EAAtC,sCAAsC;sBAAC,gBAAgB,EAA3E,SAAS,CAA8E;;;;;;QAAA3D,CAAA,CAAA,GAAA,GAAA2D,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA3D,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA4D,GAAA;IAAA,IAAA5D,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAD1FyD,GAAA,iBAAA,0TAAC,aAAU;YAAW,SAA2D,EAA3D,2DAA2D,CAC/E;;gBAAAD,GAAuF;8BACvF,6LAAA,GAEM;oBAFS,SAA8E,EAA9E,8EAA8E;4CAC3F,mZAAC,aAAU;wBAAW,SAAoB,EAApB,oBAAoB,GAC5C,EAFA,GAEM,CACR,EALC,UAAU,CAKE;;;;;;;;;;;;;;;;;QAAA3D,CAAA,CAAA,GAAA,GAAA4D,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA5D,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA6D,GAAA;IAAA,IAAA7D,CAAA,CAAA,GAAA,KAAA2B,kBAAA,EAAA;QAEXkC,GAAA,iBAAA,6LAAA,GAA8E;YAA/D,SAAmC,EAAnC,mCAAmC,CAAElC;;gBAAAA,kBAAiB;gBAAE,CAAC,EAAxE,GAA8E;;;;;;;QAAA3B,CAAA,CAAA,GAAA,GAAA2B,kBAAA;QAAA3B,CAAA,CAAA,GAAA,GAAA6D,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA7D,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA8D,GAAA;IAAA,IAAA9D,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAC9E2D,GAAA,iBAAA,6LAAA,CAEI;YAFS,SAAyC,EAAzC,yCAAyC;sBAAC,mBAEvD,EAFA,CAEI;;;;;;QAAA9D,CAAA,CAAA,GAAA,GAAA8D,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA9D,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA+D,GAAA;IAAA,IAAA/D,CAAA,CAAA,GAAA,KAAA6D,GAAA,EAAA;QAXRE,GAAA,iBAAA,0TAAC,OAAI;YAAW,SAAmK,EAAnK,mKAAmK,CACjL;;gBAAAH,GAKY;8BACZ,0TAAC,cAAW,CACV;;wBAAAC,GAA6E,CAC7E;wBAAAC,GAEG,CACL,EALC,WAAW,CAMd,EAbC,IAAI,CAaE;;;;;;;;;;;;;QAAA9D,CAAA,CAAA,GAAA,GAAA6D,GAAA;QAAA7D,CAAA,CAAA,GAAA,GAAA+D,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA/D,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAgE,GAAA;IAAA,IAAAhE,CAAA,CAAA,GAAA,KAAAgD,GAAA,IAAAhD,CAAA,CAAA,GAAA,KAAAqD,GAAA,IAAArD,CAAA,CAAA,GAAA,KAAA0D,GAAA,IAAA1D,CAAA,CAAA,GAAA,KAAA+D,GAAA,EAAA;QA3DTC,GAAA,iBAAA,6LAAA,GA4DM;YA5DS,SAA0C,EAA1C,0CAA0C,CACvD;;gBAAAhB,GAaM,CAEN;gBAAAK,GAaM,CAEN;gBAAAK,GAaM,CAEN;gBAAAK,GAaM,CACR,EA5DA,GA4DM;;;;;;;QAAA/D,CAAA,CAAA,GAAA,GAAAgD,GAAA;QAAAhD,CAAA,CAAA,GAAA,GAAAqD,GAAA;QAAArD,CAAA,CAAA,GAAA,GAAA0D,GAAA;QAAA1D,CAAA,CAAA,GAAA,GAAA+D,GAAA;QAAA/D,CAAA,CAAA,GAAA,GAAAgE,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAhE,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAiE,GAAA;IAAA,IAAAjE,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAMA8D,GAAA,iBAAA,0TAAC,aAAU;YAAW,SAAM,EAAN,MAAM;oCAC1B,yTAAC,aAAS;gBAAW,SAA4D,EAA5D,4DAA4D;;kCAC/E,6LAAA,GAEM;wBAFS,SAA6G,EAA7G,6GAA6G;gDAC1H,gZAAC,aAAS;4BAAW,SAAoB,EAApB,oBAAoB,GAC3C,EAFA,GAEM;;;;;;;;;;;oBAAA,mBAER,EALC,SAAS,CAMZ,EAPC,UAAU,CAOE;;;;;;;;;;;;QAAAjE,CAAA,CAAA,GAAA,GAAAiE,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAjE,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAkE,GAAA;IAAA,IAAAlE,CAAA,CAAA,GAAA,KAAAM,WAAA,EAAA;QAGR4D,GAAA,GAAA5D,WAAW,CAAA6D,KAAA,CAAA,GAAA,CAAW,CAAC,CAAAhC,GAAA,CAAAiC,MAavB,CAAC;QAAApE,CAAA,CAAA,GAAA,GAAAM,WAAA;QAAAN,CAAA,CAAA,GAAA,GAAAkE,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAlE,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAqE,GAAA;IAAA,IAAArE,CAAA,CAAA,GAAA,KAAAkE,GAAA,EAAA;QAdJG,GAAA,iBAAA,6LAAA,GAeM;YAfS,SAA2B,EAA3B,2BAA2B,CACvC;sBAAAH,GAaA,CACH,EAfA,GAeM;;;;;;QAAAlE,CAAA,CAAA,GAAA,GAAAkE,GAAA;QAAAlE,CAAA,CAAA,GAAA,GAAAqE,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAArE,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAsE,GAAA;IAAA,IAAAtE,CAAA,CAAA,GAAA,KAAAM,WAAA,CAAAc,MAAA,EAAA;QAELkD,GAAA,GAAAhE,WAAW,CAAAc,MAAA,GAAA,CAAW,kBACrB,6LAAA,GAIM;YAJS,SAAkB,EAAlB,kBAAkB;oCAC/B,4TAAC,SAAM;gBAAS,OAAS,EAAT,SAAS;gBAAW,SAAkD,EAAlD,kDAAkD;;oBAAC,sBAC9D;oBAAAd,WAAW,CAAAc,MAAM;oBAAE,CAC5C,EAFC,MAAM,CAGT,EAJA,GAIM,CACP;;;;;;;;;;;;QAAApB,CAAA,CAAA,GAAA,GAAAM,WAAA,CAAAc,MAAA;QAAApB,CAAA,CAAA,GAAA,GAAAsE,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAtE,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAuE,GAAA;IAAA,IAAAvE,CAAA,CAAA,GAAA,KAAAqE,GAAA,IAAArE,CAAA,CAAA,GAAA,KAAAsE,GAAA,EAAA;QAlCPC,GAAA,iBAAA,6LAAA,GAqCM;YArCS,SAAe,EAAf,eAAe;oCAC5B,0TAAC,OAAI;gBAAW,SAAiD,EAAjD,iDAAiD,CAC/D;;oBAAAN,GAOY;kCACZ,0TAAC,cAAW,CACV;;4BAAAI,GAeK,CAEJ;4BAAAC,GAMD,CACF,EAzBC,WAAW,CA0Bd,EAnCC,IAAI,CAoCP,EArCA,GAqCM;;;;;;;;;;;;;;;;;;QAAAtE,CAAA,CAAA,GAAA,GAAAqE,GAAA;QAAArE,CAAA,CAAA,GAAA,GAAAsE,GAAA;QAAAtE,CAAA,CAAA,GAAA,GAAAuE,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAvE,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAwE,GAAA;IAAA,IAAAxE,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAKFqE,GAAA,iBAAA,0TAAC,aAAU;YAAW,SAAM,EAAN,MAAM;oCAC1B,yTAAC,aAAS;gBAAW,SAA2D,EAA3D,2DAA2D;;kCAC9E,6LAAA,GAEM;wBAFS,SAA2G,EAA3G,2GAA2G;gDACxH,2YAAC,WAAQ;4BAAW,SAAoB,EAApB,oBAAoB,GAC1C,EAFA,GAEM;;;;;;;;;;;oBAAA,eAER,EALC,SAAS,CAMZ,EAPC,UAAU,CAOE;;;;;;;;;;;;QAAAxE,CAAA,CAAA,GAAA,GAAAwE,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAxE,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAyE,GAAA;IAAA,IAAAzE,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QATjBsE,GAAA,iBAAA,6LAAA,GA+BM;oCA9BJ,0TAAC,OAAI;gBAAW,SAAiD,EAAjD,iDAAiD,CAC/D;;oBAAAD,GAOY;kCACZ,0TAAC,cAAW;gDACV,6LAAA,GAiBM;4BAjBS,SAAW,EAAX,WAAW,CACvB;sCAAAxC,cAAc,CAAAG,GAAA,CAAAuC,MAed,EACH,EAjBA,GAiBM,CACR,EAnBC,WAAW,CAoBd,EA7BC,IAAI,CA8BP,EA/BA,GA+BM;;;;;;;;;;;;;;;;;;;;;;QAAA1E,CAAA,CAAA,GAAA,GAAAyE,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAzE,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA2E,GAAA;IAAA,IAAA3E,CAAA,CAAA,GAAA,KAAAuE,GAAA,EAAA;QAxERI,GAAA,iBAAA,6LAAA,GAyEM;YAzES,SAA2B,EAA3B,2BAA2B,CACxC;;gBAAAJ,GAqCK,CAGL;gBAAAE,GA+BK,CACP,EAzEA,GAyEM;;;;;;;QAAAzE,CAAA,CAAA,GAAA,GAAAuE,GAAA;QAAAvE,CAAA,CAAA,GAAA,GAAA2E,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA3E,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA4E,GAAA;IAAA,IAAA5E,CAAA,CAAA,GAAA,KAAAgE,GAAA,IAAAhE,CAAA,CAAA,GAAA,KAAA2E,GAAA,IAAA3E,CAAA,CAAA,GAAA,KAAA2C,EAAA,EAAA;QAjKRiC,GAAA,iBAAA,6LAAA,GAkKM;YAlKS,SAAwE,EAAxE,wEAAwE,CAErF;;gBAAAjC,EAoBK,CAGL;gBAAAqB,GA4DK,CAGL;gBAAAW,GAyEK,CACP,EAlKA,GAkKM;;;;;;;QAAA3E,CAAA,CAAA,GAAA,GAAAgE,GAAA;QAAAhE,CAAA,CAAA,GAAA,GAAA2E,GAAA;QAAA3E,CAAA,CAAA,GAAA,GAAA2C,EAAA;QAAA3C,CAAA,CAAA,GAAA,GAAA4E,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA5E,CAAA,CAAA,GAAA;IAAA;IAAA,OAlKN4E,GAkKM;AAAA;;;uKAjOkB,CAAW,CAAC;uIACStF,aAAA,CAAW,CAAC;iJAI3DC,gBAAA,CAAc,CAAC;;;KANFQ,cAAA;AAAA,SAAA2E,OAAAG,QAAA,EAAAC,KAAA;IAAA,qBA8MG,6LAAA,GAaM,CAbIA,GAAK,CAALA;QAAiB,SAA8K,EAA9K,8KAA8K;;0BACvM,6LAAA,GAEM;gBAFS,SAA+H,EAA/H,+HAA+H;wCAC5I,6LAAA,GAAqD;oBAAtC,SAA+B,EAA/B,+BAA+B,GAChD,EAFA,GAEM;;;;;;;;;;;0BACN,6LAAA,GAQM;gBARS,SAAkB,EAAlB,kBAAkB;;kCAC/B,6LAAA,GAA8E;wBAA/D,SAAuC,EAAvC,uCAAuC,CAAE;kCAAAD,QAAQ,CAAAhD,MAAM,CAAE,EAAxE,GAA8E;;;;;;kCAC9E,6LAAA,GAEM;wBAFS,SAA+B,EAA/B,+BAA+B,CAC3C;kCAAAgD,QAAQ,CAAA/C,OAAO,CAClB,EAFA,GAEM;;;;;;kCACN,6LAAA,GAEM;wBAFS,SAA2C,EAA3C,2CAA2C,CACvD;kCAAA+C,QAAQ,CAAA9C,IAAI,CACf,EAFA,GAEM,CACR,EARA,GAQM,CACR,EAbA,GAaM;;;;;;;;;;;;;OAbI+C,KAAI,CAAC;;;;;AAaT;AA3NT,SAAAV,OAAAW,UAAA;IAAA,qBAsKG,+UAAC,iBAAc,CACR,GAAa,CAAb;QACOA,UAAU,CAAVA,CAAAA,UAAS,CAAC;QACP,aAGd,CAHc,CAAA;YAEb7D,OAAA,CAAA+D,GAAA,CAAY,iBAAiB,EAAEF,UAAU,CAAAxC,IAAK,CAAC;QAAA,CACjD,CAAC;QACc,aAGd,CAHc,CAAA;YAEbrB,OAAA,CAAA+D,GAAA,CAAY,kBAAkB,EAAEF,UAAU,CAAAxC,IAAK,CAAC;QAAA,CAClD,CAAC,GACD;OAVKwC,UAAU,CAAAC,EAAE,CAAC;;;;;AAUlB;AAjLL,SAAA5C,OAAA8C,CAAA,EAAAC,CAAA;IAAA,qBA+CH,6LAAC,oIAAI,CAAMA,GAAC,CAADA;QAAa,SAAe,EAAf,eAAe;;0BACrC,0TAAC,aAAU;gBAAW,SAA2D,EAA3D,2DAA2D;;kCAC/E,6LAAA,GAAiD;wBAAlC,SAA2B,EAA3B,2BAA2B;;;;;;kCAC1C,6LAAA,GAAgD;wBAAjC,SAA0B,EAA1B,0BAA0B,GAC3C,EAHC,UAAU;;;;;;;;;;;;0BAIX,6LAAC,2IAAW;;kCACV,6LAAA,GAAsD;wBAAvC,SAAgC,EAAhC,gCAAgC;;;;;;kCAC/C,6LAAA,GAAiD;wBAAlC,SAA2B,EAA3B,2BAA2B,GAC5C,EAHC,WAAW,CAId,EATC,IAAI,CASE;;;;;;;;;;;;;OATIA,CAAA,CAAC;;;;;AASL;AAxDJ,SAAAzD,OAAA0D,GAAA,EAAAC,IAAA;;IA8BL,MAAAC,aAAA,4BAA0B/E,SAAA,oDAAJ8E,IAAI,YAAAjE,MAAA,KAAA,CAAuB;IAAA,OAC1CgE,GAAG,GAAIE,aAAa,GAAGD,IAAI,CAAAE,QAAS,GAAA,GAAO;AAAA;AA/B7C,SAAAxE,MAAAyE,OAAA;IAAA,OAasBC,UAAA,CAAWD,OAAO,EAAA,GAAK,CAAC;AAAA", "ignoreList": [], "debugId": null}}, {"offset": {"line": 2343, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40radix-ui/react-progress/src/progress.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Progress\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROGRESS_NAME = 'Progress';\nconst DEFAULT_MAX = 100;\n\ntype ScopedProps<P> = P & { __scopeProgress?: Scope };\nconst [createProgressContext, createProgressScope] = createContextScope(PROGRESS_NAME);\n\ntype ProgressState = 'indeterminate' | 'complete' | 'loading';\ntype ProgressContextValue = { value: number | null; max: number };\nconst [ProgressProvider, useProgressContext] =\n  createProgressContext<ProgressContextValue>(PROGRESS_NAME);\n\ntype ProgressElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ProgressProps extends PrimitiveDivProps {\n  value?: number | null | undefined;\n  max?: number;\n  getValueLabel?(value: number, max: number): string;\n}\n\nconst Progress = React.forwardRef<ProgressElement, ProgressProps>(\n  (props: ScopedProps<ProgressProps>, forwardedRef) => {\n    const {\n      __scopeProgress,\n      value: valueProp = null,\n      max: maxProp,\n      getValueLabel = defaultGetValueLabel,\n      ...progressProps\n    } = props;\n\n    if ((maxProp || maxProp === 0) && !isValidMaxNumber(maxProp)) {\n      console.error(getInvalidMaxError(`${maxProp}`, 'Progress'));\n    }\n\n    const max = isValidMaxNumber(maxProp) ? maxProp : DEFAULT_MAX;\n\n    if (valueProp !== null && !isValidValueNumber(valueProp, max)) {\n      console.error(getInvalidValueError(`${valueProp}`, 'Progress'));\n    }\n\n    const value = isValidValueNumber(valueProp, max) ? valueProp : null;\n    const valueLabel = isNumber(value) ? getValueLabel(value, max) : undefined;\n\n    return (\n      <ProgressProvider scope={__scopeProgress} value={value} max={max}>\n        <Primitive.div\n          aria-valuemax={max}\n          aria-valuemin={0}\n          aria-valuenow={isNumber(value) ? value : undefined}\n          aria-valuetext={valueLabel}\n          role=\"progressbar\"\n          data-state={getProgressState(value, max)}\n          data-value={value ?? undefined}\n          data-max={max}\n          {...progressProps}\n          ref={forwardedRef}\n        />\n      </ProgressProvider>\n    );\n  }\n);\n\nProgress.displayName = PROGRESS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ProgressIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'ProgressIndicator';\n\ntype ProgressIndicatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface ProgressIndicatorProps extends PrimitiveDivProps {}\n\nconst ProgressIndicator = React.forwardRef<ProgressIndicatorElement, ProgressIndicatorProps>(\n  (props: ScopedProps<ProgressIndicatorProps>, forwardedRef) => {\n    const { __scopeProgress, ...indicatorProps } = props;\n    const context = useProgressContext(INDICATOR_NAME, __scopeProgress);\n    return (\n      <Primitive.div\n        data-state={getProgressState(context.value, context.max)}\n        data-value={context.value ?? undefined}\n        data-max={context.max}\n        {...indicatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nProgressIndicator.displayName = INDICATOR_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction defaultGetValueLabel(value: number, max: number) {\n  return `${Math.round((value / max) * 100)}%`;\n}\n\nfunction getProgressState(value: number | undefined | null, maxValue: number): ProgressState {\n  return value == null ? 'indeterminate' : value === maxValue ? 'complete' : 'loading';\n}\n\nfunction isNumber(value: any): value is number {\n  return typeof value === 'number';\n}\n\nfunction isValidMaxNumber(max: any): max is number {\n  // prettier-ignore\n  return (\n    isNumber(max) &&\n    !isNaN(max) &&\n    max > 0\n  );\n}\n\nfunction isValidValueNumber(value: any, max: number): value is number {\n  // prettier-ignore\n  return (\n    isNumber(value) &&\n    !isNaN(value) &&\n    value <= max &&\n    value >= 0\n  );\n}\n\n// Split this out for clearer readability of the error message.\nfunction getInvalidMaxError(propValue: string, componentName: string) {\n  return `Invalid prop \\`max\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. Only numbers greater than 0 are valid max values. Defaulting to \\`${DEFAULT_MAX}\\`.`;\n}\n\nfunction getInvalidValueError(propValue: string, componentName: string) {\n  return `Invalid prop \\`value\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. The \\`value\\` prop must be:\n  - a positive number\n  - less than the value passed to \\`max\\` (or ${DEFAULT_MAX} if no \\`max\\` prop is set)\n  - \\`null\\` or \\`undefined\\` if the progress is indeterminate.\n\nDefaulting to \\`null\\`.`;\n}\n\nconst Root = Progress;\nconst Indicator = ProgressIndicator;\n\nexport {\n  createProgressScope,\n  //\n  Progress,\n  ProgressIndicator,\n  //\n  Root,\n  Indicator,\n};\nexport type { ProgressProps, ProgressIndicatorProps };\n"], "names": [], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,0BAA0B;AACnC,SAAS,iBAAiB;AAoDlB;;;;;;AA5CR,IAAM,gBAAgB;AACtB,IAAM,cAAc;AAGpB,IAAM,CAAC,uBAAuB,mBAAmB,CAAA,8KAAI,qBAAA,EAAmB,aAAa;AAIrF,IAAM,CAAC,kBAAkB,kBAAkB,CAAA,GACzC,sBAA4C,aAAa;AAU3D,IAAM,yKAAiB,aAAA,CACrB,CAAC,OAAmC,iBAAiB;IACnD,MAAM,EACJ,eAAA,EACA,OAAO,YAAY,IAAA,EACnB,KAAK,OAAA,EACL,gBAAgB,oBAAA,EAChB,GAAG,eACL,GAAI;IAEJ,IAAA,CAAK,WAAW,YAAY,CAAA,KAAM,CAAC,iBAAiB,OAAO,GAAG;QAC5D,QAAQ,KAAA,CAAM,mBAAmB,GAAU,OAAP,OAAO,GAAI,UAAU,CAAC;IAC5D;IAEA,MAAM,MAAM,iBAAiB,OAAO,IAAI,UAAU;IAElD,IAAI,cAAc,QAAQ,CAAC,mBAAmB,WAAW,GAAG,GAAG;QAC7D,QAAQ,KAAA,CAAM,qBAAqB,GAAY,OAAT,SAAS,GAAI,UAAU,CAAC;IAChE;IAEA,MAAM,QAAQ,mBAAmB,WAAW,GAAG,IAAI,YAAY;IAC/D,MAAM,aAAa,SAAS,KAAK,IAAI,cAAc,OAAO,GAAG,IAAI,KAAA;IAEjE,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,EAAC,kBAAA;QAAiB,OAAO;QAAiB;QAAc;QACtD,UAAA,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;YACC,iBAAe;YACf,iBAAe;YACf,iBAAe,SAAS,KAAK,IAAI,QAAQ,KAAA;YACzC,kBAAgB;YAChB,MAAK;YACL,cAAY,iBAAiB,OAAO,GAAG;YACvC,mDAAY,QAAS,KAAA;YACrB,YAAU;YACT,GAAG,aAAA;YACJ,KAAK;QAAA;IACP,CACF;AAEJ;AAGF,SAAS,WAAA,GAAc;AAMvB,IAAM,iBAAiB;AAKvB,IAAM,kLAA0B,aAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,eAAA,EAAiB,GAAG,eAAe,CAAA,GAAI;IAC/C,MAAM,UAAU,mBAAmB,gBAAgB,eAAe;QAIlD;IAHhB,OACE,aAAA,GAAA,CAAA,GAAA,sKAAA,CAAA,MAAA,2KAAC,YAAA,CAAU,GAAA,EAAV;QACC,cAAY,iBAAiB,QAAQ,KAAA,EAAO,QAAQ,GAAG;QACvD,wCAAoB,KAAA,2DAAS,KAAA;QAC7B,YAAU,QAAQ,GAAA;QACjB,GAAG,cAAA;QACJ,KAAK;IAAA;AAGX;AAGF,kBAAkB,WAAA,GAAc;AAIhC,SAAS,qBAAqB,KAAA,EAAe,GAAA,EAAa;IACxD,OAAO,GAAkC,OAA/B,KAAK,KAAA,CAAO,QAAQ,MAAO,GAAG,CAAC,EAAA;AAC3C;AAEA,SAAS,iBAAiB,KAAA,EAAkC,QAAA,EAAiC;IAC3F,OAAO,SAAS,OAAO,kBAAkB,UAAU,WAAW,aAAa;AAC7E;AAEA,SAAS,SAAS,KAAA,EAA6B;IAC7C,OAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,iBAAiB,GAAA,EAAyB;IAEjD,OACE,SAAS,GAAG,KACZ,CAAC,MAAM,GAAG,KACV,MAAM;AAEV;AAEA,SAAS,mBAAmB,KAAA,EAAY,GAAA,EAA8B;IAEpE,OACE,SAAS,KAAK,KACd,CAAC,MAAM,KAAK,KACZ,SAAS,OACT,SAAS;AAEb;AAGA,SAAS,mBAAmB,SAAA,EAAmB,aAAA,EAAuB;IACpE,OAAO,uCAAmC,SAAS,EAAA,0BAAoB,aAAa,EAAA,wEAAoF,OAAX,WAAW,EAAA;AAC1K;AAEA,SAAS,qBAAqB,SAAA,EAAmB,aAAA,EAAuB;IACtE,OAAO,yCAAqC,SAAS,EAAA,0BAAoB,aAAa,EAAA,qGAE7B,OAAX,WAAW,EAAA;AAI3D;AAEA,IAAM,OAAO;AACb,IAAM,YAAY", "debugId": null}}, {"offset": {"line": 2437, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/plus.js", "sources": ["file:///G:/Augment%20code/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('Plus', [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n]);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2KAAO,UAAA,EAAiB,MAAQ,CAAA,CAAA,CAAA;IACpC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC1C,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 2477, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/zustand/esm/vanilla.mjs"], "sourcesContent": ["const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const destroy = () => {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n      console.warn(\n        \"[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected.\"\n      );\n    }\n    listeners.clear();\n  };\n  const api = { setState, getState, getInitialState, subscribe, destroy };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\nvar vanilla = (createState) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use import { createStore } from 'zustand/vanilla'.\"\n    );\n  }\n  return createStore(createState);\n};\n\nexport { createStore, vanilla as default };\n"], "names": [], "mappings": ";;;;;;;;;AAAA,MAAM,kBAAkB,CAAC;IACvB,IAAI;IACJ,MAAM,YAAY,aAAa,GAAG,IAAI;IACtC,MAAM,WAAW,CAAC,SAAS;QACzB,MAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,SAAS;QACnE,IAAI,CAAC,OAAO,EAAE,CAAC,WAAW,QAAQ;YAChC,MAAM,gBAAgB;YACtB,QAAQ,CAAC,WAAW,OAAO,UAAU,OAAO,cAAc,YAAY,cAAc,IAAI,IAAI,YAAY,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;YACjI,UAAU,OAAO,CAAC,CAAC,WAAa,SAAS,OAAO;QAClD;IACF;IACA,MAAM,WAAW,IAAM;IACvB,MAAM,kBAAkB,IAAM;IAC9B,MAAM,YAAY,CAAC;QACjB,UAAU,GAAG,CAAC;QACd,OAAO,IAAM,UAAU,MAAM,CAAC;IAChC;IACA,MAAM,UAAU;QACd,IAAI,CAAC,8BAAY,GAAG,GAAG,8BAAY,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,cAAc;YACtE,QAAQ,IAAI,CACV;QAEJ;QACA,UAAU,KAAK;IACjB;IACA,MAAM,MAAM;QAAE;QAAU;QAAU;QAAiB;QAAW;IAAQ;IACtE,MAAM,eAAe,QAAQ,YAAY,UAAU,UAAU;IAC7D,OAAO;AACT;AACA,MAAM,cAAc,CAAC,cAAgB,cAAc,gBAAgB,eAAe;AAClF,IAAI,UAAU,CAAC;IACb,IAAI,CAAC,8BAAY,GAAG,GAAG,8BAAY,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,cAAc;QACtE,QAAQ,IAAI,CACV;IAEJ;IACA,OAAO,YAAY;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2533, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      shim = require(\"use-sync-external-store/shim\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = shim.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (\n      subscribe,\n      getSnapshot,\n      getServerSnapshot,\n      selector,\n      isEqual\n    ) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = { hasValue: !1, value: null };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(\n        function () {\n          function memoizedSelector(nextSnapshot) {\n            if (!hasMemo) {\n              hasMemo = !0;\n              memoizedSnapshot = nextSnapshot;\n              nextSnapshot = selector(nextSnapshot);\n              if (void 0 !== isEqual && inst.hasValue) {\n                var currentSelection = inst.value;\n                if (isEqual(currentSelection, nextSnapshot))\n                  return (memoizedSelection = currentSelection);\n              }\n              return (memoizedSelection = nextSnapshot);\n            }\n            currentSelection = memoizedSelection;\n            if (objectIs(memoizedSnapshot, nextSnapshot))\n              return currentSelection;\n            var nextSelection = selector(nextSnapshot);\n            if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n              return (memoizedSnapshot = nextSnapshot), currentSelection;\n            memoizedSnapshot = nextSnapshot;\n            return (memoizedSelection = nextSelection);\n          }\n          var hasMemo = !1,\n            memoizedSnapshot,\n            memoizedSelection,\n            maybeGetServerSnapshot =\n              void 0 === getServerSnapshot ? null : getServerSnapshot;\n          return [\n            function () {\n              return memoizedSelector(getSnapshot());\n            },\n            null === maybeGetServerSnapshot\n              ? void 0\n              : function () {\n                  return memoizedSelector(maybeGetServerSnapshot());\n                }\n          ];\n        },\n        [getSnapshot, getServerSnapshot, selector, isEqual]\n      );\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(\n        function () {\n          inst.hasValue = !0;\n          inst.value = value;\n        },\n        [value]\n      );\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,OAAO,AAAC,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,KAAO,MAAM,KAAK,MAAM;IACxE;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,IAAI,uHACF,0HACA,WAAW,eAAe,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,IACzD,uBAAuB,KAAK,oBAAoB,EAChD,SAAS,MAAM,MAAM,EACrB,YAAY,MAAM,SAAS,EAC3B,UAAU,MAAM,OAAO,EACvB,gBAAgB,MAAM,aAAa;IACrC,QAAQ,gCAAgC,GAAG,SACzC,SAAS,EACT,WAAW,EACX,iBAAiB,EACjB,QAAQ,EACR,OAAO;QAEP,IAAI,UAAU,OAAO;QACrB,IAAI,SAAS,QAAQ,OAAO,EAAE;YAC5B,IAAI,OAAO;gBAAE,UAAU,CAAC;gBAAG,OAAO;YAAK;YACvC,QAAQ,OAAO,GAAG;QACpB,OAAO,OAAO,QAAQ,OAAO;QAC7B,UAAU,QACR;YACE,SAAS,iBAAiB,YAAY;gBACpC,IAAI,CAAC,SAAS;oBACZ,UAAU,CAAC;oBACX,mBAAmB;oBACnB,eAAe,SAAS;oBACxB,IAAI,KAAK,MAAM,WAAW,KAAK,QAAQ,EAAE;wBACvC,IAAI,mBAAmB,KAAK,KAAK;wBACjC,IAAI,QAAQ,kBAAkB,eAC5B,OAAQ,oBAAoB;oBAChC;oBACA,OAAQ,oBAAoB;gBAC9B;gBACA,mBAAmB;gBACnB,IAAI,SAAS,kBAAkB,eAC7B,OAAO;gBACT,IAAI,gBAAgB,SAAS;gBAC7B,IAAI,KAAK,MAAM,WAAW,QAAQ,kBAAkB,gBAClD,OAAO,AAAC,mBAAmB,cAAe;gBAC5C,mBAAmB;gBACnB,OAAQ,oBAAoB;YAC9B;YACA,IAAI,UAAU,CAAC,GACb,kBACA,mBACA,yBACE,KAAK,MAAM,oBAAoB,OAAO;YAC1C,OAAO;gBACL;oBACE,OAAO,iBAAiB;gBAC1B;gBACA,SAAS,yBACL,KAAK,IACL;oBACE,OAAO,iBAAiB;gBAC1B;aACL;QACH,GACA;YAAC;YAAa;YAAmB;YAAU;SAAQ;QAErD,IAAI,QAAQ,qBAAqB,WAAW,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE;QAClE,UACE;YACE,KAAK,QAAQ,GAAG,CAAC;YACjB,KAAK,KAAK,GAAG;QACf,GACA;YAAC;SAAM;QAET,cAAc;QACd,OAAO;IACT;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2610, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/use-sync-external-store/shim/with-selector.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2621, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/zustand/esm/index.mjs"], "sourcesContent": ["import { createStore } from 'zustand/vanilla';\nexport * from 'zustand/vanilla';\nimport ReactExports from 'react';\nimport useSyncExternalStoreExports from 'use-sync-external-store/shim/with-selector.js';\n\nconst { useDebugValue } = ReactExports;\nconst { useSyncExternalStoreWithSelector } = useSyncExternalStoreExports;\nlet didWarnAboutEqualityFn = false;\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity, equalityFn) {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && equalityFn && !didWarnAboutEqualityFn) {\n    console.warn(\n      \"[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937\"\n    );\n    didWarnAboutEqualityFn = true;\n  }\n  const slice = useSyncExternalStoreWithSelector(\n    api.subscribe,\n    api.getState,\n    api.getServerState || api.getInitialState,\n    selector,\n    equalityFn\n  );\n  useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && typeof createState !== \"function\") {\n    console.warn(\n      \"[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.\"\n    );\n  }\n  const api = typeof createState === \"function\" ? createStore(createState) : createState;\n  const useBoundStore = (selector, equalityFn) => useStore(api, selector, equalityFn);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\nvar react = (createState) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use `import { create } from 'zustand'`.\"\n    );\n  }\n  return create(createState);\n};\n\nexport { create, react as default, useStore };\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AACA;;;;;;;;;;AAEA,MAAM,EAAE,aAAa,EAAE,GAAG,6JAAA,CAAA,UAAY;AACtC,MAAM,EAAE,gCAAgC,EAAE,GAAG,+KAAA,CAAA,UAA2B;AACxE,IAAI,yBAAyB;AAC7B,MAAM,WAAW,CAAC,MAAQ;AAC1B,SAAS,SAAS,GAAG;QAAE,WAAA,iEAAW,UAAU;IAC1C,IAAI,CAAC,8BAAY,GAAG,GAAG,8BAAY,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,gBAAgB,cAAc,CAAC,wBAAwB;QAC/G,QAAQ,IAAI,CACV;QAEF,yBAAyB;IAC3B;IACA,MAAM,QAAQ,iCACZ,IAAI,SAAS,EACb,IAAI,QAAQ,EACZ,IAAI,cAAc,IAAI,IAAI,eAAe,EACzC,UACA;IAEF,cAAc;IACd,OAAO;AACT;AACA,MAAM,aAAa,CAAC;IAClB,IAAI,CAAC,8BAAY,GAAG,GAAG,8BAAY,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,gBAAgB,OAAO,gBAAgB,YAAY;QAC3G,QAAQ,IAAI,CACV;IAEJ;IACA,MAAM,MAAM,OAAO,gBAAgB,aAAa,CAAA,GAAA,6IAAA,CAAA,cAAW,AAAD,EAAE,eAAe;IAC3E,MAAM,gBAAgB,CAAC,UAAU,aAAe,SAAS,KAAK,UAAU;IACxE,OAAO,MAAM,CAAC,eAAe;IAC7B,OAAO;AACT;AACA,MAAM,SAAS,CAAC,cAAgB,cAAc,WAAW,eAAe;AACxE,IAAI,QAAQ,CAAC;IACX,IAAI,CAAC,8BAAY,GAAG,GAAG,8BAAY,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,cAAc;QACtE,QAAQ,IAAI,CACV;IAEJ;IACA,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2673, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/zustand/esm/middleware.mjs"], "sourcesContent": ["const reduxImpl = (reducer, initial) => (set, _get, api) => {\n  api.dispatch = (action) => {\n    set((state) => reducer(state, action), false, action);\n    return action;\n  };\n  api.dispatchFromDevtools = true;\n  return { dispatch: (...a) => api.dispatch(...a), ...initial };\n};\nconst redux = reduxImpl;\n\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name) => {\n  const api = trackedConnections.get(name);\n  if (!api) return {};\n  return Object.fromEntries(\n    Object.entries(api.stores).map(([key, api2]) => [key, api2.getState()])\n  );\n};\nconst extractConnectionInformation = (store, extensionConnector, options) => {\n  if (store === void 0) {\n    return {\n      type: \"untracked\",\n      connection: extensionConnector.connect(options)\n    };\n  }\n  const existingConnection = trackedConnections.get(options.name);\n  if (existingConnection) {\n    return { type: \"tracked\", store, ...existingConnection };\n  }\n  const newConnection = {\n    connection: extensionConnector.connect(options),\n    stores: {}\n  };\n  trackedConnections.set(options.name, newConnection);\n  return { type: \"tracked\", store, ...newConnection };\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {}) => (set, get, api) => {\n  const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n  let extensionConnector;\n  try {\n    extensionConnector = (enabled != null ? enabled : (import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (_e) {\n  }\n  if (!extensionConnector) {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && enabled) {\n      console.warn(\n        \"[zustand devtools middleware] Please install/enable Redux devtools extension\"\n      );\n    }\n    return fn(set, get, api);\n  }\n  const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n  let isRecording = true;\n  api.setState = (state, replace, nameOrAction) => {\n    const r = set(state, replace);\n    if (!isRecording) return r;\n    const action = nameOrAction === void 0 ? { type: anonymousActionType || \"anonymous\" } : typeof nameOrAction === \"string\" ? { type: nameOrAction } : nameOrAction;\n    if (store === void 0) {\n      connection == null ? void 0 : connection.send(action, get());\n      return r;\n    }\n    connection == null ? void 0 : connection.send(\n      {\n        ...action,\n        type: `${store}/${action.type}`\n      },\n      {\n        ...getTrackedConnectionState(options.name),\n        [store]: api.getState()\n      }\n    );\n    return r;\n  };\n  const setStateFromDevtools = (...a) => {\n    const originalIsRecording = isRecording;\n    isRecording = false;\n    set(...a);\n    isRecording = originalIsRecording;\n  };\n  const initialState = fn(api.setState, get, api);\n  if (connectionInformation.type === \"untracked\") {\n    connection == null ? void 0 : connection.init(initialState);\n  } else {\n    connectionInformation.stores[connectionInformation.store] = api;\n    connection == null ? void 0 : connection.init(\n      Object.fromEntries(\n        Object.entries(connectionInformation.stores).map(([key, store2]) => [\n          key,\n          key === connectionInformation.store ? initialState : store2.getState()\n        ])\n      )\n    );\n  }\n  if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n    let didWarnAboutReservedActionType = false;\n    const originalDispatch = api.dispatch;\n    api.dispatch = (...a) => {\n      if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && a[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n        console.warn(\n          '[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.'\n        );\n        didWarnAboutReservedActionType = true;\n      }\n      originalDispatch(...a);\n    };\n  }\n  connection.subscribe((message) => {\n    var _a;\n    switch (message.type) {\n      case \"ACTION\":\n        if (typeof message.payload !== \"string\") {\n          console.error(\n            \"[zustand devtools middleware] Unsupported action format\"\n          );\n          return;\n        }\n        return parseJsonThen(\n          message.payload,\n          (action) => {\n            if (action.type === \"__setState\") {\n              if (store === void 0) {\n                setStateFromDevtools(action.state);\n                return;\n              }\n              if (Object.keys(action.state).length !== 1) {\n                console.error(\n                  `\n                    [zustand devtools middleware] Unsupported __setState action format. \n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `\n                );\n              }\n              const stateFromDevtools = action.state[store];\n              if (stateFromDevtools === void 0 || stateFromDevtools === null) {\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                setStateFromDevtools(stateFromDevtools);\n              }\n              return;\n            }\n            if (!api.dispatchFromDevtools) return;\n            if (typeof api.dispatch !== \"function\") return;\n            api.dispatch(action);\n          }\n        );\n      case \"DISPATCH\":\n        switch (message.payload.type) {\n          case \"RESET\":\n            setStateFromDevtools(initialState);\n            if (store === void 0) {\n              return connection == null ? void 0 : connection.init(api.getState());\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"COMMIT\":\n            if (store === void 0) {\n              connection == null ? void 0 : connection.init(api.getState());\n              return;\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"ROLLBACK\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                connection == null ? void 0 : connection.init(api.getState());\n                return;\n              }\n              setStateFromDevtools(state[store]);\n              connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n            });\n          case \"JUMP_TO_STATE\":\n          case \"JUMP_TO_ACTION\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                setStateFromDevtools(state[store]);\n              }\n            });\n          case \"IMPORT_STATE\": {\n            const { nextLiftedState } = message.payload;\n            const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;\n            if (!lastComputedState) return;\n            if (store === void 0) {\n              setStateFromDevtools(lastComputedState);\n            } else {\n              setStateFromDevtools(lastComputedState[store]);\n            }\n            connection == null ? void 0 : connection.send(\n              null,\n              // FIXME no-any\n              nextLiftedState\n            );\n            return;\n          }\n          case \"PAUSE_RECORDING\":\n            return isRecording = !isRecording;\n        }\n        return;\n    }\n  });\n  return initialState;\n};\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, f) => {\n  let parsed;\n  try {\n    parsed = JSON.parse(stringified);\n  } catch (e) {\n    console.error(\n      \"[zustand devtools middleware] Could not parse the received json\",\n      e\n    );\n  }\n  if (parsed !== void 0) f(parsed);\n};\n\nconst subscribeWithSelectorImpl = (fn) => (set, get, api) => {\n  const origSubscribe = api.subscribe;\n  api.subscribe = (selector, optListener, options) => {\n    let listener = selector;\n    if (optListener) {\n      const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n      let currentSlice = selector(api.getState());\n      listener = (state) => {\n        const nextSlice = selector(state);\n        if (!equalityFn(currentSlice, nextSlice)) {\n          const previousSlice = currentSlice;\n          optListener(currentSlice = nextSlice, previousSlice);\n        }\n      };\n      if (options == null ? void 0 : options.fireImmediately) {\n        optListener(currentSlice, currentSlice);\n      }\n    }\n    return origSubscribe(listener);\n  };\n  const initialState = fn(set, get, api);\n  return initialState;\n};\nconst subscribeWithSelector = subscribeWithSelectorImpl;\n\nconst combine = (initialState, create) => (...a) => Object.assign({}, initialState, create(...a));\n\nfunction createJSONStorage(getStorage, options) {\n  let storage;\n  try {\n    storage = getStorage();\n  } catch (_e) {\n    return;\n  }\n  const persistStorage = {\n    getItem: (name) => {\n      var _a;\n      const parse = (str2) => {\n        if (str2 === null) {\n          return null;\n        }\n        return JSON.parse(str2, options == null ? void 0 : options.reviver);\n      };\n      const str = (_a = storage.getItem(name)) != null ? _a : null;\n      if (str instanceof Promise) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (name, newValue) => storage.setItem(\n      name,\n      JSON.stringify(newValue, options == null ? void 0 : options.replacer)\n    ),\n    removeItem: (name) => storage.removeItem(name)\n  };\n  return persistStorage;\n}\nconst toThenable = (fn) => (input) => {\n  try {\n    const result = fn(input);\n    if (result instanceof Promise) {\n      return result;\n    }\n    return {\n      then(onFulfilled) {\n        return toThenable(onFulfilled)(result);\n      },\n      catch(_onRejected) {\n        return this;\n      }\n    };\n  } catch (e) {\n    return {\n      then(_onFulfilled) {\n        return this;\n      },\n      catch(onRejected) {\n        return toThenable(onRejected)(e);\n      }\n    };\n  }\n};\nconst oldImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    getStorage: () => localStorage,\n    serialize: JSON.stringify,\n    deserialize: JSON.parse,\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage;\n  try {\n    storage = options.getStorage();\n  } catch (_e) {\n  }\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const thenableSerialize = toThenable(options.serialize);\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    let errorInSync;\n    const thenable = thenableSerialize({ state, version: options.version }).then(\n      (serializedValue) => storage.setItem(options.name, serializedValue)\n    ).catch((e) => {\n      errorInSync = e;\n    });\n    if (errorInSync) {\n      throw errorInSync;\n    }\n    return thenable;\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => cb(get()));\n    const postRehydrationCallback = ((_a = options.onRehydrateStorage) == null ? void 0 : _a.call(options, get())) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((storageValue) => {\n      if (storageValue) {\n        return options.deserialize(storageValue);\n      }\n    }).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            return options.migrate(\n              deserializedStorageValue.state,\n              deserializedStorageValue.version\n            );\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return deserializedStorageValue.state;\n        }\n      }\n    }).then((migratedState) => {\n      var _a2;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      return setItem();\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.getStorage) {\n        storage = newOptions.getStorage();\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  hydrate();\n  return stateFromStorage || configResult;\n};\nconst newImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    storage: createJSONStorage(() => localStorage),\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage = options.storage;\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    return storage.setItem(options.name, {\n      state,\n      version: options.version\n    });\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  api.getInitialState = () => configResult;\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a, _b;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => {\n      var _a2;\n      return cb((_a2 = get()) != null ? _a2 : configResult);\n    });\n    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            return [\n              true,\n              options.migrate(\n                deserializedStorageValue.state,\n                deserializedStorageValue.version\n              )\n            ];\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return [false, deserializedStorageValue.state];\n        }\n      }\n      return [false, void 0];\n    }).then((migrationResult) => {\n      var _a2;\n      const [migrated, migratedState] = migrationResult;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      if (migrated) {\n        return setItem();\n      }\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      stateFromStorage = get();\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.storage) {\n        storage = newOptions.storage;\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  if (!options.skipHydration) {\n    hydrate();\n  }\n  return stateFromStorage || configResult;\n};\nconst persistImpl = (config, baseOptions) => {\n  if (\"getStorage\" in baseOptions || \"serialize\" in baseOptions || \"deserialize\" in baseOptions) {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n      console.warn(\n        \"[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead.\"\n      );\n    }\n    return oldImpl(config, baseOptions);\n  }\n  return newImpl(config, baseOptions);\n};\nconst persist = persistImpl;\n\nexport { combine, createJSONStorage, devtools, persist, redux, subscribeWithSelector };\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,MAAM,YAAY,CAAC,SAAS,UAAY,CAAC,KAAK,MAAM;QAClD,IAAI,QAAQ,GAAG,CAAC;YACd,IAAI,CAAC,QAAU,QAAQ,OAAO,SAAS,OAAO;YAC9C,OAAO;QACT;QACA,IAAI,oBAAoB,GAAG;QAC3B,OAAO;YAAE,UAAU;iDAAI;oBAAA;;uBAAM,IAAI,QAAQ,IAAI;;YAAI,GAAG,OAAO;QAAC;IAC9D;AACA,MAAM,QAAQ;AAEd,MAAM,qBAAqB,aAAa,GAAG,IAAI;AAC/C,MAAM,4BAA4B,CAAC;IACjC,MAAM,MAAM,mBAAmB,GAAG,CAAC;IACnC,IAAI,CAAC,KAAK,OAAO,CAAC;IAClB,OAAO,OAAO,WAAW,CACvB,OAAO,OAAO,CAAC,IAAI,MAAM,EAAE,GAAG,CAAC;YAAC,CAAC,KAAK,KAAK;eAAK;YAAC;YAAK,KAAK,QAAQ;SAAG;;AAE1E;AACA,MAAM,+BAA+B,CAAC,OAAO,oBAAoB;IAC/D,IAAI,UAAU,KAAK,GAAG;QACpB,OAAO;YACL,MAAM;YACN,YAAY,mBAAmB,OAAO,CAAC;QACzC;IACF;IACA,MAAM,qBAAqB,mBAAmB,GAAG,CAAC,QAAQ,IAAI;IAC9D,IAAI,oBAAoB;QACtB,OAAO;YAAE,MAAM;YAAW;YAAO,GAAG,kBAAkB;QAAC;IACzD;IACA,MAAM,gBAAgB;QACpB,YAAY,mBAAmB,OAAO,CAAC;QACvC,QAAQ,CAAC;IACX;IACA,mBAAmB,GAAG,CAAC,QAAQ,IAAI,EAAE;IACrC,OAAO;QAAE,MAAM;QAAW;QAAO,GAAG,aAAa;IAAC;AACpD;AACA,MAAM,eAAe,SAAC;QAAI,mFAAkB,CAAC;WAAM,CAAC,KAAK,KAAK;QAC5D,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE,KAAK,EAAE,GAAG,SAAS,GAAG;QAC5D,IAAI;QACJ,IAAI;YACF,qBAAqB,CAAC,WAAW,OAAO,UAAU,CAAC,8BAAY,GAAG,GAAG,8BAAY,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,YAAY,KAAK,OAAO,4BAA4B;QAC9J,EAAE,OAAO,IAAI,CACb;QACA,IAAI,CAAC,oBAAoB;YACvB,IAAI,CAAC,8BAAY,GAAG,GAAG,8BAAY,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,gBAAgB,SAAS;gBACjF,QAAQ,IAAI,CACV;YAEJ;YACA,OAAO,GAAG,KAAK,KAAK;QACtB;QACA,MAAM,EAAE,UAAU,EAAE,GAAG,uBAAuB,GAAG,6BAA6B,OAAO,oBAAoB;QACzG,IAAI,cAAc;QAClB,IAAI,QAAQ,GAAG,CAAC,OAAO,SAAS;YAC9B,MAAM,IAAI,IAAI,OAAO;YACrB,IAAI,CAAC,aAAa,OAAO;YACzB,MAAM,SAAS,iBAAiB,KAAK,IAAI;gBAAE,MAAM,uBAAuB;YAAY,IAAI,OAAO,iBAAiB,WAAW;gBAAE,MAAM;YAAa,IAAI;YACpJ,IAAI,UAAU,KAAK,GAAG;gBACpB,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC,QAAQ;gBACtD,OAAO;YACT;YACA,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAC3C;gBACE,GAAG,MAAM;gBACT,MAAM,AAAC,GAAW,OAAT,OAAM,KAAe,OAAZ,OAAO,IAAI;YAC/B,GACA;gBACE,GAAG,0BAA0B,QAAQ,IAAI,CAAC;gBAC1C,CAAC,MAAM,EAAE,IAAI,QAAQ;YACvB;YAEF,OAAO;QACT;QACA,MAAM,uBAAuB;6CAAI;gBAAA;;YAC/B,MAAM,sBAAsB;YAC5B,cAAc;YACd,OAAO;YACP,cAAc;QAChB;QACA,MAAM,eAAe,GAAG,IAAI,QAAQ,EAAE,KAAK;QAC3C,IAAI,sBAAsB,IAAI,KAAK,aAAa;YAC9C,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC;QAChD,OAAO;YACL,sBAAsB,MAAM,CAAC,sBAAsB,KAAK,CAAC,GAAG;YAC5D,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAC3C,OAAO,WAAW,CAChB,OAAO,OAAO,CAAC,sBAAsB,MAAM,EAAE,GAAG,CAAC;oBAAC,CAAC,KAAK,OAAO;uBAAK;oBAClE;oBACA,QAAQ,sBAAsB,KAAK,GAAG,eAAe,OAAO,QAAQ;iBACrE;;QAGP;QACA,IAAI,IAAI,oBAAoB,IAAI,OAAO,IAAI,QAAQ,KAAK,YAAY;YAClE,IAAI,iCAAiC;YACrC,MAAM,mBAAmB,IAAI,QAAQ;YACrC,IAAI,QAAQ,GAAG;iDAAI;oBAAA;;gBACjB,IAAI,CAAC,8BAAY,GAAG,GAAG,8BAAY,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,gBAAgB,CAAC,CAAC,EAAE,CAAC,IAAI,KAAK,gBAAgB,CAAC,gCAAgC;oBACvI,QAAQ,IAAI,CACV;oBAEF,iCAAiC;gBACnC;gBACA,oBAAoB;YACtB;QACF;QACA,WAAW,SAAS,CAAC,CAAC;YACpB,IAAI;YACJ,OAAQ,QAAQ,IAAI;gBAClB,KAAK;oBACH,IAAI,OAAO,QAAQ,OAAO,KAAK,UAAU;wBACvC,QAAQ,KAAK,CACX;wBAEF;oBACF;oBACA,OAAO,cACL,QAAQ,OAAO,EACf,CAAC;wBACC,IAAI,OAAO,IAAI,KAAK,cAAc;4BAChC,IAAI,UAAU,KAAK,GAAG;gCACpB,qBAAqB,OAAO,KAAK;gCACjC;4BACF;4BACA,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,EAAE,MAAM,KAAK,GAAG;gCAC1C,QAAQ,KAAK,CACV;4BAML;4BACA,MAAM,oBAAoB,OAAO,KAAK,CAAC,MAAM;4BAC7C,IAAI,sBAAsB,KAAK,KAAK,sBAAsB,MAAM;gCAC9D;4BACF;4BACA,IAAI,KAAK,SAAS,CAAC,IAAI,QAAQ,QAAQ,KAAK,SAAS,CAAC,oBAAoB;gCACxE,qBAAqB;4BACvB;4BACA;wBACF;wBACA,IAAI,CAAC,IAAI,oBAAoB,EAAE;wBAC/B,IAAI,OAAO,IAAI,QAAQ,KAAK,YAAY;wBACxC,IAAI,QAAQ,CAAC;oBACf;gBAEJ,KAAK;oBACH,OAAQ,QAAQ,OAAO,CAAC,IAAI;wBAC1B,KAAK;4BACH,qBAAqB;4BACrB,IAAI,UAAU,KAAK,GAAG;gCACpB,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC,IAAI,QAAQ;4BACnE;4BACA,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC,0BAA0B,QAAQ,IAAI;wBAC7F,KAAK;4BACH,IAAI,UAAU,KAAK,GAAG;gCACpB,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC,IAAI,QAAQ;gCAC1D;4BACF;4BACA,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC,0BAA0B,QAAQ,IAAI;wBAC7F,KAAK;4BACH,OAAO,cAAc,QAAQ,KAAK,EAAE,CAAC;gCACnC,IAAI,UAAU,KAAK,GAAG;oCACpB,qBAAqB;oCACrB,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC,IAAI,QAAQ;oCAC1D;gCACF;gCACA,qBAAqB,KAAK,CAAC,MAAM;gCACjC,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAAC,0BAA0B,QAAQ,IAAI;4BACtF;wBACF,KAAK;wBACL,KAAK;4BACH,OAAO,cAAc,QAAQ,KAAK,EAAE,CAAC;gCACnC,IAAI,UAAU,KAAK,GAAG;oCACpB,qBAAqB;oCACrB;gCACF;gCACA,IAAI,KAAK,SAAS,CAAC,IAAI,QAAQ,QAAQ,KAAK,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG;oCACnE,qBAAqB,KAAK,CAAC,MAAM;gCACnC;4BACF;wBACF,KAAK;4BAAgB;gCACnB,MAAM,EAAE,eAAe,EAAE,GAAG,QAAQ,OAAO;gCAC3C,MAAM,oBAAoB,CAAC,KAAK,gBAAgB,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,GAAG,KAAK;gCACxG,IAAI,CAAC,mBAAmB;gCACxB,IAAI,UAAU,KAAK,GAAG;oCACpB,qBAAqB;gCACvB,OAAO;oCACL,qBAAqB,iBAAiB,CAAC,MAAM;gCAC/C;gCACA,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,CAC3C,MACA,eAAe;gCACf;gCAEF;4BACF;wBACA,KAAK;4BACH,OAAO,cAAc,CAAC;oBAC1B;oBACA;YACJ;QACF;QACA,OAAO;IACT;;AACA,MAAM,WAAW;AACjB,MAAM,gBAAgB,CAAC,aAAa;IAClC,IAAI;IACJ,IAAI;QACF,SAAS,KAAK,KAAK,CAAC;IACtB,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CACX,mEACA;IAEJ;IACA,IAAI,WAAW,KAAK,GAAG,EAAE;AAC3B;AAEA,MAAM,4BAA4B,CAAC,KAAO,CAAC,KAAK,KAAK;QACnD,MAAM,gBAAgB,IAAI,SAAS;QACnC,IAAI,SAAS,GAAG,CAAC,UAAU,aAAa;YACtC,IAAI,WAAW;YACf,IAAI,aAAa;gBACf,MAAM,aAAa,CAAC,WAAW,OAAO,KAAK,IAAI,QAAQ,UAAU,KAAK,OAAO,EAAE;gBAC/E,IAAI,eAAe,SAAS,IAAI,QAAQ;gBACxC,WAAW,CAAC;oBACV,MAAM,YAAY,SAAS;oBAC3B,IAAI,CAAC,WAAW,cAAc,YAAY;wBACxC,MAAM,gBAAgB;wBACtB,YAAY,eAAe,WAAW;oBACxC;gBACF;gBACA,IAAI,WAAW,OAAO,KAAK,IAAI,QAAQ,eAAe,EAAE;oBACtD,YAAY,cAAc;gBAC5B;YACF;YACA,OAAO,cAAc;QACvB;QACA,MAAM,eAAe,GAAG,KAAK,KAAK;QAClC,OAAO;IACT;AACA,MAAM,wBAAwB;AAE9B,MAAM,UAAU,CAAC,cAAc,SAAW;yCAAI;YAAA;;eAAM,OAAO,MAAM,CAAC,CAAC,GAAG,cAAc,UAAU;;AAE9F,SAAS,kBAAkB,UAAU,EAAE,OAAO;IAC5C,IAAI;IACJ,IAAI;QACF,UAAU;IACZ,EAAE,OAAO,IAAI;QACX;IACF;IACA,MAAM,iBAAiB;QACrB,SAAS,CAAC;YACR,IAAI;YACJ,MAAM,QAAQ,CAAC;gBACb,IAAI,SAAS,MAAM;oBACjB,OAAO;gBACT;gBACA,OAAO,KAAK,KAAK,CAAC,MAAM,WAAW,OAAO,KAAK,IAAI,QAAQ,OAAO;YACpE;YACA,MAAM,MAAM,CAAC,KAAK,QAAQ,OAAO,CAAC,KAAK,KAAK,OAAO,KAAK;YACxD,IAAI,eAAe,SAAS;gBAC1B,OAAO,IAAI,IAAI,CAAC;YAClB;YACA,OAAO,MAAM;QACf;QACA,SAAS,CAAC,MAAM,WAAa,QAAQ,OAAO,CAC1C,MACA,KAAK,SAAS,CAAC,UAAU,WAAW,OAAO,KAAK,IAAI,QAAQ,QAAQ;QAEtE,YAAY,CAAC,OAAS,QAAQ,UAAU,CAAC;IAC3C;IACA,OAAO;AACT;AACA,MAAM,aAAa,CAAC,KAAO,CAAC;QAC1B,IAAI;YACF,MAAM,SAAS,GAAG;YAClB,IAAI,kBAAkB,SAAS;gBAC7B,OAAO;YACT;YACA,OAAO;gBACL,MAAK,WAAW;oBACd,OAAO,WAAW,aAAa;gBACjC;gBACA,OAAM,WAAW;oBACf,OAAO,IAAI;gBACb;YACF;QACF,EAAE,OAAO,GAAG;YACV,OAAO;gBACL,MAAK,YAAY;oBACf,OAAO,IAAI;gBACb;gBACA,OAAM,UAAU;oBACd,OAAO,WAAW,YAAY;gBAChC;YACF;QACF;IACF;AACA,MAAM,UAAU,CAAC,QAAQ,cAAgB,CAAC,KAAK,KAAK;QAClD,IAAI,UAAU;YACZ,YAAY,IAAM;YAClB,WAAW,KAAK,SAAS;YACzB,aAAa,KAAK,KAAK;YACvB,YAAY,CAAC,QAAU;YACvB,SAAS;YACT,OAAO,CAAC,gBAAgB,eAAiB,CAAC;oBACxC,GAAG,YAAY;oBACf,GAAG,cAAc;gBACnB,CAAC;YACD,GAAG,WAAW;QAChB;QACA,IAAI,cAAc;QAClB,MAAM,qBAAqB,aAAa,GAAG,IAAI;QAC/C,MAAM,2BAA2B,aAAa,GAAG,IAAI;QACrD,IAAI;QACJ,IAAI;YACF,UAAU,QAAQ,UAAU;QAC9B,EAAE,OAAO,IAAI,CACb;QACA,IAAI,CAAC,SAAS;YACZ,OAAO,OACL;iDAAI;oBAAA;;gBACF,QAAQ,IAAI,CACV,AAAC,uDAAmE,OAAb,QAAQ,IAAI,EAAC;gBAEtE,OAAO;YACT,GACA,KACA;QAEJ;QACA,MAAM,oBAAoB,WAAW,QAAQ,SAAS;QACtD,MAAM,UAAU;YACd,MAAM,QAAQ,QAAQ,UAAU,CAAC;gBAAE,GAAG,KAAK;YAAC;YAC5C,IAAI;YACJ,MAAM,WAAW,kBAAkB;gBAAE;gBAAO,SAAS,QAAQ,OAAO;YAAC,GAAG,IAAI,CAC1E,CAAC,kBAAoB,QAAQ,OAAO,CAAC,QAAQ,IAAI,EAAE,kBACnD,KAAK,CAAC,CAAC;gBACP,cAAc;YAChB;YACA,IAAI,aAAa;gBACf,MAAM;YACR;YACA,OAAO;QACT;QACA,MAAM,gBAAgB,IAAI,QAAQ;QAClC,IAAI,QAAQ,GAAG,CAAC,OAAO;YACrB,cAAc,OAAO;YACrB,KAAK;QACP;QACA,MAAM,eAAe,OACnB;6CAAI;gBAAA;;YACF,OAAO;YACP,KAAK;QACP,GACA,KACA;QAEF,IAAI;QACJ,MAAM,UAAU;YACd,IAAI;YACJ,IAAI,CAAC,SAAS;YACd,cAAc;YACd,mBAAmB,OAAO,CAAC,CAAC,KAAO,GAAG;YACtC,MAAM,0BAA0B,CAAC,CAAC,KAAK,QAAQ,kBAAkB,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI,CAAC,SAAS,MAAM,KAAK,KAAK;YACvH,OAAO,WAAW,QAAQ,OAAO,CAAC,IAAI,CAAC,UAAU,QAAQ,IAAI,EAAE,IAAI,CAAC,CAAC;gBACnE,IAAI,cAAc;oBAChB,OAAO,QAAQ,WAAW,CAAC;gBAC7B;YACF,GAAG,IAAI,CAAC,CAAC;gBACP,IAAI,0BAA0B;oBAC5B,IAAI,OAAO,yBAAyB,OAAO,KAAK,YAAY,yBAAyB,OAAO,KAAK,QAAQ,OAAO,EAAE;wBAChH,IAAI,QAAQ,OAAO,EAAE;4BACnB,OAAO,QAAQ,OAAO,CACpB,yBAAyB,KAAK,EAC9B,yBAAyB,OAAO;wBAEpC;wBACA,QAAQ,KAAK,CACV;oBAEL,OAAO;wBACL,OAAO,yBAAyB,KAAK;oBACvC;gBACF;YACF,GAAG,IAAI,CAAC,CAAC;gBACP,IAAI;gBACJ,mBAAmB,QAAQ,KAAK,CAC9B,eACA,CAAC,MAAM,KAAK,KAAK,OAAO,MAAM;gBAEhC,IAAI,kBAAkB;gBACtB,OAAO;YACT,GAAG,IAAI,CAAC;gBACN,2BAA2B,OAAO,KAAK,IAAI,wBAAwB,kBAAkB,KAAK;gBAC1F,cAAc;gBACd,yBAAyB,OAAO,CAAC,CAAC,KAAO,GAAG;YAC9C,GAAG,KAAK,CAAC,CAAC;gBACR,2BAA2B,OAAO,KAAK,IAAI,wBAAwB,KAAK,GAAG;YAC7E;QACF;QACA,IAAI,OAAO,GAAG;YACZ,YAAY,CAAC;gBACX,UAAU;oBACR,GAAG,OAAO;oBACV,GAAG,UAAU;gBACf;gBACA,IAAI,WAAW,UAAU,EAAE;oBACzB,UAAU,WAAW,UAAU;gBACjC;YACF;YACA,cAAc;gBACZ,WAAW,OAAO,KAAK,IAAI,QAAQ,UAAU,CAAC,QAAQ,IAAI;YAC5D;YACA,YAAY,IAAM;YAClB,WAAW,IAAM;YACjB,aAAa,IAAM;YACnB,WAAW,CAAC;gBACV,mBAAmB,GAAG,CAAC;gBACvB,OAAO;oBACL,mBAAmB,MAAM,CAAC;gBAC5B;YACF;YACA,mBAAmB,CAAC;gBAClB,yBAAyB,GAAG,CAAC;gBAC7B,OAAO;oBACL,yBAAyB,MAAM,CAAC;gBAClC;YACF;QACF;QACA;QACA,OAAO,oBAAoB;IAC7B;AACA,MAAM,UAAU,CAAC,QAAQ,cAAgB,CAAC,KAAK,KAAK;QAClD,IAAI,UAAU;YACZ,SAAS,kBAAkB,IAAM;YACjC,YAAY,CAAC,QAAU;YACvB,SAAS;YACT,OAAO,CAAC,gBAAgB,eAAiB,CAAC;oBACxC,GAAG,YAAY;oBACf,GAAG,cAAc;gBACnB,CAAC;YACD,GAAG,WAAW;QAChB;QACA,IAAI,cAAc;QAClB,MAAM,qBAAqB,aAAa,GAAG,IAAI;QAC/C,MAAM,2BAA2B,aAAa,GAAG,IAAI;QACrD,IAAI,UAAU,QAAQ,OAAO;QAC7B,IAAI,CAAC,SAAS;YACZ,OAAO,OACL;iDAAI;oBAAA;;gBACF,QAAQ,IAAI,CACV,AAAC,uDAAmE,OAAb,QAAQ,IAAI,EAAC;gBAEtE,OAAO;YACT,GACA,KACA;QAEJ;QACA,MAAM,UAAU;YACd,MAAM,QAAQ,QAAQ,UAAU,CAAC;gBAAE,GAAG,KAAK;YAAC;YAC5C,OAAO,QAAQ,OAAO,CAAC,QAAQ,IAAI,EAAE;gBACnC;gBACA,SAAS,QAAQ,OAAO;YAC1B;QACF;QACA,MAAM,gBAAgB,IAAI,QAAQ;QAClC,IAAI,QAAQ,GAAG,CAAC,OAAO;YACrB,cAAc,OAAO;YACrB,KAAK;QACP;QACA,MAAM,eAAe,OACnB;6CAAI;gBAAA;;YACF,OAAO;YACP,KAAK;QACP,GACA,KACA;QAEF,IAAI,eAAe,GAAG,IAAM;QAC5B,IAAI;QACJ,MAAM,UAAU;YACd,IAAI,IAAI;YACR,IAAI,CAAC,SAAS;YACd,cAAc;YACd,mBAAmB,OAAO,CAAC,CAAC;gBAC1B,IAAI;gBACJ,OAAO,GAAG,CAAC,MAAM,KAAK,KAAK,OAAO,MAAM;YAC1C;YACA,MAAM,0BAA0B,CAAC,CAAC,KAAK,QAAQ,kBAAkB,KAAK,OAAO,KAAK,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,KAAK,KAAK,OAAO,KAAK,aAAa,KAAK,KAAK;YAC1J,OAAO,WAAW,QAAQ,OAAO,CAAC,IAAI,CAAC,UAAU,QAAQ,IAAI,EAAE,IAAI,CAAC,CAAC;gBACnE,IAAI,0BAA0B;oBAC5B,IAAI,OAAO,yBAAyB,OAAO,KAAK,YAAY,yBAAyB,OAAO,KAAK,QAAQ,OAAO,EAAE;wBAChH,IAAI,QAAQ,OAAO,EAAE;4BACnB,OAAO;gCACL;gCACA,QAAQ,OAAO,CACb,yBAAyB,KAAK,EAC9B,yBAAyB,OAAO;6BAEnC;wBACH;wBACA,QAAQ,KAAK,CACV;oBAEL,OAAO;wBACL,OAAO;4BAAC;4BAAO,yBAAyB,KAAK;yBAAC;oBAChD;gBACF;gBACA,OAAO;oBAAC;oBAAO,KAAK;iBAAE;YACxB,GAAG,IAAI,CAAC,CAAC;gBACP,IAAI;gBACJ,MAAM,CAAC,UAAU,cAAc,GAAG;gBAClC,mBAAmB,QAAQ,KAAK,CAC9B,eACA,CAAC,MAAM,KAAK,KAAK,OAAO,MAAM;gBAEhC,IAAI,kBAAkB;gBACtB,IAAI,UAAU;oBACZ,OAAO;gBACT;YACF,GAAG,IAAI,CAAC;gBACN,2BAA2B,OAAO,KAAK,IAAI,wBAAwB,kBAAkB,KAAK;gBAC1F,mBAAmB;gBACnB,cAAc;gBACd,yBAAyB,OAAO,CAAC,CAAC,KAAO,GAAG;YAC9C,GAAG,KAAK,CAAC,CAAC;gBACR,2BAA2B,OAAO,KAAK,IAAI,wBAAwB,KAAK,GAAG;YAC7E;QACF;QACA,IAAI,OAAO,GAAG;YACZ,YAAY,CAAC;gBACX,UAAU;oBACR,GAAG,OAAO;oBACV,GAAG,UAAU;gBACf;gBACA,IAAI,WAAW,OAAO,EAAE;oBACtB,UAAU,WAAW,OAAO;gBAC9B;YACF;YACA,cAAc;gBACZ,WAAW,OAAO,KAAK,IAAI,QAAQ,UAAU,CAAC,QAAQ,IAAI;YAC5D;YACA,YAAY,IAAM;YAClB,WAAW,IAAM;YACjB,aAAa,IAAM;YACnB,WAAW,CAAC;gBACV,mBAAmB,GAAG,CAAC;gBACvB,OAAO;oBACL,mBAAmB,MAAM,CAAC;gBAC5B;YACF;YACA,mBAAmB,CAAC;gBAClB,yBAAyB,GAAG,CAAC;gBAC7B,OAAO;oBACL,yBAAyB,MAAM,CAAC;gBAClC;YACF;QACF;QACA,IAAI,CAAC,QAAQ,aAAa,EAAE;YAC1B;QACF;QACA,OAAO,oBAAoB;IAC7B;AACA,MAAM,cAAc,CAAC,QAAQ;IAC3B,IAAI,gBAAgB,eAAe,eAAe,eAAe,iBAAiB,aAAa;QAC7F,IAAI,CAAC,8BAAY,GAAG,GAAG,8BAAY,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,cAAc;YACtE,QAAQ,IAAI,CACV;QAEJ;QACA,OAAO,QAAQ,QAAQ;IACzB;IACA,OAAO,QAAQ,QAAQ;AACzB;AACA,MAAM,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3262, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/user-plus.js", "sources": ["file:///G:/Augment%20code/node_modules/lucide-react/src/icons/user-plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name UserPlus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iNyIgcj0iNCIgLz4KICA8bGluZSB4MT0iMTkiIHgyPSIxOSIgeTE9IjgiIHkyPSIxNCIgLz4KICA8bGluZSB4MT0iMjIiIHgyPSIxNiIgeTE9IjExIiB5Mj0iMTEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user-plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UserPlus = createLucideIcon('UserPlus', [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n  ['line', { x1: '19', x2: '19', y1: '8', y2: '14', key: '1bvyxn' }],\n  ['line', { x1: '22', x2: '16', y1: '11', y2: '11', key: '1shjgl' }],\n]);\n\nexport default UserPlus;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2KAAW,UAAA,EAAiB,UAAY,CAAA,CAAA,CAAA;IAC5C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC1E;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAS;KAAA,CAAA;IACrD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACnE,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 3324, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js", "sources": ["file:///G:/Augment%20code/node_modules/lucide-react/src/icons/trending-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIyMiA3IDEzLjUgMTUuNSA4LjUgMTAuNSAyIDE3IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjE2IDcgMjIgNyAyMiAxMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('TrendingUp', [\n  ['polyline', { points: '22 7 13.5 15.5 8.5 10.5 2 17', key: '126l90' }],\n  ['polyline', { points: '16 7 22 7 22 13', key: 'kwv8wd' }],\n]);\n\nexport default TrendingUp;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2KAAa,UAAA,EAAiB,YAAc,CAAA,CAAA,CAAA;IAChD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;QAAA,CAAA;YAAE,QAAQ,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACtE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;QAAA,CAAA;YAAE,QAAQ,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC1D,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 3364, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/download.js", "sources": ["file:///G:/Augment%20code/node_modules/lucide-react/src/icons/download.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Download\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTV2NGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtNCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSI3IDEwIDEyIDE1IDE3IDEwIiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMTUiIHkyPSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/download\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Download = createLucideIcon('Download', [\n  ['path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4', key: 'ih7n3h' }],\n  ['polyline', { points: '7 10 12 15 17 10', key: '2ggqvy' }],\n  ['line', { x1: '12', x2: '12', y1: '15', y2: '3', key: '1vk2je' }],\n]);\n\nexport default Download;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2KAAW,UAAA,EAAiB,UAAY,CAAA,CAAA,CAAA;IAC5C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;QAAA,CAAA;YAAE,QAAQ,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC1D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAClE,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 3414, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/activity.js", "sources": ["file:///G:/Augment%20code/node_modules/lucide-react/src/icons/activity.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Activity\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMTJoLTRsLTMgOUw5IDNsLTMgOUgyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/activity\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Activity = createLucideIcon('Activity', [\n  ['path', { d: 'M22 12h-4l-3 9L9 3l-3 9H2', key: 'd5dnw9' }],\n]);\n\nexport default Activity;\n"], "names": [], "mappings": ";;;;;;;;;;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2KAAW,UAAA,EAAiB,UAAY,CAAA,CAAA,CAAA;IAC5C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3D,CAAA,CAAA", "debugId": null}}]}