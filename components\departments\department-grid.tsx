"use client"

import { useState } from "react"
import { Department, Employee } from "@/lib/types"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  Building2, 
  Users, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  UserPlus,
  AlertTriangle,
  CheckCircle
} from "lucide-react"

interface DepartmentWithStats extends Department {
  employees: Employee[]
  utilization: number
  isOverCapacity: boolean
  isNearCapacity: boolean
}

interface DepartmentGridProps {
  departments: DepartmentWithStats[]
}

export function DepartmentGrid({ departments }: DepartmentGridProps) {
  const handleEdit = (department: DepartmentWithStats) => {
    // TODO: Implement edit functionality
    console.log('Edit department:', department.id)
  }

  const handleDelete = (department: DepartmentWithStats) => {
    // TODO: Implement delete functionality
    console.log('Delete department:', department.id)
  }

  const handleAddEmployee = (department: DepartmentWithStats) => {
    // TODO: Implement add employee functionality
    console.log('Add employee to department:', department.id)
  }

  const getUtilizationColor = (utilization: number, isOverCapacity: boolean) => {
    if (isOverCapacity) return "bg-red-500"
    if (utilization >= 80) return "bg-orange-500"
    if (utilization >= 60) return "bg-yellow-500"
    return "bg-green-500"
  }

  const getStatusIcon = (department: DepartmentWithStats) => {
    if (department.isOverCapacity) {
      return <AlertTriangle className="h-4 w-4 text-red-500" />
    }
    if (department.isNearCapacity) {
      return <AlertTriangle className="h-4 w-4 text-orange-500" />
    }
    return <CheckCircle className="h-4 w-4 text-green-500" />
  }

  const getStatusBadge = (department: DepartmentWithStats) => {
    if (department.isOverCapacity) {
      return <Badge variant="destructive">Over Capacity</Badge>
    }
    if (department.isNearCapacity) {
      return <Badge variant="secondary">Near Capacity</Badge>
    }
    return <Badge variant="default">Healthy</Badge>
  }

  if (departments.length === 0) {
    return (
      <div className="text-center py-8">
        <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground">No departments found.</p>
      </div>
    )
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {departments.map((department) => (
        <Card key={department.id} className="hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="flex items-center gap-2">
              <Building2 className="h-5 w-5 text-muted-foreground" />
              <CardTitle className="text-lg font-medium">
                {department.name}
              </CardTitle>
              {getStatusIcon(department)}
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem onClick={() => handleEdit(department)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Department
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleAddEmployee(department)}>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add Employee
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => handleDelete(department)}
                  className="text-destructive"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CardHeader>
          
          <CardContent>
            <div className="space-y-4">
              {/* Department Code and Status */}
              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">
                  Code: <span className="font-mono font-medium">{department.code}</span>
                </div>
                {getStatusBadge(department)}
              </div>
              
              {/* Employee Count and Capacity */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    Employees
                  </span>
                  <span className="font-medium">
                    {department.employees.length} / {department.capacity}
                  </span>
                </div>
                
                <Progress 
                  value={department.utilization} 
                  className="h-2"
                />
                
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Capacity Utilization</span>
                  <span className={`font-medium ${
                    department.isOverCapacity ? 'text-red-600' : 
                    department.isNearCapacity ? 'text-orange-600' : 
                    'text-green-600'
                  }`}>
                    {department.utilization}%
                  </span>
                </div>
              </div>
              
              {/* Employee List Preview */}
              {department.employees.length > 0 && (
                <div className="space-y-2">
                  <div className="text-sm font-medium">Recent Employees:</div>
                  <div className="space-y-1">
                    {department.employees.slice(0, 3).map((employee) => (
                      <div key={employee.id} className="text-xs text-muted-foreground">
                        {employee.name}
                      </div>
                    ))}
                    {department.employees.length > 3 && (
                      <div className="text-xs text-muted-foreground">
                        +{department.employees.length - 3} more...
                      </div>
                    )}
                  </div>
                </div>
              )}
              
              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => handleAddEmployee(department)}
                  className="flex-1"
                >
                  <UserPlus className="h-4 w-4 mr-1" />
                  Add Employee
                </Button>
                
                <Button 
                  size="sm" 
                  variant="secondary" 
                  onClick={() => handleEdit(department)}
                  className="flex-1"
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
