(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[680],{198:(e,r,t)=>{"use strict";t.d(r,{Vy:()=>o,Yq:()=>d,cn:()=>n,pp:()=>i});var s=t(5403),a=t(9055);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}function i(e,r){return"".concat(e,"-").concat(r.toString().padStart(3,"0"))}function d(e){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(e)}function o(e,r){return Math.round(e/r*100)}},360:(e,r,t)=>{Promise.resolve().then(t.bind(t,8142))},425:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var s=t(5155),a=t(2115),n=t(5441),i=t(1335),d=t(198);let o=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef((e,r)=>{let{className:t,variant:a,size:i,asChild:l=!1,...c}=e,u=l?n.DX:"button";return(0,s.jsx)(u,{className:(0,d.cn)(o({variant:a,size:i,className:t})),ref:r,...c})});l.displayName="Button"},6031:(e,r,t)=>{"use strict";t.d(r,{b:()=>d});var s=t(2115),a=t(4372),n=t(5155),i=s.forwardRef((e,r)=>(0,n.jsx)(a.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var d=i},7013:(e,r,t)=>{"use strict";t.d(r,{J:()=>l});var s=t(5155),a=t(2115),n=t(6031),i=t(1335),d=t(198);let o=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)(n.b,{ref:r,className:(0,d.cn)(o(),t),...a})});l.displayName=n.b.displayName},7071:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(5155),a=t(2115),n=t(198);let i=a.forwardRef((e,r)=>{let{className:t,type:a,...i}=e;return(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...i})});i.displayName="Input"},8142:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(5155),a=t(2115),n=t(235),i=t(425),d=t(7071),o=t(9683),l=t(7013),c=t(9554);function u(){let[e,r]=(0,a.useState)(""),[t,u]=(0,a.useState)(""),[m,f]=(0,a.useState)(!1),p=async r=>{r.preventDefault(),f(!0);try{let r=await (0,n.signIn)("credentials",{email:e,password:t,redirect:!1});(null==r?void 0:r.error)?c.oR.error("Invalid credentials"):(c.oR.success("Signed in successfully"),window.location.href="/dashboard")}catch(e){c.oR.error("An error occurred during sign in")}finally{f(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 p-4",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] -z-10"}),(0,s.jsxs)(o.Zp,{className:"w-full max-w-md shadow-strong hover-lift border-0 bg-white/80 backdrop-blur-sm",children:[(0,s.jsxs)(o.aR,{className:"space-y-6 text-center pb-8",children:[(0,s.jsx)("div",{className:"mx-auto w-16 h-16 bg-gradient-primary rounded-2xl flex items-center justify-center shadow-medium",children:(0,s.jsx)("div",{className:"text-white text-2xl font-bold",children:"HR"})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.ZB,{className:"text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"HR Synergy"}),(0,s.jsx)(o.BT,{className:"text-base text-muted-foreground",children:"Welcome back! Please sign in to your account"})]})]}),(0,s.jsxs)(o.Wu,{className:"space-y-6",children:[(0,s.jsxs)("form",{onSubmit:p,className:"space-y-5",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"email",className:"text-sm font-medium text-foreground",children:"Email Address"}),(0,s.jsx)(d.p,{id:"email",type:"email",placeholder:"Enter your email address",value:e,onChange:e=>r(e.target.value),className:"h-11 border-2 border-border/50 focus:border-primary transition-colors",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"password",className:"text-sm font-medium text-foreground",children:"Password"}),(0,s.jsx)(d.p,{id:"password",type:"password",placeholder:"Enter your password",value:t,onChange:e=>u(e.target.value),className:"h-11 border-2 border-border/50 focus:border-primary transition-colors",required:!0})]}),(0,s.jsx)(i.$,{type:"submit",className:"w-full h-11 bg-gradient-primary hover:opacity-90 transition-all duration-200 font-medium shadow-medium",disabled:m,children:m?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),"Signing in..."]}):"Sign In"})]}),(0,s.jsx)("div",{className:"pt-4 border-t border-border/50",children:(0,s.jsxs)("div",{className:"text-center text-sm text-muted-foreground",children:[(0,s.jsx)("p",{className:"mb-2",children:"Demo Credentials:"}),(0,s.jsxs)("div",{className:"space-y-1 text-xs",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Admin:"})," <EMAIL> / admin123"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"HR Manager:"})," <EMAIL> / hr123"]})]})]})})]})]})]})}},9683:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>d});var s=t(5155),a=t(2115),n=t(198);let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});i.displayName="Card";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...a})});d.displayName="CardHeader";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});o.displayName="CardTitle";let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...a})});l.displayName="CardDescription";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",t),...a})});c.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"}},e=>{e.O(0,[235,666,554,441,326,358],()=>e(e.s=360)),_N_E=e.O()}]);