/* [next]/internal/font/google/inter_59dee874.module.css [app-client] (css) */
@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/2a2d10660758e7fa-s.91b7455f.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/d6f0f7ef0a66b318-s.927aef78.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/c0062fcfb5f4a9e6-s.b7398c1c.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/1a97932d2ea76c90-s.ac666cb5.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/e27fd546b8a0677f-s.569fab99.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/a973f82a0d056f9e-s.99c7dd4e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/06ba6ef833b337bc-s.p.0faac26c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Fallback;
  src: local(Arial);
  ascent-override: 90.44%;
  descent-override: 22.52%;
  line-gap-override: 0.0%;
  size-adjust: 107.12%;
}

.inter_59dee874-module__9CtR0q__className {
  font-family: Inter, Inter Fallback;
  font-style: normal;
}

/* [project]/app/globals.css [app-client] (css) */
@tailwind base;

@tailwind components;

@tailwind utilities;

@layer base {
  :root {
    --background: 240 10% 98%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 98%;
    --primary-hover: 217 91% 55%;
    --secondary: 262 83% 58%;
    --secondary-foreground: 0 0% 98%;
    --accent: 173 80% 40%;
    --accent-foreground: 0 0% 98%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 217 91% 60%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;
    --radius: .75rem;
    --chart-1: 217 91% 60%;
    --chart-2: 262 83% 58%;
    --chart-3: 173 80% 40%;
    --chart-4: 38 92% 50%;
    --chart-5: 142 76% 36%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 217 91% 60%;
    --primary-foreground: 240 10% 3.9%;
    --primary-hover: 217 91% 65%;
    --secondary: 262 83% 58%;
    --secondary-foreground: 240 10% 3.9%;
    --accent: 173 80% 40%;
    --accent-foreground: 240 10% 3.9%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 217 91% 60%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 240 10% 3.9%;
    --chart-1: 217 91% 60%;
    --chart-2: 262 83% 58%;
    --chart-3: 173 80% 40%;
    --chart-4: 38 92% 50%;
    --chart-5: 142 76% 36%;
  }

  @apply border-border;

  @apply bg-background text-foreground font-sans antialiased;

  body {
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  @apply font-semibold tracking-tight;

  @apply text-4xl lg:text-5xl;

  @apply text-3xl lg:text-4xl;

  @apply text-2xl lg:text-3xl;

  @apply text-xl lg:text-2xl;
}

@layer components {
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 100%);
  }

  .gradient-card {
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
  }

  .gradient-accent {
    background: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(var(--primary)) 100%);
  }

  .glass {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, .1);
    border: 1px solid rgba(255, 255, 255, .2);
  }

  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, .07), 0 10px 20px -2px rgba(0, 0, 0, .04);
  }

  .shadow-medium {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, .1), 0 10px 10px -5px rgba(0, 0, 0, .04);
  }

  .shadow-strong {
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, .15), 0 2px 10px -2px rgba(0, 0, 0, .05);
  }

  .hover-lift {
    transition: all .2s ease-in-out;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px -5px rgba(0, 0, 0, .15);
  }

  @apply bg-green-50 text-green-700 border-green-200;

  @apply bg-yellow-50 text-yellow-700 border-yellow-200;

  @apply bg-red-50 text-red-700 border-red-200;

  @apply bg-blue-50 text-blue-700 border-blue-200;

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }

    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes pulse-soft {
    0%, 100% {
      opacity: 1;
    }

    50% {
      opacity: .8;
    }
  }

  .animate-fade-in {
    animation: .5s ease-out fadeIn;
  }

  .animate-slide-in {
    animation: .3s ease-out slideIn;
  }

  .animate-pulse-soft {
    animation: 2s infinite pulse-soft;
  }

  .bg-grid-slate-100 {
    background-image: linear-gradient(to right, rgba(241, 245, 249, .5) 1px, rgba(0, 0, 0, 0) 1px), linear-gradient(rgba(241, 245, 249, .5) 1px, rgba(0, 0, 0, 0) 1px);
    background-size: 20px 20px;
  }

  html {
    scroll-behavior: smooth;
  }

  @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background;
}

/*# sourceMappingURL=%5Broot-of-the-server%5D__33bd1d78._.css.map*/