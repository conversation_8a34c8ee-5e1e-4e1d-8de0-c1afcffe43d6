(()=>{var a={};a.id=228,a.ids=[228],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(18962).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},45224:(a,b,c)=>{Promise.resolve().then(c.bind(c,85803))},58376:(a,b,c)=>{Promise.resolve().then(c.bind(c,86327))},60525:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["employees",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,86327)),"G:\\Augment code\\app\\(dashboard)\\employees\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,71934)),"G:\\Augment code\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"G:\\Augment code\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["G:\\Augment code\\app\\(dashboard)\\employees\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/employees/page",pathname:"/employees",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/employees/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(a,b,c)=>{"use strict";c.d(b,{bq:()=>m,eb:()=>q,gC:()=>p,l6:()=>k,yv:()=>l});var d=c(60687),e=c(43210),f=c(97822),g=c(78272),h=c(3589),i=c(13964),j=c(96241);let k=f.bL;f.YJ;let l=f.WT,m=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.l9,{ref:e,className:(0,j.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...c,children:[b,(0,d.jsx)(f.In,{asChild:!0,children:(0,d.jsx)(g.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=f.l9.displayName;let n=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.PP,{ref:c,className:(0,j.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(h.A,{className:"h-4 w-4"})}));n.displayName=f.PP.displayName;let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wn,{ref:c,className:(0,j.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(g.A,{className:"h-4 w-4"})}));o.displayName=f.wn.displayName;let p=e.forwardRef(({className:a,children:b,position:c="popper",...e},g)=>(0,d.jsx)(f.ZL,{children:(0,d.jsxs)(f.UC,{ref:g,className:(0,j.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...e,children:[(0,d.jsx)(n,{}),(0,d.jsx)(f.LM,{className:(0,j.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:b}),(0,d.jsx)(o,{})]})}));p.displayName=f.UC.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.JU,{ref:c,className:(0,j.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...b})).displayName=f.JU.displayName;let q=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.q7,{ref:e,className:(0,j.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(f.VF,{children:(0,d.jsx)(i.A,{className:"h-4 w-4"})})}),(0,d.jsx)(f.p4,{children:b})]}));q.displayName=f.q7.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wv,{ref:c,className:(0,j.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=f.wv.displayName},85803:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>J});var d=c(60687),e=c(43210),f=c(82136),g=c(55192),h=c(24934),i=c(68988),j=c(42850),k=c(59821),l=c(96241),m=c(96752),n=c(55629),o=c(93437),p=c(91356),q=c(9923),r=c(70334),s=c(88233);function t({employees:a,departments:b}){let{selectedEmployees:c,toggleEmployeeSelection:e,selectAllEmployees:f,clearSelection:g}=(0,j.d)(),i=a.length>0&&c.length===a.length,t=c.length>0&&c.length<a.length;return 0===a.length?(0,d.jsx)("div",{className:"text-center py-8",children:(0,d.jsx)("p",{className:"text-muted-foreground",children:"No employees found."})}):(0,d.jsx)("div",{className:"rounded-md border",children:(0,d.jsxs)(m.XI,{children:[(0,d.jsx)(m.A0,{children:(0,d.jsxs)(m.Hj,{children:[(0,d.jsx)(m.nd,{className:"w-12",children:(0,d.jsx)(o.S,{checked:i,indeterminate:t,onCheckedChange:()=>{i?g():f(a.map(a=>a.id))},"aria-label":"Select all employees"})}),(0,d.jsx)(m.nd,{children:"Employee ID"}),(0,d.jsx)(m.nd,{children:"Name"}),(0,d.jsx)(m.nd,{children:"Email"}),(0,d.jsx)(m.nd,{children:"Department"}),(0,d.jsx)(m.nd,{children:"Status"}),(0,d.jsx)(m.nd,{children:"Hire Date"}),(0,d.jsx)(m.nd,{className:"w-12"})]})}),(0,d.jsx)(m.BF,{children:a.map(a=>{var f;return(0,d.jsxs)(m.Hj,{children:[(0,d.jsx)(m.nA,{children:(0,d.jsx)(o.S,{checked:c.includes(a.id),onCheckedChange:()=>e(a.id),"aria-label":`Select ${a.name}`})}),(0,d.jsx)(m.nA,{className:"font-mono text-sm",children:a.id}),(0,d.jsx)(m.nA,{className:"font-medium",children:a.name}),(0,d.jsx)(m.nA,{className:"text-muted-foreground",children:a.email}),(0,d.jsx)(m.nA,{children:(a=>{if(!a)return"Unassigned";let c=b.find(b=>b.id===a);return c?.name||"Unknown"})(a.departmentId)}),(0,d.jsx)(m.nA,{children:(f=a.status,(0,d.jsx)(k.E,{variant:{ACTIVE:"default",TRANSFERRED:"secondary",PENDING_REMOVAL:"destructive",ARCHIVED:"outline"}[f],children:f.replace("_"," ")}))}),(0,d.jsx)(m.nA,{className:"text-muted-foreground",children:(0,l.Yq)(a.hireDate)}),(0,d.jsx)(m.nA,{children:(0,d.jsxs)(n.rI,{children:[(0,d.jsx)(n.ty,{asChild:!0,children:(0,d.jsxs)(h.$,{variant:"ghost",className:"h-8 w-8 p-0",children:[(0,d.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,d.jsx)(p.A,{className:"h-4 w-4"})]})}),(0,d.jsxs)(n.SQ,{align:"end",children:[(0,d.jsx)(n.lp,{children:"Actions"}),(0,d.jsxs)(n._2,{onClick:()=>{console.log("Edit employee:",a.id)},children:[(0,d.jsx)(q.A,{className:"mr-2 h-4 w-4"}),"Edit Employee"]}),(0,d.jsxs)(n._2,{onClick:()=>{console.log("Transfer employee:",a.id)},children:[(0,d.jsx)(r.A,{className:"mr-2 h-4 w-4"}),"Transfer"]}),(0,d.jsx)(n.mB,{}),(0,d.jsxs)(n._2,{onClick:()=>{console.log("Delete employee:",a.id)},className:"text-destructive",children:[(0,d.jsx)(s.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})})]},a.id)})})]})})}var u=c(39390),v=c(63974);function w({filters:a,onFiltersChange:b,departments:c}){return(0,d.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(u.J,{htmlFor:"department-filter",children:"Department"}),(0,d.jsxs)(v.l6,{value:a.department||"all",onValueChange:c=>{b({...a,department:"all"===c?"":c})},children:[(0,d.jsx)(v.bq,{children:(0,d.jsx)(v.yv,{placeholder:"All departments"})}),(0,d.jsxs)(v.gC,{children:[(0,d.jsx)(v.eb,{value:"all",children:"All Departments"}),(0,d.jsx)(v.eb,{value:"",children:"Unassigned"}),c.map(a=>(0,d.jsx)(v.eb,{value:a.id,children:a.name},a.id))]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(u.J,{htmlFor:"status-filter",children:"Status"}),(0,d.jsxs)(v.l6,{value:a.status||"all",onValueChange:c=>{b({...a,status:"all"===c?"":c})},children:[(0,d.jsx)(v.bq,{children:(0,d.jsx)(v.yv,{placeholder:"All statuses"})}),(0,d.jsxs)(v.gC,{children:[(0,d.jsx)(v.eb,{value:"all",children:"All Statuses"}),(0,d.jsx)(v.eb,{value:"ACTIVE",children:"Active"}),(0,d.jsx)(v.eb,{value:"TRANSFERRED",children:"Transferred"}),(0,d.jsx)(v.eb,{value:"PENDING_REMOVAL",children:"Pending Removal"}),(0,d.jsx)(v.eb,{value:"ARCHIVED",children:"Archived"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(u.J,{children:"Date Range"}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:"Date range filtering coming soon"})]})]})}var x=c(27253);function y({open:a,onOpenChange:b,departments:c}){let{addEmployee:f,employees:g}=(0,j.d)(),[k,m]=(0,e.useState)(!1),[n,o]=(0,e.useState)({name:"",email:"",departmentId:""}),p=async a=>{a.preventDefault(),m(!0);try{let a=c.find(a=>a.id===n.departmentId),d=a?.code||"GEN",e=g.filter(a=>a.id.startsWith(d)).map(a=>parseInt(a.id.split("-")[1])||0),h=Math.max(0,...e)+1,i={id:(0,l.pp)(d,h),name:n.name,email:n.email,departmentId:n.departmentId||null,status:"ACTIVE",hireDate:new Date,transferHistory:[],createdAt:new Date,updatedAt:new Date};f(i),o({name:"",email:"",departmentId:""}),b(!1)}catch(a){console.error("Failed to add employee:",a)}finally{m(!1)}},q=(a,b)=>{o(c=>({...c,[a]:b}))};return(0,d.jsx)(x.lG,{open:a,onOpenChange:b,children:(0,d.jsxs)(x.Cf,{className:"sm:max-w-[425px]",children:[(0,d.jsxs)(x.c7,{children:[(0,d.jsx)(x.L3,{children:"Add New Employee"}),(0,d.jsx)(x.rr,{children:"Create a new employee record. They will be assigned to the selected department."})]}),(0,d.jsxs)("form",{onSubmit:p,children:[(0,d.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,d.jsxs)("div",{className:"grid gap-2",children:[(0,d.jsx)(u.J,{htmlFor:"name",children:"Full Name"}),(0,d.jsx)(i.p,{id:"name",value:n.name,onChange:a=>q("name",a.target.value),placeholder:"Enter employee name",required:!0})]}),(0,d.jsxs)("div",{className:"grid gap-2",children:[(0,d.jsx)(u.J,{htmlFor:"email",children:"Email Address"}),(0,d.jsx)(i.p,{id:"email",type:"email",value:n.email,onChange:a=>q("email",a.target.value),placeholder:"Enter email address",required:!0})]}),(0,d.jsxs)("div",{className:"grid gap-2",children:[(0,d.jsx)(u.J,{htmlFor:"department",children:"Department"}),(0,d.jsxs)(v.l6,{value:n.departmentId,onValueChange:a=>q("departmentId",a),children:[(0,d.jsx)(v.bq,{children:(0,d.jsx)(v.yv,{placeholder:"Select department"})}),(0,d.jsxs)(v.gC,{children:[(0,d.jsx)(v.eb,{value:"",children:"Unassigned (Free Bucket)"}),c.map(a=>(0,d.jsxs)(v.eb,{value:a.id,children:[a.name," (",a.code,")"]},a.id))]})]})]})]}),(0,d.jsxs)(x.Es,{children:[(0,d.jsx)(h.$,{type:"button",variant:"outline",onClick:()=>b(!1),disabled:k,children:"Cancel"}),(0,d.jsx)(h.$,{type:"submit",disabled:k,children:k?"Adding...":"Add Employee"})]})]})]})})}var z=c(41312),A=c(77026),B=c(11860);function C({selectedCount:a,onClearSelection:b}){let{selectedEmployees:c,executeBulkOperation:f}=(0,j.d)(),[g,i]=(0,e.useState)(!1),k=async()=>{i(!0);try{console.log("Bulk transfer:",c)}catch(a){console.error("Bulk transfer failed:",a)}finally{i(!1)}},l=async()=>{i(!0);try{await f({type:"status_update",employeeIds:c,newStatus:"ARCHIVED"})}catch(a){console.error("Bulk archive failed:",a)}finally{i(!1)}},m=async()=>{if(confirm(`Are you sure you want to delete ${a} employees? This action cannot be undone.`)){i(!0);try{await f({type:"delete",employeeIds:c})}catch(a){console.error("Bulk delete failed:",a)}finally{i(!1)}}};return(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 bg-muted/50 rounded-lg border",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(z.A,{className:"h-4 w-4"}),(0,d.jsxs)("span",{className:"text-sm font-medium",children:[a," employee",1!==a?"s":""," selected"]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)(h.$,{variant:"outline",size:"sm",onClick:k,disabled:g,children:[(0,d.jsx)(r.A,{className:"h-4 w-4 mr-2"}),"Transfer"]}),(0,d.jsxs)(h.$,{variant:"outline",size:"sm",onClick:l,disabled:g,children:[(0,d.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Archive"]}),(0,d.jsxs)(h.$,{variant:"outline",size:"sm",onClick:m,disabled:g,className:"text-destructive hover:text-destructive",children:[(0,d.jsx)(s.A,{className:"h-4 w-4 mr-2"}),"Delete"]}),(0,d.jsx)(h.$,{variant:"ghost",size:"sm",onClick:b,disabled:g,children:(0,d.jsx)(B.A,{className:"h-4 w-4"})})]})]})}var D=c(18516),E=c(16023),F=c(31158),G=c(23026),H=c(99270),I=c(98492);function J(){let{data:a}=(0,f.useSession)(),{employees:b,departments:c,selectedEmployees:k,searchQuery:l,setSearchQuery:m,getFilteredEmployees:n,clearSelection:o}=(0,j.d)(),[p,q]=(0,e.useState)(!1),[r,s]=(0,e.useState)(!1),[u,v]=(0,e.useState)({department:"",status:"",dateRange:{from:null,to:null}});(0,D.s)();let x=n(),A=k.length>0;return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Employees"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Manage your organization's employees and their assignments."})]}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsxs)(h.$,{variant:"outline",onClick:()=>{console.log("Importing employees...")},children:[(0,d.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Import CSV"]}),(0,d.jsxs)(h.$,{variant:"outline",onClick:()=>{console.log("Exporting employees...")},children:[(0,d.jsx)(F.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]}),(0,d.jsxs)(h.$,{onClick:()=>q(!0),children:[(0,d.jsx)(G.A,{className:"h-4 w-4 mr-2"}),"Add Employee"]})]})]}),(0,d.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"Total Employees"}),(0,d.jsx)(z.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:b.length}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active employees"})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"Active"}),(0,d.jsx)(z.A,{className:"h-4 w-4 text-green-600"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:b.filter(a=>"ACTIVE"===a.status).length}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Currently active"})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"Transferred"}),(0,d.jsx)(z.A,{className:"h-4 w-4 text-blue-600"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:b.filter(a=>"TRANSFERRED"===a.status).length}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Recently transferred"})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"Unassigned"}),(0,d.jsx)(z.A,{className:"h-4 w-4 text-orange-600"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:b.filter(a=>null===a.departmentId).length}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"In free bucket"})]})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)(g.ZB,{children:"Employee Management"}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(H.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(i.p,{placeholder:"Search employees...",value:l,onChange:a=>{m(a.target.value)},className:"pl-8 w-[300px]"})]}),(0,d.jsxs)(h.$,{variant:"outline",size:"sm",onClick:()=>s(!r),children:[(0,d.jsx)(I.A,{className:"h-4 w-4 mr-2"}),"Filters"]}),(l||u.department||u.status)&&(0,d.jsx)(h.$,{variant:"ghost",size:"sm",onClick:()=>{v({department:"",status:"",dateRange:{from:null,to:null}}),m("")},children:"Clear"})]})]})}),(0,d.jsxs)(g.Wu,{children:[r&&(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)(w,{filters:u,onFiltersChange:v,departments:c})}),A&&(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)(C,{selectedCount:k.length,onClearSelection:o})}),(0,d.jsx)(t,{employees:x,departments:c})]})]}),(0,d.jsx)(y,{open:p,onOpenChange:q,departments:c})]})}},86327:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\employees\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Augment code\\app\\(dashboard)\\employees\\page.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},93437:(a,b,c)=>{"use strict";c.d(b,{S:()=>i});var d=c(60687),e=c(43210),f=c(40211),g=c(13964),h=c(96241);let i=e.forwardRef(({className:a,indeterminate:b,...c},e)=>(0,d.jsx)(f.bL,{ref:e,className:(0,h.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...c,children:(0,d.jsx)(f.C1,{className:(0,h.cn)("flex items-center justify-center text-current"),children:b?(0,d.jsx)("div",{className:"h-2 w-2 bg-current rounded-sm"}):(0,d.jsx)(g.A,{className:"h-4 w-4"})})}));i.displayName=f.bL.displayName},96752:(a,b,c)=>{"use strict";c.d(b,{A0:()=>h,BF:()=>i,Hj:()=>j,XI:()=>g,nA:()=>l,nd:()=>k});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{className:"relative w-full overflow-auto",children:(0,d.jsx)("table",{ref:c,className:(0,f.cn)("w-full caption-bottom text-sm",a),...b})}));g.displayName="Table";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("thead",{ref:c,className:(0,f.cn)("[&_tr]:border-b",a),...b}));h.displayName="TableHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("tbody",{ref:c,className:(0,f.cn)("[&_tr:last-child]:border-0",a),...b}));i.displayName="TableBody",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("tfoot",{ref:c,className:(0,f.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...b})).displayName="TableFooter";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("tr",{ref:c,className:(0,f.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...b}));j.displayName="TableRow";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("th",{ref:c,className:(0,f.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...b}));k.displayName="TableHead";let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("td",{ref:c,className:(0,f.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...b}));l.displayName="TableCell",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("caption",{ref:c,className:(0,f.cn)("mt-4 text-sm text-muted-foreground",a),...b})).displayName="TableCaption"},98492:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(18962).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[602,440,579,175,362,682,786],()=>b(b.s=60525));module.exports=c})();