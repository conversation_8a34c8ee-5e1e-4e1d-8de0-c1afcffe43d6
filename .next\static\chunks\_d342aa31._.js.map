{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/lib/store/hr-store.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { subscribeWithSelector } from 'zustand/middleware'\nimport { Employee, Department, BulkOperation } from '@/lib/types'\n\ninterface HRState {\n  // Data\n  employees: Employee[]\n  departments: Department[]\n  freeBucket: Employee[]\n  \n  // UI State\n  selectedEmployees: string[]\n  isLoading: boolean\n  searchQuery: string\n  \n  // Actions\n  setEmployees: (employees: Employee[]) => void\n  setDepartments: (departments: Department[]) => void\n  addEmployee: (employee: Employee) => void\n  updateEmployee: (id: string, updates: Partial<Employee>) => void\n  removeEmployee: (id: string) => void\n  \n  // Selection\n  toggleEmployeeSelection: (id: string) => void\n  selectAllEmployees: (ids: string[]) => void\n  clearSelection: () => void\n  \n  // Bulk Operations\n  executeBulkOperation: (operation: BulkOperation) => Promise<void>\n  \n  // Search & Filter\n  setSearchQuery: (query: string) => void\n  getFilteredEmployees: () => Employee[]\n  \n  // Free Bucket\n  moveToFreeBucket: (employeeIds: string[]) => void\n  removeFromFreeBucket: (employeeIds: string[]) => void\n}\n\nexport const useHRStore = create<HRState>()(\n  subscribeWithSelector((set, get) => ({\n    // Initial state\n    employees: [],\n    departments: [],\n    freeBucket: [],\n    selectedEmployees: [],\n    isLoading: false,\n    searchQuery: '',\n\n    // Data actions\n    setEmployees: (employees) => set((state) => ({\n      employees,\n      freeBucket: employees.filter(emp => emp.departmentId === null)\n    })),\n    setDepartments: (departments) => set({ departments }),\n    \n    addEmployee: (employee) => set((state) => ({\n      employees: [...state.employees, employee]\n    })),\n    \n    updateEmployee: (id, updates) => set((state) => ({\n      employees: state.employees.map(emp => \n        emp.id === id ? { ...emp, ...updates } : emp\n      )\n    })),\n    \n    removeEmployee: (id) => set((state) => ({\n      employees: state.employees.filter(emp => emp.id !== id),\n      selectedEmployees: state.selectedEmployees.filter(empId => empId !== id)\n    })),\n\n    // Selection actions\n    toggleEmployeeSelection: (id) => set((state) => ({\n      selectedEmployees: state.selectedEmployees.includes(id)\n        ? state.selectedEmployees.filter(empId => empId !== id)\n        : [...state.selectedEmployees, id]\n    })),\n    \n    selectAllEmployees: (ids) => set({ selectedEmployees: ids }),\n    clearSelection: () => set({ selectedEmployees: [] }),\n\n    // Bulk operations\n    executeBulkOperation: async (operation) => {\n      const { employees, selectedEmployees } = get()\n      \n      switch (operation.type) {\n        case 'transfer':\n          set((state) => ({\n            employees: state.employees.map(emp =>\n              operation.employeeIds.includes(emp.id)\n                ? { ...emp, departmentId: operation.targetDepartmentId || null }\n                : emp\n            ),\n            freeBucket: state.employees\n              .map(emp =>\n                operation.employeeIds.includes(emp.id)\n                  ? { ...emp, departmentId: operation.targetDepartmentId || null }\n                  : emp\n              )\n              .filter(emp => emp.departmentId === null)\n          }))\n          break\n          \n        case 'status_update':\n          if (operation.newStatus) {\n            set((state) => ({\n              employees: state.employees.map(emp =>\n                operation.employeeIds.includes(emp.id)\n                  ? { ...emp, status: operation.newStatus! }\n                  : emp\n              )\n            }))\n          }\n          break\n          \n        case 'delete':\n          set((state) => ({\n            employees: state.employees.filter(emp => \n              !operation.employeeIds.includes(emp.id)\n            )\n          }))\n          break\n      }\n      \n      set({ selectedEmployees: [] })\n    },\n\n    // Search & filter\n    setSearchQuery: (query) => set({ searchQuery: query }),\n    \n    getFilteredEmployees: () => {\n      const { employees, searchQuery } = get()\n      if (!searchQuery) return employees\n      \n      return employees.filter(emp =>\n        emp.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        emp.email.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        emp.id.toLowerCase().includes(searchQuery.toLowerCase())\n      )\n    },\n\n    // Free bucket operations\n    moveToFreeBucket: (employeeIds) => set((state) => {\n      const movedEmployees = state.employees\n        .filter(emp => employeeIds.includes(emp.id))\n        .map(emp => ({ ...emp, departmentId: null }))\n      \n      return {\n        employees: state.employees.map(emp =>\n          employeeIds.includes(emp.id) ? { ...emp, departmentId: null } : emp\n        ),\n        freeBucket: [...state.freeBucket, ...movedEmployees]\n      }\n    }),\n    \n    removeFromFreeBucket: (employeeIds) => set((state) => ({\n      freeBucket: state.freeBucket.filter(emp => !employeeIds.includes(emp.id)),\n      employees: state.employees.filter(emp => !employeeIds.includes(emp.id))\n    }))\n  }))\n)"], "names": [], "mappings": ";;;AAAA;AACA;;;AAsCO,MAAM,aAAa,CAAA,GAAA,2JAAA,CAAA,SAAM,AAAD,IAC7B,CAAA,GAAA,gJAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC,KAAK,MAAQ,CAAC;QACnC,gBAAgB;QAChB,WAAW,EAAE;QACb,aAAa,EAAE;QACf,YAAY,EAAE;QACd,mBAAmB,EAAE;QACrB,WAAW;QACX,aAAa;QAEb,eAAe;QACf,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C;oBACA,YAAY,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK;gBAC3D,CAAC;QACD,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QAEnD,aAAa,CAAC,WAAa,IAAI,CAAC,QAAU,CAAC;oBACzC,WAAW;2BAAI,MAAM,SAAS;wBAAE;qBAAS;gBAC3C,CAAC;QAED,gBAAgB,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC/C,WAAW,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,MAC7B,IAAI,EAAE,KAAK,KAAK;4BAAE,GAAG,GAAG;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAE7C,CAAC;QAED,gBAAgB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBACtC,WAAW,MAAM,SAAS,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;oBACpD,mBAAmB,MAAM,iBAAiB,CAAC,MAAM,CAAC,CAAA,QAAS,UAAU;gBACvE,CAAC;QAED,oBAAoB;QACpB,yBAAyB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAC/C,mBAAmB,MAAM,iBAAiB,CAAC,QAAQ,CAAC,MAChD,MAAM,iBAAiB,CAAC,MAAM,CAAC,CAAA,QAAS,UAAU,MAClD;2BAAI,MAAM,iBAAiB;wBAAE;qBAAG;gBACtC,CAAC;QAED,oBAAoB,CAAC,MAAQ,IAAI;gBAAE,mBAAmB;YAAI;QAC1D,gBAAgB,IAAM,IAAI;gBAAE,mBAAmB,EAAE;YAAC;QAElD,kBAAkB;QAClB,sBAAsB,OAAO;YAC3B,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAE,GAAG;YAEzC,OAAQ,UAAU,IAAI;gBACpB,KAAK;oBACH,IAAI,CAAC,QAAU,CAAC;4BACd,WAAW,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,MAC7B,UAAU,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,IACjC;oCAAE,GAAG,GAAG;oCAAE,cAAc,UAAU,kBAAkB,IAAI;gCAAK,IAC7D;4BAEN,YAAY,MAAM,SAAS,CACxB,GAAG,CAAC,CAAA,MACH,UAAU,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,IACjC;oCAAE,GAAG,GAAG;oCAAE,cAAc,UAAU,kBAAkB,IAAI;gCAAK,IAC7D,KAEL,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK;wBACxC,CAAC;oBACD;gBAEF,KAAK;oBACH,IAAI,UAAU,SAAS,EAAE;wBACvB,IAAI,CAAC,QAAU,CAAC;gCACd,WAAW,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,MAC7B,UAAU,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,IACjC;wCAAE,GAAG,GAAG;wCAAE,QAAQ,UAAU,SAAS;oCAAE,IACvC;4BAER,CAAC;oBACH;oBACA;gBAEF,KAAK;oBACH,IAAI,CAAC,QAAU,CAAC;4BACd,WAAW,MAAM,SAAS,CAAC,MAAM,CAAC,CAAA,MAChC,CAAC,UAAU,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE;wBAE1C,CAAC;oBACD;YACJ;YAEA,IAAI;gBAAE,mBAAmB,EAAE;YAAC;QAC9B;QAEA,kBAAkB;QAClB,gBAAgB,CAAC,QAAU,IAAI;gBAAE,aAAa;YAAM;QAEpD,sBAAsB;YACpB,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG;YACnC,IAAI,CAAC,aAAa,OAAO;YAEzB,OAAO,UAAU,MAAM,CAAC,CAAA,MACtB,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACvD,IAAI,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACxD,IAAI,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAEzD;QAEA,yBAAyB;QACzB,kBAAkB,CAAC,cAAgB,IAAI,CAAC;gBACtC,MAAM,iBAAiB,MAAM,SAAS,CACnC,MAAM,CAAC,CAAA,MAAO,YAAY,QAAQ,CAAC,IAAI,EAAE,GACzC,GAAG,CAAC,CAAA,MAAO,CAAC;wBAAE,GAAG,GAAG;wBAAE,cAAc;oBAAK,CAAC;gBAE7C,OAAO;oBACL,WAAW,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,MAC7B,YAAY,QAAQ,CAAC,IAAI,EAAE,IAAI;4BAAE,GAAG,GAAG;4BAAE,cAAc;wBAAK,IAAI;oBAElE,YAAY;2BAAI,MAAM,UAAU;2BAAK;qBAAe;gBACtD;YACF;QAEA,sBAAsB,CAAC,cAAgB,IAAI,CAAC,QAAU,CAAC;oBACrD,YAAY,MAAM,UAAU,CAAC,MAAM,CAAC,CAAA,MAAO,CAAC,YAAY,QAAQ,CAAC,IAAI,EAAE;oBACvE,WAAW,MAAM,SAAS,CAAC,MAAM,CAAC,CAAA,MAAO,CAAC,YAAY,QAAQ,CAAC,IAAI,EAAE;gBACvE,CAAC;IACH,CAAC", "debugId": null}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/G%3A/Augment%20code/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": ["React", "cva", "cn", "badgeVariants", "variants", "variant", "default", "secondary", "destructive", "outline", "defaultVariants", "Badge", "t0", "$", "_c", "$i", "Symbol", "for", "className", "props", "t1", "t2"], "mappings": ";;;;;;AACA,SAASC,GAAG,QAA2B,0BAA0B;AACjE,SAASC,EAAE,QAAQ,aAAa;;;;;AAEhC,MAAMC,aAAa,2KAAGF,MAAAA,AAAG,EACvB,wKAAwK,EACxK;IACEG,QAAQ,EAAE;QACRC,OAAO,EAAE;YACPC,OAAO,EACL,2EAA2E;YAC7EC,SAAS,EACP,iFAAiF;YACnFC,WAAW,EACT,uFAAuF;YACzFC,OAAO,EAAE;QACX;IACF,CAAC;IACDC,eAAe,EAAE;QACfL,OAAO,EAAE;IACX;AACF,CACF,CAAC;AAMD,eAAAO,EAAA;IAAA,MAAAC,CAAA,kLAAAC,KAAAA,AAAA,EAAA;IAAA,IAAAD,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAE,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAF,CAAA,CAAAE,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAJ,CAAA,CAAA,EAAA,GAAA;IAAA;IAAA,IAAAK,SAAA;IAAA,IAAAC,KAAA;IAAA,IAAAd,OAAA;IAAA,IAAAQ,CAAA,CAAA,EAAA,KAAAD,EAAA,EAAA;QAAe,CAAA,EAAAM,SAAA,EAAAb,OAAA,EAAA,GAAAc,OAAA,GAAAP,EAA4C;QAAAC,CAAA,CAAA,EAAA,GAAAD,EAAA;QAAAC,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAM,KAAA;QAAAN,CAAA,CAAA,EAAA,GAAAR,OAAA;IAAA,OAAA;QAAAa,SAAA,GAAAL,CAAA,CAAA,EAAA;QAAAM,KAAA,GAAAN,CAAA,CAAA,EAAA;QAAAR,OAAA,GAAAQ,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAO,EAAA;IAAA,IAAAP,CAAA,CAAA,EAAA,KAAAK,SAAA,IAAAL,CAAA,CAAA,EAAA,KAAAR,OAAA,EAAA;QAEvCe,EAAA,uHAAAlB,KAAAA,AAAA,EAAGC,aAAA,CAAA;YAAAE;QAAA,CAAyB,CAAC,EAAEa,SAAS,CAAC;QAAAL,CAAA,CAAA,EAAA,GAAAK,SAAA;QAAAL,CAAA,CAAA,EAAA,GAAAR,OAAA;QAAAQ,CAAA,CAAA,EAAA,GAAAO,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAP,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAQ,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAM,KAAA,IAAAN,CAAA,CAAA,EAAA,KAAAO,EAAA,EAAA;QAAzDC,EAAA,iBAAA,6LAAA,GAAwE;YAAxD,SAAyC,CAAzC,CAAAD,EAAwC,CAAC;YAAA,GAAMD,KAAK,IAAI;;;;;;QAAAN,CAAA,CAAA,EAAA,GAAAM,KAAA;QAAAN,CAAA,CAAA,EAAA,GAAAO,EAAA;QAAAP,CAAA,CAAA,GAAA,GAAAQ,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAR,CAAA,CAAA,GAAA;IAAA;IAAA,OAAxEQ,EAAwE;AAAA;KAF5EV", "ignoreList": [], "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/progress.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAG/B,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,AAAC,eAAiC,OAAnB,MAAM,CAAC,SAAS,CAAC,GAAE;YAAI;;;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/bulk/G%3A/Augment%20code/components/bulk/csv-import-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useRef } from \"react\"\nimport { Department, Employee, CSVImportResult } from \"@/lib/types\"\nimport { useHRStore } from \"@/lib/store/hr-store\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { generateEmployeeId } from \"@/lib/utils\"\nimport { \n  Upload, \n  FileText, \n  CheckCircle, \n  AlertCircle,\n  Download\n} from \"lucide-react\"\n\ninterface CSVImportSectionProps {\n  departments: Department[]\n}\n\nexport function CSVImportSection({ departments }: CSVImportSectionProps) {\n  const { employees, addEmployee } = useHRStore()\n  const [isUploading, setIsUploading] = useState(false)\n  const [uploadProgress, setUploadProgress] = useState(0)\n  const [importResult, setImportResult] = useState<CSVImportResult | null>(null)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  const handleFileSelect = () => {\n    fileInputRef.current?.click()\n  }\n\n  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (!file) return\n\n    if (!file.name.endsWith('.csv')) {\n      alert('Please select a CSV file')\n      return\n    }\n\n    setIsUploading(true)\n    setUploadProgress(0)\n    setImportResult(null)\n\n    try {\n      const text = await file.text()\n      const result = await processCSV(text)\n      setImportResult(result)\n    } catch (error) {\n      console.error('CSV import failed:', error)\n      alert('Failed to import CSV file')\n    } finally {\n      setIsUploading(false)\n      setUploadProgress(0)\n      // Reset file input\n      if (fileInputRef.current) {\n        fileInputRef.current.value = ''\n      }\n    }\n  }\n\n  const processCSV = async (csvText: string): Promise<CSVImportResult> => {\n    const lines = csvText.trim().split('\\n')\n    const headers = lines[0].split(',').map(h => h.trim().toLowerCase())\n    \n    // Validate required headers\n    const requiredHeaders = ['name', 'email']\n    const missingHeaders = requiredHeaders.filter(h => !headers.includes(h))\n    \n    if (missingHeaders.length > 0) {\n      throw new Error(`Missing required headers: ${missingHeaders.join(', ')}`)\n    }\n\n    const result: CSVImportResult = {\n      success: [],\n      errors: []\n    }\n\n    // Process each row\n    for (let i = 1; i < lines.length; i++) {\n      setUploadProgress((i / (lines.length - 1)) * 100)\n      \n      const values = lines[i].split(',').map(v => v.trim())\n      const rowData: Record<string, string> = {}\n      \n      headers.forEach((header, index) => {\n        rowData[header] = values[index] || ''\n      })\n\n      try {\n        // Validate required fields\n        if (!rowData.name || !rowData.email) {\n          throw new Error('Name and email are required')\n        }\n\n        // Check if email already exists\n        if (employees.some(emp => emp.email.toLowerCase() === rowData.email.toLowerCase())) {\n          throw new Error('Email already exists')\n        }\n\n        // Find department if specified\n        let departmentId: string | null = null\n        if (rowData.departmentcode) {\n          const department = departments.find(d => \n            d.code.toLowerCase() === rowData.departmentcode.toLowerCase()\n          )\n          if (!department) {\n            throw new Error(`Department with code '${rowData.departmentcode}' not found`)\n          }\n          departmentId = department.id\n        }\n\n        // Generate employee ID\n        const department = departments.find(d => d.id === departmentId)\n        const departmentCode = department?.code || 'GEN'\n        const existingIds = employees\n          .filter(emp => emp.id.startsWith(departmentCode))\n          .map(emp => parseInt(emp.id.split('-')[1]) || 0)\n        const nextSequence = Math.max(0, ...existingIds) + result.success.length + 1\n        const employeeId = generateEmployeeId(departmentCode, nextSequence)\n\n        // Validate status if provided\n        const validStatuses = ['ACTIVE', 'TRANSFERRED', 'PENDING_REMOVAL', 'ARCHIVED']\n        const status = rowData.status?.toUpperCase() || 'ACTIVE'\n        if (!validStatuses.includes(status)) {\n          throw new Error(`Invalid status '${rowData.status}'. Must be one of: ${validStatuses.join(', ')}`)\n        }\n\n        const newEmployee: Employee = {\n          id: employeeId,\n          name: rowData.name,\n          email: rowData.email,\n          departmentId,\n          status: status as Employee['status'],\n          hireDate: new Date(),\n          transferHistory: [],\n          createdAt: new Date(),\n          updatedAt: new Date(),\n        }\n\n        result.success.push(newEmployee)\n      } catch (error) {\n        result.errors.push({\n          row: i + 1,\n          data: rowData,\n          error: error instanceof Error ? error.message : 'Unknown error'\n        })\n      }\n\n      // Small delay to show progress\n      await new Promise(resolve => setTimeout(resolve, 10))\n    }\n\n    return result\n  }\n\n  const handleConfirmImport = () => {\n    if (!importResult) return\n\n    // Add all successful employees\n    importResult.success.forEach(employee => {\n      addEmployee(employee)\n    })\n\n    alert(`Successfully imported ${importResult.success.length} employees`)\n    setImportResult(null)\n  }\n\n  const downloadTemplate = () => {\n    const template = [\n      'name,email,departmentcode,status',\n      'John Doe,<EMAIL>,ENG,ACTIVE',\n      'Jane Smith,<EMAIL>,MKT,ACTIVE',\n      'Bob Johnson,<EMAIL>,,ACTIVE'\n    ].join('\\n')\n\n    const blob = new Blob([template], { type: 'text/csv' })\n    const url = URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = 'employee-import-template.csv'\n    a.click()\n    URL.revokeObjectURL(url)\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Upload Section */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Upload className=\"h-5 w-5\" />\n            Upload CSV File\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center gap-4\">\n              <Button onClick={handleFileSelect} disabled={isUploading}>\n                <FileText className=\"h-4 w-4 mr-2\" />\n                Select CSV File\n              </Button>\n              \n              <Button variant=\"outline\" onClick={downloadTemplate}>\n                <Download className=\"h-4 w-4 mr-2\" />\n                Download Template\n              </Button>\n            </div>\n\n            {isUploading && (\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span>Processing CSV...</span>\n                  <span>{Math.round(uploadProgress)}%</span>\n                </div>\n                <Progress value={uploadProgress} />\n              </div>\n            )}\n\n            <input\n              ref={fileInputRef}\n              type=\"file\"\n              accept=\".csv\"\n              onChange={handleFileChange}\n              className=\"hidden\"\n            />\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Import Results */}\n      {importResult && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <CheckCircle className=\"h-5 w-5 text-green-600\" />\n              Import Results\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"flex gap-4\">\n                <Badge variant=\"default\" className=\"bg-green-100 text-green-800\">\n                  {importResult.success.length} Successful\n                </Badge>\n                {importResult.errors.length > 0 && (\n                  <Badge variant=\"destructive\">\n                    {importResult.errors.length} Errors\n                  </Badge>\n                )}\n              </div>\n\n              {importResult.errors.length > 0 && (\n                <div className=\"space-y-2\">\n                  <h4 className=\"font-medium text-red-800\">Errors:</h4>\n                  <div className=\"max-h-40 overflow-y-auto space-y-1\">\n                    {importResult.errors.map((error, index) => (\n                      <div key={index} className=\"text-sm p-2 bg-red-50 rounded border-l-4 border-red-400\">\n                        <div className=\"font-medium\">Row {error.row}:</div>\n                        <div className=\"text-red-700\">{error.error}</div>\n                        <div className=\"text-xs text-red-600 mt-1\">\n                          Data: {JSON.stringify(error.data)}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {importResult.success.length > 0 && (\n                <div className=\"flex gap-2\">\n                  <Button onClick={handleConfirmImport}>\n                    Import {importResult.success.length} Employees\n                  </Button>\n                  <Button variant=\"outline\" onClick={() => setImportResult(null)}>\n                    Cancel\n                  </Button>\n                </div>\n              )}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Instructions */}\n      <Card className=\"border-blue-200 bg-blue-50\">\n        <CardHeader>\n          <CardTitle className=\"text-blue-800\">CSV Format Instructions</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-2 text-sm text-blue-700\">\n            <p><strong>Required columns:</strong> name, email</p>\n            <p><strong>Optional columns:</strong> departmentcode, status</p>\n            <p><strong>Department codes:</strong> {departments.map(d => d.code).join(', ')}</p>\n            <p><strong>Valid statuses:</strong> ACTIVE, TRANSFERRED, PENDING_REMOVAL, ARCHIVED</p>\n            <p><strong>Note:</strong> If departmentcode is empty, employee will be added to the free bucket</p>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": ["useState", "useRef", "useHRStore", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "Badge", "Progress", "generateEmployeeId", "Upload", "FileText", "CheckCircle", "Download", "CSVImportSection", "departments", "employees", "addEmployee", "isUploading", "setIsUploading", "uploadProgress", "setUploadProgress", "importResult", "setImportResult", "fileInputRef", "handleFileSelect", "current", "click", "handleFileChange", "event", "file", "target", "files", "name", "endsWith", "alert", "text", "result", "processCSV", "error", "console", "value", "csvText", "lines", "trim", "split", "headers", "map", "h", "toLowerCase", "requiredHeaders", "missingHeaders", "filter", "includes", "length", "Error", "join", "success", "errors", "i", "values", "v", "rowData", "for<PERSON>ach", "header", "index", "email", "some", "emp", "departmentId", "departmentcode", "department", "find", "d", "code", "id", "departmentCode", "existingIds", "startsWith", "parseInt", "nextSequence", "Math", "max", "employeeId", "validStatuses", "status", "toUpperCase", "newEmployee", "hireDate", "Date", "transferHistory", "createdAt", "updatedAt", "push", "row", "data", "message", "Promise", "resolve", "setTimeout", "handleConfirmImport", "employee", "downloadTemplate", "template", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "revokeObjectURL", "round", "JSON", "stringify"], "mappings": ";;;;AAEA,SAASA,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAExC,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,sBAAsB;AAC/E,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,kBAAkB,QAAQ,aAAa;;;;AAChD,SACEC,MAAM,EACNC,QAAQ,EACRC,WAAW,EAEXC,QAAQ,QACH,cAAc;;;AAhBrB,YAAY;;;;;;;;;AAsBL;UAA4BE,WAAAA,EAAoC,EAAE,CAAxC;;IAC/B,MAAM,EAAEC,SAAS,EAAEC,WAAAA,EAAa,mJAAGhB;IACnC,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,qKAAGpB,WAAQ,AAARA,EAAS,KAAK,CAAC;IACrD,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,qKAAGtB,WAAQ,AAARA,EAAS,CAAC,CAAC;IACvD,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,qKAAGxB,WAAAA,AAAQ,EAAyB,IAAI,CAAC;IAC9E,MAAMyB,YAAY,OAAGxB,uKAAAA,AAAM,EAAmB,IAAI,CAAC;IAEnD,MAAMyB,gBAAgB,GAAGA,CAAA,KAAM;YAC7BD,YAAY;8CAACE,OAAO,gFAAEC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,MAAMC,gBAAgB,GAAG,OAAOC,KAA0C,IAAK;;QAC7E,MAAMC,IAAI,gCAASC,MAAM,CAACC,KAAK,wDAAlBH,KAAK,cAAa,CAAG,CAAC,CAAC;QACpC,IAAI,CAACC,IAAI,EAAE;QAEX,IAAI,CAACA,IAAI,CAACG,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC/BC,KAAK,CAAC,0BAA0B,CAAC;YACjC;QACF;QAEAhB,cAAc,CAAC,IAAI,CAAC;QACpBE,iBAAiB,CAAC,CAAC,CAAC;QACpBE,eAAe,CAAC,IAAI,CAAC;QAErB,IAAI;YACF,MAAMa,IAAI,GAAG,MAAMN,IAAI,CAACM,IAAI,CAAC,CAAC;YAC9B,MAAMC,MAAM,GAAG,MAAMC,UAAU,CAACF,IAAI,CAAC;YACrCb,eAAe,CAACc,MAAM,CAAC;QACzB,CAAC,CAAC,OAAOE,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;YAC1CJ,KAAK,CAAC,2BAA2B,CAAC;QACpC,CAAC,QAAS;YACRhB,cAAc,CAAC,KAAK,CAAC;YACrBE,iBAAiB,CAAC,CAAC,CAAC;YACpB,mBAAA;YACA,IAAIG,YAAY,CAACE,OAAO,EAAE;gBACxBF,YAAY,CAACE,OAAO,CAACe,KAAK,GAAG,EAAE;YACjC;QACF;IACF,CAAC;IAED,MAAMH,UAAU,GAAG,OAAOI,OAAe,IAA+B;QACtE,MAAMC,KAAK,GAAGD,OAAO,CAACE,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC;QACxC,MAAMC,OAAO,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,EAACC,CAAC,GAAIA,CAAC,CAACJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC,CAAC;QAEpE,4BAAA;QACA,MAAMC,eAAe,GAAG;YAAC,MAAM;YAAE,OAAO;SAAC;QACzC,MAAMC,cAAc,GAAGD,eAAe,CAACE,MAAM,EAACJ,GAAC,GAAI,CAACF,OAAO,CAACO,QAAQ,CAACL,GAAC,CAAC,CAAC;QAExE,IAAIG,cAAc,CAACG,MAAM,GAAG,CAAC,EAAE;YAC7B,MAAM,IAAIC,KAAK,CAAC,6BAAsD,CAAE,CAAC,KAA5BJ,cAAc,CAACK,IAAI,CAAC,IAAI,CAAC;QACxE;QAEA,MAAMnB,QAAuB,GAAG;YAC9BoB,OAAO,EAAE,EAAE;YACXC,MAAM,EAAE,EAAA;QACV,CAAC;QAED,mBAAA;QACA,IAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,KAAK,CAACW,MAAM,EAAEK,CAAC,EAAE,CAAE;YACrCtC,iBAAiB,CAAEsC,CAAC,GAAA,CAAIhB,KAAK,CAACW,MAAM,GAAG,CAAC,CAAC,GAAI,GAAG,CAAC;YAEjD,MAAMM,MAAM,GAAGjB,KAAK,CAACgB,CAAC,CAAC,CAACd,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,EAACc,CAAC,GAAIA,CAAC,CAACjB,IAAI,CAAC,CAAC,CAAC;YACrD,MAAMkB,OAA+B,GAAG,CAAC,CAAC;YAE1ChB,OAAO,CAACiB,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;gBACjCH,OAAO,CAACE,MAAM,CAAC,GAAGJ,MAAM,CAACK,KAAK,CAAC,IAAI,EAAE;YACvC,CAAC,CAAC;YAEF,IAAI;;gBACF,2BAAA;gBACA,IAAI,CAACH,OAAO,CAAC7B,IAAI,IAAI,CAAC6B,OAAO,CAACI,KAAK,EAAE;oBACnC,MAAM,IAAIX,KAAK,CAAC,6BAA6B,CAAC;gBAChD;gBAEA,gCAAA;gBACA,IAAIvC,SAAS,CAACmD,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACF,KAAK,CAACjB,WAAW,CAAC,CAAC,KAAKa,OAAO,CAACI,KAAK,CAACjB,WAAW,CAAC,CAAC,CAAC,EAAE;oBAClF,MAAM,IAAIM,KAAK,CAAC,sBAAsB,CAAC;gBACzC;gBAEA,+BAAA;gBACA,IAAIc,YAA2B,GAAG,IAAI;gBACtC,IAAIP,OAAO,CAACQ,cAAc,EAAE;oBAC1B,MAAMC,UAAU,GAAGxD,WAAW,CAACyD,IAAI,EAACC,CAAC,GACnCA,CAAC,CAACC,IAAI,CAACzB,WAAW,CAAC,CAAC,KAAKa,OAAO,CAACQ,cAAc,CAACrB,WAAW,CAAC,CAC9D,CAAC;oBACD,IAAI,CAACsB,UAAU,EAAE;wBACf,MAAM,IAAIhB,KAAK,CAAC,yBAA+C,OAAtBO,KAAmC,CAAC,CAA7B,CAACQ,cAAc,EAAA;oBACjE;oBACAD,YAAY,GAAGE,UAAU,CAACI,EAAE;gBAC9B;gBAEA,uBAAA;gBACA,MAAMJ,YAAU,GAAGxD,WAAW,CAACyD,IAAI,EAACC,GAAC,GAAIA,GAAC,CAACE,EAAE,KAAKN,YAAY,CAAC;gBAC/D,MAAMO,cAAc,gEAAGL,YAAU,CAAEG,IAAI,KAAI,KAAK;gBAChD,MAAMG,WAAW,GAAG7D,SAAS,CAC1BoC,MAAM,EAACgB,KAAG,GAAIA,KAAG,CAACO,EAAE,CAACG,UAAU,CAACF,cAAc,CAAC,CAAC,CAChD7B,GAAG,EAACqB,KAAG,GAAIW,QAAQ,CAACX,KAAG,CAACO,EAAE,CAAC9B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBAClD,MAAMmC,YAAY,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,GAAGL,WAAW,CAAC,GAAGxC,QAAM,CAACoB,OAAO,CAACH,MAAM,GAAG,CAAC;gBAC5E,MAAM6B,UAAU,uHAAG1E,qBAAAA,AAAkB,EAACmE,cAAc,EAAEI,YAAY,CAAC;gBAEnE,8BAAA;gBACA,MAAMI,aAAa,GAAG;oBAAC,QAAQ;oBAAE,aAAa;oBAAE,iBAAiB;oBAAE,UAAU;iBAAC;gBAC9E,MAAMC,MAAM,+BAAWA,MAAM,oDAAdvB,OAAO,SAASwB,WAAW,CAAC,CAAC,KAAI,QAAQ;gBACxD,IAAI,CAACF,aAAa,CAAC/B,QAAQ,CAACgC,MAAM,CAAC,EAAE;oBACnC,MAAM,IAAI9B,KAAK,CAAC,0BAAmBO,OAAO,CAACuB,MAAM,EAAA,uBAA8C,CAAE,CAAC,KAA3BD,aAAa,CAAC5B,IAAI,CAAC,IAAI,CAAC;gBACjG;gBAEA,MAAM+B,WAAqB,GAAG;oBAC5BZ,EAAE,EAAEQ,UAAU;oBACdlD,IAAI,EAAE6B,OAAO,CAAC7B,IAAI;oBAClBiC,KAAK,EAAEJ,OAAO,CAACI,KAAK;oBACpBG,YAAY;oBACZgB,MAAM,EAAEA,MAA4B;oBACpCG,QAAQ,EAAE,IAAIC,IAAI,CAAC,CAAC;oBACpBC,eAAe,EAAE,EAAE;oBACnBC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC;oBACrBG,SAAS,EAAE,IAAIH,IAAI,CAAC;gBACtB,CAAC;gBAEDpD,QAAM,CAACoB,OAAO,CAACoC,IAAI,CAACN,WAAW,CAAC;YAClC,CAAC,CAAC,OAAOhD,OAAK,EAAE;gBACdF,QAAM,CAACqB,MAAM,CAACmC,IAAI,CAAC;oBACjBC,GAAG,EAAEnC,CAAC,GAAG,CAAC;oBACVoC,IAAI,EAAEjC,OAAO;oBACbvB,KAAK,EAAEA,OAAK,YAAYgB,KAAK,GAAGhB,OAAK,CAACyD,OAAO,GAAG;gBAClD,CAAC,CAAC;YACJ;YAEA,+BAAA;YACA,MAAM,IAAIC,OAAO,EAACC,OAAO,GAAIC,UAAU,CAACD,OAAO,EAAE,EAAE,CAAC,CAAC;QACvD;QAEA,OAAO7D,QAAM;IACf,CAAC;IAED,MAAM+D,mBAAmB,GAAGA,CAAA,KAAM;QAChC,IAAI,CAAC9E,YAAY,EAAE;QAEnB,+BAAA;QACAA,YAAY,CAACmC,OAAO,CAACM,OAAO,EAACsC,QAAQ,IAAI;YACvCpF,WAAW,CAACoF,QAAQ,CAAC;QACvB,CAAC,CAAC;QAEFlE,KAAK,CAAC,yBAAoD,OAA3Bb,YAAY,CAACmC,OAAO,CAACH,MAAM,EAAA,WAAY,CAAC;QACvE/B,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC;IAED,MAAM+E,gBAAgB,GAAGA,CAAA,KAAM;QAC7B,MAAMC,QAAQ,GAAG;YACf,kCAAkC;YAClC,0CAA0C;YAC1C,8CAA8C;YAC9C,6CAA6C;SAC9C,CAAC/C,IAAI,CAAC,IAAI,CAAC;QAEZ,MAAMgD,IAAI,GAAG,IAAIC,IAAI,CAAC;YAACF,QAAQ;SAAC,EAAE;YAAEG,IAAI,EAAE;QAAW,CAAC,CAAC;QACvD,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;QACrC,MAAMM,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACrCF,CAAC,CAACG,IAAI,GAAGN,GAAG;QACZG,CAAC,CAACI,QAAQ,GAAG,8BAA8B;QAC3CJ,CAAC,CAACnF,KAAK,CAAC,CAAC;QACTiF,GAAG,CAACO,eAAe,CAACR,GAAG,CAAC;IAC1B,CAAC;IAED,qBACE,6LAAC,GAAG;QAAC,SAAS,EAAC,WAAW;;0BAExB,6LAAC,oIAAI;;kCACH,0TAAC,aAAU;gDACT,0TAAC,YAAS;4BAAC,SAAS,EAAC,yBAAyB;;8CAC5C,uYAAC,SAAM;oCAAC,SAAS,EAAC,SAAS;;;;;;gCAAA;;;;;;;;;;;;kCAI/B,6LAAC,2IAAW;gDACV,6LAAC,GAAG;4BAAC,SAAS,EAAC,WAAW;;8CACxB,6LAAC,GAAG;oCAAC,SAAS,EAAC,yBAAyB;;sDACtC,4TAAC,SAAM;4CAAC,OAAO,CAAC,CAAClF,gBAAgB,CAAC;4CAAC,QAAQ,CAAC,CAACP,WAAW,CAAC;;8DACvD,+YAAC,WAAQ;oDAAC,SAAS,EAAC,cAAc;;;;;;gDAAA;;;;;;;sDAIpC,4TAAC,SAAM;4CAAC,OAAO,EAAC,SAAS;4CAAC,OAAO,CAAC,CAACoF,gBAAgB,CAAC;;8DAClD,2YAAC,WAAQ;oDAAC,SAAS,EAAC,cAAc;;;;;;gDAAA;;;;;;;;;;;;;gCAKrCpF,WAAW,kBACV,6LAAC,GAAG;oCAAC,SAAS,EAAC,WAAW;;sDACxB,6LAAC,GAAG;4CAAC,SAAS,EAAC,2CAA2C;;8DACxD,6LAAC,IAAI;8DAAC,iBAAiB,EAAE,IAAI;;;;;;8DAC7B,6LAAC,IAAI,CAAC;;wDAAC+D,IAAI,CAACmC,KAAK,CAAChG,cAAc,CAAC;wDAAC,CAAC,EAAE,IAAI;;;;;;;;;;;;;sDAE3C,8TAAC,WAAQ;4CAAC,KAAK,CAAC,CAACA,cAAc,CAAC;;;;;;;;;;;;8CAIpC,6LAAC,KAAK;oCACJ,GAAG,CAAC,CAACI,YAAY,CAAC;oCAClB,IAAI,EAAC,MAAM;oCACX,MAAM,EAAC,MAAM;oCACb,QAAQ,CAAC,CAACI,gBAAgB,CAAC;oCAC3B,SAAS,EAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;YAOzBN,YAAY,kBACX,0TAAC,OAAI;;kCACH,0TAAC,aAAU;kCACT,uUAAC,aAAS;4BAAC,SAAS,EAAC,yBAAyB;;8CAC5C,oZAAC,eAAW;oCAAC,SAAS,EAAC,wBAAwB;;;;;;gCAAA;;;;;;;;;;;;kCAInD,0TAAC,cAAW;gDACV,6LAAC,GAAG;4BAAC,SAAS,EAAC,WAAW;;8CACxB,6LAAC,GAAG;oCAAC,SAAS,EAAC,YAAY;;sDACzB,2TAAC,QAAK;4CAAC,OAAO,EAAC,SAAS;4CAAC,SAAS,EAAC,6BAA6B;;gDAC7DA,YAAY,CAACmC,OAAO,CAACH,MAAM;gDAAC;;;;;;;wCAE9BhC,YAAY,CAACoC,MAAM,CAACJ,MAAM,GAAG,CAAC,kBAC7B,0TAAC,SAAK;4CAAC,OAAO,EAAC,aAAa;;gDACzBhC,YAAY,CAACoC,MAAM,CAACJ,MAAM;gDAAC;;;;;;;;;;;;;gCAKjChC,YAAY,CAACoC,MAAM,CAACJ,MAAM,GAAG,CAAC,kBAC7B,6LAAC,GAAG;oCAAC,SAAS,EAAC,WAAW;;sDACxB,6LAAC,EAAE;4CAAC,SAAS,EAAC,0BAA0B;sDAAC,OAAO,EAAE,EAAE;;;;;;sDACpD,6LAAC,GAAG;4CAAC,SAAS,EAAC,oCAAoC;sDAChDhC,YAAY,CAACoC,MAAM,CAACX,GAAG,CAAC,CAACR,OAAK,EAAE0B,OAAK,iBACpC,6LAAC,GAAG,CAAC,GAAG,CAAC;oDAAQ,SAAS,EAAC,yDAAyD;;sEAClF,6LAAC,GAAG;4DAAC,SAAS,EAAC,aAAa;;gEAAC,IAAI;gEAAC1B,OAAK,CAACuD,GAAG;gEAAC,CAAC,EAAE,GAAG;;;;;;;sEAClD,6LAAC,GAAG;4DAAC,SAAS,EAAC,cAAc,CAAC;sEAACvD,OAAK,CAACA,KAAK,CAAC,EAAE,GAAG;;;;;;sEAChD,6LAAC,GAAG;4DAAC,SAAS,EAAC,2BAA2B;;gEAAA;gEACjC8E,IAAI,CAACC,SAAS,CAAC/E,OAAK,CAACwD,IAAI,CAAC;;;;;;;;mDAJ3B9B,OAAK,CAAC;;;;;;;;;;;;;;;;gCAYvB3C,YAAY,CAACmC,OAAO,CAACH,MAAM,GAAG,CAAC,kBAC9B,6LAAC,GAAG;oCAAC,SAAS,EAAC,YAAY;;sDACzB,6LAAC,wIAAM;4CAAC,OAAO,CAAC,CAAC8C,mBAAmB,CAAC;;gDAAA;gDAC3B9E,YAAY,CAACmC,OAAO,CAACH,MAAM;gDAAC;;;;;;;sDAEtC,4TAAC,SAAM;4CAAC,OAAO,EAAC,SAAS;4CAAC,OAAO,CAAC,CAAC,IAAM/B,eAAe,CAAC,IAAI,CAAC,CAAC;sDAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW3E,6LAAC,oIAAI;gBAAC,SAAS,EAAC,4BAA4B;;kCAC1C,0TAAC,aAAU;gDACT,0TAAC,YAAS;4BAAC,SAAS,EAAC,eAAe;sCAAC,uBAAuB,EAAE,SAAS;;;;;;;;;;;kCAEzE,0TAAC,cAAW;gDACV,6LAAC,GAAG;4BAAC,SAAS,EAAC,iCAAiC;;8CAC9C,6LAAC,CAAC;;sDAAC,6LAAC,MAAM;sDAAC,iBAAiB,EAAE,MAAM;;;;;;wCAAC,YAAY,EAAE,CAAC;;;;;;;8CACpD,6LAAC,CAAC;;sDAAC,6LAAC,MAAM;sDAAC,iBAAiB,EAAE,MAAM;;;;;;wCAAC,uBAAuB,EAAE,CAAC;;;;;;;8CAC/D,6LAAC,CAAC;;sDAAC,6LAAC,MAAM;sDAAC,iBAAiB,EAAE,MAAM;;;;;;wCAAC,CAAC;wCAACR,WAAW,CAACgC,GAAG,EAAC0B,GAAC,GAAIA,GAAC,CAACC,IAAI,CAAC,CAAClB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;;;;;;;8CAClF,6LAAC,CAAC;;sDAAC,6LAAC,MAAM;sDAAC,eAAe,EAAE,MAAM;;;;;;wCAAC,+CAA+C,EAAE,CAAC;;;;;;;8CACrF,6LAAC,CAAC;;sDAAC,6LAAC,MAAM;sDAAC,KAAK,EAAE,MAAM;;;;;;wCAAC,sEAAsE,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9G;;;uIAxRqCvD,aAAU,CAAC,CAAC;;;KADjCa,gBAAgBA", "ignoreList": [], "debugId": null}}, {"offset": {"line": 1032, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAI5B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1073, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/checkbox.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root> & {\n    indeterminate?: boolean\n  }\n>(({ className, indeterminate, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      {indeterminate ? (\n        <div className=\"h-2 w-2 bg-current rounded-sm\" />\n      ) : (\n        <Check className=\"h-4 w-4\" />\n      )}\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAK/B,QAAyC;QAAxC,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,OAAO;yBACvC,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE;sBAEb,8BACC,6LAAC;gBAAI,WAAU;;;;;yEAEf,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;;AAKzB,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1133, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/select.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;AAAA;AACA;;;;;;AAEA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,6JAAA,CAAA,aAAgB,MAGpC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,6JAAA,CAAA,aAAgB,CAG3C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,6JAAA,CAAA,aAAgB,CAG7C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,6JAAA,CAAA,aAAgB,OAGpC,QAAyD;QAAxD,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO;yBACvD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,QAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1368, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/bulk/G%3A/Augment%20code/components/bulk/csv-export-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Employee, Department } from \"@/lib/types\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Label } from \"@/components/ui/label\"\nimport { Checkbox } from \"@/components/ui/checkbox\"\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\"\nimport { formatDate } from \"@/lib/utils\"\nimport { \n  Download, \n  FileText,\n  Filter\n} from \"lucide-react\"\n\ninterface CSVExportSectionProps {\n  employees: Employee[]\n  departments: Department[]\n}\n\nexport function CSVExportSection({ employees, departments }: CSVExportSectionProps) {\n  const [filters, setFilters] = useState({\n    department: 'all',\n    status: 'all',\n    includeTransferHistory: false\n  })\n  const [selectedColumns, setSelectedColumns] = useState({\n    id: true,\n    name: true,\n    email: true,\n    department: true,\n    status: true,\n    hireDate: true,\n    createdAt: false,\n    updatedAt: false\n  })\n\n  const availableColumns = [\n    { key: 'id', label: 'Employee ID', required: true },\n    { key: 'name', label: 'Name', required: true },\n    { key: 'email', label: 'Email', required: true },\n    { key: 'department', label: 'Department', required: false },\n    { key: 'status', label: 'Status', required: false },\n    { key: 'hireDate', label: 'Hire Date', required: false },\n    { key: 'createdAt', label: 'Created Date', required: false },\n    { key: 'updatedAt', label: 'Updated Date', required: false }\n  ]\n\n  const getFilteredEmployees = () => {\n    return employees.filter(employee => {\n      // Department filter\n      if (filters.department !== 'all') {\n        if (filters.department === 'unassigned') {\n          if (employee.departmentId !== null) return false\n        } else {\n          if (employee.departmentId !== filters.department) return false\n        }\n      }\n\n      // Status filter\n      if (filters.status !== 'all' && employee.status !== filters.status) {\n        return false\n      }\n\n      return true\n    })\n  }\n\n  const getDepartmentName = (departmentId: string | null) => {\n    if (!departmentId) return 'Unassigned'\n    const department = departments.find(dept => dept.id === departmentId)\n    return department?.name || 'Unknown'\n  }\n\n  const generateCSV = () => {\n    const filteredEmployees = getFilteredEmployees()\n    \n    if (filteredEmployees.length === 0) {\n      alert('No employees match the current filters')\n      return\n    }\n\n    // Generate headers\n    const headers: string[] = []\n    if (selectedColumns.id) headers.push('Employee ID')\n    if (selectedColumns.name) headers.push('Name')\n    if (selectedColumns.email) headers.push('Email')\n    if (selectedColumns.department) headers.push('Department')\n    if (selectedColumns.status) headers.push('Status')\n    if (selectedColumns.hireDate) headers.push('Hire Date')\n    if (selectedColumns.createdAt) headers.push('Created Date')\n    if (selectedColumns.updatedAt) headers.push('Updated Date')\n\n    // Generate rows\n    const rows = filteredEmployees.map(employee => {\n      const row: string[] = []\n      if (selectedColumns.id) row.push(employee.id)\n      if (selectedColumns.name) row.push(employee.name)\n      if (selectedColumns.email) row.push(employee.email)\n      if (selectedColumns.department) row.push(getDepartmentName(employee.departmentId))\n      if (selectedColumns.status) row.push(employee.status)\n      if (selectedColumns.hireDate) row.push(formatDate(employee.hireDate))\n      if (selectedColumns.createdAt) row.push(formatDate(employee.createdAt))\n      if (selectedColumns.updatedAt) row.push(formatDate(employee.updatedAt))\n      return row\n    })\n\n    // Create CSV content\n    const csvContent = [\n      headers.join(','),\n      ...rows.map(row => row.map(cell => `\"${cell}\"`).join(','))\n    ].join('\\n')\n\n    // Download file\n    const blob = new Blob([csvContent], { type: 'text/csv' })\n    const url = URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = `employees-export-${new Date().toISOString().split('T')[0]}.csv`\n    a.click()\n    URL.revokeObjectURL(url)\n  }\n\n  const handleColumnToggle = (column: string, checked: boolean) => {\n    setSelectedColumns(prev => ({\n      ...prev,\n      [column]: checked\n    }))\n  }\n\n  const filteredEmployees = getFilteredEmployees()\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Filters */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Filter className=\"h-5 w-5\" />\n            Export Filters\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid gap-4 md:grid-cols-2\">\n            <div className=\"space-y-2\">\n              <Label>Department</Label>\n              <Select\n                value={filters.department}\n                onValueChange={(value) => setFilters(prev => ({ ...prev, department: value }))}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">All Departments</SelectItem>\n                  <SelectItem value=\"unassigned\">Unassigned</SelectItem>\n                  {departments.map((dept) => (\n                    <SelectItem key={dept.id} value={dept.id}>\n                      {dept.name}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>Status</Label>\n              <Select\n                value={filters.status}\n                onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">All Statuses</SelectItem>\n                  <SelectItem value=\"ACTIVE\">Active</SelectItem>\n                  <SelectItem value=\"TRANSFERRED\">Transferred</SelectItem>\n                  <SelectItem value=\"PENDING_REMOVAL\">Pending Removal</SelectItem>\n                  <SelectItem value=\"ARCHIVED\">Archived</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Column Selection */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Select Columns to Export</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid gap-3 md:grid-cols-2\">\n            {availableColumns.map((column) => (\n              <div key={column.key} className=\"flex items-center space-x-2\">\n                <Checkbox\n                  id={column.key}\n                  checked={selectedColumns[column.key as keyof typeof selectedColumns]}\n                  onCheckedChange={(checked) => handleColumnToggle(column.key, checked as boolean)}\n                  disabled={column.required}\n                />\n                <Label \n                  htmlFor={column.key}\n                  className={column.required ? 'text-muted-foreground' : ''}\n                >\n                  {column.label}\n                  {column.required && ' (Required)'}\n                </Label>\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Export Summary */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <FileText className=\"h-5 w-5\" />\n            Export Summary\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            <div className=\"grid gap-2 md:grid-cols-3\">\n              <div className=\"text-center p-4 bg-muted rounded-lg\">\n                <div className=\"text-2xl font-bold\">{filteredEmployees.length}</div>\n                <div className=\"text-sm text-muted-foreground\">Employees to export</div>\n              </div>\n              <div className=\"text-center p-4 bg-muted rounded-lg\">\n                <div className=\"text-2xl font-bold\">\n                  {Object.values(selectedColumns).filter(Boolean).length}\n                </div>\n                <div className=\"text-sm text-muted-foreground\">Columns selected</div>\n              </div>\n              <div className=\"text-center p-4 bg-muted rounded-lg\">\n                <div className=\"text-2xl font-bold\">CSV</div>\n                <div className=\"text-sm text-muted-foreground\">Export format</div>\n              </div>\n            </div>\n\n            <Button \n              onClick={generateCSV} \n              disabled={filteredEmployees.length === 0}\n              className=\"w-full\"\n            >\n              <Download className=\"h-4 w-4 mr-2\" />\n              Export {filteredEmployees.length} Employees to CSV\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": ["c", "_c", "useState", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "Label", "Checkbox", "Select", "SelectContent", "SelectItem", "SelectTrigger", "SelectValue", "formatDate", "Download", "FileText", "Filter", "CSVExportSection", "t0", "$", "$i", "Symbol", "for", "employees", "departments", "t1", "department", "status", "includeTransferHistory", "filters", "setFilters", "t2", "id", "name", "email", "hireDate", "createdAt", "updatedAt", "selectedColumns", "setSelectedColumns", "t3", "key", "label", "required", "availableColumns", "t4", "filter", "employee", "departmentId", "getFilteredEmployees", "t5", "find", "dept", "getDepartmentName", "t6", "filteredEmployees", "length", "alert", "headers", "push", "rows", "map", "employee_0", "row", "csv<PERSON><PERSON>nt", "join", "_temp2", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "Date", "toISOString", "split", "click", "revokeObjectURL", "generateCSV", "t7", "column", "checked", "prev", "handleColumnToggle", "t8", "filteredEmployees_0", "t9", "t10", "t11", "t12", "value", "prev_0", "t13", "t14", "t15", "t16", "_temp3", "t17", "t18", "t19", "t20", "value_0", "prev_1", "t21", "t22", "t23", "t24", "t25", "t26", "column_0", "checked_0", "t27", "t28", "t29", "t30", "t31", "Object", "values", "Boolean", "t32", "t33", "t34", "t35", "t36", "t37", "t38", "t39", "t40", "t41", "dept_0", "row_0", "_temp", "cell"], "mappings": ";;;;AAAY,SAAAA,CAAA,IAAAC,EAAA;AAEZ,SAASC,QAAQ,QAAQ,OAAO;AAEhC,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,sBAAsB;AAC/E,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SACEC,MAAM,EACNC,aAAa,EACbC,UAAU,EACVC,aAAa,EACbC,WAAW,QACN,wBAAwB;AAC/B,SAASC,UAAU,QAAQ,aAAa;;AACxC,SACEC,QAAQ,EACRC,QAAQ,EACRC,MAAM,QACD,cAAc;;;;AApBrB,YAAY;;;;;;;;;;AA2BL,0BAAAE,EAAA;;IAAA,MAAAC,CAAA,mLAAApB,IAAAA,AAAA,EAAA;IAAA,IAAAoB,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;IAA0B,MAAA,EAAAI,SAAA,EAAAC,WAAAA,EAAA,GAAAN,EAAiD;IAAA,IAAAO,EAAA;IAAA,IAAAN,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QACzCG,EAAA,GAAA;YAAAC,UAAA,EACzB,KAAK;YAAAC,MAAA,EACT,KAAK;YAAAC,sBAAA,EAAA;QAAA;QAEdT,CAAA,CAAA,EAAA,GAAAM,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAN,CAAA,CAAA,EAAA;IAAA;IAJD,MAAA,CAAAU,OAAA,EAAAC,UAAA,CAAA,qKAA8B9B,WAAAA,AAAA,EAASyB,EAItC,CAAC;IAAA,IAAAM,EAAA;IAAA,IAAAZ,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QACqDS,EAAA,GAAA;YAAAC,EAAA,EAAA;YAAAC,IAAA,EAAA;YAAAC,KAAA,EAAA;YAAAR,UAAA,EAAA;YAAAC,MAAA,EAAA;YAAAQ,QAAA,EAAA;YAAAC,SAAA,EAAA;YAAAC,SAAA,EAAA;QAAA;QAStDlB,CAAA,CAAA,EAAA,GAAAY,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAZ,CAAA,CAAA,EAAA;IAAA;IATD,MAAA,CAAAmB,eAAA,EAAAC,kBAAA,CAAA,qKAA8CvC,WAAAA,AAAA,EAAS+B,EAStD,CAAC;IAAA,IAAAS,EAAA;IAAA,IAAArB,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAEuBkB,EAAA,GAAA;YAAA;gBAAAC,GAAA,EAChB,IAAI;gBAAAC,KAAA,EAAS,aAAa;gBAAAC,QAAA,EAAA;YAAA;YAAA;gBAAAF,GAAA,EAC1B,MAAM;gBAAAC,KAAA,EAAS,MAAM;gBAAAC,QAAA,EAAA;YAAA;YAAA;gBAAAF,GAAA,EACrB,OAAO;gBAAAC,KAAA,EAAS,OAAO;gBAAAC,QAAA,EAAA;YAAA;YAAA;gBAAAF,GAAA,EACvB,YAAY;gBAAAC,KAAA,EAAS,YAAY;gBAAAC,QAAA,EAAA;YAAA;YAAA;gBAAAF,GAAA,EACjC,QAAQ;gBAAAC,KAAA,EAAS,QAAQ;gBAAAC,QAAA,EAAA;YAAA;YAAA;gBAAAF,GAAA,EACzB,UAAU;gBAAAC,KAAA,EAAS,WAAW;gBAAAC,QAAA,EAAA;YAAA;YAAA;gBAAAF,GAAA,EAC9B,WAAW;gBAAAC,KAAA,EAAS,cAAc;gBAAAC,QAAA,EAAA;YAAA;YAAA;gBAAAF,GAAA,EAClC,WAAW;gBAAAC,KAAA,EAAS,cAAc;gBAAAC,QAAA,EAAA;YAAA;SAAA;QAC1CxB,CAAA,CAAA,EAAA,GAAAqB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAArB,CAAA,CAAA,EAAA;IAAA;IATD,MAAAyB,gBAAA,GAAyBJ,EASxB;IAAA,IAAAK,EAAA;IAAA,IAAA1B,CAAA,CAAA,EAAA,KAAAI,SAAA,IAAAJ,CAAA,CAAA,EAAA,KAAAU,OAAA,CAAAH,UAAA,IAAAP,CAAA,CAAA,EAAA,KAAAU,OAAA,CAAAF,MAAA,EAAA;QAE4BkB,EAAA,GAAAA,CAAA,GACpBtB,SAAS,CAAAuB,MAAA,EAAAC,QAAA;gBAAA,IAEVlB,OAAO,CAAAH,UAAA,KAAgB,KAAK,EAAA;oBAAA,IAC1BG,OAAO,CAAAH,UAAA,KAAgB,YAAY,EAAA;wBAAA,IACjCqB,QAAQ,CAAAC,YAAA,KAAA,IAAsB,EAAA;4BAAA,OAAA;wBAAA;oBAAA,OAAA;wBAAA,IAE9BD,QAAQ,CAAAC,YAAA,KAAkBnB,OAAO,CAAAH,UAAW,EAAA;4BAAA,OAAA;wBAAA;oBAAA;gBAAA;gBAAA,IAKhDG,OAAO,CAAAF,MAAA,KAAY,KAAK,IAAIoB,QAAQ,CAAApB,MAAA,KAAYE,OAAO,CAAAF,MAAO,EAAA;oBAAA,OAAA;gBAAA;gBAAA,OAAA;YAAA,CAKnE,CAAC;QACHR,CAAA,CAAA,EAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,EAAA,GAAAU,OAAA,CAAAH,UAAA;QAAAP,CAAA,CAAA,EAAA,GAAAU,OAAA,CAAAF,MAAA;QAAAR,CAAA,CAAA,EAAA,GAAA0B,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAA1B,CAAA,CAAA,EAAA;IAAA;IAlBD,MAAA8B,oBAAA,GAA6BJ,EAkB5B;IAAA,IAAAK,EAAA;IAAA,IAAA/B,CAAA,CAAA,EAAA,KAAAK,WAAA,EAAA;QAEyB0B,EAAA,IAAAF,YAAA;YAAA,IAAA,CACnBA,YAAY,EAAA;gBAAA,OAAS,YAAY;YAAA;YACtC,MAAAtB,UAAA,GAAmBF,WAAW,CAAA2B,IAAA,EAAAC,IAAA,GAAcA,IAAI,CAAApB,EAAA,KAAQgB,YAAY,CAAC;YAAA,gEAC9DtB,UAAU,CAAAO,IAAA,KAAU,SAAS;QAAA;QACrCd,CAAA,CAAA,EAAA,GAAAK,WAAA;QAAAL,CAAA,CAAA,EAAA,GAAA+B,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAA/B,CAAA,CAAA,EAAA;IAAA;IAJD,MAAAkC,iBAAA,GAA0BH,EAIzB;IAAA,IAAAI,EAAA;IAAA,IAAAnC,CAAA,CAAA,GAAA,KAAAkC,iBAAA,IAAAlC,CAAA,CAAA,GAAA,KAAA8B,oBAAA,IAAA9B,CAAA,CAAA,GAAA,KAAAmB,eAAA,EAAA;QAEmBgB,EAAA,GAAAA,CAAA;YAClB,MAAAC,iBAAA,GAA0BN,oBAAoB,CAAC,CAAC;YAAA,IAE5CM,iBAAiB,CAAAC,MAAA,KAAA,CAAa,EAAA;gBAChCC,KAAA,CAAM,wCAAwC,CAAC;gBAAA;YAAA;YAKjD,MAAAC,OAAA,GAAA,EAAA;YAA4B,IACxBpB,eAAe,CAAAN,EAAA,EAAA;gBAAK0B,OAAO,CAAAC,IAAA,CAAM,aAAa,CAAC;YAAA;YAAA,IAC/CrB,eAAe,CAAAL,IAAA,EAAA;gBAAOyB,OAAO,CAAAC,IAAA,CAAM,MAAM,CAAC;YAAA;YAAA,IAC1CrB,eAAe,CAAAJ,KAAA,EAAA;gBAAQwB,OAAO,CAAAC,IAAA,CAAM,OAAO,CAAC;YAAA;YAAA,IAC5CrB,eAAe,CAAAZ,UAAA,EAAA;gBAAagC,OAAO,CAAAC,IAAA,CAAM,YAAY,CAAC;YAAA;YAAA,IACtDrB,eAAe,CAAAX,MAAA,EAAA;gBAAS+B,OAAO,CAAAC,IAAA,CAAM,QAAQ,CAAC;YAAA;YAAA,IAC9CrB,eAAe,CAAAH,QAAA,EAAA;gBAAWuB,OAAO,CAAAC,IAAA,CAAM,WAAW,CAAC;YAAA;YAAA,IACnDrB,eAAe,CAAAF,SAAA,EAAA;gBAAYsB,OAAO,CAAAC,IAAA,CAAM,cAAc,CAAC;YAAA;YAAA,IACvDrB,eAAe,CAAAD,SAAA,EAAA;gBAAYqB,OAAO,CAAAC,IAAA,CAAM,cAAc,CAAC;YAAA;YAG3D,MAAAC,IAAA,GAAaL,iBAAiB,CAAAM,GAAA,EAAAC,UAAA;gBAC5B,MAAAC,GAAA,GAAA,EAAA;gBAAwB,IACpBzB,eAAe,CAAAN,EAAA,EAAA;oBAAK+B,GAAG,CAAAJ,IAAA,CAAMZ,UAAQ,CAAAf,EAAG,CAAC;gBAAA;gBAAA,IACzCM,eAAe,CAAAL,IAAA,EAAA;oBAAO8B,GAAG,CAAAJ,IAAA,CAAMZ,UAAQ,CAAAd,IAAK,CAAC;gBAAA;gBAAA,IAC7CK,eAAe,CAAAJ,KAAA,EAAA;oBAAQ6B,GAAG,CAAAJ,IAAA,CAAMZ,UAAQ,CAAAb,KAAM,CAAC;gBAAA;gBAAA,IAC/CI,eAAe,CAAAZ,UAAA,EAAA;oBAAaqC,GAAG,CAAAJ,IAAA,CAAMN,iBAAiB,CAACN,UAAQ,CAAAC,YAAa,CAAC,CAAC;gBAAA;gBAAA,IAC9EV,eAAe,CAAAX,MAAA,EAAA;oBAASoC,GAAG,CAAAJ,IAAA,CAAMZ,UAAQ,CAAApB,MAAO,CAAC;gBAAA;gBAAA,IACjDW,eAAe,CAAAH,QAAA,EAAA;oBAAW4B,GAAG,CAAAJ,IAAA,qHAAM9C,aAAAA,AAAA,EAAWkC,UAAQ,CAAAZ,QAAS,CAAC,CAAC;gBAAA;gBAAA,IACjEG,eAAe,CAAAF,SAAA,EAAA;oBAAY2B,GAAG,CAAAJ,IAAA,qHAAM9C,aAAAA,AAAA,EAAWkC,UAAQ,CAAAX,SAAU,CAAC,CAAC;gBAAA;gBAAA,IACnEE,eAAe,CAAAD,SAAA,EAAA;oBAAY0B,GAAG,CAAAJ,IAAA,EAAM9C,gIAAAA,AAAA,EAAWkC,UAAQ,CAAAV,SAAU,CAAC,CAAC;gBAAA;gBAAA,OAChE0B,GAAG;YAAA,CACX,CAAC;YAGF,MAAAC,UAAA,GAAmB;gBACjBN,OAAO,CAAAO,IAAA,CAAM,GAAG,CAAC;mBACdL,IAAI,CAAAC,GAAA,CAAAK,MAAkD,CAAC;aAAA,CAAAD,IAAA,CACrD,IAAI,CAAC;YAGZ,MAAAE,IAAA,GAAA,IAAAC,IAAA,CAAA;gBAAuBJ,UAAU;aAAA,EAAA;gBAAAK,IAAA,EAAW;YAAU;YACtD,MAAAC,GAAA,GAAYC,GAAA,CAAAC,eAAA,CAAoBL,IAAI,CAAC;YACrC,MAAAM,CAAA,GAAUC,QAAA,CAAAC,aAAA,CAAuB,GAAG,CAAC;YACrCF,CAAC,CAAAG,IAAA,GAAQN,GAAG;YACZG,CAAC,CAAAI,QAAA,GAAY,oBAAuD,KAAS,EAA5C,IAAAC,IAAA,GAAAC,WAAA,CAAuB,CAAC,CAAAC,KAAA,CAAO,GAAG,CAAC,CAAA,EAAA,EAAA;YACpEP,CAAC,CAAAQ,KAAA,CAAO,CAAC;YACTV,GAAA,CAAAW,eAAA,CAAoBZ,GAAG,CAAC;QAAA;QACzBnD,CAAA,CAAA,GAAA,GAAAkC,iBAAA;QAAAlC,CAAA,CAAA,GAAA,GAAA8B,oBAAA;QAAA9B,CAAA,CAAA,GAAA,GAAAmB,eAAA;QAAAnB,CAAA,CAAA,GAAA,GAAAmC,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAnC,CAAA,CAAA,GAAA;IAAA;IA/CD,MAAAgE,WAAA,GAAoB7B,EA+CnB;IAAA,IAAA8B,EAAA;IAAA,IAAAjE,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAE0B8D,EAAA,GAAAA,CAAAC,MAAA,EAAAC,OAAA;YACzB/C,kBAAkB,EAAAgD,IAAA,GAAA,CAAA;oBAAA,GACbA,IAAI;oBAAA,CACNF,MAAM,CAAA,EAAGC;gBAAO,CAAA,CACjB,CAAC;QAAA;QACJnE,CAAA,CAAA,GAAA,GAAAiE,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAjE,CAAA,CAAA,GAAA;IAAA;IALD,MAAAqE,kBAAA,GAA2BJ,EAK1B;IAAA,IAAAK,EAAA;IAAA,IAAAtE,CAAA,CAAA,GAAA,KAAA8B,oBAAA,EAAA;QAEyBwC,EAAA,GAAAxC,oBAAoB,CAAC,CAAC;QAAA9B,CAAA,CAAA,GAAA,GAAA8B,oBAAA;QAAA9B,CAAA,CAAA,GAAA,GAAAsE,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAtE,CAAA,CAAA,GAAA;IAAA;IAAhD,MAAAuE,mBAAA,GAA0BD,EAAsB;IAAA,IAAAE,EAAA;IAAA,IAAAxE,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAM1CqE,EAAA,iBAAA,0TAAC,aAAU;oCACT,0TAAC,YAAS;gBAAW,SAAyB,EAAzB,yBAAyB;;kCAC5C,uYAAC,SAAM;wBAAW,SAAS,EAAT,SAAS;;;;;;oBAAG,cAEhC,EAHC,SAAS,CAIZ,EALC,UAAU,CAKE;;;;;;;;;;;;QAAAxE,CAAA,CAAA,GAAA,GAAAwE,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAxE,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAyE,GAAA;IAAA,IAAAzE,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAIPsE,GAAA,iBAAA,2TAAC,QAAK;sBAAC,UAAU,EAAhB,KAAK,CAAmB;;;;;;QAAAzE,CAAA,CAAA,GAAA,GAAAyE,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAzE,CAAA,CAAA,GAAA;IAAA;IAEhB,MAAA0E,GAAA,GAAAhE,OAAO,CAAAH,UAAA;IAAW,IAAAoE,GAAA;IAAA,IAAA3E,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QACVwE,GAAA,IAAAC,KAAA,GAAWjE,UAAU,EAAAkE,MAAA,GAAA,CAAA;oBAAA,GAAeT,MAAI;oBAAA7D,UAAA,EAAcqE;gBAAK,CAAA,CAAG,CAAC;QAAA5E,CAAA,CAAA,GAAA,GAAA2E,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA3E,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA8E,GAAA;IAAA,IAAA9E,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAE9E2E,GAAA,iBAAA,4TAAC,gBAAa;oCACZ,4TAAC,cAAW,GACd,EAFC,aAAa,CAEE;;;;;;;;;;QAAA9E,CAAA,CAAA,GAAA,GAAA8E,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA9E,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA+E,GAAA;IAAA,IAAAC,GAAA;IAAA,IAAAhF,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAEd4E,GAAA,iBAAA,4TAAC,aAAU;YAAO,KAAK,EAAL,KAAK;sBAAC,eAAe,EAAtC,UAAU,CAAyC;;;;;;QACpDC,GAAA,iBAAA,6LAAC,4IAAU;YAAO,KAAY,EAAZ,YAAY;sBAAC,UAAU,EAAxC,UAAU,CAA2C;;;;;;QAAAhF,CAAA,CAAA,GAAA,GAAA+E,GAAA;QAAA/E,CAAA,CAAA,GAAA,GAAAgF,GAAA;IAAA,OAAA;QAAAD,GAAA,GAAA/E,CAAA,CAAA,GAAA;QAAAgF,GAAA,GAAAhF,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAiF,GAAA;IAAA,IAAAjF,CAAA,CAAA,GAAA,KAAAK,WAAA,EAAA;QACrD4E,GAAA,GAAA5E,WAAW,CAAAqC,GAAA,CAAAwC,MAIX,CAAC;QAAAlF,CAAA,CAAA,GAAA,GAAAK,WAAA;QAAAL,CAAA,CAAA,GAAA,GAAAiF,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAjF,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAmF,GAAA;IAAA,IAAAnF,CAAA,CAAA,GAAA,KAAAiF,GAAA,EAAA;QAPJE,GAAA,iBAAA,4TAAC,gBAAa,CACZ;;gBAAAJ,GAAmD,CACnD;gBAAAC,GAAqD,CACpD;gBAAAC,GAIA,CACH,EARC,aAAa,CAQE;;;;;;;QAAAjF,CAAA,CAAA,GAAA,GAAAiF,GAAA;QAAAjF,CAAA,CAAA,GAAA,GAAAmF,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAnF,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAoF,GAAA;IAAA,IAAApF,CAAA,CAAA,GAAA,KAAAU,OAAA,CAAAH,UAAA,IAAAP,CAAA,CAAA,GAAA,KAAAmF,GAAA,EAAA;QAjBpBC,GAAA,iBAAA,6LAAA,GAmBM;YAnBS,SAAW,EAAX,WAAW,CACxB;;gBAAAX,GAAwB;8BACxB,4TAAC,SAAM;oBACE,KAAkB,CAAlB,CAAAC,GAAiB,CAAC;oBACV,aAA+D,CAA/D,CAAAC,GAA8D,CAAC,CAE9E;;wBAAAG,GAEe,CACf;wBAAAK,GAQe,CACjB,EAhBC,MAAM,CAiBT,EAnBA,GAmBM;;;;;;;;;;;;;QAAAnF,CAAA,CAAA,GAAA,GAAAU,OAAA,CAAAH,UAAA;QAAAP,CAAA,CAAA,GAAA,GAAAmF,GAAA;QAAAnF,CAAA,CAAA,GAAA,GAAAoF,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAApF,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAqF,GAAA;IAAA,IAAArF,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAGJkF,GAAA,iBAAA,6LAAC,sIAAK;sBAAC,MAAM,EAAZ,KAAK,CAAe;;;;;;QAAArF,CAAA,CAAA,GAAA,GAAAqF,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAArF,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAsF,GAAA;IAAA,IAAAtF,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAGJmF,GAAA,IAAAC,OAAA,GAAW5E,UAAU,EAAA6E,MAAA,GAAA,CAAA;oBAAA,GAAepB,MAAI;oBAAA5D,MAAA,EAAUoE;gBAAK,CAAA,CAAG,CAAC;QAAA5E,CAAA,CAAA,GAAA,GAAAsF,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAtF,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAyF,GAAA;IAAA,IAAAzF,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAE1EsF,GAAA,iBAAA,2TAAC,iBAAa;sBACZ,2MAAC,6IAAW,GACd,EAFC,aAAa,CAEE;;;;;;;;;;QAAAzF,CAAA,CAAA,GAAA,GAAAyF,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAzF,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA0F,GAAA;IAAA,IAAA1F,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAChBuF,GAAA,iBAAA,4TAAC,gBAAa;;8BACZ,4TAAC,aAAU;oBAAO,KAAK,EAAL,KAAK;8BAAC,YAAY,EAAnC,UAAU;;;;;;8BACX,4TAAC,aAAU;oBAAO,KAAQ,EAAR,QAAQ;8BAAC,MAAM,EAAhC,UAAU;;;;;;8BACX,4TAAC,aAAU;oBAAO,KAAa,EAAb,aAAa;8BAAC,WAAW,EAA1C,UAAU;;;;;;8BACX,4TAAC,aAAU;oBAAO,KAAiB,EAAjB,iBAAiB;8BAAC,eAAe,EAAlD,UAAU;;;;;;8BACX,4TAAC,aAAU;oBAAO,KAAU,EAAV,UAAU;8BAAC,QAAQ,EAApC,UAAU,CACb,EANC,aAAa,CAME;;;;;;;;;;;;QAAA1F,CAAA,CAAA,GAAA,GAAA0F,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA1F,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA2F,GAAA;IAAA,IAAA3F,CAAA,CAAA,GAAA,KAAAU,OAAA,CAAAF,MAAA,EAAA;QAfpBmF,GAAA,iBAAA,6LAAA,GAiBM;YAjBS,SAAW,EAAX,WAAW,CACxB;;gBAAAN,GAAoB;8BACpB,4TAAC,SAAM;oBACE,KAAc,CAAd,CAAA3E,OAAO,CAAAF,MAAM,CAAC;oBACN,aAA2D,CAA3D,CAAA8E,GAA0D,CAAC,CAE1E;;wBAAAG,GAEe,CACf;wBAAAC,GAMe,CACjB,EAdC,MAAM,CAeT,EAjBA,GAiBM;;;;;;;;;;;;;QAAA1F,CAAA,CAAA,GAAA,GAAAU,OAAA,CAAAF,MAAA;QAAAR,CAAA,CAAA,GAAA,GAAA2F,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA3F,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA4F,GAAA;IAAA,IAAA5F,CAAA,CAAA,GAAA,KAAAoF,GAAA,IAAApF,CAAA,CAAA,GAAA,KAAA2F,GAAA,EAAA;QA/CZC,GAAA,iBAAA,0TAAC,OAAI,CACH;;gBAAApB,EAKY;8BACZ,0TAAC,cAAW;4CACV,6LAAA,GAwCM;wBAxCS,SAA2B,EAA3B,2BAA2B,CACxC;;4BAAAY,GAmBK,CAEL;4BAAAO,GAiBK,CACP,EAxCA,GAwCM,CACR,EA1CC,WAAW,CA2Cd,EAlDC,IAAI,CAkDE;;;;;;;;;;;;;;;;;;QAAA3F,CAAA,CAAA,GAAA,GAAAoF,GAAA;QAAApF,CAAA,CAAA,GAAA,GAAA2F,GAAA;QAAA3F,CAAA,CAAA,GAAA,GAAA4F,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA5F,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA6F,GAAA;IAAA,IAAA7F,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAIL0F,GAAA,iBAAA,0TAAC,aAAU;oCACT,0TAAC,YAAS;0BAAC,wBAAwB,EAAlC,SAAS,CACZ,EAFC,UAAU,CAEE;;;;;;;;;;;QAAA7F,CAAA,CAAA,GAAA,GAAA6F,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA7F,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA8F,GAAA;IAAA,IAAA9F,CAAA,CAAA,GAAA,KAAAmB,eAAA,EAAA;QAHf2E,GAAA,iBAAA,0TAAC,OAAI,CACH;;gBAAAD,GAEY;8BACZ,0TAAC,cAAW;4CACV,6LAAA,GAkBM;wBAlBS,SAA2B,EAA3B,2BAA2B,CACvC;kCAAApE,gBAAgB,CAAAiB,GAAA,CAAAqD,QAAA,kBACf,6LAAA,GAcM,CAdI,GAAU,CAAV;gCAAsB,SAA6B,EAA7B,6BAA6B;;kDAC3D,8TAAC,WAAQ;wCACH,EAAU,CAAV,CAAA7B,QAAM,CAAA5C,GAAG,CAAC;wCACL,OAA2D,CAA3D,CAAAH,eAAe,CAAC+C,QAAM,CAAA5C,GAAA,CAAoC,CAAC;wCACnD,eAA+D,CAA/D,EAAA0E,SAAA,GAAa3B,kBAAkB,CAACH,QAAM,CAAA5C,GAAA,EAAM6C,SAAkB,EAAC;wCACtE,QAAe,CAAf,CAAAD,QAAM,CAAA1C,QAAQ,CAAC;;;;;;kDAE3B,2TAAC,QAAK;wCACK,OAAU,CAAV,CAAA0C,QAAM,CAAA5C,GAAG,CAAC;wCACR,SAA8C,CAA9C,CAAA4C,QAAM,CAAA1C,QAAA,GAAY,uBAAuB,GAAG,EAAC,CAAC,CAExD;;4CAAA0C,QAAM,CAAA3C,KAAK,CACX;4CAAA2C,QAAM,CAAA1C,QAAA,IAAa,aAAY,CAClC,EANC,KAAK,CAOR,EAdA,GAcM,CACP,EACH,EAlBA,GAkBM,CACR,EApBC,WAAW,CAqBd,EAzBC,IAAI,CAyBE;;;;;;;;+BAlBW0C,QAAM,CAAA5C,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;QAkBrBtB,CAAA,CAAA,GAAA,GAAAmB,eAAA;QAAAnB,CAAA,CAAA,GAAA,GAAA8F,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA9F,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAiG,GAAA;IAAA,IAAAjG,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAIL8F,GAAA,iBAAA,0TAAC,aAAU;sBACT,wUAAC,YAAS;gBAAW,SAAyB,EAAzB,yBAAyB;;kCAC5C,+YAAC,WAAQ;wBAAW,SAAS,EAAT,SAAS;;;;;;oBAAG,cAElC,EAHC,SAAS,CAIZ,EALC,UAAU,CAKE;;;;;;;;;;;;QAAAjG,CAAA,CAAA,GAAA,GAAAiG,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAjG,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAkG,GAAA;IAAA,IAAAlG,CAAA,CAAA,GAAA,KAAAuE,mBAAA,CAAAlC,MAAA,EAAA;QAKL6D,GAAA,iBAAA,6LAAA,GAAoE;YAArD,SAAoB,EAApB,oBAAoB,CAAE;sBAAA9D,mBAAiB,CAAAC,MAAM,CAAE,EAA9D,GAAoE;;;;;;QAAArC,CAAA,CAAA,GAAA,GAAAuE,mBAAA,CAAAlC,MAAA;QAAArC,CAAA,CAAA,GAAA,GAAAkG,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAlG,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAmG,GAAA;IAAA,IAAAnG,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QACpEgG,GAAA,iBAAA,6LAAA,GAAwE;YAAzD,SAA+B,EAA/B,+BAA+B;sBAAC,mBAAmB,EAAlE,GAAwE;;;;;;QAAAnG,CAAA,CAAA,GAAA,GAAAmG,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAnG,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAoG,GAAA;IAAA,IAAApG,CAAA,CAAA,GAAA,KAAAkG,GAAA,EAAA;QAF1EE,GAAA,iBAAA,6LAAA,GAGM;YAHS,SAAqC,EAArC,qCAAqC,CAClD;;gBAAAF,GAAmE,CACnE;gBAAAC,GAAuE,CACzE,EAHA,GAGM;;;;;;;QAAAnG,CAAA,CAAA,GAAA,GAAAkG,GAAA;QAAAlG,CAAA,CAAA,GAAA,GAAAoG,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAApG,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAqG,GAAA;IAAA,IAAArG,CAAA,CAAA,GAAA,KAAAmB,eAAA,EAAA;QAGDkF,GAAA,GAAAC,MAAA,CAAAC,MAAA,CAAcpF,eAAe,CAAC,CAAAQ,MAAA,CAAA6E,OAAe,CAAC;QAAAxG,CAAA,CAAA,GAAA,GAAAmB,eAAA;QAAAnB,CAAA,CAAA,GAAA,GAAAqG,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAArG,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAyG,GAAA;IAAA,IAAAzG,CAAA,CAAA,GAAA,KAAAqG,GAAA,CAAAhE,MAAA,EAAA;QADjDoE,GAAA,iBAAA,6LAAA,GAEM;YAFS,SAAoB,EAApB,oBAAoB,CAChC;sBAAAJ,GAA8C,CAAAhE,MAAM,CACvD,EAFA,GAEM;;;;;;QAAArC,CAAA,CAAA,GAAA,GAAAqG,GAAA,CAAAhE,MAAA;QAAArC,CAAA,CAAA,GAAA,GAAAyG,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAzG,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA0G,GAAA;IAAA,IAAA1G,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QACNuG,GAAA,iBAAA,6LAAA,GAAqE;YAAtD,SAA+B,EAA/B,+BAA+B;sBAAC,gBAAgB,EAA/D,GAAqE;;;;;;QAAA1G,CAAA,CAAA,GAAA,GAAA0G,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA1G,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA2G,GAAA;IAAA,IAAA3G,CAAA,CAAA,GAAA,KAAAyG,GAAA,EAAA;QAJvEE,GAAA,iBAAA,6LAAA,GAKM;YALS,SAAqC,EAArC,qCAAqC,CAClD;;gBAAAF,GAEK,CACL;gBAAAC,GAAoE,CACtE,EALA,GAKM;;;;;;;QAAA1G,CAAA,CAAA,GAAA,GAAAyG,GAAA;QAAAzG,CAAA,CAAA,GAAA,GAAA2G,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA3G,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA4G,GAAA;IAAA,IAAA5G,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QACNyG,GAAA,iBAAA,6LAAA,GAGM;YAHS,SAAqC,EAArC,qCAAqC;;8BAClD,6LAAA,GAA6C;oBAA9B,SAAoB,EAApB,oBAAoB;8BAAC,GAAG,EAAvC,GAA6C;;;;;;8BAC7C,6LAAA,GAAkE;oBAAnD,SAA+B,EAA/B,+BAA+B;8BAAC,aAAa,EAA5D,GAAkE,CACpE,EAHA,GAGM;;;;;;;;;;;;QAAA5G,CAAA,CAAA,GAAA,GAAA4G,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA5G,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA6G,GAAA;IAAA,IAAA7G,CAAA,CAAA,GAAA,KAAAoG,GAAA,IAAApG,CAAA,CAAA,GAAA,KAAA2G,GAAA,EAAA;QAdRE,GAAA,iBAAA,6LAAA,GAeM;YAfS,SAA2B,EAA3B,2BAA2B,CACxC;;gBAAAT,GAGK,CACL;gBAAAO,GAKK,CACL;gBAAAC,GAGK,CACP,EAfA,GAeM;;;;;;;QAAA5G,CAAA,CAAA,GAAA,GAAAoG,GAAA;QAAApG,CAAA,CAAA,GAAA,GAAA2G,GAAA;QAAA3G,CAAA,CAAA,GAAA,GAAA6G,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA7G,CAAA,CAAA,GAAA;IAAA;IAIM,MAAA8G,GAAA,GAAA1E,mBAAiB,CAAAC,MAAA,KAAA,CAAa;IAAA,IAAA0E,GAAA;IAAA,IAAA/G,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAGxC4G,GAAA,iBAAA,2YAAC,WAAQ;YAAW,SAAc,EAAd,cAAc,GAAG;;;;;;QAAA/G,CAAA,CAAA,GAAA,GAAA+G,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA/G,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAgH,GAAA;IAAA,IAAAhH,CAAA,CAAA,GAAA,KAAAuE,mBAAA,CAAAlC,MAAA,IAAArC,CAAA,CAAA,GAAA,KAAAgE,WAAA,IAAAhE,CAAA,CAAA,GAAA,KAAA8G,GAAA,EAAA;QALvCE,GAAA,iBAAA,4TAAC,SAAM;YACIhD,OAAW,CAAXA,CAAAA,WAAU,CAAC;YACV,QAA8B,CAA9B,CAAA8C,GAA6B,CAAC;YAC9B,SAAQ,EAAR,QAAQ,CAElB;;gBAAAC,GAAoC;gBAAC,OAC7B;gBAAA3E,mBAAiB,CAAAC,MAAM;gBAAE,iBACnC,EAPC,MAAM,CAOE;;;;;;;QAAArC,CAAA,CAAA,GAAA,GAAAuE,mBAAA,CAAAlC,MAAA;QAAArC,CAAA,CAAA,GAAA,GAAAgE,WAAA;QAAAhE,CAAA,CAAA,GAAA,GAAA8G,GAAA;QAAA9G,CAAA,CAAA,GAAA,GAAAgH,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAhH,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAiH,GAAA;IAAA,IAAAjH,CAAA,CAAA,GAAA,KAAA6G,GAAA,IAAA7G,CAAA,CAAA,GAAA,KAAAgH,GAAA,EAAA;QAjCfC,GAAA,iBAAA,0TAAC,OAAI,CACH;;gBAAAhB,GAKY;8BACZ,0TAAC,cAAW;4CACV,6LAAA,GA0BM;wBA1BS,SAAW,EAAX,WAAW,CACxB;;4BAAAY,GAeK,CAEL;4BAAAG,GAOQ,CACV,EA1BA,GA0BM,CACR,EA5BC,WAAW,CA6Bd,EApCC,IAAI,CAoCE;;;;;;;;;;;;;;;;;;QAAAhH,CAAA,CAAA,GAAA,GAAA6G,GAAA;QAAA7G,CAAA,CAAA,GAAA,GAAAgH,GAAA;QAAAhH,CAAA,CAAA,GAAA,GAAAiH,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAjH,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAkH,GAAA;IAAA,IAAAlH,CAAA,CAAA,GAAA,KAAA4F,GAAA,IAAA5F,CAAA,CAAA,GAAA,KAAA8F,GAAA,IAAA9F,CAAA,CAAA,GAAA,KAAAiH,GAAA,EAAA;QAvHTC,GAAA,iBAAA,6LAAA,GAwHM;YAxHS,SAAW,EAAX,WAAW,CAExB;;gBAAAtB,GAkDM,CAGN;gBAAAE,GAyBM,CAGN;gBAAAmB,GAoCM,CACR,EAxHA,GAwHM;;;;;;;QAAAjH,CAAA,CAAA,GAAA,GAAA4F,GAAA;QAAA5F,CAAA,CAAA,GAAA,GAAA8F,GAAA;QAAA9F,CAAA,CAAA,GAAA,GAAAiH,GAAA;QAAAjH,CAAA,CAAA,GAAA,GAAAkH,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAlH,CAAA,CAAA,GAAA;IAAA;IAAA,OAxHNkH,GAwHM;AAAA;;KAzOHpH;AAAA,SAAAoF,OAAAiC,MAAA;IAAA,qBAyIa,4TAAC,aAAU,CAAM,GAAO,CAAP;QAAgB,KAAO,CAAP,CAAAlF,MAAI,CAAApB,EAAE,CAAC,CACrC;kBAAAoB,MAAI,CAAAnB,IAAI,CACX,EAFC,UAAU,CAEE;OAFImB,MAAI,CAAApB,EAAE,CAAC;;;;;AAEX;AA3I1B,SAAAkC,OAAAqE,KAAA;IAAA,OA0FkBxE,KAAG,CAAAF,GAAA,CAAA2E,KAAwB,CAAC,CAAAvE,IAAA,CAAM,GAAG,CAAC;AAAA;AA1FxD,SAAAuE,MAAAC,IAAA;IAAA,OA0FkC,IAAQ,OAAJA,IAAI,EAAA,EAAG;AAAA", "ignoreList": [], "debugId": null}}, {"offset": {"line": 2325, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/bulk/G%3A/Augment%20code/components/bulk/bulk-transfer-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Employee, Department } from \"@/lib/types\"\nimport { useHRStore } from \"@/lib/store/hr-store\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Label } from \"@/components/ui/label\"\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { \n  ArrowRight, \n  Users,\n  AlertCircle,\n  CheckCircle\n} from \"lucide-react\"\n\ninterface BulkTransferSectionProps {\n  employees: Employee[]\n  departments: Department[]\n  selectedEmployees: string[]\n}\n\nexport function BulkTransferSection({ \n  employees, \n  departments, \n  selectedEmployees \n}: BulkTransferSectionProps) {\n  const { executeBulkOperation, clearSelection } = useHRStore()\n  const [targetDepartment, setTargetDepartment] = useState<string>(\"\")\n  const [isTransferring, setIsTransferring] = useState(false)\n\n  const selectedEmployeeData = employees.filter(emp => \n    selectedEmployees.includes(emp.id)\n  )\n\n  const getDepartmentName = (departmentId: string | null) => {\n    if (!departmentId) return \"Unassigned\"\n    const department = departments.find(dept => dept.id === departmentId)\n    return department?.name || \"Unknown\"\n  }\n\n  const getTargetDepartment = () => {\n    if (targetDepartment === \"unassigned\") return null\n    return departments.find(dept => dept.id === targetDepartment) || null\n  }\n\n  const getCapacityInfo = () => {\n    const target = getTargetDepartment()\n    if (!target) return null\n\n    const currentEmployees = employees.filter(emp => emp.departmentId === target.id)\n    const afterTransfer = currentEmployees.length + selectedEmployees.length\n    const utilization = Math.round((afterTransfer / target.capacity) * 100)\n\n    return {\n      current: currentEmployees.length,\n      afterTransfer,\n      capacity: target.capacity,\n      utilization,\n      isOverCapacity: afterTransfer > target.capacity\n    }\n  }\n\n  const handleTransfer = async () => {\n    if (!targetDepartment || selectedEmployees.length === 0) return\n\n    const targetDeptId = targetDepartment === \"unassigned\" ? null : targetDepartment\n\n    setIsTransferring(true)\n    try {\n      await executeBulkOperation({\n        type: 'transfer',\n        employeeIds: selectedEmployees,\n        targetDepartmentId: targetDeptId\n      })\n\n      clearSelection()\n      setTargetDepartment(\"\")\n      alert(`Successfully transferred ${selectedEmployees.length} employees`)\n    } catch (error) {\n      console.error('Bulk transfer failed:', error)\n      alert('Transfer failed. Please try again.')\n    } finally {\n      setIsTransferring(false)\n    }\n  }\n\n  const capacityInfo = getCapacityInfo()\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Selection Summary */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Users className=\"h-5 w-5\" />\n            Selected Employees ({selectedEmployees.length})\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          {selectedEmployees.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <AlertCircle className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n              <p className=\"text-muted-foreground\">\n                No employees selected. Go to the Employees page to select employees for transfer.\n              </p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              <div className=\"grid gap-2 max-h-40 overflow-y-auto\">\n                {selectedEmployeeData.map((employee) => (\n                  <div key={employee.id} className=\"flex items-center justify-between p-2 bg-muted rounded\">\n                    <div>\n                      <div className=\"font-medium\">{employee.name}</div>\n                      <div className=\"text-sm text-muted-foreground\">{employee.email}</div>\n                    </div>\n                    <Badge variant=\"outline\">\n                      {getDepartmentName(employee.departmentId)}\n                    </Badge>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Transfer Configuration */}\n      {selectedEmployees.length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <ArrowRight className=\"h-5 w-5\" />\n              Transfer Configuration\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label>Target Department</Label>\n                <Select\n                  value={targetDepartment}\n                  onValueChange={setTargetDepartment}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select target department\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"unassigned\">Unassigned (Free Bucket)</SelectItem>\n                    {departments.map((dept) => (\n                      <SelectItem key={dept.id} value={dept.id}>\n                        {dept.name} ({dept.code})\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Capacity Warning */}\n              {capacityInfo && (\n                <Card className={`border-2 ${\n                  capacityInfo.isOverCapacity \n                    ? 'border-red-200 bg-red-50' \n                    : capacityInfo.utilization >= 80 \n                    ? 'border-orange-200 bg-orange-50'\n                    : 'border-green-200 bg-green-50'\n                }`}>\n                  <CardContent className=\"pt-4\">\n                    <div className=\"flex items-center gap-2 mb-2\">\n                      {capacityInfo.isOverCapacity ? (\n                        <AlertCircle className=\"h-5 w-5 text-red-600\" />\n                      ) : (\n                        <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                      )}\n                      <span className=\"font-medium\">\n                        Capacity Check\n                      </span>\n                    </div>\n                    \n                    <div className=\"space-y-1 text-sm\">\n                      <div>Current: {capacityInfo.current} / {capacityInfo.capacity}</div>\n                      <div>After transfer: {capacityInfo.afterTransfer} / {capacityInfo.capacity}</div>\n                      <div>Utilization: {capacityInfo.utilization}%</div>\n                    </div>\n\n                    {capacityInfo.isOverCapacity && (\n                      <div className=\"mt-2 text-sm text-red-700\">\n                        ⚠️ This transfer will exceed department capacity by {capacityInfo.afterTransfer - capacityInfo.capacity} employees.\n                      </div>\n                    )}\n                  </CardContent>\n                </Card>\n              )}\n\n              <Button \n                onClick={handleTransfer}\n                disabled={!targetDepartment || isTransferring}\n                className=\"w-full\"\n              >\n                {isTransferring ? 'Transferring...' : `Transfer ${selectedEmployees.length} Employees`}\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Instructions */}\n      <Card className=\"border-blue-200 bg-blue-50\">\n        <CardHeader>\n          <CardTitle className=\"text-blue-800\">Transfer Instructions</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-2 text-sm text-blue-700\">\n            <p>1. Go to the <strong>Employees</strong> page and select the employees you want to transfer</p>\n            <p>2. Return to this page and select the target department</p>\n            <p>3. Review the capacity information and confirm the transfer</p>\n            <p>4. The system will update all selected employees' department assignments</p>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": ["useState", "useHRStore", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "Label", "Select", "SelectContent", "SelectItem", "SelectTrigger", "SelectValue", "Badge", "ArrowRight", "Users", "AlertCircle", "CheckCircle", "BulkTransferSection", "employees", "departments", "selectedEmployees", "executeBulkOperation", "clearSelection", "targetDepartment", "setTargetDepartment", "isTransferring", "setIsTransferring", "selectedEmployeeData", "filter", "emp", "includes", "id", "getDepartmentName", "departmentId", "department", "find", "dept", "name", "getTargetDepartment", "getCapacityInfo", "target", "currentEmployees", "afterTransfer", "length", "utilization", "Math", "round", "capacity", "current", "isOverCapacity", "handleTransfer", "targetDeptId", "type", "employeeIds", "targetDepartmentId", "alert", "error", "console", "capacityInfo", "map", "employee", "email", "code"], "mappings": ";;;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAEhC,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,sBAAsB;AAC/E,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SACEC,MAAM,EACNC,aAAa,EACbC,UAAU,EACVC,aAAa,EACbC,WAAW,QACN,wBAAwB;AAC/B,SAASC,KAAK,QAAQ,uBAAuB;;;;AAC7C,SACEC,UAAU,EACVC,KAAK,EACLC,WAAW,EACXC,WAAW,QACN,cAAc;;;AArBrB,YAAY;;;;;;;;;AA6BL,SAASC,mBAAmBA;QAAC,EAClCC,SAAS,EACTC,WAAW,EACXC,iBAAAA,EACyB,EAAE;;IAC3B,MAAM,EAAEC,oBAAoB,EAAEC,cAAAA,EAAgB,mJAAGtB;IACjD,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,oKAAGzB,YAAQ,AAARA,EAAiB,EAAE,CAAC;IACpE,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,qKAAG3B,WAAAA,AAAQ,EAAC,KAAK,CAAC;IAE3D,MAAM4B,oBAAoB,GAAGT,SAAS,CAACU,MAAM,EAACC,GAAG,GAC/CT,iBAAiB,CAACU,QAAQ,CAACD,GAAG,CAACE,EAAE,CACnC,CAAC;IAED,MAAMC,iBAAiB,IAAIC,YAA2B,IAAK;QACzD,IAAI,CAACA,YAAY,EAAE,OAAO,YAAY;QACtC,MAAMC,UAAU,GAAGf,WAAW,CAACgB,IAAI,EAACC,IAAI,GAAIA,IAAI,CAACL,EAAE,KAAKE,YAAY,CAAC;QACrE,gEAAOC,UAAU,CAAEG,IAAI,KAAI,SAAS;IACtC,CAAC;IAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;QAChC,IAAIf,gBAAgB,KAAK,YAAY,EAAE,OAAO,IAAI;QAClD,OAAOJ,WAAW,CAACgB,IAAI,EAACC,MAAI,GAAIA,MAAI,CAACL,EAAE,KAAKR,gBAAgB,CAAC,IAAI,IAAI;IACvE,CAAC;IAED,MAAMgB,eAAe,GAAGA,CAAA,KAAM;QAC5B,MAAMC,MAAM,GAAGF,mBAAmB,CAAC,CAAC;QACpC,IAAI,CAACE,MAAM,EAAE,OAAO,IAAI;QAExB,MAAMC,gBAAgB,GAAGvB,SAAS,CAACU,MAAM,EAACC,KAAG,GAAIA,KAAG,CAACI,YAAY,KAAKO,MAAM,CAACT,EAAE,CAAC;QAChF,MAAMW,aAAa,GAAGD,gBAAgB,CAACE,MAAM,GAAGvB,iBAAiB,CAACuB,MAAM;QACxE,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAEJ,aAAa,GAAGF,MAAM,CAACO,QAAQ,GAAI,GAAG,CAAC;QAEvE,OAAO;YACLC,OAAO,EAAEP,gBAAgB,CAACE,MAAM;YAChCD,aAAa;YACbK,QAAQ,EAAEP,MAAM,CAACO,QAAQ;YACzBH,WAAW;YACXK,cAAc,EAAEP,aAAa,GAAGF,MAAM,CAACO,QAAAA;QACzC,CAAC;IACH,CAAC;IAED,MAAMG,cAAc,GAAG,MAAAA,CAAA,KAAY;QACjC,IAAI,CAAC3B,gBAAgB,IAAIH,iBAAiB,CAACuB,MAAM,KAAK,CAAC,EAAE;QAEzD,MAAMQ,YAAY,GAAG5B,gBAAgB,KAAK,YAAY,GAAG,IAAI,GAAGA,gBAAgB;QAEhFG,iBAAiB,CAAC,IAAI,CAAC;QACvB,IAAI;YACF,MAAML,oBAAoB,CAAC;gBACzB+B,IAAI,EAAE,UAAU;gBAChBC,WAAW,EAAEjC,iBAAiB;gBAC9BkC,kBAAkB,EAAEH;YACtB,CAAC,CAAC;YAEF7B,cAAc,CAAC,CAAC;YAChBE,mBAAmB,CAAC,EAAE,CAAC;YACvB+B,KAAK,CAAC,4BAAoD,OAAxBnC,IAAoC,CAAC,YAApB,CAACuB,MAAM,EAAA;QAC5D,CAAC,CAAC,OAAOa,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;YAC7CD,KAAK,CAAC,oCAAoC,CAAC;QAC7C,CAAC,QAAS;YACR7B,iBAAiB,CAAC,KAAK,CAAC;QAC1B;IACF,CAAC;IAED,MAAMgC,YAAY,GAAGnB,eAAe,CAAC,CAAC;IAEtC,qBACE,6LAAC,GAAG;QAAC,SAAS,EAAC,WAAW;;0BAExB,0TAAC,OAAI;;kCACH,0TAAC,aAAU;gDACT,yTAAC,aAAS;4BAAC,SAAS,EAAC,yBAAyB;;8CAC5C,qYAAC,QAAK;oCAAC,SAAS,EAAC,SAAS;;;;;;gCAAA;gCACLnB,iBAAiB,CAACuB,MAAM;gCAAC;;;;;;;;;;;;kCAGlD,0TAAC,cAAW;kCACTvB,iBAAiB,CAACuB,MAAM,KAAK,CAAC,iBAC7B,6LAAC,GAAG;4BAAC,SAAS,EAAC,kBAAkB;;8CAC/B,qZAAC,cAAW;oCAAC,SAAS,EAAC,8CAA8C;;;;;;8CACrE,6LAAC,CAAC;oCAAC,SAAS,EAAC,uBAAuB;8CAAA;;;;;;;;;;;iDAKtC,6LAAC,GAAG;4BAAC,SAAS,EAAC,WAAW;oDACxB,6LAAC,GAAG;gCAAC,SAAS,EAAC,qCAAqC;0CACjDhB,oBAAoB,CAACgC,GAAG,EAAEC,QAAQ,iBACjC,6LAAC,GAAG,CAAC,GAAG,CAAC;wCAAc,SAAS,EAAC,wDAAwD;;0DACvF,6LAAC,GAAG;;kEACF,6LAAC,GAAG;wDAAC,SAAS,EAAC,aAAa,CAAC;kEAACA,QAAQ,CAACvB,IAAI,CAAC,EAAE,GAAG;;;;;;kEACjD,6LAAC,GAAG;wDAAC,SAAS,EAAC,+BAA+B,CAAC;kEAACuB,QAAQ,CAACC,KAAK,CAAC,EAAE,GAAG;;;;;;;;;;;;0DAEtE,2TAAC,QAAK;gDAAC,OAAO,EAAC,SAAS;0DACrB7B,iBAAiB,CAAC4B,QAAQ,CAAC3B,YAAY,CAAC;;;;;;;uCANnC2B,QAAQ,CAAC7B,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;YAiBjCX,iBAAiB,CAACuB,MAAM,GAAG,CAAC,kBAC3B,0TAAC,OAAI;;kCACH,6LAAC,0IAAU;kCACT,wUAAC,YAAS;4BAAC,SAAS,EAAC,yBAAyB;;8CAC5C,mZAAC,aAAU;oCAAC,SAAS,EAAC,SAAS;;;;;;gCAAA;;;;;;;;;;;;kCAInC,0TAAC,cAAW;gDACV,6LAAC,GAAG;4BAAC,SAAS,EAAC,WAAW;;8CACxB,6LAAC,GAAG;oCAAC,SAAS,EAAC,WAAW;;sDACxB,2TAAC,QAAK;sDAAC,iBAAiB,EAAE,KAAK;;;;;;sDAC/B,4TAAC,SAAM;4CACL,KAAK,CAAC,CAACpB,gBAAgB,CAAC;4CACxB,aAAa,CAAC,CAACC,mBAAmB,CAAC;;8DAEnC,6LAAC,+IAAa;4EACZ,2TAAC,eAAW;wDAAC,WAAW,EAAC,0BAA0B;;;;;;;;;;;8DAErD,4TAAC,gBAAa;;sEACZ,6LAAC,4IAAU;4DAAC,KAAK,EAAC,YAAY;sEAAC,wBAAwB,EAAE,UAAU;;;;;;wDAClEL,WAAW,CAACwC,GAAG,EAAEvB,MAAI,iBACpB,4TAAC,aAAU,CAAC,GAAG,CAAC;gEAAU,KAAK,CAAC,CAACA,MAAI,CAACL,EAAE,CAAC;;oEACtCK,MAAI,CAACC,IAAI;oEAAC,EAAE;oEAACD,MAAI,CAAC0B,IAAI;oEAAC;;+DADT1B,MAAI,CAACL,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;gCAShC2B,YAAY,kBACX,yTAAC,QAAI;oCAAC,SAAS,CAAC,CAAC,YAKmB,CAClC,CAAC,KALDA,YAAY,CAACT,cAAc,GACvB,0BAA0B,GAC1BS,YAAY,CAACd,WAAW,IAAI,EAAE,GAC9B,gCAAgC,GAChC,8BAA8B;4DAElC,0TAAC,cAAW;wCAAC,SAAS,EAAC,MAAM;;0DAC3B,6LAAC,GAAG;gDAAC,SAAS,EAAC,8BAA8B;;oDAC1Cc,YAAY,CAACT,cAAc,iBAC1B,qZAAC,cAAW;wDAAC,SAAS,EAAC,sBAAsB,GAAG;;;;;6EAEhD,qZAAC,cAAW;wDAAC,SAAS,EAAC,wBAAwB,GAChD;;;;;;kEACD,6LAAC,IAAI;wDAAC,SAAS,EAAC,aAAa;kEAAA;;;;;;;;;;;;0DAK/B,6LAAC,GAAG;gDAAC,SAAS,EAAC,mBAAmB;;kEAChC,6LAAC,GAAG;;4DAAC,SAAS;4DAACS,YAAY,CAACV,OAAO;4DAAC,GAAG;4DAACU,YAAY,CAACX,QAAQ,CAAC,EAAE,GAAG;;;;;;;kEACnE,6LAAC,GAAG;;4DAAC,gBAAgB;4DAACW,YAAY,CAAChB,aAAa;4DAAC,GAAG;4DAACgB,YAAY,CAACX,QAAQ,CAAC,EAAE,GAAG;;;;;;;kEAChF,6LAAC,GAAG;;4DAAC,aAAa;4DAACW,YAAY,CAACd,WAAW;4DAAC,CAAC,EAAE,GAAG;;;;;;;;;;;;;4CAGnDc,YAAY,CAACT,cAAc,kBAC1B,6LAAC,GAAG;gDAAC,SAAS,EAAC,2BAA2B;;oDAAA;oDACaS,YAAY,CAAChB,aAAa,GAAGgB,YAAY,CAACX,QAAQ;oDAAC;;;;;;;;;;;;;;;;;;8CAOlH,4TAAC,SAAM;oCACL,OAAO,CAAC,CAACG,cAAc,CAAC;oCACxB,QAAQ,CAAC,CAAC,CAAC3B,gBAAgB,IAAIE,cAAc,CAAC;oCAC9C,SAAS,EAAC,QAAQ;8CAEjBA,cAAc,GAAG,iBAAiB,GAAG,YAAoC,OAAxBL,iBAAiB,CAACuB,MAAM,EAAA,WAAY;;;;;;;;;;;;;;;;;;;;;;;0BAQhG,0TAAC,OAAI;gBAAC,SAAS,EAAC,4BAA4B;;kCAC1C,0TAAC,aAAU;gDACT,0TAAC,YAAS;4BAAC,SAAS,EAAC,eAAe;sCAAC,qBAAqB,EAAE,SAAS;;;;;;;;;;;kCAEvE,0TAAC,cAAW;gDACV,6LAAC,GAAG;4BAAC,SAAS,EAAC,iCAAiC;;8CAC9C,6LAAC,CAAC;;wCAAC;sDAAa,6LAAC,MAAM;sDAAC,SAAS,EAAE,MAAM;;;;;;wCAAC,mDAAmD,EAAE,CAAC;;;;;;;8CAChG,6LAAC,CAAC;8CAAC,uDAAuD,EAAE,CAAC;;;;;;8CAC7D,6LAAC,CAAC;8CAAC,2DAA2D,EAAE,CAAC;;;;;;8CACjE,6LAAC,CAAC;8CAAC,wEAAwE,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1F;;;uIAnMmD3C,aAAU,CAAC,CAAC", "ignoreList": [], "debugId": null}}, {"offset": {"line": 2848, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/bulk/G%3A/Augment%20code/components/bulk/bulk-status-update-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Employee } from \"@/lib/types\"\nimport { useHRStore } from \"@/lib/store/hr-store\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Label } from \"@/components/ui/label\"\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { \n  Users,\n  AlertCircle,\n  RefreshCw\n} from \"lucide-react\"\n\ninterface BulkStatusUpdateSectionProps {\n  employees: Employee[]\n  selectedEmployees: string[]\n}\n\nexport function BulkStatusUpdateSection({ \n  employees, \n  selectedEmployees \n}: BulkStatusUpdateSectionProps) {\n  const { executeBulkOperation, clearSelection } = useHRStore()\n  const [newStatus, setNewStatus] = useState<Employee['status'] | \"\">(\"\")\n  const [isUpdating, setIsUpdating] = useState(false)\n\n  const selectedEmployeeData = employees.filter(emp => \n    selectedEmployees.includes(emp.id)\n  )\n\n  const statusOptions = [\n    { value: 'ACTIVE', label: 'Active', description: 'Employee is currently active' },\n    { value: 'TRANSFERRED', label: 'Transferred', description: 'Employee has been transferred' },\n    { value: 'PENDING_REMOVAL', label: 'Pending Removal', description: 'Employee is scheduled for removal' },\n    { value: 'ARCHIVED', label: 'Archived', description: 'Employee record is archived' }\n  ] as const\n\n  const getStatusBadge = (status: Employee['status']) => {\n    const variants = {\n      ACTIVE: \"default\",\n      TRANSFERRED: \"secondary\",\n      PENDING_REMOVAL: \"destructive\",\n      ARCHIVED: \"outline\"\n    } as const\n\n    return (\n      <Badge variant={variants[status]}>\n        {status.replace('_', ' ')}\n      </Badge>\n    )\n  }\n\n  const getStatusCounts = () => {\n    const counts = selectedEmployeeData.reduce((acc, emp) => {\n      acc[emp.status] = (acc[emp.status] || 0) + 1\n      return acc\n    }, {} as Record<Employee['status'], number>)\n\n    return counts\n  }\n\n  const handleStatusUpdate = async () => {\n    if (!newStatus || selectedEmployees.length === 0) return\n\n    setIsUpdating(true)\n    try {\n      await executeBulkOperation({\n        type: 'status_update',\n        employeeIds: selectedEmployees,\n        newStatus: newStatus as Employee['status']\n      })\n\n      clearSelection()\n      setNewStatus(\"\")\n      alert(`Successfully updated status for ${selectedEmployees.length} employees`)\n    } catch (error) {\n      console.error('Bulk status update failed:', error)\n      alert('Status update failed. Please try again.')\n    } finally {\n      setIsUpdating(false)\n    }\n  }\n\n  const statusCounts = getStatusCounts()\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Selection Summary */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Users className=\"h-5 w-5\" />\n            Selected Employees ({selectedEmployees.length})\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          {selectedEmployees.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <AlertCircle className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n              <p className=\"text-muted-foreground\">\n                No employees selected. Go to the Employees page to select employees for status update.\n              </p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {/* Current Status Distribution */}\n              <div>\n                <h4 className=\"font-medium mb-2\">Current Status Distribution:</h4>\n                <div className=\"flex flex-wrap gap-2\">\n                  {Object.entries(statusCounts).map(([status, count]) => (\n                    <div key={status} className=\"flex items-center gap-2\">\n                      {getStatusBadge(status as Employee['status'])}\n                      <span className=\"text-sm text-muted-foreground\">({count})</span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Employee List */}\n              <div className=\"grid gap-2 max-h-40 overflow-y-auto\">\n                {selectedEmployeeData.map((employee) => (\n                  <div key={employee.id} className=\"flex items-center justify-between p-2 bg-muted rounded\">\n                    <div>\n                      <div className=\"font-medium\">{employee.name}</div>\n                      <div className=\"text-sm text-muted-foreground\">{employee.email}</div>\n                    </div>\n                    {getStatusBadge(employee.status)}\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Status Update Configuration */}\n      {selectedEmployees.length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <RefreshCw className=\"h-5 w-5\" />\n              Status Update Configuration\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <Label>New Status</Label>\n                <Select\n                  value={newStatus}\n                  onValueChange={(value) => setNewStatus(value as Employee['status'])}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select new status\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {statusOptions.map((option) => (\n                      <SelectItem key={option.value} value={option.value}>\n                        <div>\n                          <div className=\"font-medium\">{option.label}</div>\n                          <div className=\"text-xs text-muted-foreground\">{option.description}</div>\n                        </div>\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Preview */}\n              {newStatus && (\n                <Card className=\"border-blue-200 bg-blue-50\">\n                  <CardContent className=\"pt-4\">\n                    <div className=\"flex items-center gap-2 mb-2\">\n                      <RefreshCw className=\"h-4 w-4 text-blue-600\" />\n                      <span className=\"font-medium text-blue-800\">Preview Changes</span>\n                    </div>\n                    <div className=\"text-sm text-blue-700\">\n                      {selectedEmployees.length} employees will be updated to: {getStatusBadge(newStatus as Employee['status'])}\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n\n              <Button \n                onClick={handleStatusUpdate}\n                disabled={!newStatus || isUpdating}\n                className=\"w-full\"\n              >\n                {isUpdating ? 'Updating...' : `Update Status for ${selectedEmployees.length} Employees`}\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Status Descriptions */}\n      <Card className=\"border-gray-200 bg-gray-50\">\n        <CardHeader>\n          <CardTitle className=\"text-gray-800\">Status Descriptions</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-2 text-sm text-gray-700\">\n            {statusOptions.map((option) => (\n              <div key={option.value} className=\"flex items-center gap-2\">\n                {getStatusBadge(option.value)}\n                <span>{option.description}</span>\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Instructions */}\n      <Card className=\"border-blue-200 bg-blue-50\">\n        <CardHeader>\n          <CardTitle className=\"text-blue-800\">Status Update Instructions</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-2 text-sm text-blue-700\">\n            <p>1. Go to the <strong>Employees</strong> page and select the employees whose status you want to update</p>\n            <p>2. Return to this page and select the new status</p>\n            <p>3. Review the preview and confirm the update</p>\n            <p>4. All selected employees will have their status updated simultaneously</p>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": ["useState", "useHRStore", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "Label", "Select", "SelectContent", "SelectItem", "SelectTrigger", "SelectValue", "Badge", "Users", "AlertCircle", "RefreshCw", "BulkStatusUpdateSection", "employees", "selectedEmployees", "executeBulkOperation", "clearSelection", "newStatus", "setNewStatus", "isUpdating", "setIsUpdating", "selectedEmployeeData", "filter", "emp", "includes", "id", "statusOptions", "value", "label", "description", "getStatusBadge", "status", "variants", "ACTIVE", "TRANSFERRED", "PENDING_REMOVAL", "ARCHIVED", "replace", "getStatusCounts", "counts", "reduce", "acc", "handleStatusUpdate", "length", "type", "employeeIds", "alert", "error", "console", "statusCounts", "Object", "entries", "map", "count", "employee", "name", "email", "option"], "mappings": ";;;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAEhC,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,sBAAsB;AAC/E,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SACEC,MAAM,EACNC,aAAa,EACbC,UAAU,EACVC,aAAa,EACbC,WAAW,QACN,wBAAwB;AAC/B,SAASC,KAAK,QAAQ,uBAAuB;;;AAC7C,SACEC,KAAK,EACLC,WAAW,EACXC,SAAS,QACJ,cAAc;;;AApBrB,YAAY;;;;;;;;;AA2BL,SAASC,uBAAuBA,MAGR,EAAE;QAHO,EACtCC,SAAS,EACTC,iBAAAA;;IAEA,MAAM,EAAEC,oBAAoB,EAAEC,cAAAA,EAAgB,mJAAGpB;IACjD,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,6KAAAA,AAAQ,EAA0B,EAAE,CAAC;IACvE,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,qKAAGzB,WAAQ,AAARA,EAAS,KAAK,CAAC;IAEnD,MAAM0B,oBAAoB,GAAGR,SAAS,CAACS,MAAM,EAACC,GAAG,GAC/CT,iBAAiB,CAACU,QAAQ,CAACD,GAAG,CAACE,EAAE,CACnC,CAAC;IAED,MAAMC,aAAa,GAAG;QACpB;YAAEC,KAAK,EAAE,QAAQ;YAAEC,KAAK,EAAE,QAAQ;YAAEC,WAAW,EAAE;QAA+B,CAAC;QACjF;YAAEF,KAAK,EAAE,aAAa;YAAEC,KAAK,EAAE,aAAa;YAAEC,WAAW,EAAE;QAAgC,CAAC;QAC5F;YAAEF,KAAK,EAAE,iBAAiB;YAAEC,KAAK,EAAE,iBAAiB;YAAEC,WAAW,EAAE;QAAoC,CAAC;QACxG;YAAEF,KAAK,EAAE,UAAU;YAAEC,KAAK,EAAE,UAAU;YAAEC,WAAW,EAAE;QAA8B,CAAC;KAC5E;IAEV,MAAMC,cAAc,IAAIC,MAA0B,IAAK;QACrD,MAAMC,QAAQ,GAAG;YACfC,MAAM,EAAE,SAAS;YACjBC,WAAW,EAAE,WAAW;YACxBC,eAAe,EAAE,aAAa;YAC9BC,QAAQ,EAAE;QACZ,CAAU;QAEV,qBACE,2TAAC,QAAK;YAAC,OAAO,CAAC,CAACJ,QAAQ,CAACD,MAAM,CAAC,CAAC;sBAC9BA,MAAM,CAACM,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;;;;;;IAG/B,CAAC;IAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;QAC5B,MAAMC,MAAM,GAAGlB,oBAAoB,CAACmB,MAAM,CAAC,CAACC,GAAG,EAAElB,KAAG,KAAK;YACvDkB,GAAG,CAAClB,KAAG,CAACQ,MAAM,CAAC,GAAG,CAACU,GAAG,CAAClB,KAAG,CAACQ,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;YAC5C,OAAOU,GAAG;QACZ,CAAC,EAAE,CAAC,CAAuC,CAAC;QAE5C,OAAOF,MAAM;IACf,CAAC;IAED,MAAMG,kBAAkB,GAAG,MAAAA,CAAA,KAAY;QACrC,IAAI,CAACzB,SAAS,IAAIH,iBAAiB,CAAC6B,MAAM,KAAK,CAAC,EAAE;QAElDvB,aAAa,CAAC,IAAI,CAAC;QACnB,IAAI;YACF,MAAML,oBAAoB,CAAC;gBACzB6B,IAAI,EAAE,eAAe;gBACrBC,WAAW,EAAE/B,iBAAiB;gBAC9BG,SAAS,EAAEA;YACb,CAAC,CAAC;YAEFD,cAAc,CAAC,CAAC;YAChBE,YAAY,CAAC,EAAE,CAAC;YAChB4B,KAAK,CAAC,mCAA2D,OAAxBhC,IAAoC,CAAC,YAApB,CAAC6B,MAAM,EAAA;QACnE,CAAC,CAAC,OAAOI,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;YAClDD,KAAK,CAAC,yCAAyC,CAAC;QAClD,CAAC,QAAS;YACR1B,aAAa,CAAC,KAAK,CAAC;QACtB;IACF,CAAC;IAED,MAAM6B,YAAY,GAAGX,eAAe,CAAC,CAAC;IAEtC,qBACE,6LAAC,GAAG;QAAC,SAAS,EAAC,WAAW;;0BAExB,0TAAC,OAAI;;kCACH,0TAAC,aAAU;gDACT,0TAAC,YAAS;4BAAC,SAAS,EAAC,yBAAyB;;8CAC5C,qYAAC,QAAK;oCAAC,SAAS,EAAC,SAAS;;;;;;gCAAA;gCACLxB,iBAAiB,CAAC6B,MAAM;gCAAC;;;;;;;;;;;;kCAGlD,6LAAC,2IAAW;kCACT7B,iBAAiB,CAAC6B,MAAM,KAAK,CAAC,iBAC7B,6LAAC,GAAG;4BAAC,SAAS,EAAC,kBAAkB;;8CAC/B,qZAAC,cAAW;oCAAC,SAAS,EAAC,8CAA8C;;;;;;8CACrE,6LAAC,CAAC;oCAAC,SAAS,EAAC,uBAAuB;8CAAA;;;;;;;;;;;iDAKtC,6LAAC,GAAG;4BAAC,SAAS,EAAC,WAAW;;8CAExB,6LAAC,GAAG;;sDACF,6LAAC,EAAE;4CAAC,SAAS,EAAC,kBAAkB;sDAAC,4BAA4B,EAAE,EAAE;;;;;;sDACjE,6LAAC,GAAG;4CAAC,SAAS,EAAC,sBAAsB;sDAClCO,MAAM,CAACC,OAAO,CAACF,YAAY,CAAC,CAACG,GAAG,CAAC;oDAAC,CAACrB,QAAM,EAAEsB,KAAK,CAAC;qEAChD,6LAAC,GAAG,CAAC,GAAG,CAAC;oDAAS,SAAS,EAAC,yBAAyB;;wDAClDvB,cAAc,CAACC,QAA4B,CAAC;sEAC7C,6LAAC,IAAI;4DAAC,SAAS,EAAC,+BAA+B;;gEAAC,CAAC;gEAACsB,KAAK;gEAAC,CAAC,EAAE,IAAI;;;;;;;;mDAFvDtB,QAAM,CAAC;;;;;;;;;;;;;;;;;8CASvB,6LAAC,GAAG;oCAAC,SAAS,EAAC,qCAAqC;8CACjDV,oBAAoB,CAAC+B,GAAG,EAAEE,QAAQ,iBACjC,6LAAC,GAAG,CAAC,GAAG,CAAC;4CAAc,SAAS,EAAC,wDAAwD;;8DACvF,6LAAC,GAAG;;sEACF,6LAAC,GAAG;4DAAC,SAAS,EAAC,aAAa,CAAC;sEAACA,QAAQ,CAACC,IAAI,CAAC,EAAE,GAAG;;;;;;sEACjD,6LAAC,GAAG;4DAAC,SAAS,EAAC,+BAA+B,CAAC;sEAACD,QAAQ,CAACE,KAAK,CAAC,EAAE,GAAG;;;;;;;;;;;;gDAErE1B,cAAc,CAACwB,QAAQ,CAACvB,MAAM,CAAC;;2CALxBuB,QAAQ,CAAC7B,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;YAejCX,iBAAiB,CAAC6B,MAAM,GAAG,CAAC,kBAC3B,6LAAC,oIAAI;;kCACH,0TAAC,aAAU;gDACT,6LAAC,yIAAS;4BAAC,SAAS,EAAC,yBAAyB;;8CAC5C,iZAAC,YAAS;oCAAC,SAAS,EAAC,SAAS;;;;;;gCAAA;;;;;;;;;;;;kCAIlC,0TAAC,cAAW;gDACV,6LAAC,GAAG;4BAAC,SAAS,EAAC,WAAW;;8CACxB,6LAAC,GAAG;oCAAC,SAAS,EAAC,WAAW;;sDACxB,2TAAC,QAAK;sDAAC,UAAU,EAAE,KAAK;;;;;;sDACxB,4TAAC,SAAM;4CACL,KAAK,CAAC,CAAC1B,SAAS,CAAC;4CACjB,aAAa,CAAC,EAAEU,KAAK,GAAKT,YAAY,CAACS,KAA2B,CAAC,CAAC;;8DAEpE,4TAAC,gBAAa;4EACZ,4TAAC,cAAW;wDAAC,WAAW,EAAC,mBAAmB;;;;;;;;;;;8DAE9C,4TAAC,gBAAa;8DACXD,aAAa,CAAC0B,GAAG,EAAEK,MAAM,iBACxB,4TAAC,aAAU,CAAC,GAAG,CAAC;4DAAe,KAAK,CAAC,CAACA,MAAM,CAAC9B,KAAK,CAAC;oFACjD,6LAAC,GAAG;;kFACF,6LAAC,GAAG;wEAAC,SAAS,EAAC,aAAa,CAAC;kFAAC8B,MAAM,CAAC7B,KAAK,CAAC,EAAE,GAAG;;;;;;kFAChD,6LAAC,GAAG;wEAAC,SAAS,EAAC,+BAA+B,CAAC;kFAAC6B,MAAM,CAAC5B,WAAW,CAAC,EAAE,GAAG;;;;;;;;;;;;2DAH3D4B,MAAM,CAAC9B,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;gCAYrCV,SAAS,kBACR,6LAAC,oIAAI;oCAAC,SAAS,EAAC,4BAA4B;4DAC1C,0TAAC,cAAW;wCAAC,SAAS,EAAC,MAAM;;0DAC3B,6LAAC,GAAG;gDAAC,SAAS,EAAC,8BAA8B;;kEAC3C,iZAAC,YAAS;wDAAC,SAAS,EAAC,uBAAuB;;;;;;kEAC5C,6LAAC,IAAI;wDAAC,SAAS,EAAC,2BAA2B;kEAAC,eAAe,EAAE,IAAI;;;;;;;;;;;;0DAEnE,6LAAC,GAAG;gDAAC,SAAS,EAAC,uBAAuB;;oDACnCH,iBAAiB,CAAC6B,MAAM;oDAAC,+BAA+B;oDAACb,cAAc,CAACb,SAA+B,CAAC;;;;;;;;;;;;;;;;;;8CAMjH,6LAAC,wIAAM;oCACL,OAAO,CAAC,CAACyB,kBAAkB,CAAC;oCAC5B,QAAQ,CAAC,CAAC,CAACzB,SAAS,IAAIE,UAAU,CAAC;oCACnC,SAAS,EAAC,QAAQ;8CAEjBA,UAAU,GAAG,aAAa,GAAG,qBAA6C,OAAxBL,IAAoC,aAAnB,CAAC6B,MAAM,EAAA;;;;;;;;;;;;;;;;;;;;;;;0BAQrF,yTAAC,QAAI;gBAAC,SAAS,EAAC,4BAA4B;;kCAC1C,0TAAC,aAAU;gDACT,0TAAC,YAAS;4BAAC,SAAS,EAAC,eAAe;sCAAC,mBAAmB,EAAE,SAAS;;;;;;;;;;;kCAErE,0TAAC,cAAW;gDACV,6LAAC,GAAG;4BAAC,SAAS,EAAC,iCAAiC;sCAC7CjB,aAAa,CAAC0B,GAAG,EAAEK,QAAM,iBACxB,6LAAC,GAAG,CAAC,GAAG,CAAC;oCAAe,SAAS,EAAC,yBAAyB;;wCACxD3B,cAAc,CAAC2B,QAAM,CAAC9B,KAAK,CAAC;sDAC7B,6LAAC,IAAI,CAAC;sDAAC8B,QAAM,CAAC5B,WAAW,CAAC,EAAE,IAAI;;;;;;;mCAFxB4B,QAAM,CAAC9B,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;0BAU/B,0TAAC,OAAI;gBAAC,SAAS,EAAC,4BAA4B;;kCAC1C,0TAAC,aAAU;gDACT,0TAAC,YAAS;4BAAC,SAAS,EAAC,eAAe;sCAAC,0BAA0B,EAAE,SAAS;;;;;;;;;;;kCAE5E,0TAAC,cAAW;gDACV,6LAAC,GAAG;4BAAC,SAAS,EAAC,iCAAiC;;8CAC9C,6LAAC,CAAC;;wCAAC;sDAAa,6LAAC,MAAM;sDAAC,SAAS,EAAE,MAAM;;;;;;wCAAC,8DAA8D,EAAE,CAAC;;;;;;;8CAC3G,6LAAC,CAAC;8CAAC,gDAAgD,EAAE,CAAC;;;;;;8CACtD,6LAAC,CAAC;8CAAC,4CAA4C,EAAE,CAAC;;;;;;8CAClD,6LAAC,CAAC;8CAAC,uEAAuE,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzF;;;uIA9MmD/B,aAAU,CAAC,CAAC", "ignoreList": [], "debugId": null}}, {"offset": {"line": 3445, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/lib/sample-data.ts"], "sourcesContent": ["import { Department, Employee } from './types'\n\nexport const sampleDepartments: Department[] = [\n  {\n    id: 'dept-001',\n    name: 'Engineering',\n    code: 'ENG',\n    capacity: 25,\n    createdAt: new Date('2024-01-15'),\n    updatedAt: new Date('2024-01-15'),\n  },\n  {\n    id: 'dept-002',\n    name: 'Marketing',\n    code: 'MKT',\n    capacity: 15,\n    createdAt: new Date('2024-01-16'),\n    updatedAt: new Date('2024-01-16'),\n  },\n  {\n    id: 'dept-003',\n    name: 'Sales',\n    code: 'SAL',\n    capacity: 20,\n    createdAt: new Date('2024-01-17'),\n    updatedAt: new Date('2024-01-17'),\n  },\n  {\n    id: 'dept-004',\n    name: 'Human Resources',\n    code: 'HR',\n    capacity: 8,\n    createdAt: new Date('2024-01-18'),\n    updatedAt: new Date('2024-01-18'),\n  },\n  {\n    id: 'dept-005',\n    name: 'Finance',\n    code: 'FIN',\n    capacity: 12,\n    createdAt: new Date('2024-01-19'),\n    updatedAt: new Date('2024-01-19'),\n  },\n  {\n    id: 'dept-006',\n    name: 'Operations',\n    code: 'OPS',\n    capacity: 18,\n    createdAt: new Date('2024-01-20'),\n    updatedAt: new Date('2024-01-20'),\n  },\n]\n\nexport const sampleEmployees: Employee[] = [\n  // Engineering Department\n  {\n    id: 'ENG-001',\n    name: 'John Smith',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-03-15'),\n    transferHistory: [],\n    createdAt: new Date('2023-03-15'),\n    updatedAt: new Date('2023-03-15'),\n  },\n  {\n    id: 'ENG-002',\n    name: 'Sarah Johnson',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-05-20'),\n    transferHistory: [],\n    createdAt: new Date('2023-05-20'),\n    updatedAt: new Date('2023-05-20'),\n  },\n  {\n    id: 'ENG-003',\n    name: 'Michael Chen',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-07-10'),\n    transferHistory: [],\n    createdAt: new Date('2023-07-10'),\n    updatedAt: new Date('2023-07-10'),\n  },\n  {\n    id: 'ENG-004',\n    name: 'Emily Davis',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-09-05'),\n    transferHistory: [],\n    createdAt: new Date('2023-09-05'),\n    updatedAt: new Date('2023-09-05'),\n  },\n  {\n    id: 'ENG-005',\n    name: 'David Wilson',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-11-12'),\n    transferHistory: [],\n    createdAt: new Date('2023-11-12'),\n    updatedAt: new Date('2023-11-12'),\n  },\n  {\n    id: 'ENG-006',\n    name: 'Lisa Anderson',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-01-08'),\n    transferHistory: [],\n    createdAt: new Date('2024-01-08'),\n    updatedAt: new Date('2024-01-08'),\n  },\n  {\n    id: 'ENG-007',\n    name: 'Robert Taylor',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-02-14'),\n    transferHistory: [],\n    createdAt: new Date('2024-02-14'),\n    updatedAt: new Date('2024-02-14'),\n  },\n  {\n    id: 'ENG-008',\n    name: 'Jennifer Brown',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-03-22'),\n    transferHistory: [],\n    createdAt: new Date('2024-03-22'),\n    updatedAt: new Date('2024-03-22'),\n  },\n  {\n    id: 'ENG-009',\n    name: 'Christopher Lee',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-04-18'),\n    transferHistory: [],\n    createdAt: new Date('2024-04-18'),\n    updatedAt: new Date('2024-04-18'),\n  },\n  {\n    id: 'ENG-010',\n    name: 'Amanda White',\n    email: '<EMAIL>',\n    departmentId: 'dept-001',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-05-25'),\n    transferHistory: [],\n    createdAt: new Date('2024-05-25'),\n    updatedAt: new Date('2024-05-25'),\n  },\n\n  // Marketing Department\n  {\n    id: 'MKT-001',\n    name: 'Jessica Garcia',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-04-10'),\n    transferHistory: [],\n    createdAt: new Date('2023-04-10'),\n    updatedAt: new Date('2023-04-10'),\n  },\n  {\n    id: 'MKT-002',\n    name: 'Daniel Martinez',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-06-15'),\n    transferHistory: [],\n    createdAt: new Date('2023-06-15'),\n    updatedAt: new Date('2023-06-15'),\n  },\n  {\n    id: 'MKT-003',\n    name: 'Ashley Rodriguez',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-08-20'),\n    transferHistory: [],\n    createdAt: new Date('2023-08-20'),\n    updatedAt: new Date('2023-08-20'),\n  },\n  {\n    id: 'MKT-004',\n    name: 'Matthew Thompson',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-10-12'),\n    transferHistory: [],\n    createdAt: new Date('2023-10-12'),\n    updatedAt: new Date('2023-10-12'),\n  },\n  {\n    id: 'MKT-005',\n    name: 'Stephanie Clark',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-01-30'),\n    transferHistory: [],\n    createdAt: new Date('2024-01-30'),\n    updatedAt: new Date('2024-01-30'),\n  },\n  {\n    id: 'MKT-006',\n    name: 'Kevin Lewis',\n    email: '<EMAIL>',\n    departmentId: 'dept-002',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-03-15'),\n    transferHistory: [],\n    createdAt: new Date('2024-03-15'),\n    updatedAt: new Date('2024-03-15'),\n  },\n\n  // Sales Department\n  {\n    id: 'SAL-001',\n    name: 'Ryan Walker',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-02-28'),\n    transferHistory: [],\n    createdAt: new Date('2023-02-28'),\n    updatedAt: new Date('2023-02-28'),\n  },\n  {\n    id: 'SAL-002',\n    name: 'Nicole Hall',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-05-10'),\n    transferHistory: [],\n    createdAt: new Date('2023-05-10'),\n    updatedAt: new Date('2023-05-10'),\n  },\n  {\n    id: 'SAL-003',\n    name: 'Brandon Allen',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-07-25'),\n    transferHistory: [],\n    createdAt: new Date('2023-07-25'),\n    updatedAt: new Date('2023-07-25'),\n  },\n  {\n    id: 'SAL-004',\n    name: 'Megan Young',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-09-18'),\n    transferHistory: [],\n    createdAt: new Date('2023-09-18'),\n    updatedAt: new Date('2023-09-18'),\n  },\n  {\n    id: 'SAL-005',\n    name: 'Tyler King',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-11-30'),\n    transferHistory: [],\n    createdAt: new Date('2023-11-30'),\n    updatedAt: new Date('2023-11-30'),\n  },\n  {\n    id: 'SAL-006',\n    name: 'Rachel Wright',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-02-05'),\n    transferHistory: [],\n    createdAt: new Date('2024-02-05'),\n    updatedAt: new Date('2024-02-05'),\n  },\n  {\n    id: 'SAL-007',\n    name: 'Justin Lopez',\n    email: '<EMAIL>',\n    departmentId: 'dept-003',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-04-12'),\n    transferHistory: [],\n    createdAt: new Date('2024-04-12'),\n    updatedAt: new Date('2024-04-12'),\n  },\n\n  // HR Department\n  {\n    id: 'HR-001',\n    name: 'Laura Hill',\n    email: '<EMAIL>',\n    departmentId: 'dept-004',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-01-20'),\n    transferHistory: [],\n    createdAt: new Date('2023-01-20'),\n    updatedAt: new Date('2023-01-20'),\n  },\n  {\n    id: 'HR-002',\n    name: 'Steven Green',\n    email: '<EMAIL>',\n    departmentId: 'dept-004',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-06-08'),\n    transferHistory: [],\n    createdAt: new Date('2023-06-08'),\n    updatedAt: new Date('2023-06-08'),\n  },\n  {\n    id: 'HR-003',\n    name: 'Kimberly Adams',\n    email: '<EMAIL>',\n    departmentId: 'dept-004',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-10-25'),\n    transferHistory: [],\n    createdAt: new Date('2023-10-25'),\n    updatedAt: new Date('2023-10-25'),\n  },\n\n  // Finance Department\n  {\n    id: 'FIN-001',\n    name: 'Andrew Baker',\n    email: '<EMAIL>',\n    departmentId: 'dept-005',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-03-08'),\n    transferHistory: [],\n    createdAt: new Date('2023-03-08'),\n    updatedAt: new Date('2023-03-08'),\n  },\n  {\n    id: 'FIN-002',\n    name: 'Michelle Nelson',\n    email: '<EMAIL>',\n    departmentId: 'dept-005',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-07-15'),\n    transferHistory: [],\n    createdAt: new Date('2023-07-15'),\n    updatedAt: new Date('2023-07-15'),\n  },\n  {\n    id: 'FIN-003',\n    name: 'Joshua Carter',\n    email: '<EMAIL>',\n    departmentId: 'dept-005',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-11-20'),\n    transferHistory: [],\n    createdAt: new Date('2023-11-20'),\n    updatedAt: new Date('2023-11-20'),\n  },\n  {\n    id: 'FIN-004',\n    name: 'Samantha Mitchell',\n    email: '<EMAIL>',\n    departmentId: 'dept-005',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-02-28'),\n    transferHistory: [],\n    createdAt: new Date('2024-02-28'),\n    updatedAt: new Date('2024-02-28'),\n  },\n\n  // Operations Department\n  {\n    id: 'OPS-001',\n    name: 'Gregory Perez',\n    email: '<EMAIL>',\n    departmentId: 'dept-006',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-04-05'),\n    transferHistory: [],\n    createdAt: new Date('2023-04-05'),\n    updatedAt: new Date('2023-04-05'),\n  },\n  {\n    id: 'OPS-002',\n    name: 'Heather Roberts',\n    email: '<EMAIL>',\n    departmentId: 'dept-006',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-08-12'),\n    transferHistory: [],\n    createdAt: new Date('2023-08-12'),\n    updatedAt: new Date('2023-08-12'),\n  },\n  {\n    id: 'OPS-003',\n    name: 'Nathan Turner',\n    email: '<EMAIL>',\n    departmentId: 'dept-006',\n    status: 'ACTIVE',\n    hireDate: new Date('2023-12-01'),\n    transferHistory: [],\n    createdAt: new Date('2023-12-01'),\n    updatedAt: new Date('2023-12-01'),\n  },\n  {\n    id: 'OPS-004',\n    name: 'Brittany Phillips',\n    email: '<EMAIL>',\n    departmentId: 'dept-006',\n    status: 'ACTIVE',\n    hireDate: new Date('2024-03-10'),\n    transferHistory: [],\n    createdAt: new Date('2024-03-10'),\n    updatedAt: new Date('2024-03-10'),\n  },\n\n  // Free Bucket (Unassigned) Employees\n  {\n    id: 'FB-001',\n    name: 'Alex Campbell',\n    email: '<EMAIL>',\n    departmentId: null,\n    status: 'ACTIVE',\n    hireDate: new Date('2024-06-01'),\n    transferHistory: [],\n    createdAt: new Date('2024-06-01'),\n    updatedAt: new Date('2024-06-01'),\n  },\n  {\n    id: 'FB-002',\n    name: 'Morgan Parker',\n    email: '<EMAIL>',\n    departmentId: null,\n    status: 'ACTIVE',\n    hireDate: new Date('2024-06-15'),\n    transferHistory: [],\n    createdAt: new Date('2024-06-15'),\n    updatedAt: new Date('2024-06-15'),\n  },\n]\n\n// Helper function to populate departments with their employees\nexport const getDepartmentsWithEmployees = (): Department[] => {\n  return sampleDepartments.map(dept => ({\n    ...dept,\n    employees: sampleEmployees.filter(emp => emp.departmentId === dept.id)\n  }))\n}\n\n// Helper function to get free bucket employees\nexport const getFreeBucketEmployees = (): Employee[] => {\n  return sampleEmployees.filter(emp => emp.departmentId === null)\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,oBAAkC;IAC7C;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAEM,MAAM,kBAA8B;IACzC,yBAAyB;IACzB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,uBAAuB;IACvB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,qBAAqB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,wBAAwB;IACxB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IAEA,qCAAqC;IACrC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,cAAc;QACd,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,iBAAiB,EAAE;QACnB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,8BAA8B;IACzC,OAAO,kBAAkB,GAAG,CAAC,CAAA,OAAQ,CAAC;YACpC,GAAG,IAAI;YACP,WAAW,gBAAgB,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK,KAAK,EAAE;QACvE,CAAC;AACH;AAGO,MAAM,yBAAyB;IACpC,OAAO,gBAAgB,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK;AAC5D", "debugId": null}}, {"offset": {"line": 3924, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/lib/hooks/G%3A/Augment%20code/lib/hooks/use-sample-data.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { useHRStore } from '@/lib/store/hr-store'\nimport { \n  sampleEmployees, \n  getDepartmentsWithEmployees, \n  getFreeBucketEmployees \n} from '@/lib/sample-data'\n\nexport function useSampleData() {\n  const { setEmployees, setDepartments } = useHRStore()\n\n  useEffect(() => {\n    // Initialize with sample data\n    const departmentsWithEmployees = getDepartmentsWithEmployees()\n    const freeBucketEmployees = getFreeBucketEmployees()\n    \n    setEmployees(sampleEmployees)\n    setDepartments(departmentsWithEmployees)\n    \n    // Note: Free bucket employees are automatically calculated in the store\n    // based on employees with departmentId === null\n    \n    console.log('Sample data loaded:', {\n      employees: sampleEmployees.length,\n      departments: departmentsWithEmployees.length,\n      freeBucket: freeBucketEmployees.length\n    })\n  }, [setEmployees, setDepartments])\n}\n"], "names": ["useEffect", "useHRStore", "sampleEmployees", "getDepartmentsWithEmployees", "getFreeBucketEmployees", "useSampleData", "$", "_c", "$i", "Symbol", "for", "setEmployees", "setDepartments", "t0", "t1", "departmentsWithEmployees", "freeBucketEmployees", "console", "log", "employees", "length", "departments", "freeBucket"], "mappings": ";;;;AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SACEC,eAAe,EACfC,2BAA2B,EAC3BC,sBAAsB,QACjB,mBAAmB;;;;;;AAEnB;;IAAA,MAAAE,CAAA,mLAAAC,IAAAA,AAAA,EAAA;IAAA,IAAAD,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAE,EAAA,GAAA,GAAAA,EAAA,GAAA,GAAAA,EAAA,IAAA,EAAA;YAAAF,CAAA,CAAAE,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAJ,CAAA,CAAA,EAAA,GAAA;IAAA;IACL,MAAA,EAAAK,YAAA,EAAAC,cAAAA,EAAA,mJAAyCX;IAAY,IAAAY,EAAA;IAAA,IAAAC,EAAA;IAAA,IAAAR,CAAA,CAAA,EAAA,KAAAM,cAAA,IAAAN,CAAA,CAAA,EAAA,KAAAK,YAAA,EAAA;QAE3CE,EAAA,GAAAA,CAAA;YAER,MAAAE,wBAAA,IAAiCZ,0JAAAA,AAAA,CAA4B,CAAC;YAC9D,MAAAa,mBAAA,gIAA4BZ,yBAAAA,AAAA,CAAuB,CAAC;YAEpDO,YAAY,0HAAAT,kBAAgB,CAAC;YAC7BU,cAAc,CAACG,wBAAwB,CAAC;YAKxCE,OAAA,CAAAC,GAAA,CAAY,qBAAqB,EAAA;gBAAAC,SAAA,2HAAAjB,kBAAA,CAAAkB,MAAA;gBAAAC,WAAA,EAElBN,wBAAwB,CAAAK,MAAA;gBAAAE,UAAA,EACzBN,mBAAmB,CAAAI,MAAAA;YAAA,CAChC,CAAC;QAAA;QACDN,EAAA,GAAA;YAACH,YAAY;YAAEC,cAAc;SAAA;QAACN,CAAA,CAAA,EAAA,GAAAM,cAAA;QAAAN,CAAA,CAAA,EAAA,GAAAK,YAAA;QAAAL,CAAA,CAAA,EAAA,GAAAO,EAAA;QAAAP,CAAA,CAAA,EAAA,GAAAQ,EAAA;IAAA,OAAA;QAAAD,EAAA,GAAAP,CAAA,CAAA,EAAA;QAAAQ,EAAA,GAAAR,CAAA,CAAA,EAAA;IAAA;sKAhBjCN,YAAAA,AAAA,EAAUa,EAgBT,EAAEC,EAA8B,CAAC;AAAA;GAnB7BT,cAAA;;uIACoCJ,aAAA,CAAW,CAAC", "ignoreList": [], "debugId": null}}, {"offset": {"line": 3988, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/app/%28dashboard%29/bulk/G%3A/Augment%20code/app/%28dashboard%29/bulk/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { useSession } from \"next-auth/react\"\nimport { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { CSVImportSection } from \"@/components/bulk/csv-import-section\"\nimport { CSVExportSection } from \"@/components/bulk/csv-export-section\"\nimport { BulkTransferSection } from \"@/components/bulk/bulk-transfer-section\"\nimport { BulkStatusUpdateSection } from \"@/components/bulk/bulk-status-update-section\"\nimport { useHRStore } from \"@/lib/store/hr-store\"\nimport { useSampleData } from \"@/lib/hooks/use-sample-data\"\nimport { \n  Upload, \n  Download, \n  ArrowRight,\n  Users,\n  FileText,\n  AlertCircle\n} from \"lucide-react\"\n\nexport default function BulkOperationsPage() {\n  const { data: session } = useSession()\n  const { employees, departments, selectedEmployees } = useHRStore()\n  const [activeTab, setActiveTab] = useState<'import' | 'export' | 'transfer' | 'status'>('import')\n\n  // Load sample data\n  useSampleData()\n\n  const tabs = [\n    {\n      id: 'import' as const,\n      label: 'Import CSV',\n      icon: Upload,\n      description: 'Import employees from CSV file'\n    },\n    {\n      id: 'export' as const,\n      label: 'Export CSV',\n      icon: Download,\n      description: 'Export employees to CSV file'\n    },\n    {\n      id: 'transfer' as const,\n      label: 'Bulk Transfer',\n      icon: ArrowRight,\n      description: 'Transfer multiple employees between departments'\n    },\n    {\n      id: 'status' as const,\n      label: 'Status Update',\n      icon: Users,\n      description: 'Update status for multiple employees'\n    }\n  ]\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Bulk Operations</h1>\n          <p className=\"text-muted-foreground\">\n            Perform bulk operations on employee data including CSV import/export and mass updates.\n          </p>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid gap-4 md:grid-cols-4\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total Employees</CardTitle>\n            <Users className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{employees.length}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Available for operations\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Selected</CardTitle>\n            <Users className=\"h-4 w-4 text-blue-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{selectedEmployees.length}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Currently selected\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Departments</CardTitle>\n            <FileText className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{departments.length}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Available departments\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Unassigned</CardTitle>\n            <AlertCircle className=\"h-4 w-4 text-orange-600\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">\n              {employees.filter(emp => emp.departmentId === null).length}\n            </div>\n            <p className=\"text-xs text-muted-foreground\">\n              In free bucket\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Tab Navigation */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex space-x-1 rounded-lg bg-muted p-1\">\n            {tabs.map((tab) => {\n              const Icon = tab.icon\n              return (\n                <Button\n                  key={tab.id}\n                  variant={activeTab === tab.id ? \"default\" : \"ghost\"}\n                  size=\"sm\"\n                  onClick={() => setActiveTab(tab.id)}\n                  className=\"flex-1\"\n                >\n                  <Icon className=\"h-4 w-4 mr-2\" />\n                  {tab.label}\n                </Button>\n              )\n            })}\n          </div>\n        </CardHeader>\n\n        <CardContent>\n          <div className=\"mb-4\">\n            <h3 className=\"text-lg font-medium\">\n              {tabs.find(tab => tab.id === activeTab)?.label}\n            </h3>\n            <p className=\"text-sm text-muted-foreground\">\n              {tabs.find(tab => tab.id === activeTab)?.description}\n            </p>\n          </div>\n\n          {/* Tab Content */}\n          {activeTab === 'import' && (\n            <CSVImportSection departments={departments} />\n          )}\n\n          {activeTab === 'export' && (\n            <CSVExportSection \n              employees={employees} \n              departments={departments} \n            />\n          )}\n\n          {activeTab === 'transfer' && (\n            <BulkTransferSection \n              employees={employees}\n              departments={departments}\n              selectedEmployees={selectedEmployees}\n            />\n          )}\n\n          {activeTab === 'status' && (\n            <BulkStatusUpdateSection \n              employees={employees}\n              selectedEmployees={selectedEmployees}\n            />\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Help Section */}\n      <Card className=\"border-blue-200 bg-blue-50\">\n        <CardHeader>\n          <CardTitle className=\"text-blue-800 flex items-center gap-2\">\n            <AlertCircle className=\"h-5 w-5\" />\n            Bulk Operations Guidelines\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-2 text-sm text-blue-700\">\n            <p><strong>CSV Import:</strong> Upload a CSV file with employee data. Required columns: name, email. Optional: departmentCode, status.</p>\n            <p><strong>CSV Export:</strong> Download employee data in CSV format with filtering options.</p>\n            <p><strong>Bulk Transfer:</strong> Move multiple employees between departments. Select employees from the Employees page first.</p>\n            <p><strong>Status Update:</strong> Change the status of multiple employees at once (Active, Transferred, Archived, etc.).</p>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": ["c", "_c", "useState", "useSession", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON>", "CSVImportSection", "CSVExportSection", "BulkTransferSection", "BulkStatusUpdateSection", "useHRStore", "useSampleData", "Upload", "Download", "ArrowRight", "Users", "FileText", "AlertCircle", "BulkOperationsPage", "$", "$i", "Symbol", "for", "employees", "departments", "selectedEmployees", "activeTab", "setActiveTab", "t0", "id", "label", "icon", "description", "t1", "t2", "t3", "tabs", "t4", "t5", "t6", "length", "t7", "t8", "t9", "t10", "t11", "t12", "t13", "t14", "t15", "t16", "t17", "t18", "filter", "_temp", "t19", "t20", "t21", "t22", "t23", "map", "tab", "Icon", "t24", "find", "tab_0", "t25", "t26", "tab_1", "t27", "t28", "t29", "t30", "t31", "t32", "t33", "t34", "t35", "t36", "t37", "emp", "departmentId"], "mappings": ";;;;AAAY,SAAAA,CAAA,IAAAC,EAAA;AAEZ,SAASC,QAAQ,QAAQ,OAAO;AAChC,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,sBAAsB;AAC/E,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,mBAAmB,QAAQ,yCAAyC;AAC7E,SAASC,uBAAuB,QAAQ,8CAA8C;AACtF,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,aAAa,QAAQ,6BAA6B;;;;;;AAC3D,SACEC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,KAAK,EACLC,QAAQ,EACRC,WAAW,QACN,cAAc;;;AAnBrB,YAAY;;;;;;;;;;;;;AAqBG;;;IAAA,MAAAE,CAAA,mLAAArB,IAAAA,AAAA,EAAA;IAAA,IAAAqB,CAAA,CAAA,EAAA,KAAA,oEAAA;QAAA,IAAA,IAAAC,EAAA,GAAA,GAAAA,EAAA,GAAA,IAAAA,EAAA,IAAA,EAAA;YAAAD,CAAA,CAAAC,EAAA,CAAA,GAAAC,MAAA,CAAAC,GAAA,CAAA;QAAA;QAAAH,CAAA,CAAA,EAAA,GAAA;IAAA;uKACanB;IAC1B,MAAA,EAAAuB,SAAA,EAAAC,WAAA,EAAAC,iBAAAA,EAAA,mJAAsDf;IACtD,MAAA,CAAAgB,SAAA,EAAAC,YAAA,CAAA,IAAkC5B,4KAAA,AAAAA,EAAsD,QAAQ,CAAC;iKAGjGY;IAAe,IAAAiB,EAAA;IAAA,IAAAT,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAGbM,EAAA,GAAA;YAAAC,EAAA,EACM,QAAQ;YAAAC,KAAA,EACL,YAAY;YAAAC,IAAA,4MAAAnB,SAAA;YAAAoB,WAAA,EAEN;QAAgC;QAC9Cb,CAAA,CAAA,EAAA,GAAAS,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAT,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAc,EAAA;IAAA,IAAAd,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QACDW,EAAA,GAAA;YAAAJ,EAAA,EACM,QAAQ;YAAAC,KAAA,EACL,YAAY;YAAAC,IAAA,gNAAAlB,WAAA;YAAAmB,WAAA,EAEN;QAA8B;QAC5Cb,CAAA,CAAA,EAAA,GAAAc,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAd,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAe,EAAA;IAAA,IAAAf,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QACDY,EAAA,GAAA;YAAAL,EAAA,EACM,UAAU;YAAAC,KAAA,EACP,eAAe;YAAAC,IAAA,wNAAAjB,aAAA;YAAAkB,WAAA,EAET;QAAiD;QAC/Db,CAAA,CAAA,EAAA,GAAAe,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAf,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAgB,EAAA;IAAA,IAAAhB,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAlBUa,EAAA,GAAA;YACXP,EAKC;YACDK,EAKC;YACDC,EAKC;YAAA;gBAAAL,EAAA,EAEK,QAAQ;gBAAAC,KAAA,EACL,eAAe;gBAAAC,IAAA,0MAAAhB,QAAA;gBAAAiB,WAAA,EAET;YAAsC;SAAA;QAEtDb,CAAA,CAAA,EAAA,GAAAgB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAhB,CAAA,CAAA,EAAA;IAAA;IAzBD,MAAAiB,IAAA,GAAaD,EAyBZ;IAAA,IAAAE,EAAA;IAAA,IAAAlB,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAKGe,EAAA,iBAAA,6LAAA,GAOM;YAPS,SAAmC,EAAnC,mCAAmC;oCAChD,6LAAA,GAKM;;kCAJJ,6LAAA,EAAsE;wBAAxD,SAAmC,EAAnC,mCAAmC;kCAAC,eAAe,EAAjE,EAAsE;;;;;;kCACtE,6LAAA,CAEI;wBAFS,SAAuB,EAAvB,uBAAuB;kCAAC,sFAErC,EAFA,CAEI,CACN,EALA,GAKM,CACR,EAPA,GAOM;;;;;;;;;;;;;;;;;QAAAlB,CAAA,CAAA,EAAA,GAAAkB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAlB,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAmB,EAAA;IAAA,IAAAnB,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAKFgB,EAAA,iBAAA,0TAAC,aAAU;YAAW,SAA2D,EAA3D,2DAA2D;;8BAC/E,0TAAC,YAAS;oBAAW,SAAqB,EAArB,qBAAqB;8BAAC,eAAe,EAAzD,SAAS;;;;;;8BACV,6LAAC,gNAAK;oBAAW,SAA+B,EAA/B,+BAA+B,GAClD,EAHC,UAAU,CAGE;;;;;;;;;;;;QAAAnB,CAAA,CAAA,EAAA,GAAAmB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAnB,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAoB,EAAA;IAAA,IAAApB,CAAA,CAAA,EAAA,KAAAI,SAAA,CAAAiB,MAAA,EAAA;QAEXD,EAAA,iBAAA,6LAAA,GAA4D;YAA7C,SAAoB,EAApB,oBAAoB,CAAE;sBAAAhB,SAAS,CAAAiB,MAAM,CAAE,EAAtD,GAA4D;;;;;;QAAArB,CAAA,CAAA,EAAA,GAAAI,SAAA,CAAAiB,MAAA;QAAArB,CAAA,CAAA,EAAA,GAAAoB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAApB,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAsB,EAAA;IAAA,IAAAtB,CAAA,CAAA,EAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAC5DmB,EAAA,iBAAA,6LAAA,CAEI;YAFS,SAA+B,EAA/B,+BAA+B;sBAAC,wBAE7C,EAFA,CAEI;;;;;;QAAAtB,CAAA,CAAA,EAAA,GAAAsB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAtB,CAAA,CAAA,EAAA;IAAA;IAAA,IAAAuB,EAAA;IAAA,IAAAvB,CAAA,CAAA,GAAA,KAAAoB,EAAA,EAAA;QATRG,EAAA,iBAAA,0TAAC,OAAI,CACH;;gBAAAJ,EAGY;8BACZ,0TAAC,cAAW,CACV;;wBAAAC,EAA2D,CAC3D;wBAAAE,EAEG,CACL,EALC,WAAW,CAMd,EAXC,IAAI,CAWE;;;;;;;;;;;;;QAAAtB,CAAA,CAAA,GAAA,GAAAoB,EAAA;QAAApB,CAAA,CAAA,GAAA,GAAAuB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAvB,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAwB,EAAA;IAAA,IAAAxB,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAGLqB,EAAA,iBAAA,0TAAC,aAAU;YAAW,SAA2D,EAA3D,2DAA2D;;8BAC/E,6LAAC,yIAAS;oBAAW,SAAqB,EAArB,qBAAqB;8BAAC,QAAQ,EAAlD,SAAS;;;;;;8BACV,qYAAC,QAAK;oBAAW,SAAuB,EAAvB,uBAAuB,GAC1C,EAHC,UAAU,CAGE;;;;;;;;;;;;QAAAxB,CAAA,CAAA,GAAA,GAAAwB,EAAA;IAAA,OAAA;QAAAA,EAAA,GAAAxB,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAyB,GAAA;IAAA,IAAAzB,CAAA,CAAA,GAAA,KAAAM,iBAAA,CAAAe,MAAA,EAAA;QAEXI,GAAA,iBAAA,6LAAA,GAAoE;YAArD,SAAoB,EAApB,oBAAoB,CAAE;sBAAAnB,iBAAiB,CAAAe,MAAM,CAAE,EAA9D,GAAoE;;;;;;QAAArB,CAAA,CAAA,GAAA,GAAAM,iBAAA,CAAAe,MAAA;QAAArB,CAAA,CAAA,GAAA,GAAAyB,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAzB,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA0B,GAAA;IAAA,IAAA1B,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QACpEuB,GAAA,iBAAA,6LAAA,CAEI;YAFS,SAA+B,EAA/B,+BAA+B;sBAAC,kBAE7C,EAFA,CAEI;;;;;;QAAA1B,CAAA,CAAA,GAAA,GAAA0B,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA1B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA2B,GAAA;IAAA,IAAA3B,CAAA,CAAA,GAAA,KAAAyB,GAAA,EAAA;QATRE,GAAA,iBAAA,0TAAC,OAAI,CACH;;gBAAAH,EAGY;8BACZ,0TAAC,cAAW,CACV;;wBAAAC,GAAmE,CACnE;wBAAAC,GAEG,CACL,EALC,WAAW,CAMd,EAXC,IAAI,CAWE;;;;;;;;;;;;;QAAA1B,CAAA,CAAA,GAAA,GAAAyB,GAAA;QAAAzB,CAAA,CAAA,GAAA,GAAA2B,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA3B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA4B,GAAA;IAAA,IAAA5B,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAGLyB,GAAA,iBAAA,0TAAC,aAAU;YAAW,SAA2D,EAA3D,2DAA2D;;8BAC/E,0TAAC,YAAS;oBAAW,SAAqB,EAArB,qBAAqB;8BAAC,WAAW,EAArD,SAAS;;;;;;8BACV,6LAAC,6NAAQ;oBAAW,SAA+B,EAA/B,+BAA+B,GACrD,EAHC,UAAU,CAGE;;;;;;;;;;;;QAAA5B,CAAA,CAAA,GAAA,GAAA4B,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA5B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA6B,GAAA;IAAA,IAAA7B,CAAA,CAAA,GAAA,KAAAK,WAAA,CAAAgB,MAAA,EAAA;QAEXQ,GAAA,iBAAA,6LAAA,GAA8D;YAA/C,SAAoB,EAApB,oBAAoB,CAAE;sBAAAxB,WAAW,CAAAgB,MAAM,CAAE,EAAxD,GAA8D;;;;;;QAAArB,CAAA,CAAA,GAAA,GAAAK,WAAA,CAAAgB,MAAA;QAAArB,CAAA,CAAA,GAAA,GAAA6B,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA7B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA8B,GAAA;IAAA,IAAA9B,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAC9D2B,GAAA,iBAAA,6LAAA,CAEI;YAFS,SAA+B,EAA/B,+BAA+B;sBAAC,qBAE7C,EAFA,CAEI;;;;;;QAAA9B,CAAA,CAAA,GAAA,GAAA8B,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA9B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA+B,GAAA;IAAA,IAAA/B,CAAA,CAAA,GAAA,KAAA6B,GAAA,EAAA;QATRE,GAAA,iBAAA,0TAAC,OAAI,CACH;;gBAAAH,GAGY;8BACZ,0TAAC,cAAW,CACV;;wBAAAC,GAA6D,CAC7D;wBAAAC,GAEG,CACL,EALC,WAAW,CAMd,EAXC,IAAI,CAWE;;;;;;;;;;;;;QAAA9B,CAAA,CAAA,GAAA,GAAA6B,GAAA;QAAA7B,CAAA,CAAA,GAAA,GAAA+B,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA/B,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAgC,GAAA;IAAA,IAAAhC,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAGL6B,GAAA,iBAAA,yTAAC,cAAU;YAAW,SAA2D,EAA3D,2DAA2D;;8BAC/E,0TAAC,YAAS;oBAAW,SAAqB,EAArB,qBAAqB;8BAAC,UAAU,EAApD,SAAS;;;;;;8BACV,qZAAC,cAAW;oBAAW,SAAyB,EAAzB,yBAAyB,GAClD,EAHC,UAAU,CAGE;;;;;;;;;;;;QAAAhC,CAAA,CAAA,GAAA,GAAAgC,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAhC,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAiC,GAAA;IAAA,IAAAjC,CAAA,CAAA,GAAA,KAAAI,SAAA,EAAA;QAGR6B,GAAA,GAAA7B,SAAS,CAAA8B,MAAA,CAAAC,KAAwC,CAAC;QAAAnC,CAAA,CAAA,GAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,GAAA,GAAAiC,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAjC,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAoC,GAAA;IAAA,IAAApC,CAAA,CAAA,GAAA,KAAAiC,GAAA,CAAAZ,MAAA,EAAA;QADrDe,GAAA,iBAAA,6LAAA,GAEM;YAFS,SAAoB,EAApB,oBAAoB,CAChC;sBAAAH,GAAkD,CAAAZ,MAAM,CAC3D,EAFA,GAEM;;;;;;QAAArB,CAAA,CAAA,GAAA,GAAAiC,GAAA,CAAAZ,MAAA;QAAArB,CAAA,CAAA,GAAA,GAAAoC,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAApC,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAqC,GAAA;IAAA,IAAArC,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QACNkC,GAAA,iBAAA,6LAAA,CAEI;YAFS,SAA+B,EAA/B,+BAA+B;sBAAC,cAE7C,EAFA,CAEI;;;;;;QAAArC,CAAA,CAAA,GAAA,GAAAqC,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAArC,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAsC,GAAA;IAAA,IAAAtC,CAAA,CAAA,GAAA,KAAAoC,GAAA,EAAA;QAXRE,GAAA,iBAAA,0TAAC,OAAI,CACH;;gBAAAN,GAGY;8BACZ,0TAAC,cAAW,CACV;;wBAAAI,GAEK,CACL;wBAAAC,GAEG,CACL,EAPC,WAAW,CAQd,EAbC,IAAI,CAaE;;;;;;;;;;;;;QAAArC,CAAA,CAAA,GAAA,GAAAoC,GAAA;QAAApC,CAAA,CAAA,GAAA,GAAAsC,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAtC,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAuC,GAAA;IAAA,IAAAvC,CAAA,CAAA,GAAA,KAAA2B,GAAA,IAAA3B,CAAA,CAAA,GAAA,KAAA+B,GAAA,IAAA/B,CAAA,CAAA,GAAA,KAAAsC,GAAA,IAAAtC,CAAA,CAAA,GAAA,KAAAuB,EAAA,EAAA;QArDTgB,GAAA,iBAAA,6LAAA,GAsDM;YAtDS,SAA2B,EAA3B,2BAA2B,CACxC;;gBAAAhB,EAWM,CAEN;gBAAAI,GAWM,CAEN;gBAAAI,GAWM,CAEN;gBAAAO,GAaM,CACR,EAtDA,GAsDM;;;;;;;QAAAtC,CAAA,CAAA,GAAA,GAAA2B,GAAA;QAAA3B,CAAA,CAAA,GAAA,GAAA+B,GAAA;QAAA/B,CAAA,CAAA,GAAA,GAAAsC,GAAA;QAAAtC,CAAA,CAAA,GAAA,GAAAuB,EAAA;QAAAvB,CAAA,CAAA,GAAA,GAAAuC,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAvC,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAwC,GAAA;IAAA,IAAAxC,CAAA,CAAA,GAAA,KAAAO,SAAA,EAAA;QAIJiC,GAAA,iBAAA,0TAAC,aAAU;oCACT,6LAAA,GAgBM;gBAhBS,SAAwC,EAAxC,wCAAwC,CACpD;0BAAAvB,IAAI,CAAAwB,GAAA,EAAAC,GAAA;oBACH,MAAAC,IAAA,GAAaD,GAAG,CAAA9B,IAAA;oBAAK,qBAEnB,4TAAC,SAAM,CACA,GAAM,CAAN;wBACI,OAA0C,CAA1C,CAAAL,SAAS,KAAKmC,GAAG,CAAAhC,EAAG,GAAG,SAAS,GAAG,OAAM,CAAC;wBAC9C,IAAI,EAAJ,IAAI;wBACA,OAA0B,CAA1B,CAAA,IAAMF,YAAY,CAACkC,GAAG,CAAAhC,EAAG,EAAC;wBACzB,SAAQ,EAAR,QAAQ;;0CAElB,6LAAC,IAAI;gCAAW,SAAc,EAAd,cAAc,GAC7B;;;;;;4BAAAgC,GAAG,CAAA/B,KAAK,CACX,EATC,MAAM,CASE;;uBARF+B,GAAG,CAAAhC,EAAE,CAAC;;;;;gBAQJ,CAEZ,EACH,EAhBA,GAgBM,CACR,EAlBC,UAAU,CAkBE;;;;;;;;;;;QAAAV,CAAA,CAAA,GAAA,GAAAO,SAAA;QAAAP,CAAA,CAAA,GAAA,GAAAwC,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAxC,CAAA,CAAA,GAAA;IAAA;IAKN,MAAA4C,GAAA,sBAAIC,IAAA,EAAAC,KAAA,GAAaJ,KAAG,CAAAhC,EAAA,KAAQH,SAAS,CAAC,+CAAtCU,IAAI,OAAkCN,KAAA;IAAO,IAAAoC,GAAA;IAAA,IAAA/C,CAAA,CAAA,GAAA,KAAA4C,GAAA,EAAA;QADhDG,GAAA,iBAAA,6LAAA,EAEK;YAFS,SAAqB,EAArB,qBAAqB,CAChC;sBAAAH,GAA4C,CAC/C,EAFA,EAEK;;;;;;QAAA5C,CAAA,CAAA,GAAA,GAAA4C,GAAA;QAAA5C,CAAA,CAAA,GAAA,GAAA+C,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA/C,CAAA,CAAA,GAAA;IAAA;IAEF,MAAAgD,GAAA,kBAAA/B,IAAI,CAAA4B,IAAA,EAAAI,KAAA,GAAaP,KAAG,CAAAhC,EAAA,KAAQH,SAAS,CAAC,4DAAAM,WAAA;IAAa,IAAAqC,GAAA;IAAA,IAAAlD,CAAA,CAAA,GAAA,KAAAgD,GAAA,EAAA;QADtDE,GAAA,iBAAA,6LAAA,CAEI;YAFS,SAA+B,EAA/B,+BAA+B,CACzC;sBAAAF,GAAkD,CACrD,EAFA,CAEI;;;;;;QAAAhD,CAAA,CAAA,GAAA,GAAAgD,GAAA;QAAAhD,CAAA,CAAA,GAAA,GAAAkD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAlD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAmD,GAAA;IAAA,IAAAnD,CAAA,CAAA,GAAA,KAAA+C,GAAA,IAAA/C,CAAA,CAAA,GAAA,KAAAkD,GAAA,EAAA;QANNC,GAAA,iBAAA,6LAAA,GAOM;YAPS,SAAM,EAAN,MAAM,CACnB;;gBAAAJ,GAEI,CACJ;gBAAAG,GAEG,CACL,EAPA,GAOM;;;;;;;QAAAlD,CAAA,CAAA,GAAA,GAAA+C,GAAA;QAAA/C,CAAA,CAAA,GAAA,GAAAkD,GAAA;QAAAlD,CAAA,CAAA,GAAA,GAAAmD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAnD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAoD,GAAA;IAAA,IAAApD,CAAA,CAAA,GAAA,KAAAO,SAAA,IAAAP,CAAA,CAAA,GAAA,KAAAK,WAAA,EAAA;QAGL+C,GAAA,GAAA7C,SAAS,KAAK,QAAQ,kBACrB,6LAAC,sKAAgB;YAAcF,WAAW,CAAXA,CAAAA,WAAU,CAAC,GAC3C;;;;;;QAAAL,CAAA,CAAA,GAAA,GAAAO,SAAA;QAAAP,CAAA,CAAA,GAAA,GAAAK,WAAA;QAAAL,CAAA,CAAA,GAAA,GAAAoD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAApD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAqD,GAAA;IAAA,IAAArD,CAAA,CAAA,GAAA,KAAAO,SAAA,IAAAP,CAAA,CAAA,GAAA,KAAAK,WAAA,IAAAL,CAAA,CAAA,GAAA,KAAAI,SAAA,EAAA;QAEAiD,GAAA,GAAA9C,SAAS,KAAK,QAAQ,kBACrB,gVAAC,mBAAgB;YACJH,SAAS,CAATA,CAAAA,SAAQ,CAAC;YACPC,WAAW,CAAXA,CAAAA,WAAU,CAAC,GAE3B;;;;;;QAAAL,CAAA,CAAA,GAAA,GAAAO,SAAA;QAAAP,CAAA,CAAA,GAAA,GAAAK,WAAA;QAAAL,CAAA,CAAA,GAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,GAAA,GAAAqD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAArD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAsD,GAAA;IAAA,IAAAtD,CAAA,CAAA,GAAA,KAAAO,SAAA,IAAAP,CAAA,CAAA,GAAA,KAAAK,WAAA,IAAAL,CAAA,CAAA,GAAA,KAAAI,SAAA,IAAAJ,CAAA,CAAA,GAAA,KAAAM,iBAAA,EAAA;QAEAgD,GAAA,GAAA/C,SAAS,KAAK,UAAU,kBACvB,mVAAC,sBAAmB;YACPH,SAAS,CAATA,CAAAA,SAAQ,CAAC;YACPC,WAAW,CAAXA,CAAAA,WAAU,CAAC;YACLC,iBAAiB,CAAjBA,CAAAA,iBAAgB,CAAC,GAEvC;;;;;;QAAAN,CAAA,CAAA,GAAA,GAAAO,SAAA;QAAAP,CAAA,CAAA,GAAA,GAAAK,WAAA;QAAAL,CAAA,CAAA,GAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,GAAA,GAAAM,iBAAA;QAAAN,CAAA,CAAA,GAAA,GAAAsD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAtD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAuD,GAAA;IAAA,IAAAvD,CAAA,CAAA,GAAA,KAAAO,SAAA,IAAAP,CAAA,CAAA,GAAA,KAAAI,SAAA,IAAAJ,CAAA,CAAA,GAAA,KAAAM,iBAAA,EAAA;QAEAiD,GAAA,GAAAhD,SAAS,KAAK,QAAQ,kBACrB,2VAAC,0BAAuB;YACXH,SAAS,CAATA,CAAAA,SAAQ,CAAC;YACDE,iBAAiB,CAAjBA,CAAAA,iBAAgB,CAAC,GAEvC;;;;;;QAAAN,CAAA,CAAA,GAAA,GAAAO,SAAA;QAAAP,CAAA,CAAA,GAAA,GAAAI,SAAA;QAAAJ,CAAA,CAAA,GAAA,GAAAM,iBAAA;QAAAN,CAAA,CAAA,GAAA,GAAAuD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAvD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAwD,GAAA;IAAA,IAAAxD,CAAA,CAAA,GAAA,KAAAmD,GAAA,IAAAnD,CAAA,CAAA,GAAA,KAAAoD,GAAA,IAAApD,CAAA,CAAA,GAAA,KAAAqD,GAAA,IAAArD,CAAA,CAAA,GAAA,KAAAsD,GAAA,IAAAtD,CAAA,CAAA,GAAA,KAAAuD,GAAA,EAAA;QAnCHC,GAAA,iBAAA,0TAAC,cAAW,CACV;;gBAAAL,GAOK,CAGJ;gBAAAC,GAED,CAEC;gBAAAC,GAKD,CAEC;gBAAAC,GAMD,CAEC;gBAAAC,GAKD,CACF,EApCC,WAAW,CAoCE;;;;;;;QAAAvD,CAAA,CAAA,GAAA,GAAAmD,GAAA;QAAAnD,CAAA,CAAA,GAAA,GAAAoD,GAAA;QAAApD,CAAA,CAAA,GAAA,GAAAqD,GAAA;QAAArD,CAAA,CAAA,GAAA,GAAAsD,GAAA;QAAAtD,CAAA,CAAA,GAAA,GAAAuD,GAAA;QAAAvD,CAAA,CAAA,GAAA,GAAAwD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAxD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAAyD,GAAA;IAAA,IAAAzD,CAAA,CAAA,GAAA,KAAAwC,GAAA,IAAAxC,CAAA,CAAA,GAAA,KAAAwD,GAAA,EAAA;QAzDhBC,GAAA,iBAAA,0TAAC,OAAI,CACH;;gBAAAjB,GAkBY,CAEZ;gBAAAgB,GAoCa,CACf,EA1DC,IAAI,CA0DE;;;;;;;QAAAxD,CAAA,CAAA,GAAA,GAAAwC,GAAA;QAAAxC,CAAA,CAAA,GAAA,GAAAwD,GAAA;QAAAxD,CAAA,CAAA,GAAA,GAAAyD,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAAzD,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA0D,GAAA;IAAA,IAAA1D,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QAILuD,GAAA,iBAAA,yTAAC,cAAU;oCACT,yTAAC,aAAS;gBAAW,SAAuC,EAAvC,uCAAuC;;kCAC1D,qZAAC,cAAW;wBAAW,SAAS,EAAT,SAAS;;;;;;oBAAG,0BAErC,EAHC,SAAS,CAIZ,EALC,UAAU,CAKE;;;;;;;;;;;;QAAA1D,CAAA,CAAA,GAAA,GAAA0D,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA1D,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA2D,GAAA;IAAA,IAAA3D,CAAA,CAAA,GAAA,KAAAE,MAAA,CAAAC,GAAA,CAAA,8BAAA;QANfwD,GAAA,iBAAA,6LAAC,oIAAI;YAAW,SAA4B,EAA5B,4BAA4B,CAC1C;;gBAAAD,GAKY;8BACZ,0TAAC,cAAW;4CACV,6LAAA,GAKM;wBALS,SAAiC,EAAjC,iCAAiC;;0CAC9C,6LAAA,CAA0I;;kDAAvI,6LAAA,MAA4B;kDAApB,WAAW,EAAnB,MAA4B;;;;;;oCAAA,uGAAuG,EAAtI,CAA0I;;;;;;;0CAC1I,6LAAA,CAAgG;;kDAA7F,6LAAA,MAA4B;kDAApB,WAAW,EAAnB,MAA4B;;;;;;oCAAA,6DAA6D,EAA5F,CAAgG;;;;;;;0CAChG,6LAAA,CAAmI;;kDAAhI,6LAAA,MAA+B;kDAAvB,cAAc,EAAtB,MAA+B;;;;;;oCAAA,6FAA6F,EAA/H,CAAmI;;;;;;;0CACnI,6LAAA,CAA6H;;kDAA1H,6LAAA,MAA+B;kDAAvB,cAAc,EAAtB,MAA+B;;;;;;oCAAA,uFAAuF,EAAzH,CAA6H,CAC/H,EALA,GAKM,CACR,EAPC,WAAW,CAQd,EAfC,IAAI,CAeE;;;;;;;;;;;;;;;;;;;;;;;;QAAA1D,CAAA,CAAA,GAAA,GAAA2D,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA3D,CAAA,CAAA,GAAA;IAAA;IAAA,IAAA4D,GAAA;IAAA,IAAA5D,CAAA,CAAA,GAAA,KAAAuC,GAAA,IAAAvC,CAAA,CAAA,GAAA,KAAAyD,GAAA,EAAA;QAjJTG,GAAA,iBAAA,6LAAA,GAkJM;YAlJS,SAAW,EAAX,WAAW,CAExB;;gBAAA1C,EAOK,CAGL;gBAAAqB,GAsDK,CAGL;gBAAAkB,GA0DM,CAGN;gBAAAE,GAeM,CACR,EAlJA,GAkJM;;;;;;;QAAA3D,CAAA,CAAA,GAAA,GAAAuC,GAAA;QAAAvC,CAAA,CAAA,GAAA,GAAAyD,GAAA;QAAAzD,CAAA,CAAA,GAAA,GAAA4D,GAAA;IAAA,OAAA;QAAAA,GAAA,GAAA5D,CAAA,CAAA,GAAA;IAAA;IAAA,OAlJN4D,GAkJM;AAAA;;;0JArLkB/E,aAAA,CAAW,CAAC;uIACgBU,aAAA,CAAW,CAAC;iJAIlEC,gBAAA,CAAc,CAAC;;;KANFO,mBAAA;AAAA,SAAAoC,MAAA0B,GAAA;IAAA,OA+FwBA,GAAG,CAAAC,YAAA,KAAA,IAAsB;AAAA", "ignoreList": [], "debugId": null}}]}