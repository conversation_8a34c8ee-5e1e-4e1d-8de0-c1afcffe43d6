"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { useHRStore } from "@/lib/store/hr-store"
import { 
  ArrowRight, 
  Trash2, 
  Archive, 
  X,
  Users
} from "lucide-react"

interface BulkActionsBarProps {
  selectedCount: number
  onClearSelection: () => void
}

export function BulkActionsBar({ 
  selectedCount, 
  onClearSelection 
}: BulkActionsBarProps) {
  const { selectedEmployees, executeBulkOperation } = useHRStore()
  const [isLoading, setIsLoading] = useState(false)

  const handleBulkTransfer = async () => {
    setIsLoading(true)
    try {
      // TODO: Implement bulk transfer dialog
      console.log('Bulk transfer:', selectedEmployees)
    } catch (error) {
      console.error('Bulk transfer failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleBulkArchive = async () => {
    setIsLoading(true)
    try {
      await executeBulkOperation({
        type: 'status_update',
        employeeIds: selectedEmployees,
        newStatus: 'ARCHIVED'
      })
    } catch (error) {
      console.error('Bulk archive failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleBulkDelete = async () => {
    if (!confirm(`Are you sure you want to delete ${selectedCount} employees? This action cannot be undone.`)) {
      return
    }

    setIsLoading(true)
    try {
      await executeBulkOperation({
        type: 'delete',
        employeeIds: selectedEmployees
      })
    } catch (error) {
      console.error('Bulk delete failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg border">
      <div className="flex items-center gap-2">
        <Users className="h-4 w-4" />
        <span className="text-sm font-medium">
          {selectedCount} employee{selectedCount !== 1 ? 's' : ''} selected
        </span>
      </div>

      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleBulkTransfer}
          disabled={isLoading}
        >
          <ArrowRight className="h-4 w-4 mr-2" />
          Transfer
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={handleBulkArchive}
          disabled={isLoading}
        >
          <Archive className="h-4 w-4 mr-2" />
          Archive
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={handleBulkDelete}
          disabled={isLoading}
          className="text-destructive hover:text-destructive"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Delete
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={onClearSelection}
          disabled={isLoading}
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
