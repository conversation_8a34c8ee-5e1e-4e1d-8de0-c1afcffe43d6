{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/dist/compiled/react/cjs/react-compiler-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-compiler-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    var ReactSharedInternals =\n      require(\"next/dist/compiled/react\").__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;\n    exports.c = function (size) {\n      var dispatcher = ReactSharedInternals.H;\n      null === dispatcher &&\n        console.error(\n          \"Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\\n1. You might have mismatching versions of React and the renderer (such as React DOM)\\n2. You might be breaking the Rules of Hooks\\n3. You might have more than one copy of React in the same app\\nSee https://react.dev/link/invalid-hook-call for tips about how to debug and fix this problem.\"\n        );\n      return dispatcher.useMemoCache(size);\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,IAAI,uBACF,8GAAoC,+DAA+D;IACrG,QAAQ,CAAC,GAAG,SAAU,IAAI;QACxB,IAAI,aAAa,qBAAqB,CAAC;QACvC,SAAS,cACP,QAAQ,KAAK,CACX;QAEJ,OAAO,WAAW,YAAY,CAAC;IACjC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/dist/compiled/react/compiler-runtime.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-compiler-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-compiler-runtime.development.js');\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAIG;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/interopRequireDefault.js"], "sourcesContent": ["function _interopRequireDefault(e) {\n  return e && e.__esModule ? e : {\n    \"default\": e\n  };\n}\nmodule.exports = _interopRequireDefault, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,uBAAuB,CAAC;IAC/B,OAAO,KAAK,EAAE,UAAU,GAAG,IAAI;QAC7B,WAAW;IACb;AACF;AACA,OAAO,OAAO,GAAG,wBAAwB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/typeof.js"], "sourcesContent": ["function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,QAAQ,CAAC;IAChB;IAEA,OAAO,OAAO,OAAO,GAAG,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,QAAQ,GAAG,SAAU,CAAC;QAC/G,OAAO,OAAO;IAChB,IAAI,SAAU,CAAC;QACb,OAAO,KAAK,cAAc,OAAO,UAAU,EAAE,WAAW,KAAK,UAAU,MAAM,OAAO,SAAS,GAAG,WAAW,OAAO;IACpH,GAAG,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO,EAAE,QAAQ;AAC3F;AACA,OAAO,OAAO,GAAG,SAAS,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/OverloadYield.js"], "sourcesContent": ["function _OverloadYield(e, d) {\n  this.v = e, this.k = d;\n}\nmodule.exports = _OverloadYield, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,eAAe,CAAC,EAAE,CAAC;IAC1B,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG;AACvB;AACA,OAAO,OAAO,GAAG,gBAAgB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/regeneratorDefine.js"], "sourcesContent": ["function _regeneratorDefine(e, r, n, t) {\n  var i = Object.defineProperty;\n  try {\n    i({}, \"\", {});\n  } catch (e) {\n    i = 0;\n  }\n  module.exports = _regeneratorDefine = function regeneratorDefine(e, r, n, t) {\n    if (r) i ? i(e, r, {\n      value: n,\n      enumerable: !t,\n      configurable: !t,\n      writable: !t\n    }) : e[r] = n;else {\n      var o = function o(r, n) {\n        _regeneratorDefine(e, r, function (e) {\n          return this._invoke(r, n, e);\n        });\n      };\n      o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2);\n    }\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _regeneratorDefine(e, r, n, t);\n}\nmodule.exports = _regeneratorDefine, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,mBAAmB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACpC,IAAI,IAAI,OAAO,cAAc;IAC7B,IAAI;QACF,EAAE,CAAC,GAAG,IAAI,CAAC;IACb,EAAE,OAAO,GAAG;QACV,IAAI;IACN;IACA,OAAO,OAAO,GAAG,qBAAqB,SAAS,kBAAkB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACzE,IAAI,GAAG,IAAI,EAAE,GAAG,GAAG;YACjB,OAAO;YACP,YAAY,CAAC;YACb,cAAc,CAAC;YACf,UAAU,CAAC;QACb,KAAK,CAAC,CAAC,EAAE,GAAG;aAAO;YACjB,IAAI,IAAI,SAAS,EAAE,CAAC,EAAE,CAAC;gBACrB,mBAAmB,GAAG,GAAG,SAAU,CAAC;oBAClC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG;gBAC5B;YACF;YACA,EAAE,QAAQ,IAAI,EAAE,SAAS,IAAI,EAAE,UAAU;QAC3C;IACF,GAAG,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO,EAAE,mBAAmB,GAAG,GAAG,GAAG;AAC/G;AACA,OAAO,OAAO,GAAG,oBAAoB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/regenerator.js"], "sourcesContent": ["var regeneratorDefine = require(\"./regeneratorDefine.js\");\nfunction _regenerator() {\n  /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */\n  var e,\n    t,\n    r = \"function\" == typeof Symbol ? Symbol : {},\n    n = r.iterator || \"@@iterator\",\n    o = r.toStringTag || \"@@toStringTag\";\n  function i(r, n, o, i) {\n    var c = n && n.prototype instanceof Generator ? n : Generator,\n      u = Object.create(c.prototype);\n    return regeneratorDefine(u, \"_invoke\", function (r, n, o) {\n      var i,\n        c,\n        u,\n        f = 0,\n        p = o || [],\n        y = !1,\n        G = {\n          p: 0,\n          n: 0,\n          v: e,\n          a: d,\n          f: d.bind(e, 4),\n          d: function d(t, r) {\n            return i = t, c = 0, u = e, G.n = r, a;\n          }\n        };\n      function d(r, n) {\n        for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) {\n          var o,\n            i = p[t],\n            d = G.p,\n            l = i[2];\n          r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0));\n        }\n        if (o || r > 1) return a;\n        throw y = !0, n;\n      }\n      return function (o, p, l) {\n        if (f > 1) throw TypeError(\"Generator is already running\");\n        for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) {\n          i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u);\n          try {\n            if (f = 2, i) {\n              if (c || (o = \"next\"), t = i[o]) {\n                if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\");\n                if (!t.done) return t;\n                u = t.value, c < 2 && (c = 0);\n              } else 1 === c && (t = i[\"return\"]) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1);\n              i = e;\n            } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break;\n          } catch (t) {\n            i = e, c = 1, u = t;\n          } finally {\n            f = 1;\n          }\n        }\n        return {\n          value: t,\n          done: y\n        };\n      };\n    }(r, o, i), !0), u;\n  }\n  var a = {};\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n  t = Object.getPrototypeOf;\n  var c = [][n] ? t(t([][n]())) : (regeneratorDefine(t = {}, n, function () {\n      return this;\n    }), t),\n    u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c);\n  function f(e) {\n    return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, regeneratorDefine(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e;\n  }\n  return GeneratorFunction.prototype = GeneratorFunctionPrototype, regeneratorDefine(u, \"constructor\", GeneratorFunctionPrototype), regeneratorDefine(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", regeneratorDefine(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), regeneratorDefine(u), regeneratorDefine(u, o, \"Generator\"), regeneratorDefine(u, n, function () {\n    return this;\n  }), regeneratorDefine(u, \"toString\", function () {\n    return \"[object Generator]\";\n  }), (module.exports = _regenerator = function _regenerator() {\n    return {\n      w: i,\n      m: f\n    };\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports)();\n}\nmodule.exports = _regenerator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,SAAS;IACP,gKAAgK,GAChK,IAAI,GACF,GACA,IAAI,cAAc,OAAO,SAAS,SAAS,CAAC,GAC5C,IAAI,EAAE,QAAQ,IAAI,cAClB,IAAI,EAAE,WAAW,IAAI;IACvB,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACnB,IAAI,IAAI,KAAK,EAAE,SAAS,YAAY,YAAY,IAAI,WAClD,IAAI,OAAO,MAAM,CAAC,EAAE,SAAS;QAC/B,OAAO,kBAAkB,GAAG,WAAW,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;YACtD,IAAI,GACF,GACA,GACA,IAAI,GACJ,IAAI,KAAK,EAAE,EACX,IAAI,CAAC,GACL,IAAI;gBACF,GAAG;gBACH,<PERSON><PERSON>;gBACH,<PERSON>G;gBACH,GAAG;gBACH,GAAG,EAAE,IAAI,CAAC,GAAG;gBACb,GAAG,SAAS,EAAE,CAAC,EAAE,CAAC;oBAChB,OAAO,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG;gBACvC;YACF;YACF,SAAS,EAAE,CAAC,EAAE,CAAC;gBACb,IAAK,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,KAAK,KAAK,CAAC,KAAK,IAAI,EAAE,MAAM,EAAE,IAAK;oBAC5D,IAAI,GACF,IAAI,CAAC,CAAC,EAAE,EACR,IAAI,EAAE,CAAC,EACP,IAAI,CAAC,CAAC,EAAE;oBACV,IAAI,IAAI,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;gBAC3O;gBACA,IAAI,KAAK,IAAI,GAAG,OAAO;gBACvB,MAAM,IAAI,CAAC,GAAG;YAChB;YACA,OAAO,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;gBACtB,IAAI,IAAI,GAAG,MAAM,UAAU;gBAC3B,IAAK,KAAK,MAAM,KAAK,EAAE,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,GAAI;oBACtE,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC;oBACpE,IAAI;wBACF,IAAI,IAAI,GAAG,GAAG;4BACZ,IAAI,KAAK,CAAC,IAAI,MAAM,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE;gCAC/B,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,UAAU;gCACzC,IAAI,CAAC,EAAE,IAAI,EAAE,OAAO;gCACpB,IAAI,EAAE,KAAK,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC;4BAC9B,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,UAAU,sCAAsC,IAAI,aAAa,IAAI,CAAC;4BACtI,IAAI;wBACN,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,MAAM,GAAG;oBAC3D,EAAE,OAAO,GAAG;wBACV,IAAI,GAAG,IAAI,GAAG,IAAI;oBACpB,SAAU;wBACR,IAAI;oBACN;gBACF;gBACA,OAAO;oBACL,OAAO;oBACP,MAAM;gBACR;YACF;QACF,EAAE,GAAG,GAAG,IAAI,CAAC,IAAI;IACnB;IACA,IAAI,IAAI,CAAC;IACT,SAAS,aAAa;IACtB,SAAS,qBAAqB;IAC9B,SAAS,8BAA8B;IACvC,IAAI,OAAO,cAAc;IACzB,IAAI,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,OAAO,CAAC,kBAAkB,IAAI,CAAC,GAAG,GAAG;QAC1D,OAAO,IAAI;IACb,IAAI,CAAC,GACL,IAAI,2BAA2B,SAAS,GAAG,UAAU,SAAS,GAAG,OAAO,MAAM,CAAC;IACjF,SAAS,EAAE,CAAC;QACV,OAAO,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,GAAG,8BAA8B,CAAC,EAAE,SAAS,GAAG,4BAA4B,kBAAkB,GAAG,GAAG,oBAAoB,GAAG,EAAE,SAAS,GAAG,OAAO,MAAM,CAAC,IAAI;IAClN;IACA,OAAO,kBAAkB,SAAS,GAAG,4BAA4B,kBAAkB,GAAG,eAAe,6BAA6B,kBAAkB,4BAA4B,eAAe,oBAAoB,kBAAkB,WAAW,GAAG,qBAAqB,kBAAkB,4BAA4B,GAAG,sBAAsB,kBAAkB,IAAI,kBAAkB,GAAG,GAAG,cAAc,kBAAkB,GAAG,GAAG;QACja,OAAO,IAAI;IACb,IAAI,kBAAkB,GAAG,YAAY;QACnC,OAAO;IACT,IAAI,CAAC,OAAO,OAAO,GAAG,eAAe,SAAS;QAC5C,OAAO;YACL,GAAG;YACH,GAAG;QACL;IACF,GAAG,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO;AACjF;AACA,OAAO,OAAO,GAAG,cAAc,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/regeneratorAsyncIterator.js"], "sourcesContent": ["var OverloadYield = require(\"./OverloadYield.js\");\nvar regeneratorDefine = require(\"./regeneratorDefine.js\");\nfunction AsyncIterator(t, e) {\n  function n(r, o, i, f) {\n    try {\n      var c = t[r](o),\n        u = c.value;\n      return u instanceof OverloadYield ? e.resolve(u.v).then(function (t) {\n        n(\"next\", t, i, f);\n      }, function (t) {\n        n(\"throw\", t, i, f);\n      }) : e.resolve(u).then(function (t) {\n        c.value = t, i(c);\n      }, function (t) {\n        return n(\"throw\", t, i, f);\n      });\n    } catch (t) {\n      f(t);\n    }\n  }\n  var r;\n  this.next || (regeneratorDefine(AsyncIterator.prototype), regeneratorDefine(AsyncIterator.prototype, \"function\" == typeof Symbol && Symbol.asyncIterator || \"@asyncIterator\", function () {\n    return this;\n  })), regeneratorDefine(this, \"_invoke\", function (t, o, i) {\n    function f() {\n      return new e(function (e, r) {\n        n(t, i, e, r);\n      });\n    }\n    return r = r ? r.then(f, f) : f();\n  }, !0);\n}\nmodule.exports = AsyncIterator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AACJ,SAAS,cAAc,CAAC,EAAE,CAAC;IACzB,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACnB,IAAI;YACF,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,IACX,IAAI,EAAE,KAAK;YACb,OAAO,aAAa,gBAAgB,EAAE,OAAO,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,SAAU,CAAC;gBACjE,EAAE,QAAQ,GAAG,GAAG;YAClB,GAAG,SAAU,CAAC;gBACZ,EAAE,SAAS,GAAG,GAAG;YACnB,KAAK,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,SAAU,CAAC;gBAChC,EAAE,KAAK,GAAG,GAAG,EAAE;YACjB,GAAG,SAAU,CAAC;gBACZ,OAAO,EAAE,SAAS,GAAG,GAAG;YAC1B;QACF,EAAE,OAAO,GAAG;YACV,EAAE;QACJ;IACF;IACA,IAAI;IACJ,IAAI,CAAC,IAAI,IAAI,CAAC,kBAAkB,cAAc,SAAS,GAAG,kBAAkB,cAAc,SAAS,EAAE,cAAc,OAAO,UAAU,OAAO,aAAa,IAAI,kBAAkB;QAC5K,OAAO,IAAI;IACb,EAAE,GAAG,kBAAkB,IAAI,EAAE,WAAW,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QACvD,SAAS;YACP,OAAO,IAAI,EAAE,SAAU,CAAC,EAAE,CAAC;gBACzB,EAAE,GAAG,GAAG,GAAG;YACb;QACF;QACA,OAAO,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;IAChC,GAAG,CAAC;AACN;AACA,OAAO,OAAO,GAAG,eAAe,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/regeneratorAsyncGen.js"], "sourcesContent": ["var regenerator = require(\"./regenerator.js\");\nvar regeneratorAsyncIterator = require(\"./regeneratorAsyncIterator.js\");\nfunction _regeneratorAsyncGen(r, e, t, o, n) {\n  return new regeneratorAsyncIterator(regenerator().w(r, e, t, o), n || Promise);\n}\nmodule.exports = _regeneratorAsyncGen, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AACJ,SAAS,qBAAqB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACzC,OAAO,IAAI,yBAAyB,cAAc,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,KAAK;AACxE;AACA,OAAO,OAAO,GAAG,sBAAsB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/regeneratorAsync.js"], "sourcesContent": ["var regeneratorAsyncGen = require(\"./regeneratorAsyncGen.js\");\nfunction _regeneratorAsync(n, e, r, t, o) {\n  var a = regeneratorAsyncGen(n, e, r, t, o);\n  return a.next().then(function (n) {\n    return n.done ? n.value : a.next();\n  });\n}\nmodule.exports = _regeneratorAsync, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,SAAS,kBAAkB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACtC,IAAI,IAAI,oBAAoB,GAAG,GAAG,GAAG,GAAG;IACxC,OAAO,EAAE,IAAI,GAAG,IAAI,CAAC,SAAU,CAAC;QAC9B,OAAO,EAAE,IAAI,GAAG,EAAE,KAAK,GAAG,EAAE,IAAI;IAClC;AACF;AACA,OAAO,OAAO,GAAG,mBAAmB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/regeneratorKeys.js"], "sourcesContent": ["function _regeneratorKeys(e) {\n  var n = Object(e),\n    r = [];\n  for (var t in n) r.unshift(t);\n  return function e() {\n    for (; r.length;) if ((t = r.pop()) in n) return e.value = t, e.done = !1, e;\n    return e.done = !0, e;\n  };\n}\nmodule.exports = _regeneratorKeys, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,iBAAiB,CAAC;IACzB,IAAI,IAAI,OAAO,IACb,IAAI,EAAE;IACR,IAAK,IAAI,KAAK,EAAG,EAAE,OAAO,CAAC;IAC3B,OAAO,SAAS;QACd,MAAO,EAAE,MAAM,EAAG,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,GAAG,OAAO,EAAE,KAAK,GAAG,GAAG,EAAE,IAAI,GAAG,CAAC,GAAG;QAC3E,OAAO,EAAE,IAAI,GAAG,CAAC,GAAG;IACtB;AACF;AACA,OAAO,OAAO,GAAG,kBAAkB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/regeneratorValues.js"], "sourcesContent": ["var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction _regeneratorValues(e) {\n  if (null != e) {\n    var t = e[\"function\" == typeof Symbol && Symbol.iterator || \"@@iterator\"],\n      r = 0;\n    if (t) return t.call(e);\n    if (\"function\" == typeof e.next) return e;\n    if (!isNaN(e.length)) return {\n      next: function next() {\n        return e && r >= e.length && (e = void 0), {\n          value: e && e[r++],\n          done: !e\n        };\n      }\n    };\n  }\n  throw new TypeError(_typeof(e) + \" is not iterable\");\n}\nmodule.exports = _regeneratorValues, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI,UAAU,4GAAsB,CAAC,UAAU;AAC/C,SAAS,mBAAmB,CAAC;IAC3B,IAAI,QAAQ,GAAG;QACb,IAAI,IAAI,CAAC,CAAC,cAAc,OAAO,UAAU,OAAO,QAAQ,IAAI,aAAa,EACvE,IAAI;QACN,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;QACrB,IAAI,cAAc,OAAO,EAAE,IAAI,EAAE,OAAO;QACxC,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO;YAC3B,MAAM,SAAS;gBACb,OAAO,KAAK,KAAK,EAAE,MAAM,IAAI,CAAC,IAAI,KAAK,CAAC,GAAG;oBACzC,OAAO,KAAK,CAAC,CAAC,IAAI;oBAClB,MAAM,CAAC;gBACT;YACF;QACF;IACF;IACA,MAAM,IAAI,UAAU,QAAQ,KAAK;AACnC;AACA,OAAO,OAAO,GAAG,oBAAoB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/regeneratorRuntime.js"], "sourcesContent": ["var OverloadYield = require(\"./OverloadYield.js\");\nvar regenerator = require(\"./regenerator.js\");\nvar regeneratorAsync = require(\"./regeneratorAsync.js\");\nvar regeneratorAsyncGen = require(\"./regeneratorAsyncGen.js\");\nvar regeneratorAsyncIterator = require(\"./regeneratorAsyncIterator.js\");\nvar regeneratorKeys = require(\"./regeneratorKeys.js\");\nvar regeneratorValues = require(\"./regeneratorValues.js\");\nfunction _regeneratorRuntime() {\n  \"use strict\";\n\n  var r = regenerator(),\n    e = r.m(_regeneratorRuntime),\n    t = (Object.getPrototypeOf ? Object.getPrototypeOf(e) : e.__proto__).constructor;\n  function n(r) {\n    var e = \"function\" == typeof r && r.constructor;\n    return !!e && (e === t || \"GeneratorFunction\" === (e.displayName || e.name));\n  }\n  var o = {\n    \"throw\": 1,\n    \"return\": 2,\n    \"break\": 3,\n    \"continue\": 3\n  };\n  function a(r) {\n    var e, t;\n    return function (n) {\n      e || (e = {\n        stop: function stop() {\n          return t(n.a, 2);\n        },\n        \"catch\": function _catch() {\n          return n.v;\n        },\n        abrupt: function abrupt(r, e) {\n          return t(n.a, o[r], e);\n        },\n        delegateYield: function delegateYield(r, o, a) {\n          return e.resultName = o, t(n.d, regeneratorValues(r), a);\n        },\n        finish: function finish(r) {\n          return t(n.f, r);\n        }\n      }, t = function t(r, _t, o) {\n        n.p = e.prev, n.n = e.next;\n        try {\n          return r(_t, o);\n        } finally {\n          e.next = n.n;\n        }\n      }), e.resultName && (e[e.resultName] = n.v, e.resultName = void 0), e.sent = n.v, e.next = n.n;\n      try {\n        return r.call(this, e);\n      } finally {\n        n.p = e.prev, n.n = e.next;\n      }\n    };\n  }\n  return (module.exports = _regeneratorRuntime = function _regeneratorRuntime() {\n    return {\n      wrap: function wrap(e, t, n, o) {\n        return r.w(a(e), t, n, o && o.reverse());\n      },\n      isGeneratorFunction: n,\n      mark: r.m,\n      awrap: function awrap(r, e) {\n        return new OverloadYield(r, e);\n      },\n      AsyncIterator: regeneratorAsyncIterator,\n      async: function async(r, e, t, o, u) {\n        return (n(e) ? regeneratorAsyncGen : regeneratorAsync)(a(r), e, t, o, u);\n      },\n      keys: regeneratorKeys,\n      values: regeneratorValues\n    };\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports)();\n}\nmodule.exports = _regeneratorRuntime, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,SAAS;IACP;IAEA,IAAI,IAAI,eACN,IAAI,EAAE,CAAC,CAAC,sBACR,IAAI,CAAC,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,KAAK,EAAE,SAAS,EAAE,WAAW;IAClF,SAAS,EAAE,CAAC;QACV,IAAI,IAAI,cAAc,OAAO,KAAK,EAAE,WAAW;QAC/C,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,KAAK,wBAAwB,CAAC,EAAE,WAAW,IAAI,EAAE,IAAI,CAAC;IAC7E;IACA,IAAI,IAAI;QACN,SAAS;QACT,UAAU;QACV,SAAS;QACT,YAAY;IACd;IACA,SAAS,EAAE,CAAC;QACV,IAAI,GAAG;QACP,OAAO,SAAU,CAAC;YAChB,KAAK,CAAC,IAAI;gBACR,MAAM,SAAS;oBACb,OAAO,EAAE,EAAE,CAAC,EAAE;gBAChB;gBACA,SAAS,SAAS;oBAChB,OAAO,EAAE,CAAC;gBACZ;gBACA,QAAQ,SAAS,OAAO,CAAC,EAAE,CAAC;oBAC1B,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;gBACtB;gBACA,eAAe,SAAS,cAAc,CAAC,EAAE,CAAC,EAAE,CAAC;oBAC3C,OAAO,EAAE,UAAU,GAAG,GAAG,EAAE,EAAE,CAAC,EAAE,kBAAkB,IAAI;gBACxD;gBACA,QAAQ,SAAS,OAAO,CAAC;oBACvB,OAAO,EAAE,EAAE,CAAC,EAAE;gBAChB;YACF,GAAG,IAAI,SAAS,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;gBACxB,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE,IAAI;gBAC1B,IAAI;oBACF,OAAO,EAAE,IAAI;gBACf,SAAU;oBACR,EAAE,IAAI,GAAG,EAAE,CAAC;gBACd;YACF,CAAC,GAAG,EAAE,UAAU,IAAI,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,UAAU,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,EAAE,EAAE,IAAI,GAAG,EAAE,CAAC;YAC9F,IAAI;gBACF,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE;YACtB,SAAU;gBACR,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE,IAAI;YAC5B;QACF;IACF;IACA,OAAO,CAAC,OAAO,OAAO,GAAG,sBAAsB,SAAS;QACtD,OAAO;YACL,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;gBAC5B,OAAO,EAAE,CAAC,CAAC,EAAE,IAAI,GAAG,GAAG,KAAK,EAAE,OAAO;YACvC;YACA,qBAAqB;YACrB,MAAM,EAAE,CAAC;YACT,OAAO,SAAS,MAAM,CAAC,EAAE,CAAC;gBACxB,OAAO,IAAI,cAAc,GAAG;YAC9B;YACA,eAAe;YACf,OAAO,SAAS,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;gBACjC,OAAO,CAAC,EAAE,KAAK,sBAAsB,gBAAgB,EAAE,EAAE,IAAI,GAAG,GAAG,GAAG;YACxE;YACA,MAAM;YACN,QAAQ;QACV;IACF,GAAG,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO;AACjF;AACA,OAAO,OAAO,GAAG,qBAAqB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/regenerator/index.js"], "sourcesContent": ["// TODO(Babel 8): Remove this file.\n\nvar runtime = require(\"../helpers/regeneratorRuntime\")();\nmodule.exports = runtime;\n\n// Copied from https://github.com/facebook/regenerator/blob/main/packages/runtime/runtime.js#L736=\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  if (typeof globalThis === \"object\") {\n    globalThis.regeneratorRuntime = runtime;\n  } else {\n    Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n  }\n}\n"], "names": [], "mappings": "AAAA,mCAAmC;AAEnC,IAAI,UAAU;AACd,OAAO,OAAO,GAAG;AAEjB,kGAAkG;AAClG,IAAI;IACF,qBAAqB;AACvB,EAAE,OAAO,sBAAsB;IAC7B,IAAI,OAAO,eAAe,UAAU;QAClC,WAAW,kBAAkB,GAAG;IAClC,OAAO;QACL,SAAS,KAAK,0BAA0B;IAC1C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/toPrimitive.js"], "sourcesContent": ["var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nmodule.exports = toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI,UAAU,4GAAsB,CAAC,UAAU;AAC/C,SAAS,YAAY,CAAC,EAAE,CAAC;IACvB,IAAI,YAAY,QAAQ,MAAM,CAAC,GAAG,OAAO;IACzC,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAC7B,IAAI,KAAK,MAAM,GAAG;QAChB,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QACvB,IAAI,YAAY,QAAQ,IAAI,OAAO;QACnC,MAAM,IAAI,UAAU;IACtB;IACA,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAC5C;AACA,OAAO,OAAO,GAAG,aAAa,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 622, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/toPropertyKey.js"], "sourcesContent": ["var _typeof = require(\"./typeof.js\")[\"default\"];\nvar toPrimitive = require(\"./toPrimitive.js\");\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nmodule.exports = toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI,UAAU,4GAAsB,CAAC,UAAU;AAC/C,IAAI;AACJ,SAAS,cAAc,CAAC;IACtB,IAAI,IAAI,YAAY,GAAG;IACvB,OAAO,YAAY,QAAQ,KAAK,IAAI,IAAI;AAC1C;AACA,OAAO,OAAO,GAAG,eAAe,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/defineProperty.js"], "sourcesContent": ["var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,SAAS,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;IAC9B,OAAO,CAAC,IAAI,cAAc,EAAE,KAAK,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG;QAC/D,OAAO;QACP,YAAY,CAAC;QACb,cAAc,CAAC;QACf,UAAU,CAAC;IACb,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG;AACjB;AACA,OAAO,OAAO,GAAG,iBAAiB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 649, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/asyncToGenerator.js"], "sourcesContent": ["function asyncGeneratorStep(n, t, e, r, o, a, c) {\n  try {\n    var i = n[a](c),\n      u = i.value;\n  } catch (n) {\n    return void e(n);\n  }\n  i.done ? t(u) : Promise.resolve(u).then(r, o);\n}\nfunction _asyncToGenerator(n) {\n  return function () {\n    var t = this,\n      e = arguments;\n    return new Promise(function (r, o) {\n      var a = n.apply(t, e);\n      function _next(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n);\n      }\n      function _throw(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n);\n      }\n      _next(void 0);\n    });\n  };\n}\nmodule.exports = _asyncToGenerator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,mBAAmB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC7C,IAAI;QACF,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,IACX,IAAI,EAAE,KAAK;IACf,EAAE,OAAO,GAAG;QACV,OAAO,KAAK,EAAE;IAChB;IACA,EAAE,IAAI,GAAG,EAAE,KAAK,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG;AAC7C;AACA,SAAS,kBAAkB,CAAC;IAC1B,OAAO;QACL,IAAI,IAAI,IAAI,EACV,IAAI;QACN,OAAO,IAAI,QAAQ,SAAU,CAAC,EAAE,CAAC;YAC/B,IAAI,IAAI,EAAE,KAAK,CAAC,GAAG;YACnB,SAAS,MAAM,CAAC;gBACd,mBAAmB,GAAG,GAAG,GAAG,OAAO,QAAQ,QAAQ;YACrD;YACA,SAAS,OAAO,CAAC;gBACf,mBAAmB,GAAG,GAAG,GAAG,OAAO,QAAQ,SAAS;YACtD;YACA,MAAM,KAAK;QACb;IACF;AACF;AACA,OAAO,OAAO,GAAG,mBAAmB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/arrayWithHoles.js"], "sourcesContent": ["function _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nmodule.exports = _arrayWithHoles, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,gBAAgB,CAAC;IACxB,IAAI,MAAM,OAAO,CAAC,IAAI,OAAO;AAC/B;AACA,OAAO,OAAO,GAAG,iBAAiB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/iterableToArrayLimit.js"], "sourcesContent": ["function _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nmodule.exports = _iterableToArrayLimit, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,sBAAsB,CAAC,EAAE,CAAC;IACjC,IAAI,IAAI,QAAQ,IAAI,OAAO,eAAe,OAAO,UAAU,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa;IAChG,IAAI,QAAQ,GAAG;QACb,IAAI,GACF,GACA,GACA,GACA,IAAI,EAAE,EACN,IAAI,CAAC,GACL,IAAI,CAAC;QACP,IAAI;YACF,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,GAAG;gBACrC,IAAI,OAAO,OAAO,GAAG;gBACrB,IAAI,CAAC;YACP,OAAO,MAAO,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI,CAAC,EAAE,KAAK,GAAG,EAAE,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC;QACvF,EAAE,OAAO,GAAG;YACV,IAAI,CAAC,GAAG,IAAI;QACd,SAAU;YACR,IAAI;gBACF,IAAI,CAAC,KAAK,QAAQ,CAAC,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,IAAI,OAAO,OAAO,CAAC,GAAG;YACzE,SAAU;gBACR,IAAI,GAAG,MAAM;YACf;QACF;QACA,OAAO;IACT;AACF;AACA,OAAO,OAAO,GAAG,uBAAuB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 714, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/arrayLikeToArray.js"], "sourcesContent": ["function _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nmodule.exports = _arrayLikeToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,kBAAkB,CAAC,EAAE,CAAC;IAC7B,CAAC,QAAQ,KAAK,IAAI,EAAE,MAAM,KAAK,CAAC,IAAI,EAAE,MAAM;IAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAI,IAAI,GAAG,IAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACrD,OAAO;AACT;AACA,OAAO,OAAO,GAAG,mBAAmB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 725, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/unsupportedIterableToArray.js"], "sourcesContent": ["var arrayLikeToArray = require(\"./arrayLikeToArray.js\");\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? arrayLikeToArray(r, a) : void 0;\n  }\n}\nmodule.exports = _unsupportedIterableToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,SAAS,4BAA4B,CAAC,EAAE,CAAC;IACvC,IAAI,GAAG;QACL,IAAI,YAAY,OAAO,GAAG,OAAO,iBAAiB,GAAG;QACrD,IAAI,IAAI,CAAA,CAAC,CAAA,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;QACtC,OAAO,aAAa,KAAK,EAAE,WAAW,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,GAAG,UAAU,KAAK,UAAU,IAAI,MAAM,IAAI,CAAC,KAAK,gBAAgB,KAAK,2CAA2C,IAAI,CAAC,KAAK,iBAAiB,GAAG,KAAK,KAAK;IAC3N;AACF;AACA,OAAO,OAAO,GAAG,6BAA6B,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 739, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/nonIterableRest.js"], "sourcesContent": ["function _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nmodule.exports = _nonIterableRest, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS;IACP,MAAM,IAAI,UAAU;AACtB;AACA,OAAO,OAAO,GAAG,kBAAkB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/slicedToArray.js"], "sourcesContent": ["var arrayWithHoles = require(\"./arrayWithHoles.js\");\nvar iterableToArrayLimit = require(\"./iterableToArrayLimit.js\");\nvar unsupportedIterableToArray = require(\"./unsupportedIterableToArray.js\");\nvar nonIterableRest = require(\"./nonIterableRest.js\");\nfunction _slicedToArray(r, e) {\n  return arrayWithHoles(r) || iterableToArrayLimit(r, e) || unsupportedIterableToArray(r, e) || nonIterableRest();\n}\nmodule.exports = _slicedToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,SAAS,eAAe,CAAC,EAAE,CAAC;IAC1B,OAAO,eAAe,MAAM,qBAAqB,GAAG,MAAM,2BAA2B,GAAG,MAAM;AAChG;AACA,OAAO,OAAO,GAAG,gBAAgB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/classCallCheck.js"], "sourcesContent": ["function _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nmodule.exports = _classCallCheck, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAC3B,IAAI,CAAC,CAAC,aAAa,CAAC,GAAG,MAAM,IAAI,UAAU;AAC7C;AACA,OAAO,OAAO,GAAG,iBAAiB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 770, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/createClass.js"], "sourcesContent": ["var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nmodule.exports = _createClass, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,SAAS,kBAAkB,CAAC,EAAE,CAAC;IAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK;QACjC,IAAI,IAAI,CAAC,CAAC,EAAE;QACZ,EAAE,UAAU,GAAG,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,YAAY,GAAG,CAAC,GAAG,WAAW,KAAK,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,GAAG,OAAO,cAAc,CAAC,GAAG,cAAc,EAAE,GAAG,GAAG;IAC5I;AACF;AACA,SAAS,aAAa,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3B,OAAO,KAAK,kBAAkB,EAAE,SAAS,EAAE,IAAI,KAAK,kBAAkB,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,aAAa;QACjH,UAAU,CAAC;IACb,IAAI;AACN;AACA,OAAO,OAAO,GAAG,cAAc,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/assertThisInitialized.js"], "sourcesContent": ["function _assertThisInitialized(e) {\n  if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  return e;\n}\nmodule.exports = _assertThisInitialized, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,uBAAuB,CAAC;IAC/B,IAAI,KAAK,MAAM,GAAG,MAAM,IAAI,eAAe;IAC3C,OAAO;AACT;AACA,OAAO,OAAO,GAAG,wBAAwB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 798, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/possibleConstructorReturn.js"], "sourcesContent": ["var _typeof = require(\"./typeof.js\")[\"default\"];\nvar assertThisInitialized = require(\"./assertThisInitialized.js\");\nfunction _possibleConstructorReturn(t, e) {\n  if (e && (\"object\" == _typeof(e) || \"function\" == typeof e)) return e;\n  if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n  return assertThisInitialized(t);\n}\nmodule.exports = _possibleConstructorReturn, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI,UAAU,4GAAsB,CAAC,UAAU;AAC/C,IAAI;AACJ,SAAS,2BAA2B,CAAC,EAAE,CAAC;IACtC,IAAI,KAAK,CAAC,YAAY,QAAQ,MAAM,cAAc,OAAO,CAAC,GAAG,OAAO;IACpE,IAAI,KAAK,MAAM,GAAG,MAAM,IAAI,UAAU;IACtC,OAAO,sBAAsB;AAC/B;AACA,OAAO,OAAO,GAAG,4BAA4B,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 811, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/getPrototypeOf.js"], "sourcesContent": ["function _getPrototypeOf(t) {\n  return module.exports = _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) {\n    return t.__proto__ || Object.getPrototypeOf(t);\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _getPrototypeOf(t);\n}\nmodule.exports = _getPrototypeOf, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,gBAAgB,CAAC;IACxB,OAAO,OAAO,OAAO,GAAG,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAU,CAAC;QAC1G,OAAO,EAAE,SAAS,IAAI,OAAO,cAAc,CAAC;IAC9C,GAAG,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO,EAAE,gBAAgB;AACnG;AACA,OAAO,OAAO,GAAG,iBAAiB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 822, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/setPrototypeOf.js"], "sourcesContent": ["function _setPrototypeOf(t, e) {\n  return module.exports = _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _setPrototypeOf(t, e);\n}\nmodule.exports = _setPrototypeOf, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAC3B,OAAO,OAAO,OAAO,GAAG,kBAAkB,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,IAAI,KAAK,SAAU,CAAC,EAAE,CAAC;QAC7G,OAAO,EAAE,SAAS,GAAG,GAAG;IAC1B,GAAG,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO,EAAE,gBAAgB,GAAG;AACtG;AACA,OAAO,OAAO,GAAG,iBAAiB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 833, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/inherits.js"], "sourcesContent": ["var setPrototypeOf = require(\"./setPrototypeOf.js\");\nfunction _inherits(t, e) {\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n  t.prototype = Object.create(e && e.prototype, {\n    constructor: {\n      value: t,\n      writable: !0,\n      configurable: !0\n    }\n  }), Object.defineProperty(t, \"prototype\", {\n    writable: !1\n  }), e && setPrototypeOf(t, e);\n}\nmodule.exports = _inherits, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,SAAS,UAAU,CAAC,EAAE,CAAC;IACrB,IAAI,cAAc,OAAO,KAAK,SAAS,GAAG,MAAM,IAAI,UAAU;IAC9D,EAAE,SAAS,GAAG,OAAO,MAAM,CAAC,KAAK,EAAE,SAAS,EAAE;QAC5C,aAAa;YACX,OAAO;YACP,UAAU,CAAC;YACX,cAAc,CAAC;QACjB;IACF,IAAI,OAAO,cAAc,CAAC,GAAG,aAAa;QACxC,UAAU,CAAC;IACb,IAAI,KAAK,eAAe,GAAG;AAC7B;AACA,OAAO,OAAO,GAAG,WAAW,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 852, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/isNativeFunction.js"], "sourcesContent": ["function _isNativeFunction(t) {\n  try {\n    return -1 !== Function.toString.call(t).indexOf(\"[native code]\");\n  } catch (n) {\n    return \"function\" == typeof t;\n  }\n}\nmodule.exports = _isNativeFunction, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS,kBAAkB,CAAC;IAC1B,IAAI;QACF,OAAO,CAAC,MAAM,SAAS,QAAQ,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;IAClD,EAAE,OAAO,GAAG;QACV,OAAO,cAAc,OAAO;IAC9B;AACF;AACA,OAAO,OAAO,GAAG,mBAAmB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 865, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/isNativeReflectConstruct.js"], "sourcesContent": ["function _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n  } catch (t) {}\n  return (module.exports = _isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports)();\n}\nmodule.exports = _isNativeReflectConstruct, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,SAAS;IACP,IAAI;QACF,IAAI,IAAI,CAAC,QAAQ,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC,SAAS,EAAE,EAAE,YAAa;IACtF,EAAE,OAAO,GAAG,CAAC;IACb,OAAO,CAAC,OAAO,OAAO,GAAG,4BAA4B,SAAS;QAC5D,OAAO,CAAC,CAAC;IACX,GAAG,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO;AACjF;AACA,OAAO,OAAO,GAAG,2BAA2B,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 879, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/construct.js"], "sourcesContent": ["var isNativeReflectConstruct = require(\"./isNativeReflectConstruct.js\");\nvar setPrototypeOf = require(\"./setPrototypeOf.js\");\nfunction _construct(t, e, r) {\n  if (isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments);\n  var o = [null];\n  o.push.apply(o, e);\n  var p = new (t.bind.apply(t, o))();\n  return r && setPrototypeOf(p, r.prototype), p;\n}\nmodule.exports = _construct, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AACJ,SAAS,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;IACzB,IAAI,4BAA4B,OAAO,QAAQ,SAAS,CAAC,KAAK,CAAC,MAAM;IACrE,IAAI,IAAI;QAAC;KAAK;IACd,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAChB,IAAI,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;IAC/B,OAAO,KAAK,eAAe,GAAG,EAAE,SAAS,GAAG;AAC9C;AACA,OAAO,OAAO,GAAG,YAAY,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 896, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/%40babel/runtime/helpers/wrapNativeSuper.js"], "sourcesContent": ["var getPrototypeOf = require(\"./getPrototypeOf.js\");\nvar setPrototypeOf = require(\"./setPrototypeOf.js\");\nvar isNativeFunction = require(\"./isNativeFunction.js\");\nvar construct = require(\"./construct.js\");\nfunction _wrapNativeSuper(t) {\n  var r = \"function\" == typeof Map ? new Map() : void 0;\n  return module.exports = _wrapNativeSuper = function _wrapNativeSuper(t) {\n    if (null === t || !isNativeFunction(t)) return t;\n    if (\"function\" != typeof t) throw new TypeError(\"Super expression must either be null or a function\");\n    if (void 0 !== r) {\n      if (r.has(t)) return r.get(t);\n      r.set(t, Wrapper);\n    }\n    function Wrapper() {\n      return construct(t, arguments, getPrototypeOf(this).constructor);\n    }\n    return Wrapper.prototype = Object.create(t.prototype, {\n      constructor: {\n        value: Wrapper,\n        enumerable: !1,\n        writable: !0,\n        configurable: !0\n      }\n    }), setPrototypeOf(Wrap<PERSON>, t);\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _wrapNativeSuper(t);\n}\nmodule.exports = _wrapNativeSuper, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,SAAS,iBAAiB,CAAC;IACzB,IAAI,IAAI,cAAc,OAAO,MAAM,IAAI,QAAQ,KAAK;IACpD,OAAO,OAAO,OAAO,GAAG,mBAAmB,SAAS,iBAAiB,CAAC;QACpE,IAAI,SAAS,KAAK,CAAC,iBAAiB,IAAI,OAAO;QAC/C,IAAI,cAAc,OAAO,GAAG,MAAM,IAAI,UAAU;QAChD,IAAI,KAAK,MAAM,GAAG;YAChB,IAAI,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;YAC3B,EAAE,GAAG,CAAC,GAAG;QACX;QACA,SAAS;YACP,OAAO,UAAU,GAAG,WAAW,eAAe,IAAI,EAAE,WAAW;QACjE;QACA,OAAO,QAAQ,SAAS,GAAG,OAAO,MAAM,CAAC,EAAE,SAAS,EAAE;YACpD,aAAa;gBACX,OAAO;gBACP,YAAY,CAAC;gBACb,UAAU,CAAC;gBACX,cAAc,CAAC;YACjB;QACF,IAAI,eAAe,SAAS;IAC9B,GAAG,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO,EAAE,iBAAiB;AACpG;AACA,OAAO,OAAO,GAAG,kBAAkB,OAAO,OAAO,CAAC,UAAU,GAAG,MAAM,OAAO,OAAO,CAAC,UAAU,GAAG,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 929, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next-auth/core/errors.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.UnsupportedStrategy = exports.UnknownError = exports.OAuthCallbackError = exports.MissingSecret = exports.MissingAuthorize = exports.MissingAdapterMethods = exports.MissingAdapter = exports.MissingAPIRoute = exports.InvalidCallbackUrl = exports.AccountNotLinkedError = void 0;\nexports.adapterErrorHandler = adapterErrorHandler;\nexports.capitalize = capitalize;\nexports.eventsErrorHandler = eventsErrorHandler;\nexports.upperSnake = upperSnake;\nvar _regenerator = _interopRequireDefault(require(\"@babel/runtime/regenerator\"));\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"@babel/runtime/helpers/asyncToGenerator\"));\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\nvar _classCallCheck2 = _interopRequireDefault(require(\"@babel/runtime/helpers/classCallCheck\"));\nvar _createClass2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createClass\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(require(\"@babel/runtime/helpers/possibleConstructorReturn\"));\nvar _getPrototypeOf2 = _interopRequireDefault(require(\"@babel/runtime/helpers/getPrototypeOf\"));\nvar _inherits2 = _interopRequireDefault(require(\"@babel/runtime/helpers/inherits\"));\nvar _wrapNativeSuper2 = _interopRequireDefault(require(\"@babel/runtime/helpers/wrapNativeSuper\"));\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar UnknownError = exports.UnknownError = function (_Error) {\n  function UnknownError(error) {\n    var _message;\n    var _this;\n    (0, _classCallCheck2.default)(this, UnknownError);\n    _this = _callSuper(this, UnknownError, [(_message = error === null || error === void 0 ? void 0 : error.message) !== null && _message !== void 0 ? _message : error]);\n    _this.name = \"UnknownError\";\n    _this.code = error.code;\n    if (error instanceof Error) {\n      _this.stack = error.stack;\n    }\n    return _this;\n  }\n  (0, _inherits2.default)(UnknownError, _Error);\n  return (0, _createClass2.default)(UnknownError, [{\n    key: \"toJSON\",\n    value: function toJSON() {\n      return {\n        name: this.name,\n        message: this.message,\n        stack: this.stack\n      };\n    }\n  }]);\n}((0, _wrapNativeSuper2.default)(Error));\nvar OAuthCallbackError = exports.OAuthCallbackError = function (_UnknownError) {\n  function OAuthCallbackError() {\n    var _this2;\n    (0, _classCallCheck2.default)(this, OAuthCallbackError);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this2 = _callSuper(this, OAuthCallbackError, [].concat(args));\n    (0, _defineProperty2.default)(_this2, \"name\", \"OAuthCallbackError\");\n    return _this2;\n  }\n  (0, _inherits2.default)(OAuthCallbackError, _UnknownError);\n  return (0, _createClass2.default)(OAuthCallbackError);\n}(UnknownError);\nvar AccountNotLinkedError = exports.AccountNotLinkedError = function (_UnknownError2) {\n  function AccountNotLinkedError() {\n    var _this3;\n    (0, _classCallCheck2.default)(this, AccountNotLinkedError);\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    _this3 = _callSuper(this, AccountNotLinkedError, [].concat(args));\n    (0, _defineProperty2.default)(_this3, \"name\", \"AccountNotLinkedError\");\n    return _this3;\n  }\n  (0, _inherits2.default)(AccountNotLinkedError, _UnknownError2);\n  return (0, _createClass2.default)(AccountNotLinkedError);\n}(UnknownError);\nvar MissingAPIRoute = exports.MissingAPIRoute = function (_UnknownError3) {\n  function MissingAPIRoute() {\n    var _this4;\n    (0, _classCallCheck2.default)(this, MissingAPIRoute);\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    _this4 = _callSuper(this, MissingAPIRoute, [].concat(args));\n    (0, _defineProperty2.default)(_this4, \"name\", \"MissingAPIRouteError\");\n    (0, _defineProperty2.default)(_this4, \"code\", \"MISSING_NEXTAUTH_API_ROUTE_ERROR\");\n    return _this4;\n  }\n  (0, _inherits2.default)(MissingAPIRoute, _UnknownError3);\n  return (0, _createClass2.default)(MissingAPIRoute);\n}(UnknownError);\nvar MissingSecret = exports.MissingSecret = function (_UnknownError4) {\n  function MissingSecret() {\n    var _this5;\n    (0, _classCallCheck2.default)(this, MissingSecret);\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    _this5 = _callSuper(this, MissingSecret, [].concat(args));\n    (0, _defineProperty2.default)(_this5, \"name\", \"MissingSecretError\");\n    (0, _defineProperty2.default)(_this5, \"code\", \"NO_SECRET\");\n    return _this5;\n  }\n  (0, _inherits2.default)(MissingSecret, _UnknownError4);\n  return (0, _createClass2.default)(MissingSecret);\n}(UnknownError);\nvar MissingAuthorize = exports.MissingAuthorize = function (_UnknownError5) {\n  function MissingAuthorize() {\n    var _this6;\n    (0, _classCallCheck2.default)(this, MissingAuthorize);\n    for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n      args[_key5] = arguments[_key5];\n    }\n    _this6 = _callSuper(this, MissingAuthorize, [].concat(args));\n    (0, _defineProperty2.default)(_this6, \"name\", \"MissingAuthorizeError\");\n    (0, _defineProperty2.default)(_this6, \"code\", \"CALLBACK_CREDENTIALS_HANDLER_ERROR\");\n    return _this6;\n  }\n  (0, _inherits2.default)(MissingAuthorize, _UnknownError5);\n  return (0, _createClass2.default)(MissingAuthorize);\n}(UnknownError);\nvar MissingAdapter = exports.MissingAdapter = function (_UnknownError6) {\n  function MissingAdapter() {\n    var _this7;\n    (0, _classCallCheck2.default)(this, MissingAdapter);\n    for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n      args[_key6] = arguments[_key6];\n    }\n    _this7 = _callSuper(this, MissingAdapter, [].concat(args));\n    (0, _defineProperty2.default)(_this7, \"name\", \"MissingAdapterError\");\n    (0, _defineProperty2.default)(_this7, \"code\", \"EMAIL_REQUIRES_ADAPTER_ERROR\");\n    return _this7;\n  }\n  (0, _inherits2.default)(MissingAdapter, _UnknownError6);\n  return (0, _createClass2.default)(MissingAdapter);\n}(UnknownError);\nvar MissingAdapterMethods = exports.MissingAdapterMethods = function (_UnknownError7) {\n  function MissingAdapterMethods() {\n    var _this8;\n    (0, _classCallCheck2.default)(this, MissingAdapterMethods);\n    for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n      args[_key7] = arguments[_key7];\n    }\n    _this8 = _callSuper(this, MissingAdapterMethods, [].concat(args));\n    (0, _defineProperty2.default)(_this8, \"name\", \"MissingAdapterMethodsError\");\n    (0, _defineProperty2.default)(_this8, \"code\", \"MISSING_ADAPTER_METHODS_ERROR\");\n    return _this8;\n  }\n  (0, _inherits2.default)(MissingAdapterMethods, _UnknownError7);\n  return (0, _createClass2.default)(MissingAdapterMethods);\n}(UnknownError);\nvar UnsupportedStrategy = exports.UnsupportedStrategy = function (_UnknownError8) {\n  function UnsupportedStrategy() {\n    var _this9;\n    (0, _classCallCheck2.default)(this, UnsupportedStrategy);\n    for (var _len8 = arguments.length, args = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {\n      args[_key8] = arguments[_key8];\n    }\n    _this9 = _callSuper(this, UnsupportedStrategy, [].concat(args));\n    (0, _defineProperty2.default)(_this9, \"name\", \"UnsupportedStrategyError\");\n    (0, _defineProperty2.default)(_this9, \"code\", \"CALLBACK_CREDENTIALS_JWT_ERROR\");\n    return _this9;\n  }\n  (0, _inherits2.default)(UnsupportedStrategy, _UnknownError8);\n  return (0, _createClass2.default)(UnsupportedStrategy);\n}(UnknownError);\nvar InvalidCallbackUrl = exports.InvalidCallbackUrl = function (_UnknownError9) {\n  function InvalidCallbackUrl() {\n    var _this10;\n    (0, _classCallCheck2.default)(this, InvalidCallbackUrl);\n    for (var _len9 = arguments.length, args = new Array(_len9), _key9 = 0; _key9 < _len9; _key9++) {\n      args[_key9] = arguments[_key9];\n    }\n    _this10 = _callSuper(this, InvalidCallbackUrl, [].concat(args));\n    (0, _defineProperty2.default)(_this10, \"name\", \"InvalidCallbackUrl\");\n    (0, _defineProperty2.default)(_this10, \"code\", \"INVALID_CALLBACK_URL_ERROR\");\n    return _this10;\n  }\n  (0, _inherits2.default)(InvalidCallbackUrl, _UnknownError9);\n  return (0, _createClass2.default)(InvalidCallbackUrl);\n}(UnknownError);\nfunction upperSnake(s) {\n  return s.replace(/([A-Z])/g, \"_$1\").toUpperCase();\n}\nfunction capitalize(s) {\n  return \"\".concat(s[0].toUpperCase()).concat(s.slice(1));\n}\nfunction eventsErrorHandler(methods, logger) {\n  return Object.keys(methods).reduce(function (acc, name) {\n    acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee() {\n      var method,\n        _args = arguments;\n      return _regenerator.default.wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _context.prev = 0;\n            method = methods[name];\n            _context.next = 4;\n            return method.apply(void 0, _args);\n          case 4:\n            return _context.abrupt(\"return\", _context.sent);\n          case 7:\n            _context.prev = 7;\n            _context.t0 = _context[\"catch\"](0);\n            logger.error(\"\".concat(upperSnake(name), \"_EVENT_ERROR\"), _context.t0);\n          case 10:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee, null, [[0, 7]]);\n    }));\n    return acc;\n  }, {});\n}\nfunction adapterErrorHandler(adapter, logger) {\n  if (!adapter) return;\n  return Object.keys(adapter).reduce(function (acc, name) {\n    acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee2() {\n      var _len10,\n        args,\n        _key10,\n        method,\n        e,\n        _args2 = arguments;\n      return _regenerator.default.wrap(function _callee2$(_context2) {\n        while (1) switch (_context2.prev = _context2.next) {\n          case 0:\n            _context2.prev = 0;\n            for (_len10 = _args2.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++) {\n              args[_key10] = _args2[_key10];\n            }\n            logger.debug(\"adapter_\".concat(name), {\n              args: args\n            });\n            method = adapter[name];\n            _context2.next = 6;\n            return method.apply(void 0, args);\n          case 6:\n            return _context2.abrupt(\"return\", _context2.sent);\n          case 9:\n            _context2.prev = 9;\n            _context2.t0 = _context2[\"catch\"](0);\n            logger.error(\"adapter_error_\".concat(name), _context2.t0);\n            e = new UnknownError(_context2.t0);\n            e.name = \"\".concat(capitalize(name), \"Error\");\n            throw e;\n          case 15:\n          case \"end\":\n            return _context2.stop();\n        }\n      }, _callee2, null, [[0, 9]]);\n    }));\n    return acc;\n  }, {});\n}"], "names": [], "mappings": "AAEA,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,mBAAmB,GAAG,QAAQ,YAAY,GAAG,QAAQ,kBAAkB,GAAG,QAAQ,aAAa,GAAG,QAAQ,gBAAgB,GAAG,QAAQ,qBAAqB,GAAG,QAAQ,cAAc,GAAG,QAAQ,eAAe,GAAG,QAAQ,kBAAkB,GAAG,QAAQ,qBAAqB,GAAG,KAAK;AAC1R,QAAQ,mBAAmB,GAAG;AAC9B,QAAQ,UAAU,GAAG;AACrB,QAAQ,kBAAkB,GAAG;AAC7B,QAAQ,UAAU,GAAG;AACrB,IAAI,eAAe;AACnB,IAAI,qBAAqB;AACzB,IAAI,mBAAmB;AACvB,IAAI,mBAAmB;AACvB,IAAI,gBAAgB;AACpB,IAAI,8BAA8B;AAClC,IAAI,mBAAmB;AACvB,IAAI,aAAa;AACjB,IAAI,oBAAoB;AACxB,SAAS,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,IAAI,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,CAAC,GAAG,4BAA4B,OAAO,EAAE,GAAG,8BAA8B,QAAQ,SAAS,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,GAAG,iBAAiB,OAAO,EAAE,GAAG,WAAW,IAAI,EAAE,KAAK,CAAC,GAAG;AAAK;AACpP,SAAS;IAA8B,IAAI;QAAE,IAAI,IAAI,CAAC,QAAQ,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC,SAAS,EAAE,EAAE,YAAa;IAAK,EAAE,OAAO,GAAG,CAAC;IAAE,OAAO,CAAC,4BAA4B,SAAS;QAA8B,OAAO,CAAC,CAAC;IAAG,CAAC;AAAK;AAClP,IAAI,eAAe,QAAQ,YAAY,GAAG,SAAU,MAAM;IACxD,SAAS,aAAa,KAAK;QACzB,IAAI;QACJ,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,QAAQ,WAAW,IAAI,EAAE,cAAc;YAAC,CAAC,WAAW,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,OAAO,MAAM,QAAQ,aAAa,KAAK,IAAI,WAAW;SAAM;QACpK,MAAM,IAAI,GAAG;QACb,MAAM,IAAI,GAAG,MAAM,IAAI;QACvB,IAAI,iBAAiB,OAAO;YAC1B,MAAM,KAAK,GAAG,MAAM,KAAK;QAC3B;QACA,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,cAAc;IACtC,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE,cAAc;QAAC;YAC/C,KAAK;YACL,OAAO,SAAS;gBACd,OAAO;oBACL,MAAM,IAAI,CAAC,IAAI;oBACf,SAAS,IAAI,CAAC,OAAO;oBACrB,OAAO,IAAI,CAAC,KAAK;gBACnB;YACF;QACF;KAAE;AACJ,EAAE,CAAC,GAAG,kBAAkB,OAAO,EAAE;AACjC,IAAI,qBAAqB,QAAQ,kBAAkB,GAAG,SAAU,aAAa;IAC3E,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;YACvF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAC9B;QACA,SAAS,WAAW,IAAI,EAAE,oBAAoB,EAAE,CAAC,MAAM,CAAC;QACxD,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,oBAAoB;IAC5C,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,wBAAwB,QAAQ,qBAAqB,GAAG,SAAU,cAAc;IAClF,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,uBAAuB,EAAE,CAAC,MAAM,CAAC;QAC3D,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,uBAAuB;IAC/C,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,kBAAkB,QAAQ,eAAe,GAAG,SAAU,cAAc;IACtE,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,iBAAiB,EAAE,CAAC,MAAM,CAAC;QACrD,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,iBAAiB;IACzC,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,gBAAgB,QAAQ,aAAa,GAAG,SAAU,cAAc;IAClE,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,eAAe,EAAE,CAAC,MAAM,CAAC;QACnD,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,eAAe;IACvC,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,mBAAmB,QAAQ,gBAAgB,GAAG,SAAU,cAAc;IACxE,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,kBAAkB,EAAE,CAAC,MAAM,CAAC;QACtD,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,kBAAkB;IAC1C,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,iBAAiB,QAAQ,cAAc,GAAG,SAAU,cAAc;IACpE,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,gBAAgB,EAAE,CAAC,MAAM,CAAC;QACpD,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,gBAAgB;IACxC,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,wBAAwB,QAAQ,qBAAqB,GAAG,SAAU,cAAc;IAClF,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,uBAAuB,EAAE,CAAC,MAAM,CAAC;QAC3D,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,uBAAuB;IAC/C,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,sBAAsB,QAAQ,mBAAmB,GAAG,SAAU,cAAc;IAC9E,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,SAAS,WAAW,IAAI,EAAE,qBAAqB,EAAE,CAAC,MAAM,CAAC;QACzD,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,CAAC,GAAG,iBAAiB,OAAO,EAAE,QAAQ,QAAQ;QAC9C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,qBAAqB;IAC7C,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,IAAI,qBAAqB,QAAQ,kBAAkB,GAAG,SAAU,cAAc;IAC5E,SAAS;QACP,IAAI;QACJ,CAAC,GAAG,iBAAiB,OAAO,EAAE,IAAI,EAAE;QACpC,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC7F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAChC;QACA,UAAU,WAAW,IAAI,EAAE,oBAAoB,EAAE,CAAC,MAAM,CAAC;QACzD,CAAC,GAAG,iBAAiB,OAAO,EAAE,SAAS,QAAQ;QAC/C,CAAC,GAAG,iBAAiB,OAAO,EAAE,SAAS,QAAQ;QAC/C,OAAO;IACT;IACA,CAAC,GAAG,WAAW,OAAO,EAAE,oBAAoB;IAC5C,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE;AACpC,EAAE;AACF,SAAS,WAAW,CAAC;IACnB,OAAO,EAAE,OAAO,CAAC,YAAY,OAAO,WAAW;AACjD;AACA,SAAS,WAAW,CAAC;IACnB,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,WAAW,IAAI,MAAM,CAAC,EAAE,KAAK,CAAC;AACtD;AACA,SAAS,mBAAmB,OAAO,EAAE,MAAM;IACzC,OAAO,OAAO,IAAI,CAAC,SAAS,MAAM,CAAC,SAAU,GAAG,EAAE,IAAI;QACpD,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS;YAC7E,IAAI,QACF,QAAQ;YACV,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,SAAS,QAAQ;gBACzD,MAAO,EAAG,OAAQ,SAAS,IAAI,GAAG,SAAS,IAAI;oBAC7C,KAAK;wBACH,SAAS,IAAI,GAAG;wBAChB,SAAS,OAAO,CAAC,KAAK;wBACtB,SAAS,IAAI,GAAG;wBAChB,OAAO,OAAO,KAAK,CAAC,KAAK,GAAG;oBAC9B,KAAK;wBACH,OAAO,SAAS,MAAM,CAAC,UAAU,SAAS,IAAI;oBAChD,KAAK;wBACH,SAAS,IAAI,GAAG;wBAChB,SAAS,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC;wBAChC,OAAO,KAAK,CAAC,GAAG,MAAM,CAAC,WAAW,OAAO,iBAAiB,SAAS,EAAE;oBACvE,KAAK;oBACL,KAAK;wBACH,OAAO,SAAS,IAAI;gBACxB;YACF,GAAG,SAAS,MAAM;gBAAC;oBAAC;oBAAG;iBAAE;aAAC;QAC5B;QACA,OAAO;IACT,GAAG,CAAC;AACN;AACA,SAAS,oBAAoB,OAAO,EAAE,MAAM;IAC1C,IAAI,CAAC,SAAS;IACd,OAAO,OAAO,IAAI,CAAC,SAAS,MAAM,CAAC,SAAU,GAAG,EAAE,IAAI;QACpD,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS;YAC7E,IAAI,QACF,MACA,QACA,QACA,GACA,SAAS;YACX,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,UAAU,SAAS;gBAC3D,MAAO,EAAG,OAAQ,UAAU,IAAI,GAAG,UAAU,IAAI;oBAC/C,KAAK;wBACH,UAAU,IAAI,GAAG;wBACjB,IAAK,SAAS,OAAO,MAAM,EAAE,OAAO,IAAI,MAAM,SAAS,SAAS,GAAG,SAAS,QAAQ,SAAU;4BAC5F,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO;wBAC/B;wBACA,OAAO,KAAK,CAAC,WAAW,MAAM,CAAC,OAAO;4BACpC,MAAM;wBACR;wBACA,SAAS,OAAO,CAAC,KAAK;wBACtB,UAAU,IAAI,GAAG;wBACjB,OAAO,OAAO,KAAK,CAAC,KAAK,GAAG;oBAC9B,KAAK;wBACH,OAAO,UAAU,MAAM,CAAC,UAAU,UAAU,IAAI;oBAClD,KAAK;wBACH,UAAU,IAAI,GAAG;wBACjB,UAAU,EAAE,GAAG,SAAS,CAAC,QAAQ,CAAC;wBAClC,OAAO,KAAK,CAAC,iBAAiB,MAAM,CAAC,OAAO,UAAU,EAAE;wBACxD,IAAI,IAAI,aAAa,UAAU,EAAE;wBACjC,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC,WAAW,OAAO;wBACrC,MAAM;oBACR,KAAK;oBACL,KAAK;wBACH,OAAO,UAAU,IAAI;gBACzB;YACF,GAAG,UAAU,MAAM;gBAAC;oBAAC;oBAAG;iBAAE;aAAC;QAC7B;QACA,OAAO;IACT,GAAG,CAAC;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1204, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next-auth/utils/logger.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nexports.proxyLogger = proxyLogger;\nexports.setLogger = setLogger;\nvar _regenerator = _interopRequireDefault(require(\"@babel/runtime/regenerator\"));\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"@babel/runtime/helpers/asyncToGenerator\"));\nvar _errors = require(\"../core/errors\");\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction formatError(o) {\n  if (o instanceof Error && !(o instanceof _errors.UnknownError)) {\n    return {\n      message: o.message,\n      stack: o.stack,\n      name: o.name\n    };\n  }\n  if (hasErrorProperty(o)) {\n    var _o$message;\n    o.error = formatError(o.error);\n    o.message = (_o$message = o.message) !== null && _o$message !== void 0 ? _o$message : o.error.message;\n  }\n  return o;\n}\nfunction hasErrorProperty(x) {\n  return !!(x !== null && x !== void 0 && x.error);\n}\nvar _logger = {\n  error: function error(code, metadata) {\n    metadata = formatError(metadata);\n    console.error(\"[next-auth][error][\".concat(code, \"]\"), \"\\nhttps://next-auth.js.org/errors#\".concat(code.toLowerCase()), metadata.message, metadata);\n  },\n  warn: function warn(code) {\n    console.warn(\"[next-auth][warn][\".concat(code, \"]\"), \"\\nhttps://next-auth.js.org/warnings#\".concat(code.toLowerCase()));\n  },\n  debug: function debug(code, metadata) {\n    console.log(\"[next-auth][debug][\".concat(code, \"]\"), metadata);\n  }\n};\nfunction setLogger() {\n  var newLogger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var debug = arguments.length > 1 ? arguments[1] : undefined;\n  if (!debug) _logger.debug = function () {};\n  if (newLogger.error) _logger.error = newLogger.error;\n  if (newLogger.warn) _logger.warn = newLogger.warn;\n  if (newLogger.debug) _logger.debug = newLogger.debug;\n}\nvar _default = exports.default = _logger;\nfunction proxyLogger() {\n  var logger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : _logger;\n  var basePath = arguments.length > 1 ? arguments[1] : undefined;\n  try {\n    if (typeof window === \"undefined\") {\n      return logger;\n    }\n    var clientLogger = {};\n    var _loop = function _loop(level) {\n      clientLogger[level] = function () {\n        var _ref = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee(code, metadata) {\n          var url, body;\n          return _regenerator.default.wrap(function _callee$(_context) {\n            while (1) switch (_context.prev = _context.next) {\n              case 0:\n                _logger[level](code, metadata);\n                if (level === \"error\") {\n                  metadata = formatError(metadata);\n                }\n                ;\n                metadata.client = true;\n                url = \"\".concat(basePath, \"/_log\");\n                body = new URLSearchParams(_objectSpread({\n                  level: level,\n                  code: code\n                }, metadata));\n                if (!navigator.sendBeacon) {\n                  _context.next = 8;\n                  break;\n                }\n                return _context.abrupt(\"return\", navigator.sendBeacon(url, body));\n              case 8:\n                _context.next = 10;\n                return fetch(url, {\n                  method: \"POST\",\n                  body: body,\n                  keepalive: true\n                });\n              case 10:\n                return _context.abrupt(\"return\", _context.sent);\n              case 11:\n              case \"end\":\n                return _context.stop();\n            }\n          }, _callee);\n        }));\n        return function (_x, _x2) {\n          return _ref.apply(this, arguments);\n        };\n      }();\n    };\n    for (var level in logger) {\n      _loop(level);\n    }\n    return clientLogger;\n  } catch (_unused) {\n    return _logger;\n  }\n}"], "names": [], "mappings": "AAEA,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG,KAAK;AACvB,QAAQ,WAAW,GAAG;AACtB,QAAQ,SAAS,GAAG;AACpB,IAAI,eAAe;AACnB,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI;AACJ,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,CAAC,GAAG,iBAAiB,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACpc,SAAS,YAAY,CAAC;IACpB,IAAI,aAAa,SAAS,CAAC,CAAC,aAAa,QAAQ,YAAY,GAAG;QAC9D,OAAO;YACL,SAAS,EAAE,OAAO;YAClB,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,IAAI;QACd;IACF;IACA,IAAI,iBAAiB,IAAI;QACvB,IAAI;QACJ,EAAE,KAAK,GAAG,YAAY,EAAE,KAAK;QAC7B,EAAE,OAAO,GAAG,CAAC,aAAa,EAAE,OAAO,MAAM,QAAQ,eAAe,KAAK,IAAI,aAAa,EAAE,KAAK,CAAC,OAAO;IACvG;IACA,OAAO;AACT;AACA,SAAS,iBAAiB,CAAC;IACzB,OAAO,CAAC,CAAC,CAAC,MAAM,QAAQ,MAAM,KAAK,KAAK,EAAE,KAAK;AACjD;AACA,IAAI,UAAU;IACZ,OAAO,SAAS,MAAM,IAAI,EAAE,QAAQ;QAClC,WAAW,YAAY;QACvB,QAAQ,KAAK,CAAC,sBAAsB,MAAM,CAAC,MAAM,MAAM,qCAAqC,MAAM,CAAC,KAAK,WAAW,KAAK,SAAS,OAAO,EAAE;IAC5I;IACA,MAAM,SAAS,KAAK,IAAI;QACtB,QAAQ,IAAI,CAAC,qBAAqB,MAAM,CAAC,MAAM,MAAM,uCAAuC,MAAM,CAAC,KAAK,WAAW;IACrH;IACA,OAAO,SAAS,MAAM,IAAI,EAAE,QAAQ;QAClC,QAAQ,GAAG,CAAC,sBAAsB,MAAM,CAAC,MAAM,MAAM;IACvD;AACF;AACA,SAAS;IACP,IAAI,YAAY,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,CAAC;IACrF,IAAI,QAAQ,UAAU,MAAM,GAAG,IAAI,SAAS,CAAC,EAAE,GAAG;IAClD,IAAI,CAAC,OAAO,QAAQ,KAAK,GAAG,YAAa;IACzC,IAAI,UAAU,KAAK,EAAE,QAAQ,KAAK,GAAG,UAAU,KAAK;IACpD,IAAI,UAAU,IAAI,EAAE,QAAQ,IAAI,GAAG,UAAU,IAAI;IACjD,IAAI,UAAU,KAAK,EAAE,QAAQ,KAAK,GAAG,UAAU,KAAK;AACtD;AACA,IAAI,WAAW,QAAQ,OAAO,GAAG;AACjC,SAAS;IACP,IAAI,SAAS,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IACjF,IAAI,WAAW,UAAU,MAAM,GAAG,IAAI,SAAS,CAAC,EAAE,GAAG;IACrD,IAAI;QACF,IAAI,OAAO,WAAW,aAAa;YACjC,OAAO;QACT;QACA,IAAI,eAAe,CAAC;QACpB,IAAI,QAAQ,SAAS,MAAM,KAAK;YAC9B,YAAY,CAAC,MAAM,GAAG;gBACpB,IAAI,OAAO,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,QAAQ,IAAI,EAAE,QAAQ;oBAClG,IAAI,KAAK;oBACT,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,SAAS,QAAQ;wBACzD,MAAO,EAAG,OAAQ,SAAS,IAAI,GAAG,SAAS,IAAI;4BAC7C,KAAK;gCACH,OAAO,CAAC,MAAM,CAAC,MAAM;gCACrB,IAAI,UAAU,SAAS;oCACrB,WAAW,YAAY;gCACzB;;gCAEA,SAAS,MAAM,GAAG;gCAClB,MAAM,GAAG,MAAM,CAAC,UAAU;gCAC1B,OAAO,IAAI,gBAAgB,cAAc;oCACvC,OAAO;oCACP,MAAM;gCACR,GAAG;gCACH,IAAI,CAAC,UAAU,UAAU,EAAE;oCACzB,SAAS,IAAI,GAAG;oCAChB;gCACF;gCACA,OAAO,SAAS,MAAM,CAAC,UAAU,UAAU,UAAU,CAAC,KAAK;4BAC7D,KAAK;gCACH,SAAS,IAAI,GAAG;gCAChB,OAAO,MAAM,KAAK;oCAChB,QAAQ;oCACR,MAAM;oCACN,WAAW;gCACb;4BACF,KAAK;gCACH,OAAO,SAAS,MAAM,CAAC,UAAU,SAAS,IAAI;4BAChD,KAAK;4BACL,KAAK;gCACH,OAAO,SAAS,IAAI;wBACxB;oBACF,GAAG;gBACL;gBACA,OAAO,SAAU,EAAE,EAAE,GAAG;oBACtB,OAAO,KAAK,KAAK,CAAC,IAAI,EAAE;gBAC1B;YACF;QACF;QACA,IAAK,IAAI,SAAS,OAAQ;YACxB,MAAM;QACR;QACA,OAAO;IACT,EAAE,OAAO,SAAS;QAChB,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1340, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next-auth/utils/parse-url.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = parseUrl;\nfunction parseUrl(url) {\n  var _url2;\n  const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n  if (url && !url.startsWith(\"http\")) {\n    url = `https://${url}`;\n  }\n  const _url = new URL((_url2 = url) !== null && _url2 !== void 0 ? _url2 : defaultUrl);\n  const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname).replace(/\\/$/, \"\");\n  const base = `${_url.origin}${path}`;\n  return {\n    origin: _url.origin,\n    host: _url.host,\n    path,\n    base,\n    toString: () => base\n  };\n}"], "names": [], "mappings": "AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,OAAO,GAAG;AAClB,SAAS,SAAS,GAAG;IACnB,IAAI;IACJ,MAAM,aAAa,IAAI,IAAI;IAC3B,IAAI,OAAO,CAAC,IAAI,UAAU,CAAC,SAAS;QAClC,MAAM,AAAC,WAAc,OAAJ;IACnB;IACA,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ,UAAU,KAAK,IAAI,QAAQ;IAC1E,MAAM,OAAO,CAAC,KAAK,QAAQ,KAAK,MAAM,WAAW,QAAQ,GAAG,KAAK,QAAQ,EAAE,OAAO,CAAC,OAAO;IAC1F,MAAM,OAAO,AAAC,GAAgB,OAAd,KAAK,MAAM,EAAQ,OAAL;IAC9B,OAAO;QACL,QAAQ,KAAK,MAAM;QACnB,MAAM,KAAK,IAAI;QACf;QACA;QACA,UAAU,IAAM;IAClB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1367, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next-auth/client/_utils.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.BroadcastChannel = BroadcastChannel;\nexports.apiBaseUrl = apiBaseUrl;\nexports.fetchData = fetchData;\nexports.now = now;\nvar _regenerator = _interopRequireDefault(require(\"@babel/runtime/regenerator\"));\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"@babel/runtime/helpers/asyncToGenerator\"));\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction fetchData(_x, _x2, _x3) {\n  return _fetchData.apply(this, arguments);\n}\nfunction _fetchData() {\n  _fetchData = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee(path, __NEXTAUTH, logger) {\n    var _ref,\n      ctx,\n      _ref$req,\n      req,\n      url,\n      _req$headers,\n      options,\n      res,\n      data,\n      _args = arguments;\n    return _regenerator.default.wrap(function _callee$(_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          _ref = _args.length > 3 && _args[3] !== undefined ? _args[3] : {}, ctx = _ref.ctx, _ref$req = _ref.req, req = _ref$req === void 0 ? ctx === null || ctx === void 0 ? void 0 : ctx.req : _ref$req;\n          url = \"\".concat(apiBaseUrl(__NEXTAUTH), \"/\").concat(path);\n          _context.prev = 2;\n          options = {\n            headers: _objectSpread({\n              \"Content-Type\": \"application/json\"\n            }, req !== null && req !== void 0 && (_req$headers = req.headers) !== null && _req$headers !== void 0 && _req$headers.cookie ? {\n              cookie: req.headers.cookie\n            } : {})\n          };\n          if (req !== null && req !== void 0 && req.body) {\n            options.body = JSON.stringify(req.body);\n            options.method = \"POST\";\n          }\n          _context.next = 7;\n          return fetch(url, options);\n        case 7:\n          res = _context.sent;\n          _context.next = 10;\n          return res.json();\n        case 10:\n          data = _context.sent;\n          if (res.ok) {\n            _context.next = 13;\n            break;\n          }\n          throw data;\n        case 13:\n          return _context.abrupt(\"return\", Object.keys(data).length > 0 ? data : null);\n        case 16:\n          _context.prev = 16;\n          _context.t0 = _context[\"catch\"](2);\n          logger.error(\"CLIENT_FETCH_ERROR\", {\n            error: _context.t0,\n            url: url\n          });\n          return _context.abrupt(\"return\", null);\n        case 20:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee, null, [[2, 16]]);\n  }));\n  return _fetchData.apply(this, arguments);\n}\nfunction apiBaseUrl(__NEXTAUTH) {\n  if (typeof window === \"undefined\") {\n    return \"\".concat(__NEXTAUTH.baseUrlServer).concat(__NEXTAUTH.basePathServer);\n  }\n  return __NEXTAUTH.basePath;\n}\nfunction now() {\n  return Math.floor(Date.now() / 1000);\n}\nfunction BroadcastChannel() {\n  var name = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"nextauth.message\";\n  return {\n    receive: function receive(onReceive) {\n      var handler = function handler(event) {\n        var _event$newValue;\n        if (event.key !== name) return;\n        var message = JSON.parse((_event$newValue = event.newValue) !== null && _event$newValue !== void 0 ? _event$newValue : \"{}\");\n        if ((message === null || message === void 0 ? void 0 : message.event) !== \"session\" || !(message !== null && message !== void 0 && message.data)) return;\n        onReceive(message);\n      };\n      window.addEventListener(\"storage\", handler);\n      return function () {\n        return window.removeEventListener(\"storage\", handler);\n      };\n    },\n    post: function post(message) {\n      if (typeof window === \"undefined\") return;\n      try {\n        localStorage.setItem(name, JSON.stringify(_objectSpread(_objectSpread({}, message), {}, {\n          timestamp: now()\n        })));\n      } catch (_unused) {}\n    }\n  };\n}"], "names": [], "mappings": "AAEA,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,QAAQ,gBAAgB,GAAG;AAC3B,QAAQ,UAAU,GAAG;AACrB,QAAQ,SAAS,GAAG;AACpB,QAAQ,GAAG,GAAG;AACd,IAAI,eAAe;AACnB,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,CAAC,GAAG,iBAAiB,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACpc,SAAS,UAAU,EAAE,EAAE,GAAG,EAAE,GAAG;IAC7B,OAAO,WAAW,KAAK,CAAC,IAAI,EAAE;AAChC;AACA,SAAS;IACP,aAAa,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,QAAQ,IAAI,EAAE,UAAU,EAAE,MAAM;QAC9G,IAAI,MACF,KACA,UACA,KACA,KACA,cACA,SACA,KACA,MACA,QAAQ;QACV,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,SAAS,QAAQ;YACzD,MAAO,EAAG,OAAQ,SAAS,IAAI,GAAG,SAAS,IAAI;gBAC7C,KAAK;oBACH,OAAO,MAAM,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,KAAK,YAAY,KAAK,CAAC,EAAE,GAAG,CAAC,GAAG,MAAM,KAAK,GAAG,EAAE,WAAW,KAAK,GAAG,EAAE,MAAM,aAAa,KAAK,IAAI,QAAQ,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,GAAG;oBACxL,MAAM,GAAG,MAAM,CAAC,WAAW,aAAa,KAAK,MAAM,CAAC;oBACpD,SAAS,IAAI,GAAG;oBAChB,UAAU;wBACR,SAAS,cAAc;4BACrB,gBAAgB;wBAClB,GAAG,QAAQ,QAAQ,QAAQ,KAAK,KAAK,CAAC,eAAe,IAAI,OAAO,MAAM,QAAQ,iBAAiB,KAAK,KAAK,aAAa,MAAM,GAAG;4BAC7H,QAAQ,IAAI,OAAO,CAAC,MAAM;wBAC5B,IAAI,CAAC;oBACP;oBACA,IAAI,QAAQ,QAAQ,QAAQ,KAAK,KAAK,IAAI,IAAI,EAAE;wBAC9C,QAAQ,IAAI,GAAG,KAAK,SAAS,CAAC,IAAI,IAAI;wBACtC,QAAQ,MAAM,GAAG;oBACnB;oBACA,SAAS,IAAI,GAAG;oBAChB,OAAO,MAAM,KAAK;gBACpB,KAAK;oBACH,MAAM,SAAS,IAAI;oBACnB,SAAS,IAAI,GAAG;oBAChB,OAAO,IAAI,IAAI;gBACjB,KAAK;oBACH,OAAO,SAAS,IAAI;oBACpB,IAAI,IAAI,EAAE,EAAE;wBACV,SAAS,IAAI,GAAG;wBAChB;oBACF;oBACA,MAAM;gBACR,KAAK;oBACH,OAAO,SAAS,MAAM,CAAC,UAAU,OAAO,IAAI,CAAC,MAAM,MAAM,GAAG,IAAI,OAAO;gBACzE,KAAK;oBACH,SAAS,IAAI,GAAG;oBAChB,SAAS,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC;oBAChC,OAAO,KAAK,CAAC,sBAAsB;wBACjC,OAAO,SAAS,EAAE;wBAClB,KAAK;oBACP;oBACA,OAAO,SAAS,MAAM,CAAC,UAAU;gBACnC,KAAK;gBACL,KAAK;oBACH,OAAO,SAAS,IAAI;YACxB;QACF,GAAG,SAAS,MAAM;YAAC;gBAAC;gBAAG;aAAG;SAAC;IAC7B;IACA,OAAO,WAAW,KAAK,CAAC,IAAI,EAAE;AAChC;AACA,SAAS,WAAW,UAAU;IAC5B,IAAI,OAAO,WAAW,aAAa;QACjC,OAAO,GAAG,MAAM,CAAC,WAAW,aAAa,EAAE,MAAM,CAAC,WAAW,cAAc;IAC7E;IACA,OAAO,WAAW,QAAQ;AAC5B;AACA,SAAS;IACP,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;AACjC;AACA,SAAS;IACP,IAAI,OAAO,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IAC/E,OAAO;QACL,SAAS,SAAS,QAAQ,SAAS;YACjC,IAAI,UAAU,SAAS,QAAQ,KAAK;gBAClC,IAAI;gBACJ,IAAI,MAAM,GAAG,KAAK,MAAM;gBACxB,IAAI,UAAU,KAAK,KAAK,CAAC,CAAC,kBAAkB,MAAM,QAAQ,MAAM,QAAQ,oBAAoB,KAAK,IAAI,kBAAkB;gBACvH,IAAI,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,MAAM,aAAa,CAAC,CAAC,YAAY,QAAQ,YAAY,KAAK,KAAK,QAAQ,IAAI,GAAG;gBAClJ,UAAU;YACZ;YACA,OAAO,gBAAgB,CAAC,WAAW;YACnC,OAAO;gBACL,OAAO,OAAO,mBAAmB,CAAC,WAAW;YAC/C;QACF;QACA,MAAM,SAAS,KAAK,OAAO;YACzB,IAAI,OAAO,WAAW,aAAa;YACnC,IAAI;gBACF,aAAa,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,cAAc,cAAc,CAAC,GAAG,UAAU,CAAC,GAAG;oBACtF,WAAW;gBACb;YACF,EAAE,OAAO,SAAS,CAAC;QACrB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1499, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next-auth/react/types.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});"], "names": [], "mappings": "AAEA,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1508, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next-auth/react/index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\");\nvar _typeof = require(\"@babel/runtime/helpers/typeof\");\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _exportNames = {\n  SessionContext: true,\n  useSession: true,\n  getSession: true,\n  getCsrfToken: true,\n  getProviders: true,\n  signIn: true,\n  signOut: true,\n  SessionProvider: true\n};\nexports.SessionContext = void 0;\nexports.SessionProvider = SessionProvider;\nexports.getCsrfToken = getCsrfToken;\nexports.getProviders = getProviders;\nexports.getSession = getSession;\nexports.signIn = signIn;\nexports.signOut = signOut;\nexports.useSession = useSession;\nvar _regenerator = _interopRequireDefault(require(\"@babel/runtime/regenerator\"));\nvar _defineProperty2 = _interopRequireDefault(require(\"@babel/runtime/helpers/defineProperty\"));\nvar _asyncToGenerator2 = _interopRequireDefault(require(\"@babel/runtime/helpers/asyncToGenerator\"));\nvar _slicedToArray2 = _interopRequireDefault(require(\"@babel/runtime/helpers/slicedToArray\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _logger2 = _interopRequireWildcard(require(\"../utils/logger\"));\nvar _parseUrl = _interopRequireDefault(require(\"../utils/parse-url\"));\nvar _utils = require(\"../client/_utils\");\nvar _jsxRuntime = require(\"react/jsx-runtime\");\nvar _types = require(\"./types\");\nObject.keys(_types).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _types[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function get() {\n      return _types[key];\n    }\n  });\n});\nvar _process$env$NEXTAUTH, _ref, _process$env$NEXTAUTH2, _process$env$NEXTAUTH3, _React$createContext;\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar __NEXTAUTH = {\n  baseUrl: (0, _parseUrl.default)((_process$env$NEXTAUTH = process.env.NEXTAUTH_URL) !== null && _process$env$NEXTAUTH !== void 0 ? _process$env$NEXTAUTH : process.env.VERCEL_URL).origin,\n  basePath: (0, _parseUrl.default)(process.env.NEXTAUTH_URL).path,\n  baseUrlServer: (0, _parseUrl.default)((_ref = (_process$env$NEXTAUTH2 = process.env.NEXTAUTH_URL_INTERNAL) !== null && _process$env$NEXTAUTH2 !== void 0 ? _process$env$NEXTAUTH2 : process.env.NEXTAUTH_URL) !== null && _ref !== void 0 ? _ref : process.env.VERCEL_URL).origin,\n  basePathServer: (0, _parseUrl.default)((_process$env$NEXTAUTH3 = process.env.NEXTAUTH_URL_INTERNAL) !== null && _process$env$NEXTAUTH3 !== void 0 ? _process$env$NEXTAUTH3 : process.env.NEXTAUTH_URL).path,\n  _lastSync: 0,\n  _session: undefined,\n  _getSession: function _getSession() {}\n};\nvar broadcast = (0, _utils.BroadcastChannel)();\nvar logger = (0, _logger2.proxyLogger)(_logger2.default, __NEXTAUTH.basePath);\nfunction useOnline() {\n  var _React$useState = React.useState(typeof navigator !== \"undefined\" ? navigator.onLine : false),\n    _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),\n    isOnline = _React$useState2[0],\n    setIsOnline = _React$useState2[1];\n  var setOnline = function setOnline() {\n    return setIsOnline(true);\n  };\n  var setOffline = function setOffline() {\n    return setIsOnline(false);\n  };\n  React.useEffect(function () {\n    window.addEventListener(\"online\", setOnline);\n    window.addEventListener(\"offline\", setOffline);\n    return function () {\n      window.removeEventListener(\"online\", setOnline);\n      window.removeEventListener(\"offline\", setOffline);\n    };\n  }, []);\n  return isOnline;\n}\nvar SessionContext = exports.SessionContext = (_React$createContext = React.createContext) === null || _React$createContext === void 0 ? void 0 : _React$createContext.call(React, undefined);\nfunction useSession(options) {\n  if (!SessionContext) {\n    throw new Error(\"React Context is unavailable in Server Components\");\n  }\n  var value = React.useContext(SessionContext);\n  if (!value && process.env.NODE_ENV !== \"production\") {\n    throw new Error(\"[next-auth]: `useSession` must be wrapped in a <SessionProvider />\");\n  }\n  var _ref2 = options !== null && options !== void 0 ? options : {},\n    required = _ref2.required,\n    onUnauthenticated = _ref2.onUnauthenticated;\n  var requiredAndNotLoading = required && value.status === \"unauthenticated\";\n  React.useEffect(function () {\n    if (requiredAndNotLoading) {\n      var url = \"/api/auth/signin?\".concat(new URLSearchParams({\n        error: \"SessionRequired\",\n        callbackUrl: window.location.href\n      }));\n      if (onUnauthenticated) onUnauthenticated();else window.location.href = url;\n    }\n  }, [requiredAndNotLoading, onUnauthenticated]);\n  if (requiredAndNotLoading) {\n    return {\n      data: value.data,\n      update: value.update,\n      status: \"loading\"\n    };\n  }\n  return value;\n}\nfunction getSession(_x) {\n  return _getSession2.apply(this, arguments);\n}\nfunction _getSession2() {\n  _getSession2 = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee3(params) {\n    var _params$broadcast;\n    var session;\n    return _regenerator.default.wrap(function _callee3$(_context3) {\n      while (1) switch (_context3.prev = _context3.next) {\n        case 0:\n          _context3.next = 2;\n          return (0, _utils.fetchData)(\"session\", __NEXTAUTH, logger, params);\n        case 2:\n          session = _context3.sent;\n          if ((_params$broadcast = params === null || params === void 0 ? void 0 : params.broadcast) !== null && _params$broadcast !== void 0 ? _params$broadcast : true) {\n            broadcast.post({\n              event: \"session\",\n              data: {\n                trigger: \"getSession\"\n              }\n            });\n          }\n          return _context3.abrupt(\"return\", session);\n        case 5:\n        case \"end\":\n          return _context3.stop();\n      }\n    }, _callee3);\n  }));\n  return _getSession2.apply(this, arguments);\n}\nfunction getCsrfToken(_x2) {\n  return _getCsrfToken.apply(this, arguments);\n}\nfunction _getCsrfToken() {\n  _getCsrfToken = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee4(params) {\n    var response;\n    return _regenerator.default.wrap(function _callee4$(_context4) {\n      while (1) switch (_context4.prev = _context4.next) {\n        case 0:\n          _context4.next = 2;\n          return (0, _utils.fetchData)(\"csrf\", __NEXTAUTH, logger, params);\n        case 2:\n          response = _context4.sent;\n          return _context4.abrupt(\"return\", response === null || response === void 0 ? void 0 : response.csrfToken);\n        case 4:\n        case \"end\":\n          return _context4.stop();\n      }\n    }, _callee4);\n  }));\n  return _getCsrfToken.apply(this, arguments);\n}\nfunction getProviders() {\n  return _getProviders.apply(this, arguments);\n}\nfunction _getProviders() {\n  _getProviders = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee5() {\n    return _regenerator.default.wrap(function _callee5$(_context5) {\n      while (1) switch (_context5.prev = _context5.next) {\n        case 0:\n          _context5.next = 2;\n          return (0, _utils.fetchData)(\"providers\", __NEXTAUTH, logger);\n        case 2:\n          return _context5.abrupt(\"return\", _context5.sent);\n        case 3:\n        case \"end\":\n          return _context5.stop();\n      }\n    }, _callee5);\n  }));\n  return _getProviders.apply(this, arguments);\n}\nfunction signIn(_x3, _x4, _x5) {\n  return _signIn.apply(this, arguments);\n}\nfunction _signIn() {\n  _signIn = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee6(provider, options, authorizationParams) {\n    var _ref5, _ref5$callbackUrl, callbackUrl, _ref5$redirect, redirect, baseUrl, providers, isCredentials, isEmail, isSupportingReturn, signInUrl, _signInUrl, res, data, _data$url, url, error;\n    return _regenerator.default.wrap(function _callee6$(_context6) {\n      while (1) switch (_context6.prev = _context6.next) {\n        case 0:\n          _ref5 = options !== null && options !== void 0 ? options : {}, _ref5$callbackUrl = _ref5.callbackUrl, callbackUrl = _ref5$callbackUrl === void 0 ? window.location.href : _ref5$callbackUrl, _ref5$redirect = _ref5.redirect, redirect = _ref5$redirect === void 0 ? true : _ref5$redirect;\n          baseUrl = (0, _utils.apiBaseUrl)(__NEXTAUTH);\n          _context6.next = 4;\n          return getProviders();\n        case 4:\n          providers = _context6.sent;\n          if (providers) {\n            _context6.next = 8;\n            break;\n          }\n          window.location.href = \"\".concat(baseUrl, \"/error\");\n          return _context6.abrupt(\"return\");\n        case 8:\n          if (!(!provider || !(provider in providers))) {\n            _context6.next = 11;\n            break;\n          }\n          window.location.href = \"\".concat(baseUrl, \"/signin?\").concat(new URLSearchParams({\n            callbackUrl: callbackUrl\n          }));\n          return _context6.abrupt(\"return\");\n        case 11:\n          isCredentials = providers[provider].type === \"credentials\";\n          isEmail = providers[provider].type === \"email\";\n          isSupportingReturn = isCredentials || isEmail;\n          signInUrl = \"\".concat(baseUrl, \"/\").concat(isCredentials ? \"callback\" : \"signin\", \"/\").concat(provider);\n          _signInUrl = \"\".concat(signInUrl).concat(authorizationParams ? \"?\".concat(new URLSearchParams(authorizationParams)) : \"\");\n          _context6.t0 = fetch;\n          _context6.t1 = _signInUrl;\n          _context6.t2 = {\n            \"Content-Type\": \"application/x-www-form-urlencoded\"\n          };\n          _context6.t3 = URLSearchParams;\n          _context6.t4 = _objectSpread;\n          _context6.t5 = _objectSpread({}, options);\n          _context6.t6 = {};\n          _context6.next = 25;\n          return getCsrfToken();\n        case 25:\n          _context6.t7 = _context6.sent;\n          _context6.t8 = callbackUrl;\n          _context6.t9 = {\n            csrfToken: _context6.t7,\n            callbackUrl: _context6.t8,\n            json: true\n          };\n          _context6.t10 = (0, _context6.t4)(_context6.t5, _context6.t6, _context6.t9);\n          _context6.t11 = new _context6.t3(_context6.t10);\n          _context6.t12 = {\n            method: \"post\",\n            headers: _context6.t2,\n            body: _context6.t11\n          };\n          _context6.next = 33;\n          return (0, _context6.t0)(_context6.t1, _context6.t12);\n        case 33:\n          res = _context6.sent;\n          _context6.next = 36;\n          return res.json();\n        case 36:\n          data = _context6.sent;\n          if (!(redirect || !isSupportingReturn)) {\n            _context6.next = 42;\n            break;\n          }\n          url = (_data$url = data.url) !== null && _data$url !== void 0 ? _data$url : callbackUrl;\n          window.location.href = url;\n          if (url.includes(\"#\")) window.location.reload();\n          return _context6.abrupt(\"return\");\n        case 42:\n          error = new URL(data.url).searchParams.get(\"error\");\n          if (!res.ok) {\n            _context6.next = 46;\n            break;\n          }\n          _context6.next = 46;\n          return __NEXTAUTH._getSession({\n            event: \"storage\"\n          });\n        case 46:\n          return _context6.abrupt(\"return\", {\n            error: error,\n            status: res.status,\n            ok: res.ok,\n            url: error ? null : data.url\n          });\n        case 47:\n        case \"end\":\n          return _context6.stop();\n      }\n    }, _callee6);\n  }));\n  return _signIn.apply(this, arguments);\n}\nfunction signOut(_x6) {\n  return _signOut.apply(this, arguments);\n}\nfunction _signOut() {\n  _signOut = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee7(options) {\n    var _options$redirect;\n    var _ref6, _ref6$callbackUrl, callbackUrl, baseUrl, fetchOptions, res, data, _data$url2, url;\n    return _regenerator.default.wrap(function _callee7$(_context7) {\n      while (1) switch (_context7.prev = _context7.next) {\n        case 0:\n          _ref6 = options !== null && options !== void 0 ? options : {}, _ref6$callbackUrl = _ref6.callbackUrl, callbackUrl = _ref6$callbackUrl === void 0 ? window.location.href : _ref6$callbackUrl;\n          baseUrl = (0, _utils.apiBaseUrl)(__NEXTAUTH);\n          _context7.t0 = {\n            \"Content-Type\": \"application/x-www-form-urlencoded\"\n          };\n          _context7.t1 = URLSearchParams;\n          _context7.next = 6;\n          return getCsrfToken();\n        case 6:\n          _context7.t2 = _context7.sent;\n          _context7.t3 = callbackUrl;\n          _context7.t4 = {\n            csrfToken: _context7.t2,\n            callbackUrl: _context7.t3,\n            json: true\n          };\n          _context7.t5 = new _context7.t1(_context7.t4);\n          fetchOptions = {\n            method: \"post\",\n            headers: _context7.t0,\n            body: _context7.t5\n          };\n          _context7.next = 13;\n          return fetch(\"\".concat(baseUrl, \"/signout\"), fetchOptions);\n        case 13:\n          res = _context7.sent;\n          _context7.next = 16;\n          return res.json();\n        case 16:\n          data = _context7.sent;\n          broadcast.post({\n            event: \"session\",\n            data: {\n              trigger: \"signout\"\n            }\n          });\n          if (!((_options$redirect = options === null || options === void 0 ? void 0 : options.redirect) !== null && _options$redirect !== void 0 ? _options$redirect : true)) {\n            _context7.next = 23;\n            break;\n          }\n          url = (_data$url2 = data.url) !== null && _data$url2 !== void 0 ? _data$url2 : callbackUrl;\n          window.location.href = url;\n          if (url.includes(\"#\")) window.location.reload();\n          return _context7.abrupt(\"return\");\n        case 23:\n          _context7.next = 25;\n          return __NEXTAUTH._getSession({\n            event: \"storage\"\n          });\n        case 25:\n          return _context7.abrupt(\"return\", data);\n        case 26:\n        case \"end\":\n          return _context7.stop();\n      }\n    }, _callee7);\n  }));\n  return _signOut.apply(this, arguments);\n}\nfunction SessionProvider(props) {\n  if (!SessionContext) {\n    throw new Error(\"React Context is unavailable in Server Components\");\n  }\n  var children = props.children,\n    basePath = props.basePath,\n    refetchInterval = props.refetchInterval,\n    refetchWhenOffline = props.refetchWhenOffline;\n  if (basePath) __NEXTAUTH.basePath = basePath;\n  var hasInitialSession = props.session !== undefined;\n  __NEXTAUTH._lastSync = hasInitialSession ? (0, _utils.now)() : 0;\n  var _React$useState3 = React.useState(function () {\n      if (hasInitialSession) __NEXTAUTH._session = props.session;\n      return props.session;\n    }),\n    _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2),\n    session = _React$useState4[0],\n    setSession = _React$useState4[1];\n  var _React$useState5 = React.useState(!hasInitialSession),\n    _React$useState6 = (0, _slicedToArray2.default)(_React$useState5, 2),\n    loading = _React$useState6[0],\n    setLoading = _React$useState6[1];\n  React.useEffect(function () {\n    __NEXTAUTH._getSession = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee() {\n      var _ref4,\n        event,\n        storageEvent,\n        _args = arguments;\n      return _regenerator.default.wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _ref4 = _args.length > 0 && _args[0] !== undefined ? _args[0] : {}, event = _ref4.event;\n            _context.prev = 1;\n            storageEvent = event === \"storage\";\n            if (!(storageEvent || __NEXTAUTH._session === undefined)) {\n              _context.next = 10;\n              break;\n            }\n            __NEXTAUTH._lastSync = (0, _utils.now)();\n            _context.next = 7;\n            return getSession({\n              broadcast: !storageEvent\n            });\n          case 7:\n            __NEXTAUTH._session = _context.sent;\n            setSession(__NEXTAUTH._session);\n            return _context.abrupt(\"return\");\n          case 10:\n            if (!(!event || __NEXTAUTH._session === null || (0, _utils.now)() < __NEXTAUTH._lastSync)) {\n              _context.next = 12;\n              break;\n            }\n            return _context.abrupt(\"return\");\n          case 12:\n            __NEXTAUTH._lastSync = (0, _utils.now)();\n            _context.next = 15;\n            return getSession();\n          case 15:\n            __NEXTAUTH._session = _context.sent;\n            setSession(__NEXTAUTH._session);\n            _context.next = 22;\n            break;\n          case 19:\n            _context.prev = 19;\n            _context.t0 = _context[\"catch\"](1);\n            logger.error(\"CLIENT_SESSION_ERROR\", _context.t0);\n          case 22:\n            _context.prev = 22;\n            setLoading(false);\n            return _context.finish(22);\n          case 25:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee, null, [[1, 19, 22, 25]]);\n    }));\n    __NEXTAUTH._getSession();\n    return function () {\n      __NEXTAUTH._lastSync = 0;\n      __NEXTAUTH._session = undefined;\n      __NEXTAUTH._getSession = function () {};\n    };\n  }, []);\n  React.useEffect(function () {\n    var unsubscribe = broadcast.receive(function () {\n      return __NEXTAUTH._getSession({\n        event: \"storage\"\n      });\n    });\n    return function () {\n      return unsubscribe();\n    };\n  }, []);\n  React.useEffect(function () {\n    var _props$refetchOnWindo = props.refetchOnWindowFocus,\n      refetchOnWindowFocus = _props$refetchOnWindo === void 0 ? true : _props$refetchOnWindo;\n    var visibilityHandler = function visibilityHandler() {\n      if (refetchOnWindowFocus && document.visibilityState === \"visible\") __NEXTAUTH._getSession({\n        event: \"visibilitychange\"\n      });\n    };\n    document.addEventListener(\"visibilitychange\", visibilityHandler, false);\n    return function () {\n      return document.removeEventListener(\"visibilitychange\", visibilityHandler, false);\n    };\n  }, [props.refetchOnWindowFocus]);\n  var isOnline = useOnline();\n  var shouldRefetch = refetchWhenOffline !== false || isOnline;\n  React.useEffect(function () {\n    if (refetchInterval && shouldRefetch) {\n      var refetchIntervalTimer = setInterval(function () {\n        if (__NEXTAUTH._session) {\n          __NEXTAUTH._getSession({\n            event: \"poll\"\n          });\n        }\n      }, refetchInterval * 1000);\n      return function () {\n        return clearInterval(refetchIntervalTimer);\n      };\n    }\n  }, [refetchInterval, shouldRefetch]);\n  var value = React.useMemo(function () {\n    return {\n      data: session,\n      status: loading ? \"loading\" : session ? \"authenticated\" : \"unauthenticated\",\n      update: function update(data) {\n        return (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee2() {\n          var newSession;\n          return _regenerator.default.wrap(function _callee2$(_context2) {\n            while (1) switch (_context2.prev = _context2.next) {\n              case 0:\n                if (!(loading || !session)) {\n                  _context2.next = 2;\n                  break;\n                }\n                return _context2.abrupt(\"return\");\n              case 2:\n                setLoading(true);\n                _context2.t0 = _utils.fetchData;\n                _context2.t1 = __NEXTAUTH;\n                _context2.t2 = logger;\n                _context2.next = 8;\n                return getCsrfToken();\n              case 8:\n                _context2.t3 = _context2.sent;\n                _context2.t4 = data;\n                _context2.t5 = {\n                  csrfToken: _context2.t3,\n                  data: _context2.t4\n                };\n                _context2.t6 = {\n                  body: _context2.t5\n                };\n                _context2.t7 = {\n                  req: _context2.t6\n                };\n                _context2.next = 15;\n                return (0, _context2.t0)(\"session\", _context2.t1, _context2.t2, _context2.t7);\n              case 15:\n                newSession = _context2.sent;\n                setLoading(false);\n                if (newSession) {\n                  setSession(newSession);\n                  broadcast.post({\n                    event: \"session\",\n                    data: {\n                      trigger: \"getSession\"\n                    }\n                  });\n                }\n                return _context2.abrupt(\"return\", newSession);\n              case 19:\n              case \"end\":\n                return _context2.stop();\n            }\n          }, _callee2);\n        }))();\n      }\n    };\n  }, [session, loading]);\n  return (0, _jsxRuntime.jsx)(SessionContext.Provider, {\n    value: value,\n    children: children\n  });\n}"], "names": [], "mappings": "AAoD2D;AApD3D;AAEA,IAAI;AACJ,IAAI;AACJ,OAAO,cAAc,CAAC,SAAS,cAAc;IAC3C,OAAO;AACT;AACA,IAAI,eAAe;IACjB,gBAAgB;IAChB,YAAY;IACZ,YAAY;IACZ,cAAc;IACd,cAAc;IACd,QAAQ;IACR,SAAS;IACT,iBAAiB;AACnB;AACA,QAAQ,cAAc,GAAG,KAAK;AAC9B,QAAQ,eAAe,GAAG;AAC1B,QAAQ,YAAY,GAAG;AACvB,QAAQ,YAAY,GAAG;AACvB,QAAQ,UAAU,GAAG;AACrB,QAAQ,MAAM,GAAG;AACjB,QAAQ,OAAO,GAAG;AAClB,QAAQ,UAAU,GAAG;AACrB,IAAI,eAAe;AACnB,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI,kBAAkB;AACtB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,SAAU,GAAG;IACvC,IAAI,QAAQ,aAAa,QAAQ,cAAc;IAC/C,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,MAAM;IAC7D,IAAI,OAAO,WAAW,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,EAAE;IACpD,OAAO,cAAc,CAAC,SAAS,KAAK;QAClC,YAAY;QACZ,KAAK,SAAS;YACZ,OAAO,MAAM,CAAC,IAAI;QACpB;IACF;AACF;AACA,IAAI,uBAAuB,MAAM,wBAAwB,wBAAwB;AACjF,SAAS,yBAAyB,CAAC;IAAI,IAAI,cAAc,OAAO,SAAS,OAAO;IAAM,IAAI,IAAI,IAAI,WAAW,IAAI,IAAI;IAAW,OAAO,CAAC,2BAA2B,SAAS,yBAAyB,CAAC;QAAI,OAAO,IAAI,IAAI;IAAG,CAAC,EAAE;AAAI;AACnO,SAAS,wBAAwB,CAAC,EAAE,CAAC;IAAI,IAAI,CAAC,KAAK,KAAK,EAAE,UAAU,EAAE,OAAO;IAAG,IAAI,SAAS,KAAK,YAAY,QAAQ,MAAM,cAAc,OAAO,GAAG,OAAO;QAAE,SAAS;IAAE;IAAG,IAAI,IAAI,yBAAyB;IAAI,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,OAAO,EAAE,GAAG,CAAC;IAAI,IAAI,IAAI;QAAE,WAAW;IAAK,GAAG,IAAI,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAAE,IAAK,IAAI,KAAK,EAAG,IAAI,cAAc,KAAK,CAAA,CAAC,CAAA,EAAE,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI;QAAE,IAAI,IAAI,IAAI,OAAO,wBAAwB,CAAC,GAAG,KAAK;QAAM,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,OAAO,cAAc,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IAAE,OAAO,EAAE,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,CAAC,GAAG,IAAI;AAAG;AACpkB,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,CAAC,GAAG,iBAAiB,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACpc,IAAI,aAAa;IACf,SAAS,CAAC,GAAG,UAAU,OAAO,EAAE,CAAC,wBAAwB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM;IACxL,UAAU,CAAC,GAAG,UAAU,OAAO,EAAE,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI;IAC/D,eAAe,CAAC,GAAG,UAAU,OAAO,EAAE,CAAC,OAAO,CAAC,yBAAyB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,qBAAqB,MAAM,QAAQ,2BAA2B,KAAK,IAAI,yBAAyB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY,MAAM,QAAQ,SAAS,KAAK,IAAI,OAAO,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM;IACjR,gBAAgB,CAAC,GAAG,UAAU,OAAO,EAAE,CAAC,yBAAyB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,qBAAqB,MAAM,QAAQ,2BAA2B,KAAK,IAAI,yBAAyB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI;IAC3M,WAAW;IACX,UAAU;IACV,aAAa,SAAS,eAAe;AACvC;AACA,IAAI,YAAY,CAAC,GAAG,OAAO,gBAAgB;AAC3C,IAAI,SAAS,CAAC,GAAG,SAAS,WAAW,EAAE,SAAS,OAAO,EAAE,WAAW,QAAQ;AAC5E,SAAS;IACP,IAAI,kBAAkB,MAAM,QAAQ,CAAC,OAAO,cAAc,cAAc,UAAU,MAAM,GAAG,QACzF,mBAAmB,CAAC,GAAG,gBAAgB,OAAO,EAAE,iBAAiB,IACjE,WAAW,gBAAgB,CAAC,EAAE,EAC9B,cAAc,gBAAgB,CAAC,EAAE;IACnC,IAAI,YAAY,SAAS;QACvB,OAAO,YAAY;IACrB;IACA,IAAI,aAAa,SAAS;QACxB,OAAO,YAAY;IACrB;IACA,MAAM,SAAS;+BAAC;YACd,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,WAAW;YACnC;uCAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;8BAAG,EAAE;IACL,OAAO;AACT;AACA,IAAI,iBAAiB,QAAQ,cAAc,GAAG,CAAC,uBAAuB,MAAM,aAAa,MAAM,QAAQ,yBAAyB,KAAK,IAAI,KAAK,IAAI,qBAAqB,IAAI,CAAC,OAAO;AACnL,SAAS,WAAW,OAAO;IACzB,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,MAAM;IAClB;IACA,IAAI,QAAQ,MAAM,UAAU,CAAC;IAC7B,IAAI,CAAC,SAAS,oDAAyB,cAAc;QACnD,MAAM,IAAI,MAAM;IAClB;IACA,IAAI,QAAQ,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU,CAAC,GAC9D,WAAW,MAAM,QAAQ,EACzB,oBAAoB,MAAM,iBAAiB;IAC7C,IAAI,wBAAwB,YAAY,MAAM,MAAM,KAAK;IACzD,MAAM,SAAS;gCAAC;YACd,IAAI,uBAAuB;gBACzB,IAAI,MAAM,oBAAoB,MAAM,CAAC,IAAI,gBAAgB;oBACvD,OAAO;oBACP,aAAa,OAAO,QAAQ,CAAC,IAAI;gBACnC;gBACA,IAAI,mBAAmB;qBAAyB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzE;QACF;+BAAG;QAAC;QAAuB;KAAkB;IAC7C,IAAI,uBAAuB;QACzB,OAAO;YACL,MAAM,MAAM,IAAI;YAChB,QAAQ,MAAM,MAAM;YACpB,QAAQ;QACV;IACF;IACA,OAAO;AACT;AACA,SAAS,WAAW,EAAE;IACpB,OAAO,aAAa,KAAK,CAAC,IAAI,EAAE;AAClC;AACA,SAAS;IACP,eAAe,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,SAAS,MAAM;QAC/F,IAAI;QACJ,IAAI;QACJ,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,UAAU,SAAS;YAC3D,MAAO,EAAG,OAAQ,UAAU,IAAI,GAAG,UAAU,IAAI;gBAC/C,KAAK;oBACH,UAAU,IAAI,GAAG;oBACjB,OAAO,CAAC,GAAG,OAAO,SAAS,EAAE,WAAW,YAAY,QAAQ;gBAC9D,KAAK;oBACH,UAAU,UAAU,IAAI;oBACxB,IAAI,CAAC,oBAAoB,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,SAAS,MAAM,QAAQ,sBAAsB,KAAK,IAAI,oBAAoB,MAAM;wBAC9J,UAAU,IAAI,CAAC;4BACb,OAAO;4BACP,MAAM;gCACJ,SAAS;4BACX;wBACF;oBACF;oBACA,OAAO,UAAU,MAAM,CAAC,UAAU;gBACpC,KAAK;gBACL,KAAK;oBACH,OAAO,UAAU,IAAI;YACzB;QACF,GAAG;IACL;IACA,OAAO,aAAa,KAAK,CAAC,IAAI,EAAE;AAClC;AACA,SAAS,aAAa,GAAG;IACvB,OAAO,cAAc,KAAK,CAAC,IAAI,EAAE;AACnC;AACA,SAAS;IACP,gBAAgB,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,SAAS,MAAM;QAChG,IAAI;QACJ,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,UAAU,SAAS;YAC3D,MAAO,EAAG,OAAQ,UAAU,IAAI,GAAG,UAAU,IAAI;gBAC/C,KAAK;oBACH,UAAU,IAAI,GAAG;oBACjB,OAAO,CAAC,GAAG,OAAO,SAAS,EAAE,QAAQ,YAAY,QAAQ;gBAC3D,KAAK;oBACH,WAAW,UAAU,IAAI;oBACzB,OAAO,UAAU,MAAM,CAAC,UAAU,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS,SAAS;gBAC1G,KAAK;gBACL,KAAK;oBACH,OAAO,UAAU,IAAI;YACzB;QACF,GAAG;IACL;IACA,OAAO,cAAc,KAAK,CAAC,IAAI,EAAE;AACnC;AACA,SAAS;IACP,OAAO,cAAc,KAAK,CAAC,IAAI,EAAE;AACnC;AACA,SAAS;IACP,gBAAgB,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS;QACjF,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,UAAU,SAAS;YAC3D,MAAO,EAAG,OAAQ,UAAU,IAAI,GAAG,UAAU,IAAI;gBAC/C,KAAK;oBACH,UAAU,IAAI,GAAG;oBACjB,OAAO,CAAC,GAAG,OAAO,SAAS,EAAE,aAAa,YAAY;gBACxD,KAAK;oBACH,OAAO,UAAU,MAAM,CAAC,UAAU,UAAU,IAAI;gBAClD,KAAK;gBACL,KAAK;oBACH,OAAO,UAAU,IAAI;YACzB;QACF,GAAG;IACL;IACA,OAAO,cAAc,KAAK,CAAC,IAAI,EAAE;AACnC;AACA,SAAS,OAAO,GAAG,EAAE,GAAG,EAAE,GAAG;IAC3B,OAAO,QAAQ,KAAK,CAAC,IAAI,EAAE;AAC7B;AACA,SAAS;IACP,UAAU,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,SAAS,QAAQ,EAAE,OAAO,EAAE,mBAAmB;QAC1H,IAAI,OAAO,mBAAmB,aAAa,gBAAgB,UAAU,SAAS,WAAW,eAAe,SAAS,oBAAoB,WAAW,YAAY,KAAK,MAAM,WAAW,KAAK;QACvL,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,UAAU,SAAS;YAC3D,MAAO,EAAG,OAAQ,UAAU,IAAI,GAAG,UAAU,IAAI;gBAC/C,KAAK;oBACH,QAAQ,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU,CAAC,GAAG,oBAAoB,MAAM,WAAW,EAAE,cAAc,sBAAsB,KAAK,IAAI,OAAO,QAAQ,CAAC,IAAI,GAAG,mBAAmB,iBAAiB,MAAM,QAAQ,EAAE,WAAW,mBAAmB,KAAK,IAAI,OAAO;oBAC5Q,UAAU,CAAC,GAAG,OAAO,UAAU,EAAE;oBACjC,UAAU,IAAI,GAAG;oBACjB,OAAO;gBACT,KAAK;oBACH,YAAY,UAAU,IAAI;oBAC1B,IAAI,WAAW;wBACb,UAAU,IAAI,GAAG;wBACjB;oBACF;oBACA,OAAO,QAAQ,CAAC,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS;oBAC1C,OAAO,UAAU,MAAM,CAAC;gBAC1B,KAAK;oBACH,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,YAAY,SAAS,CAAC,GAAG;wBAC5C,UAAU,IAAI,GAAG;wBACjB;oBACF;oBACA,OAAO,QAAQ,CAAC,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,YAAY,MAAM,CAAC,IAAI,gBAAgB;wBAC/E,aAAa;oBACf;oBACA,OAAO,UAAU,MAAM,CAAC;gBAC1B,KAAK;oBACH,gBAAgB,SAAS,CAAC,SAAS,CAAC,IAAI,KAAK;oBAC7C,UAAU,SAAS,CAAC,SAAS,CAAC,IAAI,KAAK;oBACvC,qBAAqB,iBAAiB;oBACtC,YAAY,GAAG,MAAM,CAAC,SAAS,KAAK,MAAM,CAAC,gBAAgB,aAAa,UAAU,KAAK,MAAM,CAAC;oBAC9F,aAAa,GAAG,MAAM,CAAC,WAAW,MAAM,CAAC,sBAAsB,IAAI,MAAM,CAAC,IAAI,gBAAgB,wBAAwB;oBACtH,UAAU,EAAE,GAAG;oBACf,UAAU,EAAE,GAAG;oBACf,UAAU,EAAE,GAAG;wBACb,gBAAgB;oBAClB;oBACA,UAAU,EAAE,GAAG;oBACf,UAAU,EAAE,GAAG;oBACf,UAAU,EAAE,GAAG,cAAc,CAAC,GAAG;oBACjC,UAAU,EAAE,GAAG,CAAC;oBAChB,UAAU,IAAI,GAAG;oBACjB,OAAO;gBACT,KAAK;oBACH,UAAU,EAAE,GAAG,UAAU,IAAI;oBAC7B,UAAU,EAAE,GAAG;oBACf,UAAU,EAAE,GAAG;wBACb,WAAW,UAAU,EAAE;wBACvB,aAAa,UAAU,EAAE;wBACzB,MAAM;oBACR;oBACA,UAAU,GAAG,GAAG,CAAC,GAAG,UAAU,EAAE,EAAE,UAAU,EAAE,EAAE,UAAU,EAAE,EAAE,UAAU,EAAE;oBAC1E,UAAU,GAAG,GAAG,IAAI,UAAU,EAAE,CAAC,UAAU,GAAG;oBAC9C,UAAU,GAAG,GAAG;wBACd,QAAQ;wBACR,SAAS,UAAU,EAAE;wBACrB,MAAM,UAAU,GAAG;oBACrB;oBACA,UAAU,IAAI,GAAG;oBACjB,OAAO,CAAC,GAAG,UAAU,EAAE,EAAE,UAAU,EAAE,EAAE,UAAU,GAAG;gBACtD,KAAK;oBACH,MAAM,UAAU,IAAI;oBACpB,UAAU,IAAI,GAAG;oBACjB,OAAO,IAAI,IAAI;gBACjB,KAAK;oBACH,OAAO,UAAU,IAAI;oBACrB,IAAI,CAAC,CAAC,YAAY,CAAC,kBAAkB,GAAG;wBACtC,UAAU,IAAI,GAAG;wBACjB;oBACF;oBACA,MAAM,CAAC,YAAY,KAAK,GAAG,MAAM,QAAQ,cAAc,KAAK,IAAI,YAAY;oBAC5E,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACvB,IAAI,IAAI,QAAQ,CAAC,MAAM,OAAO,QAAQ,CAAC,MAAM;oBAC7C,OAAO,UAAU,MAAM,CAAC;gBAC1B,KAAK;oBACH,QAAQ,IAAI,IAAI,KAAK,GAAG,EAAE,YAAY,CAAC,GAAG,CAAC;oBAC3C,IAAI,CAAC,IAAI,EAAE,EAAE;wBACX,UAAU,IAAI,GAAG;wBACjB;oBACF;oBACA,UAAU,IAAI,GAAG;oBACjB,OAAO,WAAW,WAAW,CAAC;wBAC5B,OAAO;oBACT;gBACF,KAAK;oBACH,OAAO,UAAU,MAAM,CAAC,UAAU;wBAChC,OAAO;wBACP,QAAQ,IAAI,MAAM;wBAClB,IAAI,IAAI,EAAE;wBACV,KAAK,QAAQ,OAAO,KAAK,GAAG;oBAC9B;gBACF,KAAK;gBACL,KAAK;oBACH,OAAO,UAAU,IAAI;YACzB;QACF,GAAG;IACL;IACA,OAAO,QAAQ,KAAK,CAAC,IAAI,EAAE;AAC7B;AACA,SAAS,QAAQ,GAAG;IAClB,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAC9B;AACA,SAAS;IACP,WAAW,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,SAAS,OAAO;QAC5F,IAAI;QACJ,IAAI,OAAO,mBAAmB,aAAa,SAAS,cAAc,KAAK,MAAM,YAAY;QACzF,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,UAAU,SAAS;YAC3D,MAAO,EAAG,OAAQ,UAAU,IAAI,GAAG,UAAU,IAAI;gBAC/C,KAAK;oBACH,QAAQ,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU,CAAC,GAAG,oBAAoB,MAAM,WAAW,EAAE,cAAc,sBAAsB,KAAK,IAAI,OAAO,QAAQ,CAAC,IAAI,GAAG;oBAC1K,UAAU,CAAC,GAAG,OAAO,UAAU,EAAE;oBACjC,UAAU,EAAE,GAAG;wBACb,gBAAgB;oBAClB;oBACA,UAAU,EAAE,GAAG;oBACf,UAAU,IAAI,GAAG;oBACjB,OAAO;gBACT,KAAK;oBACH,UAAU,EAAE,GAAG,UAAU,IAAI;oBAC7B,UAAU,EAAE,GAAG;oBACf,UAAU,EAAE,GAAG;wBACb,WAAW,UAAU,EAAE;wBACvB,aAAa,UAAU,EAAE;wBACzB,MAAM;oBACR;oBACA,UAAU,EAAE,GAAG,IAAI,UAAU,EAAE,CAAC,UAAU,EAAE;oBAC5C,eAAe;wBACb,QAAQ;wBACR,SAAS,UAAU,EAAE;wBACrB,MAAM,UAAU,EAAE;oBACpB;oBACA,UAAU,IAAI,GAAG;oBACjB,OAAO,MAAM,GAAG,MAAM,CAAC,SAAS,aAAa;gBAC/C,KAAK;oBACH,MAAM,UAAU,IAAI;oBACpB,UAAU,IAAI,GAAG;oBACjB,OAAO,IAAI,IAAI;gBACjB,KAAK;oBACH,OAAO,UAAU,IAAI;oBACrB,UAAU,IAAI,CAAC;wBACb,OAAO;wBACP,MAAM;4BACJ,SAAS;wBACX;oBACF;oBACA,IAAI,CAAC,CAAC,CAAC,oBAAoB,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,QAAQ,MAAM,QAAQ,sBAAsB,KAAK,IAAI,oBAAoB,IAAI,GAAG;wBACnK,UAAU,IAAI,GAAG;wBACjB;oBACF;oBACA,MAAM,CAAC,aAAa,KAAK,GAAG,MAAM,QAAQ,eAAe,KAAK,IAAI,aAAa;oBAC/E,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACvB,IAAI,IAAI,QAAQ,CAAC,MAAM,OAAO,QAAQ,CAAC,MAAM;oBAC7C,OAAO,UAAU,MAAM,CAAC;gBAC1B,KAAK;oBACH,UAAU,IAAI,GAAG;oBACjB,OAAO,WAAW,WAAW,CAAC;wBAC5B,OAAO;oBACT;gBACF,KAAK;oBACH,OAAO,UAAU,MAAM,CAAC,UAAU;gBACpC,KAAK;gBACL,KAAK;oBACH,OAAO,UAAU,IAAI;YACzB;QACF,GAAG;IACL;IACA,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAC9B;AACA,SAAS,gBAAgB,KAAK;IAC5B,IAAI,CAAC,gBAAgB;QACnB,MAAM,IAAI,MAAM;IAClB;IACA,IAAI,WAAW,MAAM,QAAQ,EAC3B,WAAW,MAAM,QAAQ,EACzB,kBAAkB,MAAM,eAAe,EACvC,qBAAqB,MAAM,kBAAkB;IAC/C,IAAI,UAAU,WAAW,QAAQ,GAAG;IACpC,IAAI,oBAAoB,MAAM,OAAO,KAAK;IAC1C,WAAW,SAAS,GAAG,oBAAoB,CAAC,GAAG,OAAO,GAAG,MAAM;IAC/D,IAAI,mBAAmB,MAAM,QAAQ;sDAAC;YAClC,IAAI,mBAAmB,WAAW,QAAQ,GAAG,MAAM,OAAO;YAC1D,OAAO,MAAM,OAAO;QACtB;sDACA,mBAAmB,CAAC,GAAG,gBAAgB,OAAO,EAAE,kBAAkB,IAClE,UAAU,gBAAgB,CAAC,EAAE,EAC7B,aAAa,gBAAgB,CAAC,EAAE;IAClC,IAAI,mBAAmB,MAAM,QAAQ,CAAC,CAAC,oBACrC,mBAAmB,CAAC,GAAG,gBAAgB,OAAO,EAAE,kBAAkB,IAClE,UAAU,gBAAgB,CAAC,EAAE,EAC7B,aAAa,gBAAgB,CAAC,EAAE;IAClC,MAAM,SAAS;qCAAC;YACd,WAAW,WAAW,GAAG,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS;gBAC1F,IAAI,OACF,OACA,cACA,QAAQ;gBACV,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,SAAS,QAAQ;oBACzD,MAAO,EAAG,OAAQ,SAAS,IAAI,GAAG,SAAS,IAAI;wBAC7C,KAAK;4BACH,QAAQ,MAAM,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,KAAK,YAAY,KAAK,CAAC,EAAE,GAAG,CAAC,GAAG,QAAQ,MAAM,KAAK;4BACvF,SAAS,IAAI,GAAG;4BAChB,eAAe,UAAU;4BACzB,IAAI,CAAC,CAAC,gBAAgB,WAAW,QAAQ,KAAK,SAAS,GAAG;gCACxD,SAAS,IAAI,GAAG;gCAChB;4BACF;4BACA,WAAW,SAAS,GAAG,CAAC,GAAG,OAAO,GAAG;4BACrC,SAAS,IAAI,GAAG;4BAChB,OAAO,WAAW;gCAChB,WAAW,CAAC;4BACd;wBACF,KAAK;4BACH,WAAW,QAAQ,GAAG,SAAS,IAAI;4BACnC,WAAW,WAAW,QAAQ;4BAC9B,OAAO,SAAS,MAAM,CAAC;wBACzB,KAAK;4BACH,IAAI,CAAC,CAAC,CAAC,SAAS,WAAW,QAAQ,KAAK,QAAQ,CAAC,GAAG,OAAO,GAAG,MAAM,WAAW,SAAS,GAAG;gCACzF,SAAS,IAAI,GAAG;gCAChB;4BACF;4BACA,OAAO,SAAS,MAAM,CAAC;wBACzB,KAAK;4BACH,WAAW,SAAS,GAAG,CAAC,GAAG,OAAO,GAAG;4BACrC,SAAS,IAAI,GAAG;4BAChB,OAAO;wBACT,KAAK;4BACH,WAAW,QAAQ,GAAG,SAAS,IAAI;4BACnC,WAAW,WAAW,QAAQ;4BAC9B,SAAS,IAAI,GAAG;4BAChB;wBACF,KAAK;4BACH,SAAS,IAAI,GAAG;4BAChB,SAAS,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC;4BAChC,OAAO,KAAK,CAAC,wBAAwB,SAAS,EAAE;wBAClD,KAAK;4BACH,SAAS,IAAI,GAAG;4BAChB,WAAW;4BACX,OAAO,SAAS,MAAM,CAAC;wBACzB,KAAK;wBACL,KAAK;4BACH,OAAO,SAAS,IAAI;oBACxB;gBACF,GAAG,SAAS,MAAM;oBAAC;wBAAC;wBAAG;wBAAI;wBAAI;qBAAG;iBAAC;YACrC;YACA,WAAW,WAAW;YACtB;6CAAO;oBACL,WAAW,SAAS,GAAG;oBACvB,WAAW,QAAQ,GAAG;oBACtB,WAAW,WAAW;qDAAG,YAAa;;gBACxC;;QACF;oCAAG,EAAE;IACL,MAAM,SAAS;qCAAC;YACd,IAAI,cAAc,UAAU,OAAO;yDAAC;oBAClC,OAAO,WAAW,WAAW,CAAC;wBAC5B,OAAO;oBACT;gBACF;;YACA;6CAAO;oBACL,OAAO;gBACT;;QACF;oCAAG,EAAE;IACL,MAAM,SAAS;qCAAC;YACd,IAAI,wBAAwB,MAAM,oBAAoB,EACpD,uBAAuB,0BAA0B,KAAK,IAAI,OAAO;YACnE,IAAI,oBAAoB,SAAS;gBAC/B,IAAI,wBAAwB,SAAS,eAAe,KAAK,WAAW,WAAW,WAAW,CAAC;oBACzF,OAAO;gBACT;YACF;YACA,SAAS,gBAAgB,CAAC,oBAAoB,mBAAmB;YACjE;6CAAO;oBACL,OAAO,SAAS,mBAAmB,CAAC,oBAAoB,mBAAmB;gBAC7E;;QACF;oCAAG;QAAC,MAAM,oBAAoB;KAAC;IAC/B,IAAI,WAAW;IACf,IAAI,gBAAgB,uBAAuB,SAAS;IACpD,MAAM,SAAS;qCAAC;YACd,IAAI,mBAAmB,eAAe;gBACpC,IAAI,uBAAuB;sEAAY;wBACrC,IAAI,WAAW,QAAQ,EAAE;4BACvB,WAAW,WAAW,CAAC;gCACrB,OAAO;4BACT;wBACF;oBACF;qEAAG,kBAAkB;gBACrB;iDAAO;wBACL,OAAO,cAAc;oBACvB;;YACF;QACF;oCAAG;QAAC;QAAiB;KAAc;IACnC,IAAI,QAAQ,MAAM,OAAO;0CAAC;YACxB,OAAO;gBACL,MAAM;gBACN,QAAQ,UAAU,YAAY,UAAU,kBAAkB;gBAC1D,QAAQ,SAAS,OAAO,IAAI;oBAC1B,OAAO,CAAC,GAAG,mBAAmB,OAAO,EAAE,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS;wBACxE,IAAI;wBACJ,OAAO,aAAa,OAAO,CAAC,IAAI,CAAC,SAAS,UAAU,SAAS;4BAC3D,MAAO,EAAG,OAAQ,UAAU,IAAI,GAAG,UAAU,IAAI;gCAC/C,KAAK;oCACH,IAAI,CAAC,CAAC,WAAW,CAAC,OAAO,GAAG;wCAC1B,UAAU,IAAI,GAAG;wCACjB;oCACF;oCACA,OAAO,UAAU,MAAM,CAAC;gCAC1B,KAAK;oCACH,WAAW;oCACX,UAAU,EAAE,GAAG,OAAO,SAAS;oCAC/B,UAAU,EAAE,GAAG;oCACf,UAAU,EAAE,GAAG;oCACf,UAAU,IAAI,GAAG;oCACjB,OAAO;gCACT,KAAK;oCACH,UAAU,EAAE,GAAG,UAAU,IAAI;oCAC7B,UAAU,EAAE,GAAG;oCACf,UAAU,EAAE,GAAG;wCACb,WAAW,UAAU,EAAE;wCACvB,MAAM,UAAU,EAAE;oCACpB;oCACA,UAAU,EAAE,GAAG;wCACb,MAAM,UAAU,EAAE;oCACpB;oCACA,UAAU,EAAE,GAAG;wCACb,KAAK,UAAU,EAAE;oCACnB;oCACA,UAAU,IAAI,GAAG;oCACjB,OAAO,CAAC,GAAG,UAAU,EAAE,EAAE,WAAW,UAAU,EAAE,EAAE,UAAU,EAAE,EAAE,UAAU,EAAE;gCAC9E,KAAK;oCACH,aAAa,UAAU,IAAI;oCAC3B,WAAW;oCACX,IAAI,YAAY;wCACd,WAAW;wCACX,UAAU,IAAI,CAAC;4CACb,OAAO;4CACP,MAAM;gDACJ,SAAS;4CACX;wCACF;oCACF;oCACA,OAAO,UAAU,MAAM,CAAC,UAAU;gCACpC,KAAK;gCACL,KAAK;oCACH,OAAO,UAAU,IAAI;4BACzB;wBACF,GAAG;oBACL;gBACF;YACF;QACF;yCAAG;QAAC;QAAS;KAAQ;IACrB,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,eAAe,QAAQ,EAAE;QACnD,OAAO;QACP,UAAU;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2130, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/sonner/src/index.tsx", "file:///G:/Augment%20code/node_modules/sonner/src/assets.tsx", "file:///G:/Augment%20code/node_modules/sonner/src/hooks.tsx", "file:///G:/Augment%20code/node_modules/sonner/src/state.ts", "file:///G:/Augment%20code/node_modules/sonner/dist/%23style-inject%3A%23style-inject", "file:///G:/Augment%20code/node_modules/sonner/src/styles.css", "file:///G:/Augment%20code/node_modules/sonner/src/types.ts"], "sourcesContent": ["'use client';\n\nimport React, { forwardRef, isValidElement } from 'react';\nimport ReactDOM from 'react-dom';\n\nimport { CloseIcon, getAsset, Loader } from './assets';\nimport { useIsDocumentHidden } from './hooks';\nimport { toast, ToastState } from './state';\nimport './styles.css';\nimport {\n  isAction,\n  SwipeDirection,\n  type ExternalToast,\n  type HeightT,\n  type ToasterProps,\n  type ToastProps,\n  type ToastT,\n  type ToastToDismiss,\n} from './types';\n\n// Visible toasts amount\nconst VISIBLE_TOASTS_AMOUNT = 3;\n\n// Viewport padding\nconst VIEWPORT_OFFSET = '32px';\n\n// Mobile viewport padding\nconst MOBILE_VIEWPORT_OFFSET = '16px';\n\n// Default lifetime of a toasts (in ms)\nconst TOAST_LIFETIME = 4000;\n\n// Default toast width\nconst TOAST_WIDTH = 356;\n\n// Default gap between toasts\nconst GAP = 14;\n\n// Threshold to dismiss a toast\nconst SWIPE_THRESHOLD = 20;\n\n// Equal to exit animation duration\nconst TIME_BEFORE_UNMOUNT = 200;\n\nfunction cn(...classes: (string | undefined)[]) {\n  return classes.filter(Boolean).join(' ');\n}\n\nfunction getDefaultSwipeDirections(position: string): Array<SwipeDirection> {\n  const [y, x] = position.split('-');\n  const directions: Array<SwipeDirection> = [];\n\n  if (y) {\n    directions.push(y as SwipeDirection);\n  }\n\n  if (x) {\n    directions.push(x as SwipeDirection);\n  }\n\n  return directions;\n}\n\nconst Toast = (props: ToastProps) => {\n  const {\n    invert: ToasterInvert,\n    toast,\n    unstyled,\n    interacting,\n    setHeights,\n    visibleToasts,\n    heights,\n    index,\n    toasts,\n    expanded,\n    removeToast,\n    defaultRichColors,\n    closeButton: closeButtonFromToaster,\n    style,\n    cancelButtonStyle,\n    actionButtonStyle,\n    className = '',\n    descriptionClassName = '',\n    duration: durationFromToaster,\n    position,\n    gap,\n    loadingIcon: loadingIconProp,\n    expandByDefault,\n    classNames,\n    icons,\n    closeButtonAriaLabel = 'Close toast',\n    pauseWhenPageIsHidden,\n  } = props;\n  const [swipeDirection, setSwipeDirection] = React.useState<'x' | 'y' | null>(null);\n  const [swipeOutDirection, setSwipeOutDirection] = React.useState<'left' | 'right' | 'up' | 'down' | null>(null);\n  const [mounted, setMounted] = React.useState(false);\n  const [removed, setRemoved] = React.useState(false);\n  const [swiping, setSwiping] = React.useState(false);\n  const [swipeOut, setSwipeOut] = React.useState(false);\n  const [isSwiped, setIsSwiped] = React.useState(false);\n  const [offsetBeforeRemove, setOffsetBeforeRemove] = React.useState(0);\n  const [initialHeight, setInitialHeight] = React.useState(0);\n  const remainingTime = React.useRef(toast.duration || durationFromToaster || TOAST_LIFETIME);\n  const dragStartTime = React.useRef<Date | null>(null);\n  const toastRef = React.useRef<HTMLLIElement>(null);\n  const isFront = index === 0;\n  const isVisible = index + 1 <= visibleToasts;\n  const toastType = toast.type;\n  const dismissible = toast.dismissible !== false;\n  const toastClassname = toast.className || '';\n  const toastDescriptionClassname = toast.descriptionClassName || '';\n  // Height index is used to calculate the offset as it gets updated before the toast array, which means we can calculate the new layout faster.\n  const heightIndex = React.useMemo(\n    () => heights.findIndex((height) => height.toastId === toast.id) || 0,\n    [heights, toast.id],\n  );\n  const closeButton = React.useMemo(\n    () => toast.closeButton ?? closeButtonFromToaster,\n    [toast.closeButton, closeButtonFromToaster],\n  );\n  const duration = React.useMemo(\n    () => toast.duration || durationFromToaster || TOAST_LIFETIME,\n    [toast.duration, durationFromToaster],\n  );\n  const closeTimerStartTimeRef = React.useRef(0);\n  const offset = React.useRef(0);\n  const lastCloseTimerStartTimeRef = React.useRef(0);\n  const pointerStartRef = React.useRef<{ x: number; y: number } | null>(null);\n  const [y, x] = position.split('-');\n  const toastsHeightBefore = React.useMemo(() => {\n    return heights.reduce((prev, curr, reducerIndex) => {\n      // Calculate offset up until current toast\n      if (reducerIndex >= heightIndex) {\n        return prev;\n      }\n\n      return prev + curr.height;\n    }, 0);\n  }, [heights, heightIndex]);\n  const isDocumentHidden = useIsDocumentHidden();\n\n  const invert = toast.invert || ToasterInvert;\n  const disabled = toastType === 'loading';\n\n  offset.current = React.useMemo(() => heightIndex * gap + toastsHeightBefore, [heightIndex, toastsHeightBefore]);\n\n  React.useEffect(() => {\n    remainingTime.current = duration;\n  }, [duration]);\n\n  React.useEffect(() => {\n    // Trigger enter animation without using CSS animation\n    setMounted(true);\n  }, []);\n\n  React.useEffect(() => {\n    const toastNode = toastRef.current;\n    if (toastNode) {\n      const height = toastNode.getBoundingClientRect().height;\n      // Add toast height to heights array after the toast is mounted\n      setInitialHeight(height);\n      setHeights((h) => [{ toastId: toast.id, height, position: toast.position }, ...h]);\n      return () => setHeights((h) => h.filter((height) => height.toastId !== toast.id));\n    }\n  }, [setHeights, toast.id]);\n\n  React.useLayoutEffect(() => {\n    if (!mounted) return;\n    const toastNode = toastRef.current;\n    const originalHeight = toastNode.style.height;\n    toastNode.style.height = 'auto';\n    const newHeight = toastNode.getBoundingClientRect().height;\n    toastNode.style.height = originalHeight;\n\n    setInitialHeight(newHeight);\n\n    setHeights((heights) => {\n      const alreadyExists = heights.find((height) => height.toastId === toast.id);\n      if (!alreadyExists) {\n        return [{ toastId: toast.id, height: newHeight, position: toast.position }, ...heights];\n      } else {\n        return heights.map((height) => (height.toastId === toast.id ? { ...height, height: newHeight } : height));\n      }\n    });\n  }, [mounted, toast.title, toast.description, setHeights, toast.id]);\n\n  const deleteToast = React.useCallback(() => {\n    // Save the offset for the exit swipe animation\n    setRemoved(true);\n    setOffsetBeforeRemove(offset.current);\n    setHeights((h) => h.filter((height) => height.toastId !== toast.id));\n\n    setTimeout(() => {\n      removeToast(toast);\n    }, TIME_BEFORE_UNMOUNT);\n  }, [toast, removeToast, setHeights, offset]);\n\n  React.useEffect(() => {\n    if ((toast.promise && toastType === 'loading') || toast.duration === Infinity || toast.type === 'loading') return;\n    let timeoutId: NodeJS.Timeout;\n\n    // Pause the timer on each hover\n    const pauseTimer = () => {\n      if (lastCloseTimerStartTimeRef.current < closeTimerStartTimeRef.current) {\n        // Get the elapsed time since the timer started\n        const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n\n        remainingTime.current = remainingTime.current - elapsedTime;\n      }\n\n      lastCloseTimerStartTimeRef.current = new Date().getTime();\n    };\n\n    const startTimer = () => {\n      // setTimeout(, Infinity) behaves as if the delay is 0.\n      // As a result, the toast would be closed immediately, giving the appearance that it was never rendered.\n      // See: https://github.com/denysdovhan/wtfjs?tab=readme-ov-file#an-infinite-timeout\n      if (remainingTime.current === Infinity) return;\n\n      closeTimerStartTimeRef.current = new Date().getTime();\n\n      // Let the toast know it has started\n      timeoutId = setTimeout(() => {\n        toast.onAutoClose?.(toast);\n        deleteToast();\n      }, remainingTime.current);\n    };\n\n    if (expanded || interacting || (pauseWhenPageIsHidden && isDocumentHidden)) {\n      pauseTimer();\n    } else {\n      startTimer();\n    }\n\n    return () => clearTimeout(timeoutId);\n  }, [expanded, interacting, toast, toastType, pauseWhenPageIsHidden, isDocumentHidden, deleteToast]);\n\n  React.useEffect(() => {\n    if (toast.delete) {\n      deleteToast();\n    }\n  }, [deleteToast, toast.delete]);\n\n  function getLoadingIcon() {\n    if (icons?.loading) {\n      return (\n        <div\n          className={cn(classNames?.loader, toast?.classNames?.loader, 'sonner-loader')}\n          data-visible={toastType === 'loading'}\n        >\n          {icons.loading}\n        </div>\n      );\n    }\n\n    if (loadingIconProp) {\n      return (\n        <div\n          className={cn(classNames?.loader, toast?.classNames?.loader, 'sonner-loader')}\n          data-visible={toastType === 'loading'}\n        >\n          {loadingIconProp}\n        </div>\n      );\n    }\n    return <Loader className={cn(classNames?.loader, toast?.classNames?.loader)} visible={toastType === 'loading'} />;\n  }\n\n  return (\n    <li\n      tabIndex={0}\n      ref={toastRef}\n      className={cn(\n        className,\n        toastClassname,\n        classNames?.toast,\n        toast?.classNames?.toast,\n        classNames?.default,\n        classNames?.[toastType],\n        toast?.classNames?.[toastType],\n      )}\n      data-sonner-toast=\"\"\n      data-rich-colors={toast.richColors ?? defaultRichColors}\n      data-styled={!Boolean(toast.jsx || toast.unstyled || unstyled)}\n      data-mounted={mounted}\n      data-promise={Boolean(toast.promise)}\n      data-swiped={isSwiped}\n      data-removed={removed}\n      data-visible={isVisible}\n      data-y-position={y}\n      data-x-position={x}\n      data-index={index}\n      data-front={isFront}\n      data-swiping={swiping}\n      data-dismissible={dismissible}\n      data-type={toastType}\n      data-invert={invert}\n      data-swipe-out={swipeOut}\n      data-swipe-direction={swipeOutDirection}\n      data-expanded={Boolean(expanded || (expandByDefault && mounted))}\n      style={\n        {\n          '--index': index,\n          '--toasts-before': index,\n          '--z-index': toasts.length - index,\n          '--offset': `${removed ? offsetBeforeRemove : offset.current}px`,\n          '--initial-height': expandByDefault ? 'auto' : `${initialHeight}px`,\n          ...style,\n          ...toast.style,\n        } as React.CSSProperties\n      }\n      onDragEnd={() => {\n        setSwiping(false);\n        setSwipeDirection(null);\n        pointerStartRef.current = null;\n      }}\n      onPointerDown={(event) => {\n        if (disabled || !dismissible) return;\n        dragStartTime.current = new Date();\n        setOffsetBeforeRemove(offset.current);\n        // Ensure we maintain correct pointer capture even when going outside of the toast (e.g. when swiping)\n        (event.target as HTMLElement).setPointerCapture(event.pointerId);\n        if ((event.target as HTMLElement).tagName === 'BUTTON') return;\n        setSwiping(true);\n        pointerStartRef.current = { x: event.clientX, y: event.clientY };\n      }}\n      onPointerUp={() => {\n        if (swipeOut || !dismissible) return;\n\n        pointerStartRef.current = null;\n        const swipeAmountX = Number(\n          toastRef.current?.style.getPropertyValue('--swipe-amount-x').replace('px', '') || 0,\n        );\n        const swipeAmountY = Number(\n          toastRef.current?.style.getPropertyValue('--swipe-amount-y').replace('px', '') || 0,\n        );\n        const timeTaken = new Date().getTime() - dragStartTime.current?.getTime();\n\n        const swipeAmount = swipeDirection === 'x' ? swipeAmountX : swipeAmountY;\n        const velocity = Math.abs(swipeAmount) / timeTaken;\n\n        if (Math.abs(swipeAmount) >= SWIPE_THRESHOLD || velocity > 0.11) {\n          setOffsetBeforeRemove(offset.current);\n          toast.onDismiss?.(toast);\n\n          if (swipeDirection === 'x') {\n            setSwipeOutDirection(swipeAmountX > 0 ? 'right' : 'left');\n          } else {\n            setSwipeOutDirection(swipeAmountY > 0 ? 'down' : 'up');\n          }\n\n          deleteToast();\n          setSwipeOut(true);\n          setIsSwiped(false);\n          return;\n        }\n\n        setSwiping(false);\n        setSwipeDirection(null);\n      }}\n      onPointerMove={(event) => {\n        if (!pointerStartRef.current || !dismissible) return;\n\n        const isHighlighted = window.getSelection()?.toString().length > 0;\n        if (isHighlighted) return;\n\n        const yDelta = event.clientY - pointerStartRef.current.y;\n        const xDelta = event.clientX - pointerStartRef.current.x;\n\n        const swipeDirections = props.swipeDirections ?? getDefaultSwipeDirections(position);\n\n        // Determine swipe direction if not already locked\n        if (!swipeDirection && (Math.abs(xDelta) > 1 || Math.abs(yDelta) > 1)) {\n          setSwipeDirection(Math.abs(xDelta) > Math.abs(yDelta) ? 'x' : 'y');\n        }\n\n        let swipeAmount = { x: 0, y: 0 };\n\n        // Only apply swipe in the locked direction\n        if (swipeDirection === 'y') {\n          // Handle vertical swipes\n          if (swipeDirections.includes('top') || swipeDirections.includes('bottom')) {\n            if (swipeDirections.includes('top') && yDelta < 0) {\n              swipeAmount.y = yDelta;\n            } else if (swipeDirections.includes('bottom') && yDelta > 0) {\n              swipeAmount.y = yDelta;\n            }\n          }\n        } else if (swipeDirection === 'x') {\n          // Handle horizontal swipes\n          if (swipeDirections.includes('left') || swipeDirections.includes('right')) {\n            if (swipeDirections.includes('left') && xDelta < 0) {\n              swipeAmount.x = xDelta;\n            } else if (swipeDirections.includes('right') && xDelta > 0) {\n              swipeAmount.x = xDelta;\n            }\n          }\n        }\n\n        if (Math.abs(swipeAmount.x) > 0 || Math.abs(swipeAmount.y) > 0) {\n          setIsSwiped(true);\n        }\n\n        // Apply transform using both x and y values\n        toastRef.current?.style.setProperty('--swipe-amount-x', `${swipeAmount.x}px`);\n        toastRef.current?.style.setProperty('--swipe-amount-y', `${swipeAmount.y}px`);\n      }}\n    >\n      {closeButton && !toast.jsx ? (\n        <button\n          aria-label={closeButtonAriaLabel}\n          data-disabled={disabled}\n          data-close-button\n          onClick={\n            disabled || !dismissible\n              ? () => {}\n              : () => {\n                  deleteToast();\n                  toast.onDismiss?.(toast);\n                }\n          }\n          className={cn(classNames?.closeButton, toast?.classNames?.closeButton)}\n        >\n          {icons?.close ?? CloseIcon}\n        </button>\n      ) : null}\n      {/* TODO: This can be cleaner */}\n      {toast.jsx || isValidElement(toast.title) ? (\n        toast.jsx ? (\n          toast.jsx\n        ) : typeof toast.title === 'function' ? (\n          toast.title()\n        ) : (\n          toast.title\n        )\n      ) : (\n        <>\n          {toastType || toast.icon || toast.promise ? (\n            <div data-icon=\"\" className={cn(classNames?.icon, toast?.classNames?.icon)}>\n              {toast.promise || (toast.type === 'loading' && !toast.icon) ? toast.icon || getLoadingIcon() : null}\n              {toast.type !== 'loading' ? toast.icon || icons?.[toastType] || getAsset(toastType) : null}\n            </div>\n          ) : null}\n\n          <div data-content=\"\" className={cn(classNames?.content, toast?.classNames?.content)}>\n            <div data-title=\"\" className={cn(classNames?.title, toast?.classNames?.title)}>\n              {typeof toast.title === 'function' ? toast.title() : toast.title}\n            </div>\n            {toast.description ? (\n              <div\n                data-description=\"\"\n                className={cn(\n                  descriptionClassName,\n                  toastDescriptionClassname,\n                  classNames?.description,\n                  toast?.classNames?.description,\n                )}\n              >\n                {typeof toast.description === 'function' ? toast.description() : toast.description}\n              </div>\n            ) : null}\n          </div>\n          {isValidElement(toast.cancel) ? (\n            toast.cancel\n          ) : toast.cancel && isAction(toast.cancel) ? (\n            <button\n              data-button\n              data-cancel\n              style={toast.cancelButtonStyle || cancelButtonStyle}\n              onClick={(event) => {\n                // We need to check twice because typescript\n                if (!isAction(toast.cancel)) return;\n                if (!dismissible) return;\n                toast.cancel.onClick?.(event);\n                deleteToast();\n              }}\n              className={cn(classNames?.cancelButton, toast?.classNames?.cancelButton)}\n            >\n              {toast.cancel.label}\n            </button>\n          ) : null}\n          {isValidElement(toast.action) ? (\n            toast.action\n          ) : toast.action && isAction(toast.action) ? (\n            <button\n              data-button\n              data-action\n              style={toast.actionButtonStyle || actionButtonStyle}\n              onClick={(event) => {\n                // We need to check twice because typescript\n                if (!isAction(toast.action)) return;\n                toast.action.onClick?.(event);\n                if (event.defaultPrevented) return;\n                deleteToast();\n              }}\n              className={cn(classNames?.actionButton, toast?.classNames?.actionButton)}\n            >\n              {toast.action.label}\n            </button>\n          ) : null}\n        </>\n      )}\n    </li>\n  );\n};\n\nfunction getDocumentDirection(): ToasterProps['dir'] {\n  if (typeof window === 'undefined') return 'ltr';\n  if (typeof document === 'undefined') return 'ltr'; // For Fresh purpose\n\n  const dirAttribute = document.documentElement.getAttribute('dir');\n\n  if (dirAttribute === 'auto' || !dirAttribute) {\n    return window.getComputedStyle(document.documentElement).direction as ToasterProps['dir'];\n  }\n\n  return dirAttribute as ToasterProps['dir'];\n}\n\nfunction assignOffset(defaultOffset: ToasterProps['offset'], mobileOffset: ToasterProps['mobileOffset']) {\n  const styles = {} as React.CSSProperties;\n\n  [defaultOffset, mobileOffset].forEach((offset, index) => {\n    const isMobile = index === 1;\n    const prefix = isMobile ? '--mobile-offset' : '--offset';\n    const defaultValue = isMobile ? MOBILE_VIEWPORT_OFFSET : VIEWPORT_OFFSET;\n\n    function assignAll(offset: string | number) {\n      ['top', 'right', 'bottom', 'left'].forEach((key) => {\n        styles[`${prefix}-${key}`] = typeof offset === 'number' ? `${offset}px` : offset;\n      });\n    }\n\n    if (typeof offset === 'number' || typeof offset === 'string') {\n      assignAll(offset);\n    } else if (typeof offset === 'object') {\n      ['top', 'right', 'bottom', 'left'].forEach((key) => {\n        if (offset[key] === undefined) {\n          styles[`${prefix}-${key}`] = defaultValue;\n        } else {\n          styles[`${prefix}-${key}`] = typeof offset[key] === 'number' ? `${offset[key]}px` : offset[key];\n        }\n      });\n    } else {\n      assignAll(defaultValue);\n    }\n  });\n\n  return styles;\n}\n\nfunction useSonner() {\n  const [activeToasts, setActiveToasts] = React.useState<ToastT[]>([]);\n\n  React.useEffect(() => {\n    return ToastState.subscribe((toast) => {\n      if ((toast as ToastToDismiss).dismiss) {\n        setTimeout(() => {\n          ReactDOM.flushSync(() => {\n            setActiveToasts((toasts) => toasts.filter((t) => t.id !== toast.id));\n          });\n        });\n        return;\n      }\n\n      // Prevent batching, temp solution.\n      setTimeout(() => {\n        ReactDOM.flushSync(() => {\n          setActiveToasts((toasts) => {\n            const indexOfExistingToast = toasts.findIndex((t) => t.id === toast.id);\n\n            // Update the toast if it already exists\n            if (indexOfExistingToast !== -1) {\n              return [\n                ...toasts.slice(0, indexOfExistingToast),\n                { ...toasts[indexOfExistingToast], ...toast },\n                ...toasts.slice(indexOfExistingToast + 1),\n              ];\n            }\n\n            return [toast, ...toasts];\n          });\n        });\n      });\n    });\n  }, []);\n\n  return {\n    toasts: activeToasts,\n  };\n}\n\nconst Toaster = forwardRef<HTMLElement, ToasterProps>(function Toaster(props, ref) {\n  const {\n    invert,\n    position = 'bottom-right',\n    hotkey = ['altKey', 'KeyT'],\n    expand,\n    closeButton,\n    className,\n    offset,\n    mobileOffset,\n    theme = 'light',\n    richColors,\n    duration,\n    style,\n    visibleToasts = VISIBLE_TOASTS_AMOUNT,\n    toastOptions,\n    dir = getDocumentDirection(),\n    gap = GAP,\n    loadingIcon,\n    icons,\n    containerAriaLabel = 'Notifications',\n    pauseWhenPageIsHidden,\n  } = props;\n  const [toasts, setToasts] = React.useState<ToastT[]>([]);\n  const possiblePositions = React.useMemo(() => {\n    return Array.from(\n      new Set([position].concat(toasts.filter((toast) => toast.position).map((toast) => toast.position))),\n    );\n  }, [toasts, position]);\n  const [heights, setHeights] = React.useState<HeightT[]>([]);\n  const [expanded, setExpanded] = React.useState(false);\n  const [interacting, setInteracting] = React.useState(false);\n  const [actualTheme, setActualTheme] = React.useState(\n    theme !== 'system'\n      ? theme\n      : typeof window !== 'undefined'\n      ? window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches\n        ? 'dark'\n        : 'light'\n      : 'light',\n  );\n\n  const listRef = React.useRef<HTMLOListElement>(null);\n  const hotkeyLabel = hotkey.join('+').replace(/Key/g, '').replace(/Digit/g, '');\n  const lastFocusedElementRef = React.useRef<HTMLElement>(null);\n  const isFocusWithinRef = React.useRef(false);\n\n  const removeToast = React.useCallback((toastToRemove: ToastT) => {\n    setToasts((toasts) => {\n      if (!toasts.find((toast) => toast.id === toastToRemove.id)?.delete) {\n        ToastState.dismiss(toastToRemove.id);\n      }\n\n      return toasts.filter(({ id }) => id !== toastToRemove.id);\n    });\n  }, []);\n\n  React.useEffect(() => {\n    return ToastState.subscribe((toast) => {\n      if ((toast as ToastToDismiss).dismiss) {\n        setToasts((toasts) => toasts.map((t) => (t.id === toast.id ? { ...t, delete: true } : t)));\n        return;\n      }\n\n      // Prevent batching, temp solution.\n      setTimeout(() => {\n        ReactDOM.flushSync(() => {\n          setToasts((toasts) => {\n            const indexOfExistingToast = toasts.findIndex((t) => t.id === toast.id);\n\n            // Update the toast if it already exists\n            if (indexOfExistingToast !== -1) {\n              return [\n                ...toasts.slice(0, indexOfExistingToast),\n                { ...toasts[indexOfExistingToast], ...toast },\n                ...toasts.slice(indexOfExistingToast + 1),\n              ];\n            }\n\n            return [toast, ...toasts];\n          });\n        });\n      });\n    });\n  }, []);\n\n  React.useEffect(() => {\n    if (theme !== 'system') {\n      setActualTheme(theme);\n      return;\n    }\n\n    if (theme === 'system') {\n      // check if current preference is dark\n      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n        // it's currently dark\n        setActualTheme('dark');\n      } else {\n        // it's not dark\n        setActualTheme('light');\n      }\n    }\n\n    if (typeof window === 'undefined') return;\n    const darkMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n\n    try {\n      // Chrome & Firefox\n      darkMediaQuery.addEventListener('change', ({ matches }) => {\n        if (matches) {\n          setActualTheme('dark');\n        } else {\n          setActualTheme('light');\n        }\n      });\n    } catch (error) {\n      // Safari < 14\n      darkMediaQuery.addListener(({ matches }) => {\n        try {\n          if (matches) {\n            setActualTheme('dark');\n          } else {\n            setActualTheme('light');\n          }\n        } catch (e) {\n          console.error(e);\n        }\n      });\n    }\n  }, [theme]);\n\n  React.useEffect(() => {\n    // Ensure expanded is always false when no toasts are present / only one left\n    if (toasts.length <= 1) {\n      setExpanded(false);\n    }\n  }, [toasts]);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      const isHotkeyPressed = hotkey.every((key) => (event as any)[key] || event.code === key);\n\n      if (isHotkeyPressed) {\n        setExpanded(true);\n        listRef.current?.focus();\n      }\n\n      if (\n        event.code === 'Escape' &&\n        (document.activeElement === listRef.current || listRef.current?.contains(document.activeElement))\n      ) {\n        setExpanded(false);\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [hotkey]);\n\n  React.useEffect(() => {\n    if (listRef.current) {\n      return () => {\n        if (lastFocusedElementRef.current) {\n          lastFocusedElementRef.current.focus({ preventScroll: true });\n          lastFocusedElementRef.current = null;\n          isFocusWithinRef.current = false;\n        }\n      };\n    }\n  }, [listRef.current]);\n\n  return (\n    // Remove item from normal navigation flow, only available via hotkey\n    <section\n      ref={ref}\n      aria-label={`${containerAriaLabel} ${hotkeyLabel}`}\n      tabIndex={-1}\n      aria-live=\"polite\"\n      aria-relevant=\"additions text\"\n      aria-atomic=\"false\"\n      suppressHydrationWarning\n    >\n      {possiblePositions.map((position, index) => {\n        const [y, x] = position.split('-');\n\n        if (!toasts.length) return null;\n\n        return (\n          <ol\n            key={position}\n            dir={dir === 'auto' ? getDocumentDirection() : dir}\n            tabIndex={-1}\n            ref={listRef}\n            className={className}\n            data-sonner-toaster\n            data-theme={actualTheme}\n            data-y-position={y}\n            data-lifted={expanded && toasts.length > 1 && !expand}\n            data-x-position={x}\n            style={\n              {\n                '--front-toast-height': `${heights[0]?.height || 0}px`,\n                '--width': `${TOAST_WIDTH}px`,\n                '--gap': `${gap}px`,\n                ...style,\n                ...assignOffset(offset, mobileOffset),\n              } as React.CSSProperties\n            }\n            onBlur={(event) => {\n              if (isFocusWithinRef.current && !event.currentTarget.contains(event.relatedTarget)) {\n                isFocusWithinRef.current = false;\n                if (lastFocusedElementRef.current) {\n                  lastFocusedElementRef.current.focus({ preventScroll: true });\n                  lastFocusedElementRef.current = null;\n                }\n              }\n            }}\n            onFocus={(event) => {\n              const isNotDismissible =\n                event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n\n              if (isNotDismissible) return;\n\n              if (!isFocusWithinRef.current) {\n                isFocusWithinRef.current = true;\n                lastFocusedElementRef.current = event.relatedTarget as HTMLElement;\n              }\n            }}\n            onMouseEnter={() => setExpanded(true)}\n            onMouseMove={() => setExpanded(true)}\n            onMouseLeave={() => {\n              // Avoid setting expanded to false when interacting with a toast, e.g. swiping\n              if (!interacting) {\n                setExpanded(false);\n              }\n            }}\n            onDragEnd={() => setExpanded(false)}\n            onPointerDown={(event) => {\n              const isNotDismissible =\n                event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n\n              if (isNotDismissible) return;\n              setInteracting(true);\n            }}\n            onPointerUp={() => setInteracting(false)}\n          >\n            {toasts\n              .filter((toast) => (!toast.position && index === 0) || toast.position === position)\n              .map((toast, index) => (\n                <Toast\n                  key={toast.id}\n                  icons={icons}\n                  index={index}\n                  toast={toast}\n                  defaultRichColors={richColors}\n                  duration={toastOptions?.duration ?? duration}\n                  className={toastOptions?.className}\n                  descriptionClassName={toastOptions?.descriptionClassName}\n                  invert={invert}\n                  visibleToasts={visibleToasts}\n                  closeButton={toastOptions?.closeButton ?? closeButton}\n                  interacting={interacting}\n                  position={position}\n                  style={toastOptions?.style}\n                  unstyled={toastOptions?.unstyled}\n                  classNames={toastOptions?.classNames}\n                  cancelButtonStyle={toastOptions?.cancelButtonStyle}\n                  actionButtonStyle={toastOptions?.actionButtonStyle}\n                  removeToast={removeToast}\n                  toasts={toasts.filter((t) => t.position == toast.position)}\n                  heights={heights.filter((h) => h.position == toast.position)}\n                  setHeights={setHeights}\n                  expandByDefault={expand}\n                  gap={gap}\n                  loadingIcon={loadingIcon}\n                  expanded={expanded}\n                  pauseWhenPageIsHidden={pauseWhenPageIsHidden}\n                  swipeDirections={props.swipeDirections}\n                />\n              ))}\n          </ol>\n        );\n      })}\n    </section>\n  );\n});\nexport { toast, Toaster, type ExternalToast, type ToastT, type ToasterProps, useSonner };\nexport { type ToastClassnames, type ToastToDismiss, type Action } from './types';\n", "'use client';\nimport React from 'react';\nimport type { ToastTypes } from './types';\n\nexport const getAsset = (type: ToastTypes): JSX.Element | null => {\n  switch (type) {\n    case 'success':\n      return SuccessIcon;\n\n    case 'info':\n      return InfoIcon;\n\n    case 'warning':\n      return WarningIcon;\n\n    case 'error':\n      return ErrorIcon;\n\n    default:\n      return null;\n  }\n};\n\nconst bars = Array(12).fill(0);\n\nexport const Loader = ({ visible, className }: { visible: boolean, className?: string }) => {\n  return (\n    <div className={['sonner-loading-wrapper', className].filter(Boolean).join(' ')} data-visible={visible}>\n      <div className=\"sonner-spinner\">\n        {bars.map((_, i) => (\n          <div className=\"sonner-loading-bar\" key={`spinner-bar-${i}`} />\n        ))}\n      </div>\n    </div>\n  );\n};\n\nconst SuccessIcon = (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" height=\"20\" width=\"20\">\n    <path\n      fillRule=\"evenodd\"\n      d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\n\nconst WarningIcon = (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"currentColor\" height=\"20\" width=\"20\">\n    <path\n      fillRule=\"evenodd\"\n      d=\"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\n\nconst InfoIcon = (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" height=\"20\" width=\"20\">\n    <path\n      fillRule=\"evenodd\"\n      d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\n\nconst ErrorIcon = (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" height=\"20\" width=\"20\">\n    <path\n      fillRule=\"evenodd\"\n      d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\n\nexport const CloseIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"12\"\n    height=\"12\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"1.5\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"></line>\n    <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"></line>\n  </svg>\n);\n", "import React from 'react';\n\nexport const useIsDocumentHidden = () => {\n  const [isDocumentHidden, setIsDocumentHidden] = React.useState(document.hidden);\n\n  React.useEffect(() => {\n    const callback = () => {\n      setIsDocumentHidden(document.hidden);\n    };\n    document.addEventListener('visibilitychange', callback);\n    return () => window.removeEventListener('visibilitychange', callback);\n  }, []);\n\n  return isDocumentHidden;\n};\n", "import type { ExternalToast, PromiseD<PERSON>, PromiseT, ToastT, ToastToDismiss, ToastTypes } from './types';\n\nimport React from 'react';\n\nlet toastsCounter = 1;\n\ntype titleT = (() => React.ReactNode) | React.ReactNode;\n\nclass Observer {\n  subscribers: Array<(toast: ExternalToast | ToastToDismiss) => void>;\n  toasts: Array<ToastT | ToastToDismiss>;\n  dismissedToasts: Set<string | number>;\n\n  constructor() {\n    this.subscribers = [];\n    this.toasts = [];\n    this.dismissedToasts = new Set();\n  }\n\n  // We use arrow functions to maintain the correct `this` reference\n  subscribe = (subscriber: (toast: ToastT | ToastToDismiss) => void) => {\n    this.subscribers.push(subscriber);\n\n    return () => {\n      const index = this.subscribers.indexOf(subscriber);\n      this.subscribers.splice(index, 1);\n    };\n  };\n\n  publish = (data: ToastT) => {\n    this.subscribers.forEach((subscriber) => subscriber(data));\n  };\n\n  addToast = (data: ToastT) => {\n    this.publish(data);\n    this.toasts = [...this.toasts, data];\n  };\n\n  create = (\n    data: ExternalToast & {\n      message?: titleT;\n      type?: ToastTypes;\n      promise?: PromiseT;\n      jsx?: React.ReactElement;\n    },\n  ) => {\n    const { message, ...rest } = data;\n    const id = typeof data?.id === 'number' || data.id?.length > 0 ? data.id : toastsCounter++;\n    const alreadyExists = this.toasts.find((toast) => {\n      return toast.id === id;\n    });\n    const dismissible = data.dismissible === undefined ? true : data.dismissible;\n\n    if (this.dismissedToasts.has(id)) {\n      this.dismissedToasts.delete(id);\n    }\n\n    if (alreadyExists) {\n      this.toasts = this.toasts.map((toast) => {\n        if (toast.id === id) {\n          this.publish({ ...toast, ...data, id, title: message });\n          return {\n            ...toast,\n            ...data,\n            id,\n            dismissible,\n            title: message,\n          };\n        }\n\n        return toast;\n      });\n    } else {\n      this.addToast({ title: message, ...rest, dismissible, id });\n    }\n\n    return id;\n  };\n\n  dismiss = (id?: number | string) => {\n    this.dismissedToasts.add(id);\n\n    if (!id) {\n      this.toasts.forEach((toast) => {\n        this.subscribers.forEach((subscriber) => subscriber({ id: toast.id, dismiss: true }));\n      });\n    }\n    this.subscribers.forEach((subscriber) => subscriber({ id, dismiss: true }));\n    return id;\n  };\n\n  message = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, message });\n  };\n\n  error = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, message, type: 'error' });\n  };\n\n  success = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, type: 'success', message });\n  };\n\n  info = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, type: 'info', message });\n  };\n\n  warning = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, type: 'warning', message });\n  };\n\n  loading = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, type: 'loading', message });\n  };\n\n  promise = <ToastData>(promise: PromiseT<ToastData>, data?: PromiseData<ToastData>) => {\n    if (!data) {\n      // Nothing to show\n      return;\n    }\n\n    let id: string | number | undefined = undefined;\n    if (data.loading !== undefined) {\n      id = this.create({\n        ...data,\n        promise,\n        type: 'loading',\n        message: data.loading,\n        description: typeof data.description !== 'function' ? data.description : undefined,\n      });\n    }\n\n    const p = promise instanceof Promise ? promise : promise();\n\n    let shouldDismiss = id !== undefined;\n    let result: ['resolve', ToastData] | ['reject', unknown];\n\n    const originalPromise = p\n      .then(async (response) => {\n        result = ['resolve', response];\n        const isReactElementResponse = React.isValidElement(response);\n        if (isReactElementResponse) {\n          shouldDismiss = false;\n          this.create({ id, type: 'default', message: response });\n        } else if (isHttpResponse(response) && !response.ok) {\n          shouldDismiss = false;\n          const message =\n            typeof data.error === 'function' ? await data.error(`HTTP error! status: ${response.status}`) : data.error;\n          const description =\n            typeof data.description === 'function'\n              ? await data.description(`HTTP error! status: ${response.status}`)\n              : data.description;\n          this.create({ id, type: 'error', message, description });\n        } else if (data.success !== undefined) {\n          shouldDismiss = false;\n          const message = typeof data.success === 'function' ? await data.success(response) : data.success;\n          const description =\n            typeof data.description === 'function' ? await data.description(response) : data.description;\n          this.create({ id, type: 'success', message, description });\n        }\n      })\n      .catch(async (error) => {\n        result = ['reject', error];\n        if (data.error !== undefined) {\n          shouldDismiss = false;\n          const message = typeof data.error === 'function' ? await data.error(error) : data.error;\n          const description = typeof data.description === 'function' ? await data.description(error) : data.description;\n          this.create({ id, type: 'error', message, description });\n        }\n      })\n      .finally(() => {\n        if (shouldDismiss) {\n          // Toast is still in load state (and will be indefinitely — dismiss it)\n          this.dismiss(id);\n          id = undefined;\n        }\n\n        data.finally?.();\n      });\n\n    const unwrap = () =>\n      new Promise<ToastData>((resolve, reject) =>\n        originalPromise.then(() => (result[0] === 'reject' ? reject(result[1]) : resolve(result[1]))).catch(reject),\n      );\n\n    if (typeof id !== 'string' && typeof id !== 'number') {\n      // cannot Object.assign on undefined\n      return { unwrap };\n    } else {\n      return Object.assign(id, { unwrap });\n    }\n  };\n\n  custom = (jsx: (id: number | string) => React.ReactElement, data?: ExternalToast) => {\n    const id = data?.id || toastsCounter++;\n    this.create({ jsx: jsx(id), id, ...data });\n    return id;\n  };\n\n  getActiveToasts = () => {\n    return this.toasts.filter((toast) => !this.dismissedToasts.has(toast.id));\n  };\n}\n\nexport const ToastState = new Observer();\n\n// bind this to the toast function\nconst toastFunction = (message: titleT, data?: ExternalToast) => {\n  const id = data?.id || toastsCounter++;\n\n  ToastState.addToast({\n    title: message,\n    ...data,\n    id,\n  });\n  return id;\n};\n\nconst isHttpResponse = (data: any): data is Response => {\n  return (\n    data &&\n    typeof data === 'object' &&\n    'ok' in data &&\n    typeof data.ok === 'boolean' &&\n    'status' in data &&\n    typeof data.status === 'number'\n  );\n};\n\nconst basicToast = toastFunction;\n\nconst getHistory = () => ToastState.toasts;\nconst getToasts = () => ToastState.getActiveToasts();\n\n// We use `Object.assign` to maintain the correct types as we would lose them otherwise\nexport const toast = Object.assign(\n  basicToast,\n  {\n    success: ToastState.success,\n    info: ToastState.info,\n    warning: ToastState.warning,\n    error: ToastState.error,\n    custom: ToastState.custom,\n    message: ToastState.message,\n    promise: ToastState.promise,\n    dismiss: ToastState.dismiss,\n    loading: ToastState.loading,\n  },\n  { getHistory, getToasts },\n);\n", "\n          export default function styleInject(css, { insertAt } = {}) {\n            if (!css || typeof document === 'undefined') return\n          \n            const head = document.head || document.getElementsByTagName('head')[0]\n            const style = document.createElement('style')\n            style.type = 'text/css'\n          \n            if (insertAt === 'top') {\n              if (head.firstChild) {\n                head.insertBefore(style, head.firstChild)\n              } else {\n                head.appendChild(style)\n              }\n            } else {\n              head.appendChild(style)\n            }\n          \n            if (style.styleSheet) {\n              style.styleSheet.cssText = css\n            } else {\n              style.appendChild(document.createTextNode(css))\n            }\n          }\n          ", "import styleInject from '#style-inject';styleInject(\":where(html[dir=\\\"ltr\\\"]),:where([data-sonner-toaster][dir=\\\"ltr\\\"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir=\\\"rtl\\\"]),:where([data-sonner-toaster][dir=\\\"rtl\\\"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted=\\\"true\\\"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted=\\\"true\\\"]){transform:none}}:where([data-sonner-toaster][data-x-position=\\\"right\\\"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position=\\\"left\\\"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position=\\\"center\\\"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position=\\\"top\\\"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position=\\\"bottom\\\"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled=\\\"true\\\"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position=\\\"top\\\"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position=\\\"bottom\\\"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise=\\\"true\\\"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme=\\\"dark\\\"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled=\\\"true\\\"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping=\\\"true\\\"]):before{content:\\\"\\\";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position=\\\"top\\\"][data-swiping=\\\"true\\\"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position=\\\"bottom\\\"][data-swiping=\\\"true\\\"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping=\\\"false\\\"][data-removed=\\\"true\\\"]):before{content:\\\"\\\";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:\\\"\\\";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted=\\\"true\\\"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded=\\\"false\\\"][data-front=\\\"false\\\"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded=\\\"false\\\"][data-front=\\\"false\\\"][data-styled=\\\"true\\\"])>*{opacity:0}:where([data-sonner-toast][data-visible=\\\"false\\\"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted=\\\"true\\\"][data-expanded=\\\"true\\\"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed=\\\"true\\\"][data-front=\\\"true\\\"][data-swipe-out=\\\"false\\\"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\\\"true\\\"][data-front=\\\"false\\\"][data-swipe-out=\\\"false\\\"][data-expanded=\\\"true\\\"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\\\"true\\\"][data-front=\\\"false\\\"][data-swipe-out=\\\"false\\\"][data-expanded=\\\"false\\\"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed=\\\"true\\\"][data-front=\\\"false\\\"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\\n\")", "import React from 'react';\n\nexport type ToastTypes = 'normal' | 'action' | 'success' | 'info' | 'warning' | 'error' | 'loading' | 'default';\n\nexport type PromiseT<Data = any> = Promise<Data> | (() => Promise<Data>);\n\nexport type PromiseTResult<Data = any> =\n  | string\n  | React.ReactNode\n  | ((data: Data) => React.ReactNode | string | Promise<React.ReactNode | string>);\n\nexport type PromiseExternalToast = Omit<ExternalToast, 'description'>;\n\nexport type PromiseData<ToastData = any> = PromiseExternalToast & {\n  loading?: string | React.ReactNode;\n  success?: PromiseTResult<ToastData>;\n  error?: PromiseTResult;\n  description?: PromiseTResult;\n  finally?: () => void | Promise<void>;\n};\n\nexport interface ToastClassnames {\n  toast?: string;\n  title?: string;\n  description?: string;\n  loader?: string;\n  closeButton?: string;\n  cancelButton?: string;\n  actionButton?: string;\n  success?: string;\n  error?: string;\n  info?: string;\n  warning?: string;\n  loading?: string;\n  default?: string;\n  content?: string;\n  icon?: string;\n}\n\nexport interface ToastIcons {\n  success?: React.ReactNode;\n  info?: React.ReactNode;\n  warning?: React.ReactNode;\n  error?: React.ReactNode;\n  loading?: React.ReactNode;\n  close?: React.ReactNode;\n}\n\nexport interface Action {\n  label: React.ReactNode;\n  onClick: (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;\n  actionButtonStyle?: React.CSSProperties;\n}\n\nexport interface ToastT {\n  id: number | string;\n  title?: (() => React.ReactNode) | React.ReactNode;\n  type?: ToastTypes;\n  icon?: React.ReactNode;\n  jsx?: React.ReactNode;\n  richColors?: boolean;\n  invert?: boolean;\n  closeButton?: boolean;\n  dismissible?: boolean;\n  description?: (() => React.ReactNode) | React.ReactNode;\n  duration?: number;\n  delete?: boolean;\n  action?: Action | React.ReactNode;\n  cancel?: Action | React.ReactNode;\n  onDismiss?: (toast: ToastT) => void;\n  onAutoClose?: (toast: ToastT) => void;\n  promise?: PromiseT;\n  cancelButtonStyle?: React.CSSProperties;\n  actionButtonStyle?: React.CSSProperties;\n  style?: React.CSSProperties;\n  unstyled?: boolean;\n  className?: string;\n  classNames?: ToastClassnames;\n  descriptionClassName?: string;\n  position?: Position;\n}\n\nexport function isAction(action: Action | React.ReactNode): action is Action {\n  return (action as Action).label !== undefined;\n}\n\nexport type Position = 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top-center' | 'bottom-center';\nexport interface HeightT {\n  height: number;\n  toastId: number | string;\n  position: Position;\n}\n\ninterface ToastOptions {\n  className?: string;\n  closeButton?: boolean;\n  descriptionClassName?: string;\n  style?: React.CSSProperties;\n  cancelButtonStyle?: React.CSSProperties;\n  actionButtonStyle?: React.CSSProperties;\n  duration?: number;\n  unstyled?: boolean;\n  classNames?: ToastClassnames;\n}\n\ntype Offset =\n  | {\n      top?: string | number;\n      right?: string | number;\n      bottom?: string | number;\n      left?: string | number;\n    }\n  | string\n  | number;\n\nexport interface ToasterProps {\n  invert?: boolean;\n  theme?: 'light' | 'dark' | 'system';\n  position?: Position;\n  hotkey?: string[];\n  richColors?: boolean;\n  expand?: boolean;\n  duration?: number;\n  gap?: number;\n  visibleToasts?: number;\n  closeButton?: boolean;\n  toastOptions?: ToastOptions;\n  className?: string;\n  style?: React.CSSProperties;\n  offset?: Offset;\n  mobileOffset?: Offset;\n  dir?: 'rtl' | 'ltr' | 'auto';\n  swipeDirections?: SwipeDirection[];\n  /**\n   * @deprecated Please use the `icons` prop instead:\n   * ```jsx\n   * <Toaster\n   *   icons={{ loading: <LoadingIcon /> }}\n   * />\n   * ```\n   */\n  loadingIcon?: React.ReactNode;\n  icons?: ToastIcons;\n  containerAriaLabel?: string;\n  pauseWhenPageIsHidden?: boolean;\n}\n\nexport type SwipeDirection = 'top' | 'right' | 'bottom' | 'left';\n\nexport interface ToastProps {\n  toast: ToastT;\n  toasts: ToastT[];\n  index: number;\n  swipeDirections?: SwipeDirection[];\n  expanded: boolean;\n  invert: boolean;\n  heights: HeightT[];\n  setHeights: React.Dispatch<React.SetStateAction<HeightT[]>>;\n  removeToast: (toast: ToastT) => void;\n  gap?: number;\n  position: Position;\n  visibleToasts: number;\n  expandByDefault: boolean;\n  closeButton: boolean;\n  interacting: boolean;\n  style?: React.CSSProperties;\n  cancelButtonStyle?: React.CSSProperties;\n  actionButtonStyle?: React.CSSProperties;\n  duration?: number;\n  className?: string;\n  unstyled?: boolean;\n  descriptionClassName?: string;\n  loadingIcon?: React.ReactNode;\n  classNames?: ToastClassnames;\n  icons?: ToastIcons;\n  closeButtonAriaLabel?: string;\n  pauseWhenPageIsHidden: boolean;\n  defaultRichColors?: boolean;\n}\n\nexport enum SwipeStateTypes {\n  SwipedOut = 'SwipedOut',\n  SwipedBack = 'SwipedBack',\n  NotSwiped = 'NotSwiped',\n}\n\nexport type Theme = 'light' | 'dark';\n\nexport interface ToastToDismiss {\n  id: number | string;\n  dismiss: boolean;\n}\n\nexport type ExternalToast = Omit<ToastT, 'id' | 'type' | 'title' | 'jsx' | 'delete' | 'promise'> & {\n  id?: number | string;\n};\n"], "names": ["React", "forwardRef", "isValidElement", "ReactDOM", "React", "getAsset", "type", "SuccessIcon", "InfoIcon", "WarningIcon", "ErrorIcon", "bars", "Loader", "visible", "className", "_", "i", "CloseIcon", "React", "useIsDocumentHidden", "isDocumentHidden", "setIsDocumentHidden", "callback", "React", "toastsCounter", "Observer", "subscriber", "index", "data", "_a", "message", "rest", "id", "alreadyExists", "toast", "dismissible", "promise", "p", "<PERSON><PERSON><PERSON><PERSON>", "result", "originalPromise", "response", "isHttpResponse", "description", "error", "unwrap", "resolve", "reject", "jsx", "ToastState", "toastFunction", "basicToast", "getHistory", "getToasts", "styleInject", "css", "insertAt", "head", "style", "styleInject", "isAction", "action", "VISIBLE_TOASTS_AMOUNT", "VIEWPORT_OFFSET", "MOBILE_VIEWPORT_OFFSET", "TOAST_LIFETIME", "TOAST_WIDTH", "GAP", "SWIPE_THRESHOLD", "TIME_BEFORE_UNMOUNT", "cn", "classes", "getDefaultSwipeDirections", "position", "y", "x", "directions", "Toast", "props", "_a", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "_i", "_j", "_k", "ToasterInvert", "toast", "unstyled", "interacting", "setHeights", "visibleToasts", "heights", "index", "toasts", "expanded", "removeToast", "defaultRichColors", "closeButtonFromToaster", "style", "cancelButtonStyle", "actionButtonStyle", "className", "descriptionClassName", "durationFromToaster", "gap", "loadingIconProp", "expandByDefault", "classNames", "icons", "closeButtonAriaLabel", "pauseWhenPageIsHidden", "swipeDirection", "setSwipeDirection", "React", "swipeOutDirection", "setSwipeOutDirection", "mounted", "setMounted", "removed", "setRemoved", "swiping", "setSwiping", "swipeOut", "setSwipeOut", "isSwiped", "setIsSwiped", "offsetBeforeRemove", "setOffsetBeforeRemove", "initialHeight", "setInitialHeight", "remainingTime", "dragStartTime", "toastRef", "isFront", "isVisible", "toastType", "dismissible", "toastClassname", "toastDescriptionClassname", "heightIndex", "height", "closeButton", "duration", "closeTimerStartTimeRef", "offset", "lastCloseTimerStartTimeRef", "pointerStartRef", "toastsHeightBefore", "prev", "curr", "reducerIndex", "isDocumentHidden", "useIsDocumentHidden", "invert", "disabled", "toastNode", "h", "originalHeight", "newHeight", "deleteToast", "timeoutId", "elapsedTime", "getLoadingIcon", "Loader", "event", "swipeAmountX", "swipeAmountY", "timeTaken", "swipeAmount", "velocity", "y<PERSON><PERSON><PERSON>", "xDelta", "swipeDirections", "CloseIcon", "isValidElement", "getAsset", "isAction", "getDocumentDirection", "dirAttribute", "assignOffset", "defaultOffset", "mobileOffset", "styles", "isMobile", "prefix", "defaultValue", "assignAll", "key", "useSonner", "activeToasts", "setActiveToasts", "ToastState", "ReactDOM", "t", "indexOfExistingToast", "Toaster", "forwardRef", "ref", "hotkey", "expand", "theme", "richColors", "toastOptions", "dir", "loadingIcon", "containerAriaLabel", "setToasts", "possiblePositions", "setExpanded", "setInteracting", "actualTheme", "setActualTheme", "listRef", "hotkeyLabel", "lastFocusedElementRef", "isFocusWithinRef", "toast<PERSON>oRemove", "id", "darkMediaQuery", "matches", "error", "e", "handleKeyDown"], "mappings": ";;;;;AAEA,OAAOA,GAAS,cAAAC,GAAY,kBAAAC,OAAsB;AAClD,OAAOC,OAAc,YCFrB,OAAOC,MAAW;;;;;AAGX,IAAMC,KAAYC,GAAyC;IAChE,OAAQA,EAAM;QACZ,KAAK;YACH,OAAOC;QAET,KAAK;YACH,OAAOC;QAET,KAAK;YACH,OAAOC;QAET,KAAK;YACH,OAAOC;QAET;YACE,OAAO;IACX;AACF,GAEMC,KAAO,MAAM,EAAE,EAAE,IAAA,CAAK,CAAC,GAEhBC,KAAS;QAAC,EAAE,SAAAC,CAAAA,EAAS,WAAAC,CAAU,EAAA;yKAExCV,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,WAAW;YAAC;YAA0BU,CAAS;SAAA,CAAE,MAAA,CAAO,OAAO,EAAE,IAAA,CAAK,GAAG;QAAG,gBAAcD;IAAAA,iKAC7FT,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,WAAU;IAAA,GACZO,GAAK,GAAA,CAAI,CAACI,GAAGC,iKACZZ,WAAAA,CAAA,aAAA,CAAC,OAAA;YAAI,WAAU;YAAqB,KAAK,eAAeY,OAAAA;QAAAA,CAAK,CAC9D,CACH,CACF;GAIET,mKACJH,UAAAA,CAAA,aAAA,CAAC,OAAA;IAAI,OAAM;IAA6B,SAAQ;IAAY,MAAK;IAAe,QAAO;IAAK,OAAM;AAAA,iKAChGA,UAAAA,CAAA,aAAA,CAAC,QAAA;IACC,UAAS;IACT,GAAE;IACF,UAAS;AAAA,CACX,CACF,GAGIK,mKACJL,UAAAA,CAAA,aAAA,CAAC,OAAA;IAAI,OAAM;IAA6B,SAAQ;IAAY,MAAK;IAAe,QAAO;IAAK,OAAM;AAAA,iKAChGA,UAAAA,CAAA,aAAA,CAAC,QAAA;IACC,UAAS;IACT,GAAE;IACF,UAAS;AAAA,CACX,CACF,GAGII,mKACJJ,UAAAA,CAAA,aAAA,CAAC,OAAA;IAAI,OAAM;IAA6B,SAAQ;IAAY,MAAK;IAAe,QAAO;IAAK,OAAM;AAAA,iKAChGA,UAAAA,CAAA,aAAA,CAAC,QAAA;IACC,UAAS;IACT,GAAE;IACF,UAAS;AAAA,CACX,CACF,GAGIM,mKACJN,UAAAA,CAAA,aAAA,CAAC,OAAA;IAAI,OAAM;IAA6B,SAAQ;IAAY,MAAK;IAAe,QAAO;IAAK,OAAM;AAAA,iKAChGA,UAAAA,CAAA,aAAA,CAAC,QAAA;IACC,UAAS;IACT,GAAE;IACF,UAAS;AAAA,CACX,CACF,GAGWa,mKACXb,UAAAA,CAAA,aAAA,CAAC,OAAA;IACC,OAAM;IACN,OAAM;IACN,QAAO;IACP,SAAQ;IACR,MAAK;IACL,QAAO;IACP,aAAY;IACZ,eAAc;IACd,gBAAe;AAAA,iKAEfA,UAAAA,CAAA,aAAA,CAAC,QAAA;IAAK,IAAG;IAAK,IAAG;IAAI,IAAG;IAAI,IAAG;AAAA,CAAK,iKACpCA,UAAAA,CAAA,aAAA,CAAC,QAAA;IAAK,IAAG;IAAI,IAAG;IAAI,IAAG;IAAK,IAAG;AAAA,CAAK,CACtC,EC3FF,OAAOc,OAAW;;AAEX,IAAMC,KAAsB,IAAM;IACvC,IAAM,CAACC,GAAkBC,CAAmB,CAAA,iKAAIH,UAAAA,CAAM,QAAA,CAAS,SAAS,MAAM;IAE9E,qKAAAA,UAAAA,CAAM,SAAA;wBAAU,IAAM;YACpB,IAAMI;kCAAW,IAAM;oBACrBD,EAAoB,SAAS,MAAM;gBACrC;;YACA,OAAA,SAAS,gBAAA,CAAiB,oBAAoBC,CAAQ;gCAC/C,IAAM,OAAO,mBAAA,CAAoB,oBAAoBA,CAAQ;;QACtE;uBAAG,CAAC,CAAC,GAEEF;AACT,ECZA,OAAOG,OAAW;;AAElB,IAAIC,KAAgB,GAIdC,KAAN,KAAe;IAKb,aAAc;QAOd,IAAA,CAAA,SAAA,IAAaC,IAAAA,CACX,IAAA,CAAK,WAAA,CAAY,IAAA,CAAKA,CAAU,GAEzB,IAAM;gBACX,IAAMC,IAAQ,IAAA,CAAK,WAAA,CAAY,OAAA,CAAQD,CAAU;gBACjD,IAAA,CAAK,WAAA,CAAY,MAAA,CAAOC,GAAO,CAAC;YAClC,CAAA;QAGF,IAAA,CAAA,OAAA,IAAWC,GAAiB;YAC1B,IAAA,CAAK,WAAA,CAAY,OAAA,EAASF,IAAeA,EAAWE,CAAI,CAAC;QAC3D;QAEA,IAAA,CAAA,QAAA,IAAYA,GAAiB;YAC3B,IAAA,CAAK,OAAA,CAAQA,CAAI,GACjB,IAAA,CAAK,MAAA,GAAS,CAAC;mBAAG,IAAA,CAAK,MAAA;gBAAQA,CAAI;;QACrC;QAEA,IAAA,CAAA,MAAA,IACEA,GAMG;YA7CP,IAAAC;YA8CI,IAAM,EAAE,SAAAC,CAAAA,EAAS,GAAGC,CAAK,EAAA,GAAIH,GACvBI,IAAK,OAAA,CAAOJ,KAAA,OAAA,KAAA,IAAAA,EAAM,EAAA,KAAO,YAAA,CAAA,CAAYC,IAAAD,EAAK,EAAA,KAAL,OAAA,KAAA,IAAAC,EAAS,MAAA,IAAS,IAAID,EAAK,EAAA,GAAKJ,MACrES,IAAgB,IAAA,CAAK,MAAA,CAAO,IAAA,EAAMC,IAC/BA,EAAM,EAAA,KAAOF,CACrB,GACKG,IAAcP,EAAK,WAAA,KAAgB,KAAA,IAAY,CAAA,IAAOA,EAAK,WAAA;YAEjE,OAAI,IAAA,CAAK,eAAA,CAAgB,GAAA,CAAII,CAAE,KAC7B,IAAA,CAAK,eAAA,CAAgB,MAAA,CAAOA,CAAE,GAG5BC,IACF,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,MAAA,CAAO,GAAA,EAAKC,IACzBA,EAAM,EAAA,KAAOF,IAAAA,CACf,IAAA,CAAK,OAAA,CAAQ;oBAAE,GAAGE,CAAAA;oBAAO,GAAGN,CAAAA;oBAAM,IAAAI;oBAAI,OAAOF;gBAAQ,CAAC,GAC/C;oBACL,GAAGI,CAAAA;oBACH,GAAGN,CAAAA;oBACH,IAAAI;oBACA,aAAAG;oBACA,OAAOL;gBACT,CAAA,IAGKI,CACR,IAED,IAAA,CAAK,QAAA,CAAS;gBAAE,OAAOJ;gBAAS,GAAGC,CAAAA;gBAAM,aAAAI;gBAAa,IAAAH;YAAG,CAAC,GAGrDA;QACT;QAEA,IAAA,CAAA,OAAA,IAAWA,IAAAA,CACT,IAAA,CAAK,eAAA,CAAgB,GAAA,CAAIA,CAAE,GAEtBA,KACH,IAAA,CAAK,MAAA,CAAO,OAAA,EAASE,GAAU;gBAC7B,IAAA,CAAK,WAAA,CAAY,OAAA,EAASR,IAAeA,EAAW;wBAAE,IAAIQ,EAAM,EAAA;wBAAI,SAAS,CAAA;oBAAK,CAAC,CAAC;YACtF,CAAC,GAEH,IAAA,CAAK,WAAA,CAAY,OAAA,EAASR,IAAeA,EAAW;oBAAE,IAAAM;oBAAI,SAAS,CAAA;gBAAK,CAAC,CAAC,GACnEA,CAAAA;QAGT,IAAA,CAAA,OAAA,GAAU,CAACF,GAAmCF,IACrC,IAAA,CAAK,MAAA,CAAO;gBAAE,GAAGA,CAAAA;gBAAM,SAAAE;YAAQ,CAAC;QAGzC,IAAA,CAAA,KAAA,GAAQ,CAACA,GAAmCF,IACnC,IAAA,CAAK,MAAA,CAAO;gBAAE,GAAGA,CAAAA;gBAAM,SAAAE;gBAAS,MAAM;YAAQ,CAAC;QAGxD,IAAA,CAAA,OAAA,GAAU,CAACA,GAAmCF,IACrC,IAAA,CAAK,MAAA,CAAO;gBAAE,GAAGA,CAAAA;gBAAM,MAAM;gBAAW,SAAAE;YAAQ,CAAC;QAG1D,IAAA,CAAA,IAAA,GAAO,CAACA,GAAmCF,IAClC,IAAA,CAAK,MAAA,CAAO;gBAAE,GAAGA,CAAAA;gBAAM,MAAM;gBAAQ,SAAAE;YAAQ,CAAC;QAGvD,IAAA,CAAA,OAAA,GAAU,CAACA,GAAmCF,IACrC,IAAA,CAAK,MAAA,CAAO;gBAAE,GAAGA,CAAAA;gBAAM,MAAM;gBAAW,SAAAE;YAAQ,CAAC;QAG1D,IAAA,CAAA,OAAA,GAAU,CAACA,GAAmCF,IACrC,IAAA,CAAK,MAAA,CAAO;gBAAE,GAAGA,CAAAA;gBAAM,MAAM;gBAAW,SAAAE;YAAQ,CAAC;QAG1D,IAAA,CAAA,OAAA,GAAU,CAAYM,GAA8BR,IAAkC;YACpF,IAAI,CAACA,GAEH;YAGF,IAAII;YACAJ,EAAK,OAAA,KAAY,KAAA,KAAA,CACnBI,IAAK,IAAA,CAAK,MAAA,CAAO;gBACf,GAAGJ,CAAAA;gBACH,SAAAQ;gBACA,MAAM;gBACN,SAASR,EAAK,OAAA;gBACd,aAAa,OAAOA,EAAK,WAAA,IAAgB,aAAaA,EAAK,WAAA,GAAc,KAAA;YAC3E,CAAC,CAAA;YAGH,IAAMS,IAAID,aAAmB,UAAUA,IAAUA,EAAQ,GAErDE,IAAgBN,MAAO,KAAA,GACvBO,GAEEC,IAAkBH,EACrB,IAAA,CAAK,OAAOI,GAAa;gBAGxB,IAFAF,IAAS;oBAAC;oBAAWE,CAAQ;iBAAA,gKACElB,UAAAA,CAAM,cAAA,CAAekB,CAAQ,GAE1DH,IAAgB,CAAA,GAChB,IAAA,CAAK,MAAA,CAAO;oBAAE,IAAAN;oBAAI,MAAM;oBAAW,SAASS;gBAAS,CAAC;qBAAA,IAC7CC,GAAeD,CAAQ,KAAK,CAACA,EAAS,EAAA,EAAI;oBACnDH,IAAgB,CAAA;oBAChB,IAAMR,IACJ,OAAOF,EAAK,KAAA,IAAU,aAAa,MAAMA,EAAK,KAAA,CAAM,uBAAgC,CAAQ,MAAjBa,EAAS,MAAA,KAAYb,EAAK,KAAA,EACjGe,IACJ,OAAOf,EAAK,WAAA,IAAgB,aACxB,MAAMA,EAAK,WAAA,CAAY,uBAAgC,CAAQ,MAAjBa,EAAS,MAAA,KACvDb,EAAK,WAAA;oBACX,IAAA,CAAK,MAAA,CAAO;wBAAE,IAAAI;wBAAI,MAAM;wBAAS,SAAAF;wBAAS,aAAAa;oBAAY,CAAC;gBAAA,OAAA,IAC9Cf,EAAK,OAAA,KAAY,KAAA,GAAW;oBACrCU,IAAgB,CAAA;oBAChB,IAAMR,IAAU,OAAOF,EAAK,OAAA,IAAY,aAAa,MAAMA,EAAK,OAAA,CAAQa,CAAQ,IAAIb,EAAK,OAAA,EACnFe,IACJ,OAAOf,EAAK,WAAA,IAAgB,aAAa,MAAMA,EAAK,WAAA,CAAYa,CAAQ,IAAIb,EAAK,WAAA;oBACnF,IAAA,CAAK,MAAA,CAAO;wBAAE,IAAAI;wBAAI,MAAM;wBAAW,SAAAF;wBAAS,aAAAa;oBAAY,CAAC;gBAAA;YAE7D,CAAC,EACA,KAAA,CAAM,OAAOC,GAAU;gBAEtB,IADAL,IAAS;oBAAC;oBAAUK,CAAK;iBAAA,EACrBhB,EAAK,KAAA,KAAU,KAAA,GAAW;oBAC5BU,IAAgB,CAAA;oBAChB,IAAMR,IAAU,OAAOF,EAAK,KAAA,IAAU,aAAa,MAAMA,EAAK,KAAA,CAAMgB,CAAK,IAAIhB,EAAK,KAAA,EAC5Ee,IAAc,OAAOf,EAAK,WAAA,IAAgB,aAAa,MAAMA,EAAK,WAAA,CAAYgB,CAAK,IAAIhB,EAAK,WAAA;oBAClG,IAAA,CAAK,MAAA,CAAO;wBAAE,IAAAI;wBAAI,MAAM;wBAAS,SAAAF;wBAAS,aAAAa;oBAAY,CAAC;gBAAA;YAE3D,CAAC,EACA,OAAA,CAAQ,IAAM;gBA1KrB,IAAAd;gBA2KYS,KAAAA,CAEF,IAAA,CAAK,OAAA,CAAQN,CAAE,GACfA,IAAK,KAAA,CAAA,GAAA,CAGPH,IAAAD,EAAK,OAAA,KAAL,QAAAC,EAAA,IAAA,CAAAD;YACF,CAAC,GAEGiB,IAAS,IACb,IAAI,QAAmB,CAACC,GAASC,IAC/BP,EAAgB,IAAA,CAAK,IAAOD,CAAAA,CAAO,CAAC,CAAA,KAAM,WAAWQ,EAAOR,CAAAA,CAAO,CAAC,CAAC,IAAIO,EAAQP,CAAAA,CAAO,CAAC,CAAC,CAAE,EAAE,KAAA,CAAMQ,CAAM,CAC5G;YAEF,OAAI,OAAOf,KAAO,YAAY,OAAOA,KAAO,WAEnC;gBAAE,QAAAa;YAAO,IAET,OAAO,MAAA,CAAOb,GAAI;gBAAE,QAAAa;YAAO,CAAC;QAEvC;QAEA,IAAA,CAAA,MAAA,GAAS,CAACG,GAAkDpB,IAAyB;YACnF,IAAMI,IAAAA,CAAKJ,KAAA,OAAA,KAAA,IAAAA,EAAM,EAAA,KAAMJ;YACvB,OAAA,IAAA,CAAK,MAAA,CAAO;gBAAE,KAAKwB,EAAIhB,CAAE;gBAAG,IAAAA;gBAAI,GAAGJ;YAAK,CAAC,GAClCI;QACT;QAEA,IAAA,CAAA,eAAA,GAAkB,IACT,IAAA,CAAK,MAAA,CAAO,MAAA,EAAQE,IAAU,CAAC,IAAA,CAAK,eAAA,CAAgB,GAAA,CAAIA,EAAM,EAAE,CAAC;QA1LxE,IAAA,CAAK,WAAA,GAAc,CAAC,CAAA,EACpB,IAAA,CAAK,MAAA,GAAS,CAAC,CAAA,EACf,IAAA,CAAK,eAAA,GAAkB,IAAI;IAC7B;AAyLF,GAEae,IAAa,IAAIxB,IAGxByB,KAAgB,CAACpB,GAAiBF,IAAyB;IAC/D,IAAMI,IAAAA,CAAKJ,KAAA,OAAA,KAAA,IAAAA,EAAM,EAAA,KAAMJ;IAEvB,OAAAyB,EAAW,QAAA,CAAS;QAClB,OAAOnB;QACP,GAAGF,CAAAA;QACH,IAAAI;IACF,CAAC,GACMA;AACT,GAEMU,MAAkBd,IAEpBA,KACA,OAAOA,KAAS,YAChB,QAAQA,KACR,OAAOA,EAAK,EAAA,IAAO,aACnB,YAAYA,KACZ,OAAOA,EAAK,MAAA,IAAW,UAIrBuB,KAAaD,IAEbE,KAAa,IAAMH,EAAW,MAAA,EAC9BI,KAAY,IAAMJ,EAAW,eAAA,CAAgB,GAGtCf,KAAQ,OAAO,MAAA,CAC1BiB,IACA;IACE,SAASF,EAAW,OAAA;IACpB,MAAMA,EAAW,IAAA;IACjB,SAASA,EAAW,OAAA;IACpB,OAAOA,EAAW,KAAA;IAClB,QAAQA,EAAW,MAAA;IACnB,SAASA,EAAW,OAAA;IACpB,SAASA,EAAW,OAAA;IACpB,SAASA,EAAW,OAAA;IACpB,SAASA,EAAW;AACtB,GACA;IAAE,YAAAG;IAAY,WAAAC;AAAU,CAC1B;ACxPyB,SAARC,GAA6BC,CAAAA;UAAO,UAAAC,CAAS,EAAA,GAAX,iEAAe,CAAC,EAAG;IAC1D,IAAI,CAACD,KAAO,OAAO,YAAa,aAAa;IAE7C,IAAME,IAAO,SAAS,IAAA,IAAQ,SAAS,oBAAA,CAAqB,MAAM,CAAA,CAAE,CAAC,CAAA,EAC/DC,IAAQ,SAAS,aAAA,CAAc,OAAO;IAC5CA,EAAM,IAAA,GAAO,YAETF,MAAa,SACXC,EAAK,UAAA,GACPA,EAAK,YAAA,CAAaC,GAAOD,EAAK,UAAU,IAK1CA,EAAK,WAAA,CAAYC,CAAK,GAGpBA,EAAM,UAAA,GACRA,EAAM,UAAA,CAAW,OAAA,GAAUH,IAE3BG,EAAM,WAAA,CAAY,SAAS,cAAA,CAAeH,CAAG,CAAC;AAElD;ACvB8BI,GAAY;ACkF7C,SAASC,GAASC,CAAAA,CAAoD;IAC3E,OAAQA,EAAkB,KAAA,KAAU,KAAA;AACtC;AN/DA,IAAMC,KAAwB,GAGxBC,KAAkB,QAGlBC,KAAyB,QAGzBC,KAAiB,KAGjBC,KAAc,KAGdC,KAAM,IAGNC,KAAkB,IAGlBC,KAAsB;AAE5B,SAASC;IAAAA,IAAAA,IAAAA,OAAAA,UAAAA,QAAAA,IAAAA,UAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA;QAAMC,EAAND,QAAAA,SAAAA,CAAAA,KAAMC,CAAiC;;IAC9C,OAAOA,EAAQ,MAAA,CAAO,OAAO,EAAE,IAAA,CAAK,GAAG;AACzC;AAEA,SAASC,GAA0BC,CAAAA,CAAyC;IAC1E,IAAM,CAACC,GAAGC,CAAC,CAAA,GAAIF,EAAS,KAAA,CAAM,GAAG,GAC3BG,IAAoC,CAAC,CAAA;IAE3C,OAAIF,KACFE,EAAW,IAAA,CAAKF,CAAmB,GAGjCC,KACFC,EAAW,IAAA,CAAKD,CAAmB,GAG9BC;AACT;AAEA,IAAMC,MAASC,GAAsB;IA/DrC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC;IAgEE,IAAM,EACJ,QAAQC,CAAAA,EACR,OAAAC,CAAAA,EACA,UAAAC,CAAAA,EACA,aAAAC,CAAAA,EACA,YAAAC,CAAAA,EACA,eAAAC,CAAAA,EACA,SAAAC,CAAAA,EACA,OAAAC,CAAAA,EACA,QAAAC,CAAAA,EACA,UAAAC,CAAAA,EACA,aAAAC,CAAAA,EACA,mBAAAC,CAAAA,EACA,aAAaC,EAAAA,EACb,OAAAC,EAAAA,EACA,mBAAAC,EAAAA,EACA,mBAAAC,CAAAA,EACA,WAAAC,KAAY,EAAA,EACZ,sBAAAC,KAAuB,EAAA,EACvB,UAAUC,CAAAA,EACV,UAAAnC,EAAAA,EACA,KAAAoC,EAAAA,EACA,aAAaC,EAAAA,EACb,iBAAAC,CAAAA,EACA,YAAAC,CAAAA,EACA,OAAAC,CAAAA,EACA,sBAAAC,KAAuB,aAAA,EACvB,uBAAAC,EACF,EAAA,GAAIrC,GACE,CAACsC,GAAgBC,CAAiB,CAAA,iKAAIC,UAAAA,CAAM,QAAA,CAA2B,IAAI,GAC3E,CAACC,IAAmBC,CAAoB,CAAA,GAAIF,wKAAAA,CAAM,QAAA,CAAkD,IAAI,GACxG,CAACG,GAASC,CAAU,CAAA,iKAAIJ,UAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GAC5C,CAACK,GAASC,EAAU,CAAA,iKAAIN,UAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GAC5C,CAACO,GAASC,CAAU,CAAA,iKAAIR,UAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GAC5C,CAACS,IAAUC,CAAW,CAAA,iKAAIV,UAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GAC9C,CAACW,GAAUC,CAAW,CAAA,iKAAIZ,UAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GAC9C,CAACa,GAAoBC,CAAqB,CAAA,gKAAId,WAAAA,CAAM,QAAA,CAAS,CAAC,GAC9D,CAACe,GAAeC,CAAgB,CAAA,iKAAIhB,UAAAA,CAAM,QAAA,CAAS,CAAC,GACpDiB,kKAAgBjB,UAAAA,CAAM,MAAA,CAAO3B,EAAM,QAAA,IAAYiB,KAAuB3C,EAAc,GACpFuE,IAAgBlB,wKAAAA,CAAM,MAAA,CAAoB,IAAI,GAC9CmB,kKAAWnB,UAAAA,CAAM,MAAA,CAAsB,IAAI,GAC3CoB,KAAUzC,MAAU,GACpB0C,KAAY1C,IAAQ,KAAKF,GACzB6C,IAAYjD,EAAM,IAAA,EAClBkD,IAAclD,EAAM,WAAA,KAAgB,CAAA,GACpCmD,KAAiBnD,EAAM,SAAA,IAAa,IACpCoD,KAA4BpD,EAAM,oBAAA,IAAwB,IAE1DqD,mKAAc1B,UAAAA,CAAM,OAAA;0BACxB,IAAMtB,EAAQ,SAAA;mCAAWiD,IAAWA,EAAO,OAAA,KAAYtD,EAAM,EAAE;oCAAK;yBACpE;QAACK;QAASL,EAAM,EAAE;KACpB,GACMuD,mKAAc5B,UAAAA,CAAM,OAAA;0BACxB,IAAG;YArHP,IAAAvC;YAqHU,OAAA,CAAAA,IAAAY,EAAM,WAAA,KAAN,OAAAZ,IAAqBuB;QAAAA;yBAC3B;QAACX,EAAM,WAAA;QAAaW,EAAsB;KAC5C,GACM6C,mKAAW7B,UAAAA,CAAM,OAAA;0BACrB,IAAM3B,EAAM,QAAA,IAAYiB,KAAuB3C;yBAC/C;QAAC0B,EAAM,QAAA;QAAUiB,CAAmB;KACtC,GACMwC,mKAAyB9B,UAAAA,CAAM,MAAA,CAAO,CAAC,GACvC+B,kKAAS/B,UAAAA,CAAM,MAAA,CAAO,CAAC,GACvBgC,mKAA6BhC,UAAAA,CAAM,MAAA,CAAO,CAAC,GAC3CiC,kKAAkBjC,UAAAA,CAAM,MAAA,CAAwC,IAAI,GACpE,CAAC5C,IAAGC,EAAC,CAAA,GAAIF,GAAS,KAAA,CAAM,GAAG,GAC3B+E,mKAAqBlC,UAAAA,CAAM,OAAA;0BAAQ,IAChCtB,EAAQ,MAAA;kCAAO,CAACyD,GAAMC,GAAMC,IAE7BA,KAAgBX,KACXS,IAGFA,IAAOC,EAAK,MAAA;iCAClB,CAAC;yBACH;QAAC1D;QAASgD,EAAW;KAAC,GACnBY,KAAmBC,GAAoB,GAEvCC,KAASnE,EAAM,MAAA,IAAUD,GACzBqE,KAAWnB,MAAc;IAE/BS,EAAO,OAAA,iKAAU/B,UAAAA,CAAM,OAAA;sBAAQ,IAAM0B,KAAcnC,KAAM2C;qBAAoB;QAACR;QAAaQ,EAAkB;KAAC,iKAE9GlC,UAAAA,CAAM,SAAA;wBAAU,IAAM;YACpBiB,EAAc,OAAA,GAAUY;QAC1B;uBAAG;QAACA,EAAQ;KAAC,iKAEb7B,UAAAA,CAAM,SAAA;wBAAU,IAAM;YAEpBI,EAAW,CAAA,CAAI;QACjB;uBAAG,CAAC,CAAC,GAELJ,wKAAAA,CAAM,SAAA;wBAAU,IAAM;YACpB,IAAM0C,IAAYvB,EAAS,OAAA;YAC3B,IAAIuB,GAAW;gBACb,IAAMf,IAASe,EAAU,qBAAA,CAAsB,EAAE,MAAA;gBAEjD,OAAA1B,EAAiBW,CAAM,GACvBnD;qCAAYmE,IAAM;4BAAC;gCAAE,SAAStE,EAAM,EAAA;gCAAI,QAAAsD;gCAAQ,UAAUtD,EAAM;4BAAS,EAAG;+BAAGsE,CAAC;yBAAC;;oCAC1E,IAAMnE;6CAAYmE,IAAMA,EAAE,MAAA;oDAAQhB,KAAWA,EAAO,OAAA,KAAYtD,EAAM,EAAE,CAAC;;;;YAAA;QAEpF;uBAAG;QAACG;QAAYH,EAAM,EAAE;KAAC,iKAEzB2B,UAAAA,CAAM,eAAA;8BAAgB,IAAM;YAC1B,IAAI,CAACG,GAAS;YACd,IAAMuC,IAAYvB,EAAS,OAAA,EACrByB,IAAiBF,EAAU,KAAA,CAAM,MAAA;YACvCA,EAAU,KAAA,CAAM,MAAA,GAAS;YACzB,IAAMG,IAAYH,EAAU,qBAAA,CAAsB,EAAE,MAAA;YACpDA,EAAU,KAAA,CAAM,MAAA,GAASE,GAEzB5B,EAAiB6B,CAAS,GAE1BrE;uCAAYE,IACYA,EAAQ,IAAA;+CAAMiD,IAAWA,EAAO,OAAA,KAAYtD,EAAM,EAAE;+CAIjEK,EAAQ,GAAA;+CAAKiD,IAAYA,EAAO,OAAA,KAAYtD,EAAM,EAAA,GAAK;gCAAE,GAAGsD,CAAAA;gCAAQ,QAAQkB;4BAAU,IAAIlB,CAAO;+CAFjG;wBAAC;4BAAE,SAAStD,EAAM,EAAA;4BAAI,QAAQwE;4BAAW,UAAUxE,EAAM;wBAAS,EAAG;2BAAGK,CAAO;qBAIzF;;QACH;6BAAG;QAACyB;QAAS9B,EAAM,KAAA;QAAOA,EAAM,WAAA;QAAaG;QAAYH,EAAM,EAAE;KAAC;IAElE,IAAMyE,kKAAc9C,UAAAA,CAAM,WAAA;6BAAY,IAAM;YAE1CM,GAAW,CAAA,CAAI,GACfQ,EAAsBiB,EAAO,OAAO,GACpCvD;sCAAYmE,IAAMA,EAAE,MAAA;8CAAQhB,IAAWA,EAAO,OAAA,KAAYtD,EAAM,EAAE,CAAC;;qCAEnE;qCAAW,IAAM;oBACfS,EAAYT,CAAK;gBACnB;oCAAGtB,EAAmB;QACxB;4BAAG;QAACsB;QAAOS;QAAaN;QAAYuD,CAAM;KAAC;IAE3C/B,wKAAAA,CAAM,SAAA;wBAAU,IAAM;YACpB,IAAK3B,EAAM,OAAA,IAAWiD,MAAc,aAAcjD,EAAM,QAAA,KAAa,IAAA,KAAYA,EAAM,IAAA,KAAS,WAAW;YAC3G,IAAI0E;YA6BJ,OAAIlE,KAAYN,KAAgBsB,MAAyByC,KAAAA;gCA1BtC,IAAM;oBACvB,IAAIN,GAA2B,OAAA,GAAUF,GAAuB,OAAA,EAAS;wBAEvE,IAAMkB,IAAc,IAAI,KAAK,EAAE,OAAA,CAAQ,IAAIlB,GAAuB,OAAA;wBAElEb,EAAc,OAAA,GAAUA,EAAc,OAAA,GAAU+B;oBAAAA;oBAGlDhB,GAA2B,OAAA,GAAU,IAAI,KAAK,EAAE,OAAA,CAAQ;gBAC1D;aAAA,EAkBa,oBAAA;gCAhBM,IAAM;oBAInBf,EAAc,OAAA,KAAY,IAAA,KAAA,CAE9Ba,GAAuB,OAAA,GAAU,IAAI,KAAK,EAAE,OAAA,CAAQ,GAGpDiB,IAAY;wCAAW,IAAM;4BA9NnC,IAAAtF;4BAAAA,CA+NQA,IAAAY,EAAM,WAAA,KAAN,QAAAZ,EAAA,IAAA,CAAAY,GAAoBA,IACpByE,EAAY;wBACd;uCAAG7B,EAAc,OAAO,CAAA;gBAC1B;aAAA,EAKa;gCAGN,IAAM,aAAa8B,CAAS;;QACrC;uBAAG;QAAClE;QAAUN;QAAaF;QAAOiD;QAAWzB;QAAuByC;QAAkBQ,CAAW;KAAC,iKAElG9C,UAAAA,CAAM,SAAA;wBAAU,IAAM;YAChB3B,EAAM,MAAA,IACRyE,EAAY;QAEhB;uBAAG;QAACA;QAAazE,EAAM,MAAM;KAAC;IAE9B,SAAS4E,IAAiB;QAnP5B,IAAAxF,GAAAC,GAAAC;QAoPI,OAAIgC,KAAA,QAAAA,EAAO,OAAA,iKAEPK,UAAAA,CAAA,aAAA,CAAC,OAAA;YACC,WAAWhD,EAAG0C,KAAA,OAAA,KAAA,IAAAA,EAAY,MAAA,EAAA,CAAQjC,IAAAY,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAZ,EAAmB,MAAA,EAAQ,eAAe;YAC5E,gBAAc6D,MAAc;QAAA,GAE3B3B,EAAM,OACT,IAIAH,mKAEAQ,UAAAA,CAAA,aAAA,CAAC,OAAA;YACC,WAAWhD,EAAG0C,KAAA,OAAA,KAAA,IAAAA,EAAY,MAAA,EAAA,CAAQhC,IAAAW,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAX,EAAmB,MAAA,EAAQ,eAAe;YAC5E,gBAAc4D,MAAc;QAAA,GAE3B9B,EACH,kKAGGQ,UAAAA,CAAA,aAAA,CAACkD,IAAA;YAAO,WAAWlG,EAAG0C,KAAA,OAAA,KAAA,IAAAA,EAAY,MAAA,EAAA,CAAQ/B,IAAAU,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAV,EAAmB,MAAM;YAAG,SAAS2D,MAAc;QAAA,CAAW;IACjH;IAEA,qKACEtB,UAAAA,CAAA,aAAA,CAAC,MAAA;QACC,UAAU;QACV,KAAKmB;QACL,WAAWnE,EACToC,IACAoC,IACA9B,KAAA,OAAA,KAAA,IAAAA,EAAY,KAAA,EAAA,CACZjC,KAAAY,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAZ,GAAmB,KAAA,EACnBiC,KAAA,OAAA,KAAA,IAAAA,EAAY,OAAA,EACZA,KAAA,OAAA,KAAA,IAAAA,CAAAA,CAAa4B,EAAAA,EAAAA,CACb5D,KAAAW,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAX,EAAAA,CAAoB4D,EACtB;QACA,qBAAkB;QAClB,oBAAA,CAAkB3D,KAAAU,EAAM,UAAA,KAAN,OAAAV,KAAoBoB;QACtC,eAAa,CAAA,CAASV,EAAM,GAAA,IAAOA,EAAM,QAAA,IAAYC,CAAAA;QACrD,gBAAc6B;QACd,gBAAc,CAAA,CAAQ9B,EAAM,OAAA;QAC5B,eAAasC;QACb,gBAAcN;QACd,gBAAcgB;QACd,mBAAiBjE;QACjB,mBAAiBC;QACjB,cAAYsB;QACZ,cAAYyC;QACZ,gBAAcb;QACd,oBAAkBgB;QAClB,aAAWD;QACX,eAAakB;QACb,kBAAgB/B;QAChB,wBAAsBR;QACtB,iBAAe,CAAA,CAAA,CAAQpB,KAAaY,KAAmBU,CAAAA;QACvD,OACE;YACE,WAAWxB;YACX,mBAAmBA;YACnB,aAAaC,EAAO,MAAA,GAASD;YAC7B,YAAY,GAAyC,OAAtC0B,IAAUQ,IAAqBkB,EAAO,OAAA,EAAA;YACrD,oBAAoBtC,IAAkB,SAAS,GAAGsB,OAAAA,GAAAA;YAClD,GAAG9B,EAAAA;YACH,GAAGZ,EAAM;QACX;QAEF,WAAW,IAAM;YACfmC,EAAW,CAAA,CAAK,GAChBT,EAAkB,IAAI,GACtBkC,EAAgB,OAAA,GAAU;QAC5B;QACA,gBAAgBkB,GAAU;YACpBV,MAAY,CAAClB,KAAAA,CACjBL,EAAc,OAAA,GAAU,IAAI,MAC5BJ,EAAsBiB,EAAO,OAAO,GAEnCoB,EAAM,MAAA,CAAuB,iBAAA,CAAkBA,EAAM,SAAS,GAC1DA,EAAM,MAAA,CAAuB,OAAA,KAAY,YAAA,CAC9C3C,EAAW,CAAA,CAAI,GACfyB,EAAgB,OAAA,GAAU;gBAAE,GAAGkB,EAAM,OAAA;gBAAS,GAAGA,EAAM;YAAQ,CAAA,CAAA;QACjE;QACA,aAAa,IAAM;YAtUzB,IAAA1F,GAAAC,GAAAC,GAAAC;YAuUQ,IAAI6C,MAAY,CAACc,GAAa;YAE9BU,EAAgB,OAAA,GAAU;YAC1B,IAAMmB,IAAe,OAAA,CAAA,CACnB3F,IAAA0D,EAAS,OAAA,KAAT,OAAA,KAAA,IAAA1D,EAAkB,KAAA,CAAM,gBAAA,CAAiB,oBAAoB,OAAA,CAAQ,MAAM,GAAA,KAAO,CACpF,GACM4F,IAAe,OAAA,CAAA,CACnB3F,IAAAyD,EAAS,OAAA,KAAT,OAAA,KAAA,IAAAzD,EAAkB,KAAA,CAAM,gBAAA,CAAiB,oBAAoB,OAAA,CAAQ,MAAM,GAAA,KAAO,CACpF,GACM4F,IAAY,IAAI,KAAK,EAAE,OAAA,CAAQ,IAAA,CAAA,CAAI3F,IAAAuD,EAAc,OAAA,KAAd,OAAA,KAAA,IAAAvD,EAAuB,OAAA,EAAA,GAE1D4F,IAAczD,MAAmB,MAAMsD,IAAeC,GACtDG,IAAW,KAAK,GAAA,CAAID,CAAW,IAAID;YAEzC,IAAI,KAAK,GAAA,CAAIC,CAAW,KAAKzG,MAAmB0G,IAAW,KAAM;gBAC/D1C,EAAsBiB,EAAO,OAAO,GAAA,CACpCnE,IAAAS,EAAM,SAAA,KAAN,QAAAT,EAAA,IAAA,CAAAS,GAAkBA,IAGhB6B,EADEJ,MAAmB,MACAsD,IAAe,IAAI,UAAU,SAE7BC,IAAe,IAAI,SAAS,IAFO,GAK1DP,EAAY,GACZpC,EAAY,CAAA,CAAI,GAChBE,EAAY,CAAA,CAAK;gBACjB;YAAA;YAGFJ,EAAW,CAAA,CAAK,GAChBT,EAAkB,IAAI;QACxB;QACA,gBAAgBoD,GAAU;YAxWhC,IAAA1F,GAAAC,GAAAC,GAAAC;YA4WQ,IAHI,CAACqE,EAAgB,OAAA,IAAW,CAACV,KAAAA,CAAAA,CAEX9D,IAAA,OAAO,YAAA,CAAa,CAAA,KAApB,OAAA,KAAA,IAAAA,EAAuB,QAAA,GAAW,MAAA,IAAS,GAC9C;YAEnB,IAAMgG,IAASN,EAAM,OAAA,GAAUlB,EAAgB,OAAA,CAAQ,CAAA,EACjDyB,IAASP,EAAM,OAAA,GAAUlB,EAAgB,OAAA,CAAQ,CAAA,EAEjD0B,IAAAA,CAAkBjG,IAAAF,EAAM,eAAA,KAAN,OAAAE,IAAyBR,GAA0BC,EAAQ;YAG/E,CAAC2C,KAAAA,CAAmB,KAAK,GAAA,CAAI4D,CAAM,IAAI,KAAK,KAAK,GAAA,CAAID,CAAM,IAAI,CAAA,KACjE1D,EAAkB,KAAK,GAAA,CAAI2D,CAAM,IAAI,KAAK,GAAA,CAAID,CAAM,IAAI,MAAM,GAAG;YAGnE,IAAIF,IAAc;gBAAE,GAAG;gBAAG,GAAG;YAAE;YAG3BzD,MAAmB,MAAA,CAEjB6D,EAAgB,QAAA,CAAS,KAAK,KAAKA,EAAgB,QAAA,CAAS,QAAQ,CAAA,KAAA,CAClEA,EAAgB,QAAA,CAAS,KAAK,KAAKF,IAAS,KAErCE,EAAgB,QAAA,CAAS,QAAQ,KAAKF,IAAS,CAAA,KAAA,CACxDF,EAAY,CAAA,GAAIE,CAAAA,IAGX3D,MAAmB,OAAA,CAExB6D,EAAgB,QAAA,CAAS,MAAM,KAAKA,EAAgB,QAAA,CAAS,OAAO,CAAA,KAAA,CAClEA,EAAgB,QAAA,CAAS,MAAM,KAAKD,IAAS,KAEtCC,EAAgB,QAAA,CAAS,OAAO,KAAKD,IAAS,CAAA,KAAA,CACvDH,EAAY,CAAA,GAAIG,CAAAA,GAAAA,CAKlB,KAAK,GAAA,CAAIH,EAAY,CAAC,IAAI,KAAK,KAAK,GAAA,CAAIA,EAAY,CAAC,IAAI,CAAA,KAC3D3C,EAAY,CAAA,CAAI,GAAA,CAIlBjD,IAAAwD,EAAS,OAAA,KAAT,QAAAxD,EAAkB,KAAA,CAAM,WAAA,CAAY,oBAAoB,GAAe,OAAZ4F,EAAY,CAAA,EAAA,QAAA,CACvE3F,KAAAuD,EAAS,OAAA,KAAT,QAAAvD,GAAkB,KAAA,CAAM,WAAA,CAAY,oBAAoB,GAAe,OAAZ2F,EAAY,CAAA,EAAA;QACzE;IAAA,GAEC3B,MAAe,CAACvD,EAAM,GAAA,iKACrB2B,UAAAA,CAAA,aAAA,CAAC,UAAA;QACC,cAAYJ;QACZ,iBAAe6C;QACf,qBAAiB,CAAA;QACjB,SACEA,MAAY,CAAClB,IACT,IAAM,CAAC,IACP,IAAM;YAhatB,IAAA9D;YAiakBqF,EAAY,GAAA,CACZrF,IAAAY,EAAM,SAAA,KAAN,QAAAZ,EAAA,IAAA,CAAAY,GAAkBA;QACpB;QAEN,WAAWrB,EAAG0C,KAAA,OAAA,KAAA,IAAAA,EAAY,WAAA,EAAA,CAAa9B,KAAAS,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAT,GAAmB,WAAW;IAAA,GAAA,CAEpEC,KAAA8B,KAAA,OAAA,KAAA,IAAAA,EAAO,KAAA,KAAP,OAAA9B,KAAgB+F,EACnB,IACE,MAEHvF,EAAM,GAAA,sKAAOwF,iBAAAA,EAAexF,EAAM,KAAK,IACtCA,EAAM,GAAA,GACJA,EAAM,GAAA,GACJ,OAAOA,EAAM,KAAA,IAAU,aACzBA,EAAM,KAAA,CAAM,IAEZA,EAAM,KAAA,iKAGR2B,UAAAA,CAAA,aAAA,+JAAAA,UAAAA,CAAA,QAAA,EAAA,MACGsB,KAAajD,EAAM,IAAA,IAAQA,EAAM,OAAA,iKAChC2B,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,aAAU;QAAG,WAAWhD,EAAG0C,KAAA,OAAA,KAAA,IAAAA,EAAY,IAAA,EAAA,CAAM5B,KAAAO,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAP,GAAmB,IAAI;IAAA,GACtEO,EAAM,OAAA,IAAYA,EAAM,IAAA,KAAS,aAAa,CAACA,EAAM,IAAA,GAAQA,EAAM,IAAA,IAAQ4E,GAAe,IAAI,MAC9F5E,EAAM,IAAA,KAAS,YAAYA,EAAM,IAAA,IAAA,CAAQsB,KAAA,OAAA,KAAA,IAAAA,CAAAA,CAAQ2B,EAAAA,KAAcwC,GAASxC,CAAS,IAAI,IACxF,IACE,oKAEJtB,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,gBAAa;QAAG,WAAWhD,EAAG0C,KAAA,OAAA,KAAA,IAAAA,EAAY,OAAA,EAAA,CAAS3B,KAAAM,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAN,GAAmB,OAAO;IAAA,iKAChFiC,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,cAAW;QAAG,WAAWhD,EAAG0C,KAAA,OAAA,KAAA,IAAAA,EAAY,KAAA,EAAA,CAAO1B,KAAAK,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAL,GAAmB,KAAK;IAAA,GACzE,OAAOK,EAAM,KAAA,IAAU,aAAaA,EAAM,KAAA,CAAM,IAAIA,EAAM,KAC7D,GACCA,EAAM,WAAA,iKACL2B,UAAAA,CAAA,aAAA,CAAC,OAAA;QACC,oBAAiB;QACjB,WAAWhD,EACTqC,IACAoC,IACA/B,KAAA,OAAA,KAAA,IAAAA,EAAY,WAAA,EAAA,CACZzB,KAAAI,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAJ,GAAmB,WACrB;IAAA,GAEC,OAAOI,EAAM,WAAA,IAAgB,aAAaA,EAAM,WAAA,CAAY,IAAIA,EAAM,WACzE,IACE,IACN,qKACCwF,iBAAAA,EAAexF,EAAM,MAAM,IAC1BA,EAAM,MAAA,GACJA,EAAM,MAAA,IAAU0F,GAAS1F,EAAM,MAAM,kKACvC2B,UAAAA,CAAA,aAAA,CAAC,UAAA;QACC,eAAW,CAAA;QACX,eAAW,CAAA;QACX,OAAO3B,EAAM,iBAAA,IAAqBa;QAClC,UAAUiE,GAAU;YArdlC,IAAA1F,GAAAC;YAudqBqG,GAAS1F,EAAM,MAAM,KACrBkD,KAAAA,CAAAA,CACL7D,IAAAA,CAAAD,IAAAY,EAAM,MAAA,EAAO,OAAA,KAAb,QAAAX,EAAA,IAAA,CAAAD,GAAuB0F,IACvBL,EAAY,CAAA;QACd;QACA,WAAW9F,EAAG0C,KAAA,OAAA,KAAA,IAAAA,EAAY,YAAA,EAAA,CAAcxB,KAAAG,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAH,GAAmB,YAAY;IAAA,GAEtEG,EAAM,MAAA,CAAO,KAChB,IACE,wKACHwF,iBAAAA,EAAexF,EAAM,MAAM,IAC1BA,EAAM,MAAA,GACJA,EAAM,MAAA,IAAU0F,GAAS1F,EAAM,MAAM,iKACvC2B,WAAAA,CAAA,aAAA,CAAC,UAAA;QACC,eAAW,CAAA;QACX,eAAW,CAAA;QACX,OAAO3B,EAAM,iBAAA,IAAqBc;QAClC,UAAUgE,GAAU;YAxelC,IAAA1F,GAAAC;YA0eqBqG,GAAS1F,EAAM,MAAM,KAAA,CAAA,CAC1BX,IAAAA,CAAAD,IAAAY,EAAM,MAAA,EAAO,OAAA,KAAb,QAAAX,EAAA,IAAA,CAAAD,GAAuB0F,IACnB,CAAAA,EAAM,gBAAA,IACVL,EAAY,CAAA;QACd;QACA,WAAW9F,EAAG0C,KAAA,OAAA,KAAA,IAAAA,EAAY,YAAA,EAAA,CAAcvB,KAAAE,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAF,GAAmB,YAAY;IAAA,GAEtEE,EAAM,MAAA,CAAO,KAChB,IACE,IACN,CAEJ;AAEJ;AAEA,SAAS2F,IAA4C;IAEnD,IADI,OAAO,UAAW,eAClB,OAAO,YAAa,aAAa,OAAO;IAE5C,IAAMC,IAAe,SAAS,eAAA,CAAgB,YAAA,CAAa,KAAK;IAEhE,OAAIA,MAAiB,UAAU,CAACA,IACvB,OAAO,gBAAA,CAAiB,SAAS,eAAe,EAAE,SAAA,GAGpDA;AACT;AAEA,SAASC,GAAaC,CAAAA,EAAuCC,CAAAA,CAA4C;IACvG,IAAMC,IAAS,CAAC;IAEhB,OAAA;QAACF;QAAeC,CAAY;KAAA,CAAE,OAAA,CAAQ,CAACrC,GAAQpD,IAAU;QACvD,IAAM2F,IAAW3F,MAAU,GACrB4F,IAASD,IAAW,oBAAoB,YACxCE,IAAeF,IAAW5H,KAAyBD;QAEzD,SAASgI,EAAU1C,CAAAA,CAAyB;YAC1C;gBAAC;gBAAO;gBAAS;gBAAU,MAAM;aAAA,CAAE,OAAA,EAAS2C,GAAQ;gBAClDL,CAAAA,CAAO,UAAGE,GAAAA,KAAUG,CAAK,MAALA,GAAK,GAAI,OAAO3C,KAAW,WAAW,GAAGA,OAAAA,GAAAA,QAAaA;YAC5E,CAAC;QACH;QAEI,OAAOA,KAAW,YAAY,OAAOA,KAAW,WAClD0C,EAAU1C,CAAM,IACP,OAAOA,KAAW,WAC3B;YAAC;YAAO;YAAS;YAAU,MAAM;SAAA,CAAE,OAAA,EAAS2C,GAAQ;YAC9C3C,CAAAA,CAAO2C,CAAG,CAAA,KAAM,KAAA,IAClBL,CAAAA,CAAO,UAAGE,GAAAA,KAAUG,CAAK,MAALA,GAAK,GAAIF,IAE7BH,CAAAA,CAAO,UAAGE,GAAAA,KAAUG,CAAK,MAALA,GAAK,GAAI,OAAO3C,CAAAA,CAAO2C,CAAG,CAAA,IAAM,WAAW,GAAa,OAAV3C,CAAAA,CAAO2C,CAAG,CAAA,EAAA,QAAQ3C,CAAAA,CAAO2C,CAAG;QAElG,CAAC,IAEDD,EAAUD,CAAY;IAE1B,CAAC,GAEMH;AACT;AAEA,SAASM,IAAY;IACnB,IAAM,CAACC,GAAcC,CAAe,CAAA,iKAAI7E,UAAAA,CAAM,QAAA,CAAmB,CAAC,CAAC;IAEnE,qKAAAA,UAAAA,CAAM,SAAA;wBAAU,IACP8E,EAAW,SAAA;iCAAWzG,GAAU;oBACrC,IAAKA,EAAyB,OAAA,EAAS;wBACrC;4CAAW,IAAM;gCACf0G,+KAAAA,CAAS,SAAA;oDAAU,IAAM;wCACvBF;6DAAiBjG,IAAWA,EAAO,MAAA;qEAAQoG,IAAMA,EAAE,EAAA,KAAO3G,EAAM,EAAE,CAAC;;;oCACrE,CAAC;;4BACH,CAAC;;wBACD;oBAAA;oBAIF;wCAAW,IAAM;iMACf0G,UAAAA,CAAS,SAAA;gDAAU,IAAM;oCACvBF;wDAAiBjG,GAAW;4CAC1B,IAAMqG,IAAuBrG,EAAO,SAAA;mEAAWoG,IAAMA,EAAE,EAAA,KAAO3G,EAAM,EAAE;;4CAGtE,OAAI4G,MAAyB,CAAA,IACpB,CACL;mDAAGrG,EAAO,KAAA,CAAM,GAAGqG,CAAoB;gDACvC;oDAAE,GAAGrG,CAAAA,CAAOqG,CAAoB,CAAA;oDAAG,GAAG5G;gDAAM,EAC5C;mDAAGO,EAAO,KAAA,CAAMqG,IAAuB,CAAC,CAC1C;6CAAA,GAGK;gDAAC5G,EAAO;mDAAGO,CAAM;;wCAC1B,CAAC;;gCACH,CAAC;;wBACH,CAAC;;gBACH,CAAC;;uBACA,CAAC,CAAC,GAEE;QACL,QAAQgG;IACV;AACF;AAEA,IAAMM,uKAAUC,aAAAA,EAAsC,SAAiB3H,CAAAA,EAAO4H,CAAAA,CAAK;IACjF,IAAM,EACJ,QAAA5C,CAAAA,EACA,UAAArF,IAAW,cAAA,EACX,QAAAkI,IAAS;QAAC;QAAU,MAAM;KAAA,EAC1B,QAAAC,CAAAA,EACA,aAAA1D,CAAAA,EACA,WAAAxC,CAAAA,EACA,QAAA2C,CAAAA,EACA,cAAAqC,CAAAA,EACA,OAAAmB,IAAQ,OAAA,EACR,YAAAC,CAAAA,EACA,UAAA3D,EAAAA,EACA,OAAA5C,EAAAA,EACA,eAAAR,KAAgBjC,EAAAA,EAChB,cAAAiJ,CAAAA,EACA,KAAAC,KAAM1B,GAAqB,CAAA,EAC3B,KAAAzE,KAAM1C,EAAAA,EACN,aAAA8I,CAAAA,EACA,OAAAhG,EAAAA,EACA,oBAAAiG,KAAqB,eAAA,EACrB,uBAAA/F,EACF,EAAA,GAAIrC,GACE,CAACoB,GAAQiH,CAAS,CAAA,GAAI7F,wKAAAA,CAAM,QAAA,CAAmB,CAAC,CAAC,GACjD8F,kKAAoB9F,UAAAA,CAAM,OAAA;yBAAQ,IAC/B,MAAM,IAAA,CACX,IAAI,IAAI;gBAAC7C,CAAQ;aAAA,CAAE,MAAA,CAAOyB,EAAO,MAAA;kCAAQP,IAAUA,EAAM,QAAQ;gCAAE,GAAA;kCAAKA,IAAUA,EAAM,QAAQ,CAAC,CAAC,CACpG;;wBACC;QAACO;QAAQzB,CAAQ;KAAC,GACf,CAACuB,IAASF,EAAU,CAAA,iKAAIwB,UAAAA,CAAM,QAAA,CAAoB,CAAC,CAAC,GACpD,CAACnB,GAAUkH,CAAW,CAAA,iKAAI/F,UAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GAC9C,CAACzB,IAAayH,CAAc,CAAA,GAAIhG,wKAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GACpD,CAACiG,GAAaC,CAAc,CAAA,iKAAIlG,UAAAA,CAAM,QAAA,CAC1CuF,MAAU,WACNA,IACA,OAAO,UAAW,eAClB,OAAO,UAAA,IAAc,OAAO,UAAA,CAAW,8BAA8B,EAAE,OAAA,GACrE,SAEF,OACN,GAEMY,kKAAUnG,UAAAA,CAAM,MAAA,CAAyB,IAAI,GAC7CoG,KAAcf,EAAO,IAAA,CAAK,GAAG,EAAE,OAAA,CAAQ,QAAQ,EAAE,EAAE,OAAA,CAAQ,UAAU,EAAE,GACvEgB,IAAwBrG,wKAAAA,CAAM,MAAA,CAAoB,IAAI,GACtDsG,kKAAmBtG,UAAAA,CAAM,MAAA,CAAO,CAAA,CAAK,GAErClB,mKAAckB,UAAAA,CAAM,WAAA;+BAAauG,GAA0B;YAC/DV;uCAAWjH,GAAW;oBAhoB1B,IAAAnB;oBAioBM,OAAA,CAAKA,IAAAmB,EAAO,IAAA;+CAAMP,IAAUA,EAAM,EAAA,KAAOkI,EAAc,EAAE;4CAAA,KAApD,QAAA9I,EAAuD,MAAA,IAC1DqH,EAAW,OAAA,CAAQyB,EAAc,EAAE,GAG9B3H,EAAO,MAAA;8CAAO;gCAAC,EAAE,IAAA4H,CAAG,EAAA;mCAAMA,MAAOD,EAAc,EAAE;;;gBAC1D,CAAC;;QACH;6BAAG,CAAC,CAAC;IAEL,OAAAvG,wKAAAA,CAAM,SAAA;wBAAU,IACP8E,EAAW,SAAA;iCAAWzG,GAAU;oBACrC,IAAKA,EAAyB,OAAA,EAAS;wBACrCwH;6CAAWjH,IAAWA,EAAO,GAAA;qDAAKoG,IAAOA,EAAE,EAAA,KAAO3G,EAAM,EAAA,GAAK;4CAAE,GAAG2G,CAAAA;4CAAG,QAAQ,CAAA;wCAAK,IAAIA,CAAE,CAAC;;;wBACzF;oBAAA;oBAIF;wCAAW,IAAM;iMACfD,UAAAA,CAAS,SAAA;gDAAU,IAAM;oCACvBc;yDAAWjH,GAAW;4CACpB,IAAMqG,IAAuBrG,EAAO,SAAA;mEAAWoG,IAAMA,EAAE,EAAA,KAAO3G,EAAM,EAAE;;4CAGtE,OAAI4G,MAAyB,CAAA,IACpB,CACL;mDAAGrG,EAAO,KAAA,CAAM,GAAGqG,CAAoB;gDACvC;oDAAE,GAAGrG,CAAAA,CAAOqG,CAAoB,CAAA;oDAAG,GAAG5G;gDAAM,EAC5C;mDAAGO,EAAO,KAAA,CAAMqG,IAAuB,CAAC,CAC1C;6CAAA,GAGK;gDAAC5G,EAAO;mDAAGO,CAAM;;wCAC1B,CAAC;;gCACH,CAAC;;wBACH,CAAC;;gBACH,CAAC;;uBACA,CAAC,CAAC,iKAELoB,UAAAA,CAAM,SAAA;wBAAU,IAAM;YACpB,IAAIuF,MAAU,UAAU;gBACtBW,EAAeX,CAAK;gBACpB;YAAA;YAcF,IAXIA,MAAU,YAAA,CAER,OAAO,UAAA,IAAc,OAAO,UAAA,CAAW,8BAA8B,EAAE,OAAA,GAEzEW,EAAe,MAAM,IAGrBA,EAAe,OAAO,CAAA,GAItB,OAAO,UAAW,aAAa;YACnC,IAAMO,IAAiB,OAAO,UAAA,CAAW,8BAA8B;YAEvE,IAAI;gBAEFA,EAAe,gBAAA,CAAiB;oCAAU;4BAAC,EAAE,SAAAC,CAAQ,EAAA,GAAM;wBAEvDR,EADEQ,IACa,SAEA,OAFM;oBAIzB,CAAC;;YACH,EAAA,OAASC,GAAP;gBAEAF,EAAe,WAAA;oCAAY;4BAAC,EAAE,SAAAC,CAAQ,EAAA,GAAM;wBAC1C,IAAI;4BAEAR,EADEQ,IACa,SAEA,OAFM;wBAIzB,EAAA,OAASE,GAAP;4BACA,QAAQ,KAAA,CAAMA,CAAC;wBACjB;oBACF,CAAC;;YACH;QACF;uBAAG;QAACrB,CAAK;KAAC,iKAEVvF,UAAAA,CAAM,SAAA;wBAAU,IAAM;YAEhBpB,EAAO,MAAA,IAAU,KACnBmH,EAAY,CAAA,CAAK;QAErB;uBAAG;QAACnH,CAAM;KAAC,iKAEXoB,UAAAA,CAAM,SAAA;wBAAU,IAAM;YACpB,IAAM6G;mCAAiB1D,GAAyB;oBA3tBpD,IAAA1F,GAAAC;oBA4tB8B2H,EAAO,KAAA;2CAAOX,IAASvB,CAAAA,CAAcuB,CAAG,CAAA,IAAKvB,EAAM,IAAA,KAASuB,CAAG;4CAAA,CAGrFqB,EAAY,CAAA,CAAI,GAAA,CAChBtI,IAAA0I,EAAQ,OAAA,KAAR,QAAA1I,EAAiB,KAAA,EAAA,GAIjB0F,EAAM,IAAA,KAAS,YAAA,CACd,SAAS,aAAA,KAAkBgD,EAAQ,OAAA,IAAA,CAAWzI,IAAAyI,EAAQ,OAAA,KAAR,QAAAzI,EAAiB,QAAA,CAAS,SAAS,aAAA,CAAA,KAElFqI,EAAY,CAAA,CAAK;gBAErB;;YACA,OAAA,SAAS,gBAAA,CAAiB,WAAWc,CAAa;gCAE3C,IAAM,SAAS,mBAAA,CAAoB,WAAWA,CAAa;;QACpE;uBAAG;QAACxB,CAAM;KAAC,iKAEXrF,UAAAA,CAAM,SAAA;wBAAU,IAAM;YACpB,IAAImG,EAAQ,OAAA,EACV;gCAAO,IAAM;oBACPE,EAAsB,OAAA,IAAA,CACxBA,EAAsB,OAAA,CAAQ,KAAA,CAAM;wBAAE,eAAe,CAAA;oBAAK,CAAC,GAC3DA,EAAsB,OAAA,GAAU,MAChCC,EAAiB,OAAA,GAAU,CAAA,CAAA;gBAE/B;;QAEJ;uBAAG;QAACH,EAAQ,OAAO;KAAC,iKAIlBnG,UAAAA,CAAA,aAAA,CAAC,WAAA;QACC,KAAKoF;QACL,cAAY,UAAGQ,IAAAA,KAAsBQ,OAAAA;QACrC,UAAU,CAAA;QACV,aAAU;QACV,iBAAc;QACd,eAAY;QACZ,0BAAwB,CAAA;IAAA,GAEvBN,EAAkB,GAAA,CAAI,CAAC3I,GAAUwB,IAAU;QAtwBlD,IAAAlB;QAuwBQ,IAAM,CAAC,GAAGJ,CAAC,CAAA,GAAIF,EAAS,KAAA,CAAM,GAAG;QAEjC,OAAKyB,EAAO,MAAA,iKAGVoB,UAAAA,CAAA,aAAA,CAAC,MAAA;YACC,KAAK7C;YACL,KAAKuI,OAAQ,SAAS1B,GAAqB,IAAI0B;YAC/C,UAAU,CAAA;YACV,KAAKS;YACL,WAAW/G;YACX,uBAAmB,CAAA;YACnB,cAAY6G;YACZ,mBAAiB;YACjB,eAAapH,KAAYD,EAAO,MAAA,GAAS,KAAK,CAAC0G;YAC/C,mBAAiBjI;YACjB,OACE;gBACE,wBAAwB,GAAyB,OAAzB,CAAA,CAAGI,IAAAiB,EAAAA,CAAQ,CAAC,CAAA,KAAT,OAAA,KAAA,IAAAjB,EAAY,MAAA,KAAU,GAAA;gBACjD,WAAW,GAAGb,OAAAA,IAAAA;gBACd,SAAS,GAAG2C,OAAAA,IAAAA;gBACZ,GAAGN,EAAAA;gBACH,GAAGiF,GAAanC,GAAQqC,CAAY,CACtC;;YAEF,SAASjB,GAAU;gBACbmD,EAAiB,OAAA,IAAW,CAACnD,EAAM,aAAA,CAAc,QAAA,CAASA,EAAM,aAAa,KAAA,CAC/EmD,EAAiB,OAAA,GAAU,CAAA,GACvBD,EAAsB,OAAA,IAAA,CACxBA,EAAsB,OAAA,CAAQ,KAAA,CAAM;oBAAE,eAAe,CAAA;gBAAK,CAAC,GAC3DA,EAAsB,OAAA,GAAU,IAAA,CAAA;YAGtC;YACA,UAAUlD,GAAU;gBAEhBA,EAAM,MAAA,YAAkB,eAAeA,EAAM,MAAA,CAAO,OAAA,CAAQ,WAAA,KAAgB,WAIzEmD,EAAiB,OAAA,IAAA,CACpBA,EAAiB,OAAA,GAAU,CAAA,GAC3BD,EAAsB,OAAA,GAAUlD,EAAM,aAAA;YAE1C;YACA,cAAc,IAAM4C,EAAY,CAAA,CAAI;YACpC,aAAa,IAAMA,EAAY,CAAA,CAAI;YACnC,cAAc,IAAM;gBAEbxH,MACHwH,EAAY,CAAA,CAAK;YAErB;YACA,WAAW,IAAMA,EAAY,CAAA,CAAK;YAClC,gBAAgB5C,GAAU;gBAEtBA,EAAM,MAAA,YAAkB,eAAeA,EAAM,MAAA,CAAO,OAAA,CAAQ,WAAA,KAAgB,WAG9E6C,EAAe,CAAA,CAAI;YACrB;YACA,aAAa,IAAMA,EAAe,CAAA,CAAK;QAAA,GAEtCpH,EACE,MAAA,EAAQP,IAAW,CAACA,EAAM,QAAA,IAAYM,MAAU,KAAMN,EAAM,QAAA,KAAalB,CAAQ,EACjF,GAAA,CAAI,CAACkB,GAAOM,IAAO;YAx0BlC,IAAAlB,GAAAC;YAy0BgB,qKAAAsC,UAAAA,CAAA,aAAA,CAACzC,IAAA;gBACC,KAAKc,EAAM,EAAA;gBACX,OAAOsB;gBACP,OAAOhB;gBACP,OAAON;gBACP,mBAAmBmH;gBACnB,UAAA,CAAU/H,IAAAgI,KAAA,OAAA,KAAA,IAAAA,EAAc,QAAA,KAAd,OAAAhI,IAA0BoE;gBACpC,WAAW4D,KAAA,OAAA,KAAA,IAAAA,EAAc,SAAA;gBACzB,sBAAsBA,KAAA,OAAA,KAAA,IAAAA,EAAc,oBAAA;gBACpC,QAAQjD;gBACR,eAAe/D;gBACf,aAAA,CAAaf,IAAA+H,KAAA,OAAA,KAAA,IAAAA,EAAc,WAAA,KAAd,OAAA/H,IAA6BkE;gBAC1C,aAAarD;gBACb,UAAUpB;gBACV,OAAOsI,KAAA,OAAA,KAAA,IAAAA,EAAc,KAAA;gBACrB,UAAUA,KAAA,OAAA,KAAA,IAAAA,EAAc,QAAA;gBACxB,YAAYA,KAAA,OAAA,KAAA,IAAAA,EAAc,UAAA;gBAC1B,mBAAmBA,KAAA,OAAA,KAAA,IAAAA,EAAc,iBAAA;gBACjC,mBAAmBA,KAAA,OAAA,KAAA,IAAAA,EAAc,iBAAA;gBACjC,aAAa3G;gBACb,QAAQF,EAAO,MAAA,EAAQoG,IAAMA,EAAE,QAAA,IAAY3G,EAAM,QAAQ;gBACzD,SAASK,GAAQ,MAAA,EAAQiE,IAAMA,EAAE,QAAA,IAAYtE,EAAM,QAAQ;gBAC3D,YAAYG;gBACZ,iBAAiB8G;gBACjB,KAAK/F;gBACL,aAAaoG;gBACb,UAAU9G;gBACV,uBAAuBgB;gBACvB,iBAAiBrC,EAAM,eAAA;YAAA,CACzB;QAAA,CACD,CACL,IA/FyB;IAiG7B,CAAC,CACH;AAEJ,CAAC", "debugId": null}}, {"offset": {"line": 3026, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/shared/lib/router/utils/disable-smooth-scroll.ts"], "sourcesContent": ["import { warnOnce } from '../../utils/warn-once'\n\n/**\n * Run function with `scroll-behavior: auto` applied to `<html/>`.\n * This css change will be reverted after the function finishes.\n */\nexport function disableSmoothScrollDuringRouteTransition(\n  fn: () => void,\n  options: { dontForceLayout?: boolean; onlyHashChange?: boolean } = {}\n) {\n  // if only the hash is changed, we don't need to disable smooth scrolling\n  // we only care to prevent smooth scrolling when navigating to a new page to avoid jarring UX\n  if (options.onlyHashChange) {\n    fn()\n    return\n  }\n\n  const htmlElement = document.documentElement\n  const hasDataAttribute = htmlElement.dataset.scrollBehavior === 'smooth'\n\n  // Since this is a breaking change, this is temporarily flagged\n  // and will be false by default.\n  // In the next major (v16), this will be automatically enabled\n  if (process.env.__NEXT_OPTIMIZE_ROUTER_SCROLL) {\n    if (!hasDataAttribute) {\n      // No smooth scrolling configured, run directly without style manipulation\n      fn()\n      return\n    }\n  } else {\n    // Old behavior: always manipulate styles, but warn about upcoming change\n\n    // Warn if smooth scrolling is detected but no data attribute is present\n    if (\n      process.env.NODE_ENV === 'development' &&\n      !hasDataAttribute &&\n      getComputedStyle(htmlElement).scrollBehavior === 'smooth'\n    ) {\n      warnOnce(\n        'Detected `scroll-behavior: smooth` on the `<html>` element. In a future version, ' +\n          'Next.js will no longer automatically disable smooth scrolling during route transitions. ' +\n          'To prepare for this change, add `data-scroll-behavior=\"smooth\"` to your <html> element. ' +\n          'Learn more: https://nextjs.org/docs/messages/missing-data-scroll-behavior'\n      )\n    }\n  }\n\n  // Proceed with temporarily disabling smooth scrolling\n  const existing = htmlElement.style.scrollBehavior\n  htmlElement.style.scrollBehavior = 'auto'\n  if (!options.dontForceLayout) {\n    // In Chrome-based browsers we need to force reflow before calling `scrollTo`.\n    // Otherwise it will not pickup the change in scrollBehavior\n    // More info here: https://github.com/vercel/next.js/issues/40719#issuecomment-1336248042\n    htmlElement.getClientRects()\n  }\n  fn()\n  htmlElement.style.scrollBehavior = existing\n}\n"], "names": ["disableSmoothScrollDuringRouteTransition", "fn", "options", "onlyHashChange", "htmlElement", "document", "documentElement", "hasDataAttribute", "dataset", "scroll<PERSON>eh<PERSON>or", "process", "env", "__NEXT_OPTIMIZE_ROUTER_SCROLL", "NODE_ENV", "getComputedStyle", "warnOnce", "existing", "style", "dontForceLayout", "getClientRects"], "mappings": "AAuBMU,QAAQC,GAAG,CAACC,6BAA6B,EAAE;;;;;+BAjBjCZ,4CAAAA;;;eAAAA;;;0BANS;AAMlB,SAASA,yCACdC,EAAc,EACdC,OAAqE;IAArEA,IAAAA,YAAAA,KAAAA,GAAAA,UAAmE,CAAC;IAEpE,yEAAyE;IACzE,6FAA6F;IAC7F,IAAIA,QAAQC,cAAc,EAAE;QAC1BF;QACA;IACF;IAEA,MAAMG,cAAcC,SAASC,eAAe;IAC5C,MAAMC,mBAAmBH,YAAYI,OAAO,CAACC,cAAc,KAAK;IAEhE,+DAA+D;IAC/D,gCAAgC;IAChC,8DAA8D;IAC9D;;SAMO;QACL,yEAAyE;QAEzE,wEAAwE;QACxE,IACEC,QAAQC,GAAG,CAACE,QAAQ,gCAAK,iBACzB,CAACN,oBACDO,iBAAiBV,aAAaK,cAAc,KAAK,UACjD;YACAM,CAAAA,GAAAA,UAAAA,QAAQ,EACN,sFACE,6FACA,6FACA;QAEN;IACF;IAEA,sDAAsD;IACtD,MAAMC,WAAWZ,YAAYa,KAAK,CAACR,cAAc;IACjDL,YAAYa,KAAK,CAACR,cAAc,GAAG;IACnC,IAAI,CAACP,QAAQgB,eAAe,EAAE;QAC5B,8EAA8E;QAC9E,4DAA4D;QAC5D,yFAAyF;QACzFd,YAAYe,cAAc;IAC5B;IACAlB;IACAG,YAAYa,KAAK,CAACR,cAAc,GAAGO;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3078, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/client/components/bfcache.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../server/app-render/types'\nimport { useState } from 'react'\n\n// When the flag is disabled, only track the currently active tree\nconst MAX_BF_CACHE_ENTRIES = process.env.__NEXT_ROUTER_BF_CACHE ? 3 : 1\n\nexport type RouterBFCacheEntry = {\n  tree: FlightRouterState\n  stateKey: string\n  // The entries form a linked list, sorted in order of most recently active.\n  next: RouterBFCacheEntry | null\n}\n\n/**\n * Keeps track of the most recent N trees (FlightRouterStates) that were active\n * at a certain segment level. E.g. for a segment \"/a/b/[param]\", this hook\n * tracks the last N param values that the router rendered for N.\n *\n * The result of this hook precisely determines the number and order of\n * trees that are rendered in parallel at their segment level.\n *\n * The purpose of this cache is to we can preserve the React and DOM state of\n * some number of inactive trees, by rendering them in an <Activity> boundary.\n * That means it would not make sense for the the lifetime of the cache to be\n * any longer than the lifetime of the React tree; e.g. if the hook were\n * unmounted, then the React tree would be, too. So, we use React state to\n * manage it.\n *\n * Note that we don't store the RSC data for the cache entries in this hook —\n * the data for inactive segments is stored in the parent CacheNode, which\n * *does* have a longer lifetime than the React tree. This hook only determines\n * which of those trees should have their *state* preserved, by <Activity>.\n */\nexport function useRouterBFCache(\n  activeTree: FlightRouterState,\n  activeStateKey: string\n): RouterBFCacheEntry {\n  // The currently active entry. The entries form a linked list, sorted in\n  // order of most recently active. This allows us to reuse parts of the list\n  // without cloning, unless there's a reordering or removal.\n  // TODO: Once we start tracking back/forward history at each route level,\n  // we should use the history order instead. In other words, when traversing\n  // to an existing entry as a result of a popstate event, we should maintain\n  // the existing order instead of moving it to the front of the list. I think\n  // an initial implementation of this could be to pass an incrementing id\n  // to history.pushState/replaceState, then use that here for ordering.\n  const [prevActiveEntry, setPrevActiveEntry] = useState<RouterBFCacheEntry>(\n    () => {\n      const initialEntry: RouterBFCacheEntry = {\n        tree: activeTree,\n        stateKey: activeStateKey,\n        next: null,\n      }\n      return initialEntry\n    }\n  )\n\n  if (prevActiveEntry.tree === activeTree) {\n    // Fast path. The active tree hasn't changed, so we can reuse the\n    // existing state.\n    return prevActiveEntry\n  }\n\n  // The route tree changed. Note that this doesn't mean that the tree changed\n  // *at this level* — the change may be due to a child route. Either way, we\n  // need to either add or update the router tree in the bfcache.\n  //\n  // The rest of the code looks more complicated than it actually is because we\n  // can't mutate the state in place; we have to copy-on-write.\n\n  // Create a new entry for the active cache key. This is the head of the new\n  // linked list.\n  const newActiveEntry: RouterBFCacheEntry = {\n    tree: activeTree,\n    stateKey: activeStateKey,\n    next: null,\n  }\n\n  // We need to append the old list onto the new list. If the head of the new\n  // list was already present in the cache, then we'll need to clone everything\n  // that came before it. Then we can reuse the rest.\n  let n = 1\n  let oldEntry: RouterBFCacheEntry | null = prevActiveEntry\n  let clonedEntry: RouterBFCacheEntry = newActiveEntry\n  while (oldEntry !== null && n < MAX_BF_CACHE_ENTRIES) {\n    if (oldEntry.stateKey === activeStateKey) {\n      // Fast path. This entry in the old list that corresponds to the key that\n      // is now active. We've already placed a clone of this entry at the front\n      // of the new list. We can reuse the rest of the old list without cloning.\n      // NOTE: We don't need to worry about eviction in this case because we\n      // haven't increased the size of the cache, and we assume the max size\n      // is constant across renders. If we were to change it to a dynamic limit,\n      // then the implementation would need to account for that.\n      clonedEntry.next = oldEntry.next\n      break\n    } else {\n      // Clone the entry and append it to the list.\n      n++\n      const entry: RouterBFCacheEntry = {\n        tree: oldEntry.tree,\n        stateKey: oldEntry.stateKey,\n        next: null,\n      }\n      clonedEntry.next = entry\n      clonedEntry = entry\n    }\n    oldEntry = oldEntry.next\n  }\n\n  setPrevActiveEntry(newActiveEntry)\n  return newActiveEntry\n}\n"], "names": ["useRouterBFCache", "MAX_BF_CACHE_ENTRIES", "process", "env", "__NEXT_ROUTER_BF_CACHE", "activeTree", "activeStateKey", "prevActiveEntry", "setPrevActiveEntry", "useState", "initialEntry", "tree", "stateKey", "next", "newActiveEntry", "n", "oldEntry", "clonedEntry", "entry"], "mappings": "AAI6BE,QAAQC,GAAG,CAACC,sBAAsB;;;;;+BA6B/CJ,oBAAAA;;;eAAAA;;;uBAhCS;AAEzB,kEAAkE;AAClE,MAAMC,6DAA4D,0BAAI;AA6B/D,SAASD,iBACdK,UAA6B,EAC7BC,cAAsB;IAEtB,wEAAwE;IACxE,2EAA2E;IAC3E,2DAA2D;IAC3D,yEAAyE;IACzE,2EAA2E;IAC3E,2EAA2E;IAC3E,4EAA4E;IAC5E,wEAAwE;IACxE,sEAAsE;IACtE,MAAM,CAACC,iBAAiBC,mBAAmB,GAAGC,CAAAA,GAAAA,OAAAA,QAAQ,EACpD;QACE,MAAMC,eAAmC;YACvCC,MAAMN;YACNO,UAAUN;YACVO,MAAM;QACR;QACA,OAAOH;IACT;IAGF,IAAIH,gBAAgBI,IAAI,KAAKN,YAAY;QACvC,iEAAiE;QACjE,kBAAkB;QAClB,OAAOE;IACT;IAEA,4EAA4E;IAC5E,2EAA2E;IAC3E,+DAA+D;IAC/D,EAAE;IACF,6EAA6E;IAC7E,6DAA6D;IAE7D,2EAA2E;IAC3E,eAAe;IACf,MAAMO,iBAAqC;QACzCH,MAAMN;QACNO,UAAUN;QACVO,MAAM;IACR;IAEA,2EAA2E;IAC3E,6EAA6E;IAC7E,mDAAmD;IACnD,IAAIE,IAAI;IACR,IAAIC,WAAsCT;IAC1C,IAAIU,cAAkCH;IACtC,MAAOE,aAAa,QAAQD,IAAId,qBAAsB;QACpD,IAAIe,SAASJ,QAAQ,KAAKN,gBAAgB;YACxC,yEAAyE;YACzE,yEAAyE;YACzE,0EAA0E;YAC1E,sEAAsE;YACtE,sEAAsE;YACtE,0EAA0E;YAC1E,0DAA0D;YAC1DW,YAAYJ,IAAI,GAAGG,SAASH,IAAI;YAChC;QACF,OAAO;YACL,6CAA6C;YAC7CE;YACA,MAAMG,QAA4B;gBAChCP,MAAMK,SAASL,IAAI;gBACnBC,UAAUI,SAASJ,QAAQ;gBAC3BC,MAAM;YACR;YACAI,YAAYJ,IAAI,GAAGK;YACnBD,cAAcC;QAChB;QACAF,WAAWA,SAASH,IAAI;IAC1B;IAEAL,mBAAmBM;IACnB,OAAOA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3174, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/client/components/layout-router.tsx"], "sourcesContent": ["'use client'\n\nimport type {\n  <PERSON>ache<PERSON><PERSON>,\n  LazyCacheNode,\n  LoadingModuleData,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../server/app-render/types'\nimport type { ErrorComponent } from './error-boundary'\nimport {\n  ACTION_SERVER_PATCH,\n  type FocusAndScrollRef,\n} from './router-reducer/router-reducer-types'\n\nimport React, {\n  useContext,\n  use,\n  startTransition,\n  Suspense,\n  useDeferredValue,\n  type JSX,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport {\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n  TemplateContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { fetchServerResponse } from './router-reducer/fetch-server-response'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { ErrorBoundary } from './error-boundary'\nimport { matchSegment } from './match-segments'\nimport { disableSmoothScrollDuringRouteTransition } from '../../shared/lib/router/utils/disable-smooth-scroll'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary'\nimport { createRouterCacheKey } from './router-reducer/create-router-cache-key'\nimport { hasInterceptionRouteInCurrentTree } from './router-reducer/reducers/has-interception-route-in-current-tree'\nimport { dispatchAppRouterAction } from './use-action-queue'\nimport { useRouterBFCache, type RouterBFCacheEntry } from './bfcache'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\n\nconst Activity = process.env.__NEXT_ROUTER_BF_CACHE\n  ? (require('react') as typeof import('react')).unstable_Activity\n  : null!\n\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */\nfunction walkAddRefetch(\n  segmentPathToWalk: FlightSegmentPath | undefined,\n  treeToRecreate: FlightRouterState\n): FlightRouterState {\n  if (segmentPathToWalk) {\n    const [segment, parallelRouteKey] = segmentPathToWalk\n    const isLast = segmentPathToWalk.length === 2\n\n    if (matchSegment(treeToRecreate[0], segment)) {\n      if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n        if (isLast) {\n          const subTree = walkAddRefetch(\n            undefined,\n            treeToRecreate[1][parallelRouteKey]\n          )\n          return [\n            treeToRecreate[0],\n            {\n              ...treeToRecreate[1],\n              [parallelRouteKey]: [\n                subTree[0],\n                subTree[1],\n                subTree[2],\n                'refetch',\n              ],\n            },\n          ]\n        }\n\n        return [\n          treeToRecreate[0],\n          {\n            ...treeToRecreate[1],\n            [parallelRouteKey]: walkAddRefetch(\n              segmentPathToWalk.slice(2),\n              treeToRecreate[1][parallelRouteKey]\n            ),\n          },\n        ]\n      }\n    }\n  }\n\n  return treeToRecreate\n}\n\nconst __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = (\n  ReactDOM as any\n).__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE\n\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */\nfunction findDOMNode(\n  instance: React.ReactInstance | null | undefined\n): Element | Text | null {\n  // Tree-shake for server bundle\n  if (typeof window === 'undefined') return null\n\n  // __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode is null during module init.\n  // We need to lazily reference it.\n  const internal_reactDOMfindDOMNode =\n    __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode\n  return internal_reactDOMfindDOMNode(instance)\n}\n\nconst rectProperties = [\n  'bottom',\n  'height',\n  'left',\n  'right',\n  'top',\n  'width',\n  'x',\n  'y',\n] as const\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */\nfunction shouldSkipElement(element: HTMLElement) {\n  // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n  // and will result in a situation we bail on scroll because of something like a fixed nav,\n  // even though the actual page content is offscreen\n  if (['sticky', 'fixed'].includes(getComputedStyle(element).position)) {\n    if (process.env.NODE_ENV === 'development') {\n      console.warn(\n        'Skipping auto-scroll behavior due to `position: sticky` or `position: fixed` on element:',\n        element\n      )\n    }\n    return true\n  }\n\n  // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n  // because `offsetParent` doesn't consider document/body\n  const rect = element.getBoundingClientRect()\n  return rectProperties.every((item) => rect[item] === 0)\n}\n\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */\nfunction topOfElementInViewport(element: HTMLElement, viewportHeight: number) {\n  const rect = element.getBoundingClientRect()\n  return rect.top >= 0 && rect.top <= viewportHeight\n}\n\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */\nfunction getHashFragmentDomNode(hashFragment: string) {\n  // If the hash fragment is `top` the page has to scroll to the top of the page.\n  if (hashFragment === 'top') {\n    return document.body\n  }\n\n  // If the hash fragment is an id, the page has to scroll to the element with that id.\n  return (\n    document.getElementById(hashFragment) ??\n    // If the hash fragment is a name, the page has to scroll to the first element with that name.\n    document.getElementsByName(hashFragment)[0]\n  )\n}\ninterface ScrollAndFocusHandlerProps {\n  focusAndScrollRef: FocusAndScrollRef\n  children: React.ReactNode\n  segmentPath: FlightSegmentPath\n}\nclass InnerScrollAndFocusHandler extends React.Component<ScrollAndFocusHandlerProps> {\n  handlePotentialScroll = () => {\n    // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n    const { focusAndScrollRef, segmentPath } = this.props\n\n    if (focusAndScrollRef.apply) {\n      // segmentPaths is an array of segment paths that should be scrolled to\n      // if the current segment path is not in the array, the scroll is not applied\n      // unless the array is empty, in which case the scroll is always applied\n      if (\n        focusAndScrollRef.segmentPaths.length !== 0 &&\n        !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath) =>\n          segmentPath.every((segment, index) =>\n            matchSegment(segment, scrollRefSegmentPath[index])\n          )\n        )\n      ) {\n        return\n      }\n\n      let domNode:\n        | ReturnType<typeof getHashFragmentDomNode>\n        | ReturnType<typeof findDOMNode> = null\n      const hashFragment = focusAndScrollRef.hashFragment\n\n      if (hashFragment) {\n        domNode = getHashFragmentDomNode(hashFragment)\n      }\n\n      // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n      // This already caused a bug where the first child was a <link/> in head.\n      if (!domNode) {\n        domNode = findDOMNode(this)\n      }\n\n      // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n      if (!(domNode instanceof Element)) {\n        return\n      }\n\n      // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n      // If the element is skipped, try to select the next sibling and try again.\n      while (!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)) {\n        if (process.env.NODE_ENV !== 'production') {\n          if (domNode.parentElement?.localName === 'head') {\n            // TODO: We enter this state when metadata was rendered as part of the page or via Next.js.\n            // This is always a bug in Next.js and caused by React hoisting metadata.\n            // We need to replace `findDOMNode` in favor of Fragment Refs (when available) so that we can skip over metadata.\n          }\n        }\n\n        // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n        if (domNode.nextElementSibling === null) {\n          return\n        }\n        domNode = domNode.nextElementSibling\n      }\n\n      // State is mutated to ensure that the focus and scroll is applied only once.\n      focusAndScrollRef.apply = false\n      focusAndScrollRef.hashFragment = null\n      focusAndScrollRef.segmentPaths = []\n\n      disableSmoothScrollDuringRouteTransition(\n        () => {\n          // In case of hash scroll, we only need to scroll the element into view\n          if (hashFragment) {\n            ;(domNode as HTMLElement).scrollIntoView()\n\n            return\n          }\n          // Store the current viewport height because reading `clientHeight` causes a reflow,\n          // and it won't change during this function.\n          const htmlElement = document.documentElement\n          const viewportHeight = htmlElement.clientHeight\n\n          // If the element's top edge is already in the viewport, exit early.\n          if (topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            return\n          }\n\n          // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n          // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n          // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n          // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n          htmlElement.scrollTop = 0\n\n          // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n          if (!topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            // Scroll into view doesn't scroll horizontally by default when not needed\n            ;(domNode as HTMLElement).scrollIntoView()\n          }\n        },\n        {\n          // We will force layout by querying domNode position\n          dontForceLayout: true,\n          onlyHashChange: focusAndScrollRef.onlyHashChange,\n        }\n      )\n\n      // Mutate after scrolling so that it can be read by `disableSmoothScrollDuringRouteTransition`\n      focusAndScrollRef.onlyHashChange = false\n\n      // Set focus on the element\n      domNode.focus()\n    }\n  }\n\n  componentDidMount() {\n    this.handlePotentialScroll()\n  }\n\n  componentDidUpdate() {\n    // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n    if (this.props.focusAndScrollRef.apply) {\n      this.handlePotentialScroll()\n    }\n  }\n\n  render() {\n    return this.props.children\n  }\n}\n\nfunction ScrollAndFocusHandler({\n  segmentPath,\n  children,\n}: {\n  segmentPath: FlightSegmentPath\n  children: React.ReactNode\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  return (\n    <InnerScrollAndFocusHandler\n      segmentPath={segmentPath}\n      focusAndScrollRef={context.focusAndScrollRef}\n    >\n      {children}\n    </InnerScrollAndFocusHandler>\n  )\n}\n\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */\nfunction InnerLayoutRouter({\n  tree,\n  segmentPath,\n  cacheNode,\n  url,\n}: {\n  tree: FlightRouterState\n  segmentPath: FlightSegmentPath\n  cacheNode: CacheNode\n  url: string\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  const { tree: fullTree } = context\n\n  // `rsc` represents the renderable node for this segment.\n\n  // If this segment has a `prefetchRsc`, it's the statically prefetched data.\n  // We should use that on initial render instead of `rsc`. Then we'll switch\n  // to `rsc` when the dynamic response streams in.\n  //\n  // If no prefetch data is available, then we go straight to rendering `rsc`.\n  const resolvedPrefetchRsc =\n    cacheNode.prefetchRsc !== null ? cacheNode.prefetchRsc : cacheNode.rsc\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  const rsc: any = useDeferredValue(cacheNode.rsc, resolvedPrefetchRsc)\n\n  // `rsc` is either a React node or a promise for a React node, except we\n  // special case `null` to represent that this segment's data is missing. If\n  // it's a promise, we need to unwrap it so we can determine whether or not the\n  // data is missing.\n  const resolvedRsc: React.ReactNode =\n    typeof rsc === 'object' && rsc !== null && typeof rsc.then === 'function'\n      ? use(rsc)\n      : rsc\n\n  if (!resolvedRsc) {\n    // The data for this segment is not available, and there's no pending\n    // navigation that will be able to fulfill it. We need to fetch more from\n    // the server and patch the cache.\n\n    // Check if there's already a pending request.\n    let lazyData = cacheNode.lazyData\n    if (lazyData === null) {\n      /**\n       * Router state with refetch marker added\n       */\n      // TODO-APP: remove ''\n      const refetchTree = walkAddRefetch(['', ...segmentPath], fullTree)\n      const includeNextUrl = hasInterceptionRouteInCurrentTree(fullTree)\n      const navigatedAt = Date.now()\n      cacheNode.lazyData = lazyData = fetchServerResponse(\n        new URL(url, location.origin),\n        {\n          flightRouterState: refetchTree,\n          nextUrl: includeNextUrl ? context.nextUrl : null,\n        }\n      ).then((serverResponse) => {\n        startTransition(() => {\n          dispatchAppRouterAction({\n            type: ACTION_SERVER_PATCH,\n            previousTree: fullTree,\n            serverResponse,\n            navigatedAt,\n          })\n        })\n\n        return serverResponse\n      })\n\n      // Suspend while waiting for lazyData to resolve\n      use(lazyData)\n    }\n    // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n    // A falsey `resolvedRsc` indicates missing data -- we should not commit that branch, and we need to wait for the data to arrive.\n    use(unresolvedThenable) as never\n  }\n\n  // If we get to this point, then we know we have something we can render.\n  const subtree = (\n    // The layout router context narrows down tree and childNodes at each level.\n    <LayoutRouterContext.Provider\n      value={{\n        parentTree: tree,\n        parentCacheNode: cacheNode,\n        parentSegmentPath: segmentPath,\n\n        // TODO-APP: overriding of url for parallel routes\n        url: url,\n      }}\n    >\n      {resolvedRsc}\n    </LayoutRouterContext.Provider>\n  )\n  // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n  return subtree\n}\n\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */\nfunction LoadingBoundary({\n  loading,\n  children,\n}: {\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n  children: React.ReactNode\n}): JSX.Element {\n  // If loading is a promise, unwrap it. This happens in cases where we haven't\n  // yet received the loading data from the server — which includes whether or\n  // not this layout has a loading component at all.\n  //\n  // It's OK to suspend here instead of inside the fallback because this\n  // promise will resolve simultaneously with the data for the segment itself.\n  // So it will never suspend for longer than it would have if we didn't use\n  // a Suspense fallback at all.\n  let loadingModuleData\n  if (\n    typeof loading === 'object' &&\n    loading !== null &&\n    typeof (loading as any).then === 'function'\n  ) {\n    const promiseForLoading = loading as Promise<LoadingModuleData>\n    loadingModuleData = use(promiseForLoading)\n  } else {\n    loadingModuleData = loading as LoadingModuleData\n  }\n\n  if (loadingModuleData) {\n    const loadingRsc = loadingModuleData[0]\n    const loadingStyles = loadingModuleData[1]\n    const loadingScripts = loadingModuleData[2]\n    return (\n      <Suspense\n        fallback={\n          <>\n            {loadingStyles}\n            {loadingScripts}\n            {loadingRsc}\n          </>\n        }\n      >\n        {children}\n      </Suspense>\n    )\n  }\n\n  return <>{children}</>\n}\n\nfunction RenderChildren({ children }: { children: React.ReactNode }) {\n  return <>{children}</>\n}\n\n/**\n * OuterLayoutRouter handles the current segment as well as <Offscreen> rendering of other segments.\n * It can be rendered next to each other with a different `parallelRouterKey`, allowing for Parallel routes.\n */\nexport default function OuterLayoutRouter({\n  parallelRouterKey,\n  error,\n  errorStyles,\n  errorScripts,\n  templateStyles,\n  templateScripts,\n  template,\n  notFound,\n  forbidden,\n  unauthorized,\n  gracefullyDegrade,\n  segmentViewBoundaries,\n}: {\n  parallelRouterKey: string\n  error: ErrorComponent | undefined\n  errorStyles: React.ReactNode | undefined\n  errorScripts: React.ReactNode | undefined\n  templateStyles: React.ReactNode | undefined\n  templateScripts: React.ReactNode | undefined\n  template: React.ReactNode\n  notFound: React.ReactNode | undefined\n  forbidden: React.ReactNode | undefined\n  unauthorized: React.ReactNode | undefined\n  gracefullyDegrade?: boolean\n  segmentViewBoundaries?: React.ReactNode\n}) {\n  const context = useContext(LayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant expected layout router to be mounted')\n  }\n\n  const { parentTree, parentCacheNode, parentSegmentPath, url } = context\n\n  // Get the CacheNode for this segment by reading it from the parent segment's\n  // child map.\n  const parentParallelRoutes = parentCacheNode.parallelRoutes\n  let segmentMap = parentParallelRoutes.get(parallelRouterKey)\n  // If the parallel router cache node does not exist yet, create it.\n  // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n  if (!segmentMap) {\n    segmentMap = new Map()\n    parentParallelRoutes.set(parallelRouterKey, segmentMap)\n  }\n  const parentTreeSegment = parentTree[0]\n  const segmentPath =\n    parentSegmentPath === null\n      ? // TODO: The root segment value is currently omitted from the segment\n        // path. This has led to a bunch of special cases scattered throughout\n        // the code. We should clean this up.\n        [parallelRouterKey]\n      : parentSegmentPath.concat([parentTreeSegment, parallelRouterKey])\n\n  // The \"state\" key of a segment is the one passed to React — it represents the\n  // identity of the UI tree. Whenever the state key changes, the tree is\n  // recreated and the state is reset. In the App Router model, search params do\n  // not cause state to be lost, so two segments with the same segment path but\n  // different search params should have the same state key.\n  //\n  // The \"cache\" key of a segment, however, *does* include the search params, if\n  // it's possible that the segment accessed the search params on the server.\n  // (This only applies to page segments; layout segments cannot access search\n  // params on the server.)\n  const activeTree = parentTree[1][parallelRouterKey]\n  const activeSegment = activeTree[0]\n  const activeStateKey = createRouterCacheKey(activeSegment, true) // no search params\n\n  // At each level of the route tree, not only do we render the currently\n  // active segment — we also render the last N segments that were active at\n  // this level inside a hidden <Activity> boundary, to preserve their state\n  // if or when the user navigates to them again.\n  //\n  // bfcacheEntry is a linked list of FlightRouterStates.\n  let bfcacheEntry: RouterBFCacheEntry | null = useRouterBFCache(\n    activeTree,\n    activeStateKey\n  )\n  let children: Array<React.ReactNode> = []\n  do {\n    const tree = bfcacheEntry.tree\n    const stateKey = bfcacheEntry.stateKey\n    const segment = tree[0]\n    const cacheKey = createRouterCacheKey(segment)\n\n    // Read segment path from the parallel router cache node.\n    let cacheNode = segmentMap.get(cacheKey)\n    if (cacheNode === undefined) {\n      // When data is not available during rendering client-side we need to fetch\n      // it from the server.\n      const newLazyCacheNode: LazyCacheNode = {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1,\n      }\n\n      // Flight data fetch kicked off during render and put into the cache.\n      cacheNode = newLazyCacheNode\n      segmentMap.set(cacheKey, newLazyCacheNode)\n    }\n\n    /*\n    - Error boundary\n      - Only renders error boundary if error component is provided.\n      - Rendered for each segment to ensure they have their own error state.\n      - When gracefully degrade for bots, skip rendering error boundary.\n    - Loading boundary\n      - Only renders suspense boundary if loading components is provided.\n      - Rendered for each segment to ensure they have their own loading state.\n      - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n  */\n\n    const ErrorBoundaryComponent = gracefullyDegrade\n      ? RenderChildren\n      : ErrorBoundary\n\n    let segmentBoundaryTriggerNode: React.ReactNode = null\n    let segmentViewStateNode: React.ReactNode = null\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      process.env.__NEXT_DEVTOOL_SEGMENT_EXPLORER\n    ) {\n      const { SegmentBoundaryTriggerNode, SegmentViewStateNode } =\n        require('../../next-devtools/userspace/app/segment-explorer-node') as typeof import('../../next-devtools/userspace/app/segment-explorer-node')\n\n      const pagePrefix = normalizeAppPath(url)\n      segmentViewStateNode = (\n        <SegmentViewStateNode key={pagePrefix} page={pagePrefix} />\n      )\n\n      segmentBoundaryTriggerNode = (\n        <>\n          <SegmentBoundaryTriggerNode />\n        </>\n      )\n    }\n\n    // TODO: The loading module data for a segment is stored on the parent, then\n    // applied to each of that parent segment's parallel route slots. In the\n    // simple case where there's only one parallel route (the `children` slot),\n    // this is no different from if the loading module data where stored on the\n    // child directly. But I'm not sure this actually makes sense when there are\n    // multiple parallel routes. It's not a huge issue because you always have\n    // the option to define a narrower loading boundary for a particular slot. But\n    // this sort of smells like an implementation accident to me.\n    const loadingModuleData = parentCacheNode.loading\n    let child = (\n      <TemplateContext.Provider\n        key={stateKey}\n        value={\n          <ScrollAndFocusHandler segmentPath={segmentPath}>\n            <ErrorBoundaryComponent\n              errorComponent={error}\n              errorStyles={errorStyles}\n              errorScripts={errorScripts}\n            >\n              <LoadingBoundary loading={loadingModuleData}>\n                <HTTPAccessFallbackBoundary\n                  notFound={notFound}\n                  forbidden={forbidden}\n                  unauthorized={unauthorized}\n                >\n                  <RedirectBoundary>\n                    <InnerLayoutRouter\n                      url={url}\n                      tree={tree}\n                      cacheNode={cacheNode}\n                      segmentPath={segmentPath}\n                    />\n                    {segmentBoundaryTriggerNode}\n                  </RedirectBoundary>\n                </HTTPAccessFallbackBoundary>\n              </LoadingBoundary>\n            </ErrorBoundaryComponent>\n            {segmentViewStateNode}\n          </ScrollAndFocusHandler>\n        }\n      >\n        {templateStyles}\n        {templateScripts}\n        {template}\n      </TemplateContext.Provider>\n    )\n\n    if (process.env.NODE_ENV !== 'production') {\n      const { SegmentStateProvider } =\n        require('../../next-devtools/userspace/app/segment-explorer-node') as typeof import('../../next-devtools/userspace/app/segment-explorer-node')\n\n      child = (\n        <SegmentStateProvider key={stateKey}>\n          {child}\n          {segmentViewBoundaries}\n        </SegmentStateProvider>\n      )\n    }\n\n    if (process.env.__NEXT_ROUTER_BF_CACHE) {\n      child = (\n        <Activity\n          key={stateKey}\n          mode={stateKey === activeStateKey ? 'visible' : 'hidden'}\n        >\n          {child}\n        </Activity>\n      )\n    }\n\n    children.push(child)\n\n    bfcacheEntry = bfcacheEntry.next\n  } while (bfcacheEntry !== null)\n\n  return children\n}\n"], "names": ["OuterLayoutRouter", "Activity", "process", "env", "__NEXT_ROUTER_BF_CACHE", "require", "unstable_Activity", "walkAddRefetch", "segmentPathToWalk", "treeToRecreate", "segment", "parallelRouteKey", "isLast", "length", "matchSegment", "hasOwnProperty", "subTree", "undefined", "slice", "__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "ReactDOM", "findDOMNode", "instance", "window", "internal_reactDOMfindDOMNode", "rectProperties", "shouldSkipElement", "element", "includes", "getComputedStyle", "position", "NODE_ENV", "console", "warn", "rect", "getBoundingClientRect", "every", "item", "topOfElementInViewport", "viewportHeight", "top", "getHashFragmentDomNode", "hashFragment", "document", "body", "getElementById", "getElementsByName", "InnerScrollAndFocusHandler", "React", "Component", "componentDidMount", "handlePotentialScroll", "componentDidUpdate", "props", "focusAndScrollRef", "apply", "render", "children", "segmentPath", "segmentPaths", "some", "scrollRefSegmentPath", "index", "domNode", "Element", "HTMLElement", "parentElement", "localName", "nextElement<PERSON><PERSON>ling", "disableSmoothScrollDuringRouteTransition", "scrollIntoView", "htmlElement", "documentElement", "clientHeight", "scrollTop", "dontForceLayout", "onlyHashChange", "focus", "ScrollAndFocusHandler", "context", "useContext", "GlobalLayoutRouterContext", "Error", "InnerLayoutRouter", "tree", "cacheNode", "url", "fullTree", "resolvedPrefetchRsc", "prefetchRsc", "rsc", "useDeferredValue", "resolvedRsc", "then", "use", "lazyData", "refetchTree", "includeNextUrl", "hasInterceptionRouteInCurrentTree", "navigatedAt", "Date", "now", "fetchServerResponse", "URL", "location", "origin", "flightRouterState", "nextUrl", "serverResponse", "startTransition", "dispatchAppRouterAction", "type", "ACTION_SERVER_PATCH", "previousTree", "unresolvedThenable", "subtree", "LayoutRouterContext", "Provider", "value", "parentTree", "parentCacheNode", "parentSegmentPath", "LoadingBoundary", "loading", "loadingModuleData", "promiseForLoading", "loadingRsc", "loadingStyles", "loadingScripts", "Suspense", "fallback", "RenderChildren", "parallel<PERSON><PERSON>er<PERSON>ey", "error", "errorStyles", "errorScripts", "templateStyles", "templateScripts", "template", "notFound", "forbidden", "unauthorized", "gracefully<PERSON><PERSON><PERSON>", "segmentViewBoundaries", "parentParallelRoutes", "parallelRoutes", "segmentMap", "get", "Map", "set", "parentTreeSegment", "concat", "activeTree", "activeSegment", "activeStateKey", "createRouterCache<PERSON>ey", "bfcacheEntry", "useRouterBFCache", "stateKey", "cache<PERSON>ey", "newLazyCacheNode", "head", "prefetchHead", "ErrorBoundaryComponent", "Error<PERSON>ou<PERSON><PERSON>", "segmentBoundaryTriggerNode", "segmentViewStateNode", "__NEXT_DEVTOOL_SEGMENT_EXPLORER", "SegmentBoundaryTriggerNode", "SegmentViewStateNode", "pagePrefix", "normalizeAppPath", "page", "child", "TemplateContext", "errorComponent", "HTTPAccessFallbackBoundary", "RedirectBoundary", "SegmentStateProvider", "mode", "push", "next"], "mappings": "AA4CiBE,QAAQC,GAAG,CAACC,sBAAsB;AA5CnD;;;;;+BA8eA;;;CAGC,GACD,WAAA;;;eAAwBJ;;;;;;oCAnejB;iEASA;mEACc;+CAKd;qCAC6B;oCACD;+BACL;+BACD;qCAC4B;kCACxB;gCACU;sCACN;mDACa;gCACV;yBACkB;0BACzB;AAEjC,MAAMC,iDACDI,QAAQ,SAAoCC,SAC7C,QAD8D;AAGlE;;;CAGC,GACD,SAASC,eACPC,iBAAgD,EAChDC,cAAiC;IAEjC,IAAID,mBAAmB;QACrB,MAAM,CAACE,SAASC,iBAAiB,GAAGH;QACpC,MAAMI,SAASJ,kBAAkBK,MAAM,KAAK;QAE5C,IAAIC,CAAAA,GAAAA,eAAAA,YAAY,EAACL,cAAc,CAAC,EAAE,EAAEC,UAAU;YAC5C,IAAID,cAAc,CAAC,EAAE,CAACM,cAAc,CAACJ,mBAAmB;gBACtD,IAAIC,QAAQ;oBACV,MAAMI,UAAUT,eACdU,WACAR,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAErC,OAAO;wBACLF,cAAc,CAAC,EAAE;wBACjB;4BACE,GAAGA,cAAc,CAAC,EAAE;4BACpB,CAACE,iBAAiB,EAAE;gCAClBK,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACV;6BACD;wBACH;qBACD;gBACH;gBAEA,OAAO;oBACLP,cAAc,CAAC,EAAE;oBACjB;wBACE,GAAGA,cAAc,CAAC,EAAE;wBACpB,CAACE,iBAAiB,EAAEJ,eAClBC,kBAAkBU,KAAK,CAAC,IACxBT,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAEvC;iBACD;YACH;QACF;IACF;IAEA,OAAOF;AACT;AAEA,MAAMU,+DACJC,UAAAA,OAAQ,CACRD,4DAA4D;AAE9D,4FAA4F;AAC5F;;CAEC,GACD,SAASE,YACPC,QAAgD;IAEhD,+BAA+B;IAC/B,IAAI,OAAOC,WAAW,aAAa,OAAO;IAE1C,uGAAuG;IACvG,kCAAkC;IAClC,MAAMC,+BACJL,6DAA6DE,WAAW;IAC1E,OAAOG,6BAA6BF;AACtC;AAEA,MAAMG,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD;;CAEC,GACD,SAASC,kBAAkBC,OAAoB;IAC7C,kGAAkG;IAClG,0FAA0F;IAC1F,mDAAmD;IACnD,IAAI;QAAC;QAAU;KAAQ,CAACC,QAAQ,CAACC,iBAAiBF,SAASG,QAAQ,GAAG;QACpE,IAAI5B,QAAQC,GAAG,CAAC4B,QAAQ,KAAK,WAAe;YAC1CC,QAAQC,IAAI,CACV,4FACAN;QAEJ;QACA,OAAO;IACT;IAEA,2FAA2F;IAC3F,wDAAwD;IACxD,MAAMO,OAAOP,QAAQQ,qBAAqB;IAC1C,OAAOV,eAAeW,KAAK,CAAC,CAACC,OAASH,IAAI,CAACG,KAAK,KAAK;AACvD;AAEA;;CAEC,GACD,SAASC,uBAAuBX,OAAoB,EAAEY,cAAsB;IAC1E,MAAML,OAAOP,QAAQQ,qBAAqB;IAC1C,OAAOD,KAAKM,GAAG,IAAI,KAAKN,KAAKM,GAAG,IAAID;AACtC;AAEA;;;;;CAKC,GACD,SAASE,uBAAuBC,YAAoB;IAClD,+EAA+E;IAC/E,IAAIA,iBAAiB,OAAO;QAC1B,OAAOC,SAASC,IAAI;IACtB;QAIED;IAFF,qFAAqF;IACrF,OACEA,CAAAA,2BAAAA,SAASE,cAAc,CAACH,aAAAA,KAAAA,OAAxBC,2BACA,AACAA,SAASG,iBAAiB,CAACJ,aAAa,CAAC,EAAE,mDADmD;AAGlG;AAMA,MAAMK,mCAAmCC,OAAAA,OAAK,CAACC,SAAS;IA4GtDC,oBAAoB;QAClB,IAAI,CAACC,qBAAqB;IAC5B;IAEAC,qBAAqB;QACnB,sJAAsJ;QACtJ,IAAI,IAAI,CAACC,KAAK,CAACC,iBAAiB,CAACC,KAAK,EAAE;YACtC,IAAI,CAACJ,qBAAqB;QAC5B;IACF;IAEAK,SAAS;QACP,OAAO,IAAI,CAACH,KAAK,CAACI,QAAQ;IAC5B;;QAzHF,KAAA,IAAA,OAAA,IAAA,CACEN,qBAAAA,GAAwB;YACtB,qGAAqG;YACrG,MAAM,EAAEG,iBAAiB,EAAEI,WAAW,EAAE,GAAG,IAAI,CAACL,KAAK;YAErD,IAAIC,kBAAkBC,KAAK,EAAE;gBAC3B,uEAAuE;gBACvE,6EAA6E;gBAC7E,wEAAwE;gBACxE,IACED,kBAAkBK,YAAY,CAAC9C,MAAM,KAAK,KAC1C,CAACyC,kBAAkBK,YAAY,CAACC,IAAI,CAAC,CAACC,uBACpCH,YAAYtB,KAAK,CAAC,CAAC1B,SAASoD,QAC1BhD,CAAAA,GAAAA,eAAAA,YAAY,EAACJ,SAASmD,oBAAoB,CAACC,MAAM,KAGrD;oBACA;gBACF;gBAEA,IAAIC,UAEiC;gBACrC,MAAMrB,eAAeY,kBAAkBZ,YAAY;gBAEnD,IAAIA,cAAc;oBAChBqB,UAAUtB,uBAAuBC;gBACnC;gBAEA,kGAAkG;gBAClG,yEAAyE;gBACzE,IAAI,CAACqB,SAAS;oBACZA,UAAU1C,YAAY,IAAI;gBAC5B;gBAEA,uGAAuG;gBACvG,IAAI,CAAE0C,CAAAA,mBAAmBC,OAAM,GAAI;oBACjC;gBACF;gBAEA,4FAA4F;gBAC5F,2EAA2E;gBAC3E,MAAO,CAAED,CAAAA,mBAAmBE,WAAU,KAAMvC,kBAAkBqC,SAAU;oBACtE,IAAI7D,QAAQC,GAAG,CAAC4B,QAAQ,KAAK,WAAc;4BACrCgC;wBAAJ,IAAIA,CAAAA,CAAAA,yBAAAA,QAAQG,aAAa,KAAA,OAAA,KAAA,IAArBH,uBAAuBI,SAAS,MAAK,QAAQ;wBAC/C,2FAA2F;wBAC3F,yEAAyE;wBACzE,iHAAiH;wBACnH;oBACF;oBAEA,uGAAuG;oBACvG,IAAIJ,QAAQK,kBAAkB,KAAK,MAAM;wBACvC;oBACF;oBACAL,UAAUA,QAAQK,kBAAkB;gBACtC;gBAEA,6EAA6E;gBAC7Ed,kBAAkBC,KAAK,GAAG;gBAC1BD,kBAAkBZ,YAAY,GAAG;gBACjCY,kBAAkBK,YAAY,GAAG,EAAE;gBAEnCU,CAAAA,GAAAA,qBAAAA,wCAAwC,EACtC;oBACE,uEAAuE;oBACvE,IAAI3B,cAAc;;wBACdqB,QAAwBO,cAAc;wBAExC;oBACF;oBACA,oFAAoF;oBACpF,4CAA4C;oBAC5C,MAAMC,cAAc5B,SAAS6B,eAAe;oBAC5C,MAAMjC,iBAAiBgC,YAAYE,YAAY;oBAE/C,oEAAoE;oBACpE,IAAInC,uBAAuByB,SAAwBxB,iBAAiB;wBAClE;oBACF;oBAEA,2FAA2F;oBAC3F,kHAAkH;oBAClH,qHAAqH;oBACrH,6HAA6H;oBAC7HgC,YAAYG,SAAS,GAAG;oBAExB,mFAAmF;oBACnF,IAAI,CAACpC,uBAAuByB,SAAwBxB,iBAAiB;wBACnE,0EAA0E;;wBACxEwB,QAAwBO,cAAc;oBAC1C;gBACF,GACA;oBACE,oDAAoD;oBACpDK,iBAAiB;oBACjBC,gBAAgBtB,kBAAkBsB,cAAc;gBAClD;gBAGF,8FAA8F;gBAC9FtB,kBAAkBsB,cAAc,GAAG;gBAEnC,2BAA2B;gBAC3Bb,QAAQc,KAAK;YACf;QACF;;AAgBF;AAEA,SAASC,sBAAsB,KAM9B;IAN8B,IAAA,EAC7BpB,WAAW,EACXD,QAAQ,EAIT,GAN8B;IAO7B,MAAMsB,UAAUC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,+BAAAA,yBAAyB;IACpD,IAAI,CAACF,SAAS;QACZ,MAAM,OAAA,cAAuD,CAAvD,IAAIG,MAAM,+CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAsD;IAC9D;IAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACnC,4BAAAA;QACCW,aAAaA;QACbJ,mBAAmByB,QAAQzB,iBAAiB;kBAE3CG;;AAGP;AAEA;;CAEC,GACD,SAAS0B,kBAAkB,KAU1B;IAV0B,IAAA,EACzBC,IAAI,EACJ1B,WAAW,EACX2B,SAAS,EACTC,GAAG,EAMJ,GAV0B;IAWzB,MAAMP,UAAUC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,+BAAAA,yBAAyB;IACpD,IAAI,CAACF,SAAS;QACZ,MAAM,OAAA,cAAuD,CAAvD,IAAIG,MAAM,+CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAsD;IAC9D;IAEA,MAAM,EAAEE,MAAMG,QAAQ,EAAE,GAAGR;IAE3B,yDAAyD;IAEzD,4EAA4E;IAC5E,2EAA2E;IAC3E,iDAAiD;IACjD,EAAE;IACF,4EAA4E;IAC5E,MAAMS,sBACJH,UAAUI,WAAW,KAAK,OAAOJ,UAAUI,WAAW,GAAGJ,UAAUK,GAAG;IAExE,2EAA2E;IAC3E,2EAA2E;IAC3E,sCAAsC;IACtC,MAAMA,MAAWC,CAAAA,GAAAA,OAAAA,gBAAgB,EAACN,UAAUK,GAAG,EAAEF;IAEjD,wEAAwE;IACxE,2EAA2E;IAC3E,8EAA8E;IAC9E,mBAAmB;IACnB,MAAMI,cACJ,OAAOF,QAAQ,YAAYA,QAAQ,QAAQ,OAAOA,IAAIG,IAAI,KAAK,aAC3DC,CAAAA,GAAAA,OAAAA,GAAG,EAACJ,OACJA;IAEN,IAAI,CAACE,aAAa;QAChB,qEAAqE;QACrE,yEAAyE;QACzE,kCAAkC;QAElC,8CAA8C;QAC9C,IAAIG,WAAWV,UAAUU,QAAQ;QACjC,IAAIA,aAAa,MAAM;YACrB;;OAEC,GACD,sBAAsB;YACtB,MAAMC,cAAczF,eAAe;gBAAC;mBAAOmD;aAAY,EAAE6B;YACzD,MAAMU,iBAAiBC,CAAAA,GAAAA,mCAAAA,iCAAiC,EAACX;YACzD,MAAMY,cAAcC,KAAKC,GAAG;YAC5BhB,UAAUU,QAAQ,GAAGA,WAAWO,CAAAA,GAAAA,qBAAAA,mBAAmB,EACjD,IAAIC,IAAIjB,KAAKkB,SAASC,MAAM,GAC5B;gBACEC,mBAAmBV;gBACnBW,SAASV,iBAAiBlB,QAAQ4B,OAAO,GAAG;YAC9C,GACAd,IAAI,CAAC,CAACe;gBACNC,CAAAA,GAAAA,OAAAA,eAAe,EAAC;oBACdC,CAAAA,GAAAA,gBAAAA,uBAAuB,EAAC;wBACtBC,MAAMC,oBAAAA,mBAAmB;wBACzBC,cAAc1B;wBACdqB;wBACAT;oBACF;gBACF;gBAEA,OAAOS;YACT;YAEA,gDAAgD;YAChDd,CAAAA,GAAAA,OAAAA,GAAG,EAACC;QACN;QACA,yGAAyG;QACzG,iIAAiI;QACjID,CAAAA,GAAAA,OAAAA,GAAG,EAACoB,oBAAAA,kBAAkB;IACxB;IAEA,yEAAyE;IACzE,MAAMC,UACJ,cACA,CAAA,GAAA,YAAA,GAAA,EAACC,+BAAAA,UAD2E,SACxD,CAACC,QAAQ,EAAA;QAC3BC,OAAO;YACLC,YAAYnC;YACZoC,iBAAiBnC;YACjBoC,mBAAmB/D;YAEnB,kDAAkD;YAClD4B,KAAKA;QACP;kBAECM;;IAGL,iFAAiF;IACjF,OAAOuB;AACT;AAEA;;;CAGC,GACD,SAASO,gBAAgB,KAMxB;IANwB,IAAA,EACvBC,OAAO,EACPlE,QAAQ,EAIT,GANwB;IAOvB,6EAA6E;IAC7E,4EAA4E;IAC5E,kDAAkD;IAClD,EAAE;IACF,sEAAsE;IACtE,4EAA4E;IAC5E,0EAA0E;IAC1E,8BAA8B;IAC9B,IAAImE;IACJ,IACE,OAAOD,YAAY,YACnBA,YAAY,QACZ,OAAQA,QAAgB9B,IAAI,KAAK,YACjC;QACA,MAAMgC,oBAAoBF;QAC1BC,oBAAoB9B,CAAAA,GAAAA,OAAAA,GAAG,EAAC+B;IAC1B,OAAO;QACLD,oBAAoBD;IACtB;IAEA,IAAIC,mBAAmB;QACrB,MAAME,aAAaF,iBAAiB,CAAC,EAAE;QACvC,MAAMG,gBAAgBH,iBAAiB,CAAC,EAAE;QAC1C,MAAMI,iBAAiBJ,iBAAiB,CAAC,EAAE;QAC3C,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACK,OAAAA,QAAQ,EAAA;YACPC,UAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAAA,YAAA,QAAA,EAAA;;oBACGH;oBACAC;oBACAF;;;sBAIJrE;;IAGP;IAEA,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAAA,YAAA,QAAA,EAAA;kBAAGA;;AACZ;AAEA,SAAS0E,eAAe,KAA2C;IAA3C,IAAA,EAAE1E,QAAQ,EAAiC,GAA3C;IACtB,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAAA,YAAA,QAAA,EAAA;kBAAGA;;AACZ;AAMe,SAASzD,kBAAkB,KA0BzC;IA1ByC,IAAA,EACxCoI,iBAAiB,EACjBC,KAAK,EACLC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,iBAAiB,EACjBC,qBAAqB,EActB,GA1ByC;IA2BxC,MAAMhE,UAAUC,CAAAA,GAAAA,OAAAA,UAAU,EAACoC,+BAAAA,mBAAmB;IAC9C,IAAI,CAACrC,SAAS;QACZ,MAAM,OAAA,cAA2D,CAA3D,IAAIG,MAAM,mDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA0D;IAClE;IAEA,MAAM,EAAEqC,UAAU,EAAEC,eAAe,EAAEC,iBAAiB,EAAEnC,GAAG,EAAE,GAAGP;IAEhE,6EAA6E;IAC7E,aAAa;IACb,MAAMiE,uBAAuBxB,gBAAgByB,cAAc;IAC3D,IAAIC,aAAaF,qBAAqBG,GAAG,CAACf;IAC1C,mEAAmE;IACnE,yJAAyJ;IACzJ,IAAI,CAACc,YAAY;QACfA,aAAa,IAAIE;QACjBJ,qBAAqBK,GAAG,CAACjB,mBAAmBc;IAC9C;IACA,MAAMI,oBAAoB/B,UAAU,CAAC,EAAE;IACvC,MAAM7D,cACJ+D,sBAAsB,OAElB,AACA,qCAAqC,iCADiC;IAEtE;QAACW;KAAkB,GACnBX,kBAAkB8B,MAAM,CAAC;QAACD;QAAmBlB;KAAkB;IAErE,8EAA8E;IAC9E,uEAAuE;IACvE,8EAA8E;IAC9E,6EAA6E;IAC7E,0DAA0D;IAC1D,EAAE;IACF,8EAA8E;IAC9E,2EAA2E;IAC3E,4EAA4E;IAC5E,yBAAyB;IACzB,MAAMoB,aAAajC,UAAU,CAAC,EAAE,CAACa,kBAAkB;IACnD,MAAMqB,gBAAgBD,UAAU,CAAC,EAAE;IACnC,MAAME,iBAAiBC,CAAAA,GAAAA,sBAAAA,oBAAoB,EAACF,eAAe,MAAM,mBAAmB;;IAEpF,uEAAuE;IACvE,0EAA0E;IAC1E,0EAA0E;IAC1E,+CAA+C;IAC/C,EAAE;IACF,uDAAuD;IACvD,IAAIG,eAA0CC,CAAAA,GAAAA,SAAAA,gBAAgB,EAC5DL,YACAE;IAEF,IAAIjG,WAAmC,EAAE;IACzC,GAAG;QACD,MAAM2B,OAAOwE,aAAaxE,IAAI;QAC9B,MAAM0E,WAAWF,aAAaE,QAAQ;QACtC,MAAMpJ,UAAU0E,IAAI,CAAC,EAAE;QACvB,MAAM2E,WAAWJ,CAAAA,GAAAA,sBAAAA,oBAAoB,EAACjJ;QAEtC,yDAAyD;QACzD,IAAI2E,YAAY6D,WAAWC,GAAG,CAACY;QAC/B,IAAI1E,cAAcpE,WAAW;YAC3B,2EAA2E;YAC3E,sBAAsB;YACtB,MAAM+I,mBAAkC;gBACtCjE,UAAU;gBACVL,KAAK;gBACLD,aAAa;gBACbwE,MAAM;gBACNC,cAAc;gBACdjB,gBAAgB,IAAIG;gBACpBzB,SAAS;gBACTxB,aAAa,CAAC;YAChB;YAEA,qEAAqE;YACrEd,YAAY2E;YACZd,WAAWG,GAAG,CAACU,UAAUC;QAC3B;QAEA;;;;;;;;;EASF,GAEE,MAAMG,yBAAyBrB,oBAC3BX,iBACAiC,eAAAA,aAAa;QAEjB,IAAIC,6BAA8C;QAClD,IAAIC,uBAAwC;QAC5C,IACEpK,QAAQC,GAAG,CAAC4B,QAAQ,KAAK,gBACzB7B,QAAQC,GAAG,CAACoK,+BAA+B,EAC3C;;QAgBF,4EAA4E;QAC5E,wEAAwE;QACxE,2EAA2E;QAC3E,2EAA2E;QAC3E,4EAA4E;QAC5E,0EAA0E;QAC1E,8EAA8E;QAC9E,6DAA6D;QAC7D,MAAM3C,oBAAoBJ,gBAAgBG,OAAO;QACjD,IAAIkD,QAAAA,WAAAA,GACF,CAAA,GAAA,YAAA,IAAA,EAACC,+BAAAA,eAAe,CAACzD,QAAQ,EAAA;YAEvBC,OAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAACxC,uBAAAA;gBAAsBpB,aAAaA;;kCAClC,CAAA,GAAA,YAAA,GAAA,EAACyG,wBAAAA;wBACCY,gBAAgB1C;wBAChBC,aAAaA;wBACbC,cAAcA;kCAEd,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACb,iBAAAA;4BAAgBC,SAASC;sCACxB,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACoD,gBAAAA,0BAA0B,EAAA;gCACzBrC,UAAUA;gCACVC,WAAWA;gCACXC,cAAcA;0CAEd,WAAA,GAAA,CAAA,GAAA,YAAA,IAAA,EAACoC,kBAAAA,gBAAgB,EAAA;;sDACf,CAAA,GAAA,YAAA,GAAA,EAAC9F,mBAAAA;4CACCG,KAAKA;4CACLF,MAAMA;4CACNC,WAAWA;4CACX3B,aAAaA;;wCAEd2G;;;;;;oBAKRC;;;;gBAIJ9B;gBACAC;gBACAC;;WAhCIoB;QAoCT,IAAI5J,QAAQC,GAAG,CAAC4B,QAAQ,KAAK,WAAc;YACzC,MAAM,EAAEmJ,oBAAoB,EAAE,GAC5B7K,QAAQ;YAEVwK,QAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAACK,sBAAAA;;oBACEL;oBACA9B;;eAFwBe;QAK/B;QAEA,IAAI5J,QAAQC,GAAG,CAACC,sBAAsB,EAAE;;QAWxCqD,SAAS2H,IAAI,CAACP;QAEdjB,eAAeA,aAAayB,IAAI;IAClC,QAASzB,iBAAiB,KAAK;IAE/B,OAAOnG;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3710, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/client/components/render-from-template-context.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport { TemplateContext } from '../../shared/lib/app-router-context.shared-runtime'\n\nexport default function RenderFromTemplateContext(): JSX.Element {\n  const children = useContext(TemplateContext)\n  return <>{children}</>\n}\n"], "names": ["RenderFromTemplateContext", "children", "useContext", "TemplateContext"], "mappings": ";;;+BAKA,WAAA;;;eAAwBA;;;;;iEAHoB;+CACZ;AAEjB,SAASA;IACtB,MAAMC,WAAWC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,+BAAAA,eAAe;IAC3C,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAAA,YAAA,QAAA,EAAA;kBAAGF;;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3742, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/shared/lib/invariant-error.ts"], "sourcesContent": ["export class InvariantError extends Error {\n  constructor(message: string, options?: ErrorOptions) {\n    super(\n      `Invariant: ${message.endsWith('.') ? message : message + '.'} This is a bug in Next.js.`,\n      options\n    )\n    this.name = 'InvariantError'\n  }\n}\n"], "names": ["InvariantError", "Error", "constructor", "message", "options", "endsWith", "name"], "mappings": ";;;+BAAaA,kBAAAA;;;eAAAA;;;AAAN,MAAMA,uBAAuBC;IAClCC,YAAYC,OAAe,EAAEC,OAAsB,CAAE;QACnD,KAAK,CACF,gBAAaD,CAAAA,QAAQE,QAAQ,CAAC,OAAOF,UAAUA,UAAU,GAAE,IAAE,8BAC9DC;QAEF,IAAI,CAACE,IAAI,GAAG;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3763, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/server/web/spec-extension/adapters/reflect.ts"], "sourcesContent": ["export class ReflectAdapter {\n  static get<T extends object>(\n    target: T,\n    prop: string | symbol,\n    receiver: unknown\n  ): any {\n    const value = Reflect.get(target, prop, receiver)\n    if (typeof value === 'function') {\n      return value.bind(target)\n    }\n\n    return value\n  }\n\n  static set<T extends object>(\n    target: T,\n    prop: string | symbol,\n    value: any,\n    receiver: any\n  ): boolean {\n    return Reflect.set(target, prop, value, receiver)\n  }\n\n  static has<T extends object>(target: T, prop: string | symbol): boolean {\n    return Reflect.has(target, prop)\n  }\n\n  static deleteProperty<T extends object>(\n    target: T,\n    prop: string | symbol\n  ): boolean {\n    return Reflect.deleteProperty(target, prop)\n  }\n}\n"], "names": ["ReflectAdapter", "get", "target", "prop", "receiver", "value", "Reflect", "bind", "set", "has", "deleteProperty"], "mappings": ";;;+BAAaA,kBAAAA;;;eAAAA;;;AAAN,MAAMA;IACX,OAAOC,IACLC,MAAS,EACTC,IAAqB,EACrBC,QAAiB,EACZ;QACL,MAAMC,QAAQC,QAAQL,GAAG,CAACC,QAAQC,MAAMC;QACxC,IAAI,OAAOC,UAAU,YAAY;YAC/B,OAAOA,MAAME,IAAI,CAACL;QACpB;QAEA,OAAOG;IACT;IAEA,OAAOG,IACLN,MAAS,EACTC,IAAqB,EACrBE,KAAU,EACVD,QAAa,EACJ;QACT,OAAOE,QAAQE,GAAG,CAACN,QAAQC,MAAME,OAAOD;IAC1C;IAEA,OAAOK,IAAsBP,MAAS,EAAEC,IAAqB,EAAW;QACtE,OAAOG,QAAQG,GAAG,CAACP,QAAQC;IAC7B;IAEA,OAAOO,eACLR,MAAS,EACTC,IAAqB,EACZ;QACT,OAAOG,QAAQI,cAAc,CAACR,QAAQC;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3796, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/shared/lib/utils/reflect-utils.ts"], "sourcesContent": ["// This regex will have fast negatives meaning valid identifiers may not pass\n// this test. However this is only used during static generation to provide hints\n// about why a page bailed out of some or all prerendering and we can use bracket notation\n// for example while `ಠ_ಠ` is a valid identifier it's ok to print `searchParams['ಠ_ಠ']`\n// even if this would have been fine too `searchParams.ಠ_ಠ`\nconst isDefinitelyAValidIdentifier = /^[A-Za-z_$][A-Za-z0-9_$]*$/\n\nexport function describeStringPropertyAccess(target: string, prop: string) {\n  if (isDefinitelyAValidIdentifier.test(prop)) {\n    return `\\`${target}.${prop}\\``\n  }\n  return `\\`${target}[${JSON.stringify(prop)}]\\``\n}\n\nexport function describeHasCheckingStringProperty(\n  target: string,\n  prop: string\n) {\n  const stringifiedProp = JSON.stringify(prop)\n  return `\\`Reflect.has(${target}, ${stringifiedProp})\\`, \\`${stringifiedProp} in ${target}\\`, or similar`\n}\n\nexport const wellKnownProperties = new Set([\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toString',\n  'valueOf',\n  'toLocaleString',\n\n  // Promise prototype\n  // fallthrough\n  'then',\n  'catch',\n  'finally',\n\n  // React Promise extension\n  // fallthrough\n  'status',\n\n  // React introspection\n  'displayName',\n  '_debugInfo',\n\n  // Common tested properties\n  // fallthrough\n  'toJSON',\n  '$$typeof',\n  '__esModule',\n])\n"], "names": ["describeHasCheckingStringProperty", "describeStringPropertyAccess", "wellKnownProperties", "isDefinitelyAValidIdentifier", "target", "prop", "test", "JSON", "stringify", "stringifiedProp", "Set"], "mappings": "AAAA,6EAA6E;AAC7E,iFAAiF;AACjF,0FAA0F;AAC1F,uFAAuF;AACvF,2DAA2D;;;;;;;;;;;;;;;;IAU3CA,iCAAiC,EAAA;eAAjCA;;IAPAC,4BAA4B,EAAA;eAA5BA;;IAeHC,mBAAmB,EAAA;eAAnBA;;;AAjBb,MAAMC,+BAA+B;AAE9B,SAASF,6BAA6BG,MAAc,EAAEC,IAAY;IACvE,IAAIF,6BAA6BG,IAAI,CAACD,OAAO;QAC3C,OAAQ,MAAID,SAAO,MAAGC,OAAK;IAC7B;IACA,OAAQ,MAAID,SAAO,MAAGG,KAAKC,SAAS,CAACH,QAAM;AAC7C;AAEO,SAASL,kCACdI,MAAc,EACdC,IAAY;IAEZ,MAAMI,kBAAkBF,KAAKC,SAAS,CAACH;IACvC,OAAQ,kBAAgBD,SAAO,OAAIK,kBAAgB,UAASA,kBAAgB,SAAML,SAAO;AAC3F;AAEO,MAAMF,sBAAsB,IAAIQ,IAAI;IACzC;IACA;IACA;IACA;IACA;IACA;IAEA,oBAAoB;IACpB,cAAc;IACd;IACA;IACA;IAEA,0BAA0B;IAC1B,cAAc;IACd;IAEA,sBAAsB;IACtB;IACA;IAEA,2BAA2B;IAC3B,cAAc;IACd;IACA;IACA;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3867, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/client/request/search-params.browser.dev.ts"], "sourcesContent": ["import type { SearchParams } from '../../server/request/search-params'\n\nimport { ReflectAdapter } from '../../server/web/spec-extension/adapters/reflect'\nimport {\n  describeStringPropertyAccess,\n  describeHasCheckingStringProperty,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nfunction makeUntrackedExoticSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingSearchParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          warnForSyncAccess(expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          warnForSyncAccess(expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      warnForSyncSpread()\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\n// Similar to `makeUntrackedExoticSearchParamsWithDevWarnings`, but just logging\n// the sync access without actually defining the search params on the promise.\nfunction makeUntrackedSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          warnForSyncAccess(expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          warnForSyncAccess(expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      warnForSyncSpread()\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction warnForSyncAccess(expression: string) {\n  console.error(\n    `A searchParam property was accessed directly with ${expression}. ` +\n      `\\`searchParams\\` should be unwrapped with \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction warnForSyncSpread() {\n  console.error(\n    `The keys of \\`searchParams\\` were accessed directly. ` +\n      `\\`searchParams\\` should be unwrapped with \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nexport function createRenderSearchParamsFromClient(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  if (process.env.__NEXT_DYNAMIC_IO) {\n    return makeUntrackedSearchParamsWithDevWarnings(underlyingSearchParams)\n  }\n\n  return makeUntrackedExoticSearchParamsWithDevWarnings(underlyingSearchParams)\n}\n"], "names": ["createRenderSearchParamsFromClient", "CachedSearchParams", "WeakMap", "makeUntrackedExoticSearchParamsWithDevWarnings", "underlyingSearchParams", "cachedSearchParams", "get", "proxiedProperties", "Set", "unproxiedProperties", "promise", "Promise", "resolve", "Object", "keys", "for<PERSON>ach", "prop", "wellKnownProperties", "has", "push", "add", "proxiedPromise", "Proxy", "target", "receiver", "Reflect", "expression", "describeStringPropertyAccess", "warnForSyncAccess", "ReflectAdapter", "set", "value", "delete", "describeHasCheckingStringProperty", "ownKeys", "warnForSyncSpread", "makeUntrackedSearchParamsWithDevWarnings", "console", "error", "process", "env", "__NEXT_DYNAMIC_IO"], "mappings": "AAmLMuC,QAAQC,GAAG,CAACC,iBAAiB,EAAE;;;;;+BAHrBzC,sCAAAA;;;eAAAA;;;yBA9Ke;8BAKxB;AAGP,MAAMC,qBAAqB,IAAIC;AAE/B,SAASC,+CACPC,sBAAoC;IAEpC,MAAMC,qBAAqBJ,mBAAmBK,GAAG,CAACF;IAClD,IAAIC,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7C,MAAMC,UAAUC,QAAQC,OAAO,CAACR;IAEhCS,OAAOC,IAAI,CAACV,wBAAwBW,OAAO,CAAC,CAACC;QAC3C,IAAIC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEP,oBAAoBU,IAAI,CAACH;QAC3B,OAAO;YACLT,kBAAkBa,GAAG,CAACJ;YACpBN,OAAe,CAACM,KAAK,GAAGZ,sBAAsB,CAACY,KAAK;QACxD;IACF;IAEA,MAAMK,iBAAiB,IAAIC,MAAMZ,SAAS;QACxCJ,KAAIiB,MAAM,EAAEP,IAAI,EAAEQ,QAAQ;YACxB,IAAI,OAAOR,SAAS,UAAU;gBAC5B,IACE,CAACC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,SACxBT,CAAAA,kBAAkBW,GAAG,CAACF,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BS,QAAQP,GAAG,CAACK,QAAQP,UAAU,KAAI,GACpC;oBACA,MAAMU,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,gBAAgBX;oBAChEY,kBAAkBF;gBACpB;YACF;YACA,OAAOG,SAAAA,cAAc,CAACvB,GAAG,CAACiB,QAAQP,MAAMQ;QAC1C;QACAM,KAAIP,MAAM,EAAEP,IAAI,EAAEe,KAAK,EAAEP,QAAQ;YAC/B,IAAI,OAAOR,SAAS,UAAU;gBAC5BT,kBAAkByB,MAAM,CAAChB;YAC3B;YACA,OAAOS,QAAQK,GAAG,CAACP,QAAQP,MAAMe,OAAOP;QAC1C;QACAN,KAAIK,MAAM,EAAEP,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,CAACC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,SACxBT,CAAAA,kBAAkBW,GAAG,CAACF,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BS,QAAQP,GAAG,CAACK,QAAQP,UAAU,KAAI,GACpC;oBACA,MAAMU,aAAaO,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAjB;oBAEFY,kBAAkBF;gBACpB;YACF;YACA,OAAOD,QAAQP,GAAG,CAACK,QAAQP;QAC7B;QACAkB,SAAQX,MAAM;YACZY;YACA,OAAOV,QAAQS,OAAO,CAACX;QACzB;IACF;IAEAtB,mBAAmB6B,GAAG,CAAC1B,wBAAwBiB;IAC/C,OAAOA;AACT;AAEA,gFAAgF;AAChF,8EAA8E;AAC9E,SAASe,yCACPhC,sBAAoC;IAEpC,MAAMC,qBAAqBJ,mBAAmBK,GAAG,CAACF;IAClD,IAAIC,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAC7C,MAAMC,UAAUC,QAAQC,OAAO,CAACR;IAEhCS,OAAOC,IAAI,CAACV,wBAAwBW,OAAO,CAAC,CAACC;QAC3C,IAAIC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEP,oBAAoBU,IAAI,CAACH;QAC3B,OAAO;YACLT,kBAAkBa,GAAG,CAACJ;QACxB;IACF;IAEA,MAAMK,iBAAiB,IAAIC,MAAMZ,SAAS;QACxCJ,KAAIiB,MAAM,EAAEP,IAAI,EAAEQ,QAAQ;YACxB,IAAI,OAAOR,SAAS,UAAU;gBAC5B,IACE,CAACC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,SACxBT,CAAAA,kBAAkBW,GAAG,CAACF,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BS,QAAQP,GAAG,CAACK,QAAQP,UAAU,KAAI,GACpC;oBACA,MAAMU,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,gBAAgBX;oBAChEY,kBAAkBF;gBACpB;YACF;YACA,OAAOG,SAAAA,cAAc,CAACvB,GAAG,CAACiB,QAAQP,MAAMQ;QAC1C;QACAM,KAAIP,MAAM,EAAEP,IAAI,EAAEe,KAAK,EAAEP,QAAQ;YAC/B,IAAI,OAAOR,SAAS,UAAU;gBAC5BT,kBAAkByB,MAAM,CAAChB;YAC3B;YACA,OAAOS,QAAQK,GAAG,CAACP,QAAQP,MAAMe,OAAOP;QAC1C;QACAN,KAAIK,MAAM,EAAEP,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,CAACC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,SACxBT,CAAAA,kBAAkBW,GAAG,CAACF,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BS,QAAQP,GAAG,CAACK,QAAQP,UAAU,KAAI,GACpC;oBACA,MAAMU,aAAaO,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAjB;oBAEFY,kBAAkBF;gBACpB;YACF;YACA,OAAOD,QAAQP,GAAG,CAACK,QAAQP;QAC7B;QACAkB,SAAQX,MAAM;YACZY;YACA,OAAOV,QAAQS,OAAO,CAACX;QACzB;IACF;IAEAtB,mBAAmB6B,GAAG,CAAC1B,wBAAwBiB;IAC/C,OAAOA;AACT;AAEA,SAASO,kBAAkBF,UAAkB;IAC3CW,QAAQC,KAAK,CACV,uDAAoDZ,aAAW,OAC7D,4FACA;AAEP;AAEA,SAASS;IACPE,QAAQC,KAAK,CACV,wDACE,4FACA;AAEP;AAEO,SAAStC,mCACdI,sBAAoC;IAEpC;;IAIA,OAAOD,+CAA+CC;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4016, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/client/request/search-params.browser.ts"], "sourcesContent": ["export const createRenderSearchParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (\n        require('./search-params.browser.dev') as typeof import('./search-params.browser.dev')\n      ).createRenderSearchParamsFromClient\n    : (\n        require('./search-params.browser.prod') as typeof import('./search-params.browser.prod')\n      ).createRenderSearchParamsFromClient\n"], "names": ["createRenderSearchParamsFromClient", "process", "env", "NODE_ENV", "require"], "mappings": "AACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BADdH,sCAAAA;;;eAAAA;;;AAAN,MAAMA,4EAGLI,QAAQ,0HACRJ,kCAAkC,GAElCI,QAAQ,gCACRJ,kCAAkC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4041, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/client/request/params.browser.dev.ts"], "sourcesContent": ["import type { Params } from '../../server/request/params'\n\nimport { ReflectAdapter } from '../../server/web/spec-extension/adapters/reflect'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nfunction makeDynamicallyTrackedExoticParamsWithDevWarnings(\n  underlyingParams: Params\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingParams)\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          warnForSyncAccess(expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      warnForEnumeration(unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\n// Similar to `makeDynamicallyTrackedExoticParamsWithDevWarnings`, but just\n// logging the sync access without actually defining the params on the promise.\nfunction makeDynamicallyTrackedParamsWithDevWarnings(\n  underlyingParams: Params\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingParams)\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          warnForSyncAccess(expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      warnForEnumeration(unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction warnForSyncAccess(expression: string) {\n  console.error(\n    `A param property was accessed directly with ${expression}. \\`params\\` is now a Promise and should be unwrapped with \\`React.use()\\` before accessing properties of the underlying params object. In this version of Next.js direct access to param properties is still supported to facilitate migration but in a future version you will be required to unwrap \\`params\\` with \\`React.use()\\`.`\n  )\n}\n\nfunction warnForEnumeration(missingProperties: Array<string>) {\n  if (missingProperties.length) {\n    const describedMissingProperties =\n      describeListOfPropertyNames(missingProperties)\n    console.error(\n      `params are being enumerated incompletely missing these properties: ${describedMissingProperties}. ` +\n        `\\`params\\` should be unwrapped with \\`React.use()\\` before using its value. ` +\n        `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n    )\n  } else {\n    console.error(\n      `params are being enumerated. ` +\n        `\\`params\\` should be unwrapped with \\`React.use()\\` before using its value. ` +\n        `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n    )\n  }\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n\nexport function createRenderParamsFromClient(\n  clientParams: Params\n): Promise<Params> {\n  if (process.env.__NEXT_DYNAMIC_IO) {\n    return makeDynamicallyTrackedParamsWithDevWarnings(clientParams)\n  }\n\n  return makeDynamicallyTrackedExoticParamsWithDevWarnings(clientParams)\n}\n"], "names": ["createRenderParamsFromClient", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "makeDynamicallyTrackedExoticParamsWithDevWarnings", "underlyingParams", "cachedParams", "get", "promise", "Promise", "resolve", "proxiedProperties", "Set", "unproxiedProperties", "Object", "keys", "for<PERSON>ach", "prop", "wellKnownProperties", "has", "add", "proxiedPromise", "Proxy", "target", "receiver", "expression", "describeStringPropertyAccess", "warnForSyncAccess", "ReflectAdapter", "set", "value", "delete", "ownKeys", "warnForEnumeration", "Reflect", "makeDynamicallyTrackedParamsWithDevWarnings", "console", "error", "missingProperties", "length", "describedMissingProperties", "describeListOfPropertyNames", "properties", "InvariantError", "description", "i", "clientParams", "process", "env", "__NEXT_DYNAMIC_IO"], "mappings": "AA2KM8C,QAAQC,GAAG,CAACC,iBAAiB,EAAE;;;;;+BAHrBhD,gCAAAA;;;eAAAA;;;yBAtKe;gCACA;8BAIxB;AAGP,MAAMC,eAAe,IAAIC;AAEzB,SAASC,kDACPC,gBAAwB;IAExB,MAAMC,eAAeJ,aAAaK,GAAG,CAACF;IACtC,IAAIC,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUC,QAAQC,OAAO,CAACL;IAEhC,MAAMM,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7CC,OAAOC,IAAI,CAACV,kBAAkBW,OAAO,CAAC,CAACC;QACrC,IAAIC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACLN,kBAAkBS,GAAG,CAACH;YACpBT,OAAe,CAACS,KAAK,GAAGZ,gBAAgB,CAACY,KAAK;QAClD;IACF;IAEA,MAAMI,iBAAiB,IAAIC,MAAMd,SAAS;QACxCD,KAAIgB,MAAM,EAAEN,IAAI,EAAEO,QAAQ;YACxB,IAAI,OAAOP,SAAS,UAAU;gBAC5B,IACE,AACAN,kBAAkBQ,GAAG,CAACF,OACtB,0CAFuE;oBAGvE,MAAMQ,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUT;oBAC1DU,kBAAkBF;gBACpB;YACF;YACA,OAAOG,SAAAA,cAAc,CAACrB,GAAG,CAACgB,QAAQN,MAAMO;QAC1C;QACAK,KAAIN,MAAM,EAAEN,IAAI,EAAEa,KAAK,EAAEN,QAAQ;YAC/B,IAAI,OAAOP,SAAS,UAAU;gBAC5BN,kBAAkBoB,MAAM,CAACd;YAC3B;YACA,OAAOW,SAAAA,cAAc,CAACC,GAAG,CAACN,QAAQN,MAAMa,OAAON;QACjD;QACAQ,SAAQT,MAAM;YACZU,mBAAmBpB;YACnB,OAAOqB,QAAQF,OAAO,CAACT;QACzB;IACF;IAEArB,aAAa2B,GAAG,CAACxB,kBAAkBgB;IACnC,OAAOA;AACT;AAEA,2EAA2E;AAC3E,+EAA+E;AAC/E,SAASc,4CACP9B,gBAAwB;IAExB,MAAMC,eAAeJ,aAAaK,GAAG,CAACF;IACtC,IAAIC,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUC,QAAQC,OAAO,CAACL;IAEhC,MAAMM,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7CC,OAAOC,IAAI,CAACV,kBAAkBW,OAAO,CAAC,CAACC;QACrC,IAAIC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACLN,kBAAkBS,GAAG,CAACH;QACxB;IACF;IAEA,MAAMI,iBAAiB,IAAIC,MAAMd,SAAS;QACxCD,KAAIgB,MAAM,EAAEN,IAAI,EAAEO,QAAQ;YACxB,IAAI,OAAOP,SAAS,UAAU;gBAC5B,IACE,AACAN,kBAAkBQ,GAAG,CAACF,OACtB,0CAFuE;oBAGvE,MAAMQ,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUT;oBAC1DU,kBAAkBF;gBACpB;YACF;YACA,OAAOG,SAAAA,cAAc,CAACrB,GAAG,CAACgB,QAAQN,MAAMO;QAC1C;QACAK,KAAIN,MAAM,EAAEN,IAAI,EAAEa,KAAK,EAAEN,QAAQ;YAC/B,IAAI,OAAOP,SAAS,UAAU;gBAC5BN,kBAAkBoB,MAAM,CAACd;YAC3B;YACA,OAAOW,SAAAA,cAAc,CAACC,GAAG,CAACN,QAAQN,MAAMa,OAAON;QACjD;QACAQ,SAAQT,MAAM;YACZU,mBAAmBpB;YACnB,OAAOqB,QAAQF,OAAO,CAACT;QACzB;IACF;IAEArB,aAAa2B,GAAG,CAACxB,kBAAkBgB;IACnC,OAAOA;AACT;AAEA,SAASM,kBAAkBF,UAAkB;IAC3CW,QAAQC,KAAK,CACV,iDAA8CZ,aAAW;AAE9D;AAEA,SAASQ,mBAAmBK,iBAAgC;IAC1D,IAAIA,kBAAkBC,MAAM,EAAE;QAC5B,MAAMC,6BACJC,4BAA4BH;QAC9BF,QAAQC,KAAK,CACV,wEAAqEG,6BAA2B,OAC9F,6EACA;IAEP,OAAO;QACLJ,QAAQC,KAAK,CACV,kCACE,6EACA;IAEP;AACF;AAEA,SAASI,4BAA4BC,UAAyB;IAC5D,OAAQA,WAAWH,MAAM;QACvB,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,IAAII,gBAAAA,cAAc,CACtB,wFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YACH,OAAQ,MAAID,UAAU,CAAC,EAAE,GAAC;QAC5B,KAAK;YACH,OAAQ,MAAIA,UAAU,CAAC,EAAE,GAAC,YAAWA,UAAU,CAAC,EAAE,GAAC;QACrD;YAAS;gBACP,IAAIE,cAAc;gBAClB,IAAK,IAAIC,IAAI,GAAGA,IAAIH,WAAWH,MAAM,GAAG,GAAGM,IAAK;oBAC9CD,eAAgB,MAAIF,UAAU,CAACG,EAAE,GAAC;gBACpC;gBACAD,eAAgB,YAAUF,UAAU,CAACA,WAAWH,MAAM,GAAG,EAAE,GAAC;gBAC5D,OAAOK;YACT;IACF;AACF;AAEO,SAAS3C,6BACd6C,YAAoB;IAEpB;;IAIA,OAAO1C,kDAAkD0C;AAC3D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4197, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/client/request/params.browser.ts"], "sourcesContent": ["export const createRenderParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (require('./params.browser.dev') as typeof import('./params.browser.dev'))\n        .createRenderParamsFromClient\n    : (\n        require('./params.browser.prod') as typeof import('./params.browser.prod')\n      ).createRenderParamsFromClient\n"], "names": ["createRenderParamsFromClient", "process", "env", "NODE_ENV", "require"], "mappings": "AACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BADdH,gCAAAA;;;eAAAA;;;AAAN,MAAMA,sEAENI,QAAQ,mHACNJ,4BAA4B,GAE7BI,QAAQ,yBACRJ,4BAA4B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4222, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/server/create-deduped-by-callsite-server-error-logger.ts"], "sourcesContent": ["import * as React from 'react'\n\nconst errorRef: { current: null | Error } = { current: null }\n\n// React.cache is currently only available in canary/experimental React channels.\nconst cache =\n  typeof React.cache === 'function'\n    ? React.cache\n    : (fn: (key: unknown) => void) => fn\n\n// When Dynamic IO is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn = process.env.__NEXT_DYNAMIC_IO\n  ? console.error\n  : console.warn\n\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n  (key: unknown) => {\n    try {\n      logErrorOrWarn(errorRef.current)\n    } finally {\n      errorRef.current = null\n    }\n  }\n)\n\n/**\n * Creates a function that logs an error message that is deduped by the userland\n * callsite.\n * This requires no indirection between the call of this function and the userland\n * callsite i.e. there's only a single library frame above this.\n * Do not use on the Client where sourcemaps and ignore listing might be enabled.\n * Only use that for warnings need a fix independent of the callstack.\n *\n * @param getMessage\n * @returns\n */\nexport function createDedupedByCallsiteServerErrorLoggerDev<Args extends any[]>(\n  getMessage: (...args: Args) => Error\n) {\n  return function logDedupedError(...args: Args) {\n    const message = getMessage(...args)\n\n    if (process.env.NODE_ENV !== 'production') {\n      const callStackFrames = new Error().stack?.split('\\n')\n      if (callStackFrames === undefined || callStackFrames.length < 4) {\n        logErrorOrWarn(message)\n      } else {\n        // Error:\n        //   logDedupedError\n        //   asyncApiBeingAccessedSynchronously\n        //   <userland callsite>\n        // TODO: This breaks if sourcemaps with ignore lists are enabled.\n        const key = callStackFrames[4]\n        errorRef.current = message\n        flushCurrentErrorIfNew(key)\n      }\n    } else {\n      logErrorOrWarn(message)\n    }\n  }\n}\n"], "names": ["createDedupedByCallsiteServerErrorLoggerDev", "errorRef", "current", "cache", "React", "fn", "logErrorOrWarn", "process", "env", "__NEXT_DYNAMIC_IO", "console", "error", "warn", "flushCurrentErrorIfNew", "key", "getMessage", "logDedupedError", "args", "message", "NODE_ENV", "callStackFrames", "Error", "stack", "split", "undefined", "length"], "mappings": "AAauBO,QAAQC,GAAG,CAACC,iBAAiB;;;;;+BA4BpCT,+CAAAA;;;eAAAA;;;+DAzCO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvB,MAAMC,WAAsC;IAAEC,SAAS;AAAK;AAE5D,iFAAiF;AACjF,MAAMC,QACJ,OAAOC,OAAMD,KAAK,KAAK,aACnBC,OAAMD,KAAK,GACX,CAACE,KAA+BA;AAEtC,qEAAqE;AACrE,qEAAqE;AACrE,gBAAgB;AAChB,MAAMC,uDACFI,QAAQC,KAAK,aACbD,QAAQE,IAAI;AAEhB,2CAA2C;AAC3C,wGAAwG;AACxG,MAAMC,yBAAyBV,MAC7B,AACA,CAACW,yEADyE;IAExE,IAAI;QACFR,eAAeL,SAASC,OAAO;IACjC,SAAU;QACRD,SAASC,OAAO,GAAG;IACrB;AACF;AAcK,SAASF,4CACde,UAAoC;IAEpC,OAAO,SAASC;;YAAmBC,uBAAH,KAAa;;QAC3C,MAAMC,UAAUH,cAAcE;QAE9B,IAAIV,QAAQC,GAAG,CAACW,QAAQ,KAAK,WAAc;gBACjB;YAAxB,MAAMC,kBAAAA,CAAkB,SAAA,IAAIC,QAAQC,KAAK,KAAA,OAAA,KAAA,IAAjB,OAAmBC,KAAK,CAAC;YACjD,IAAIH,oBAAoBI,aAAaJ,gBAAgBK,MAAM,GAAG,GAAG;gBAC/DnB,eAAeY;YACjB,OAAO;gBACL,SAAS;gBACT,oBAAoB;gBACpB,uCAAuC;gBACvC,wBAAwB;gBACxB,iEAAiE;gBACjE,MAAMJ,MAAMM,eAAe,CAAC,EAAE;gBAC9BnB,SAASC,OAAO,GAAGgB;gBACnBL,uBAAuBC;YACzB;QACF,OAAO;;IAGT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4324, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/server/app-render/after-task-async-storage-instance.ts"], "sourcesContent": ["import type { AfterTaskAsyncStorage } from './after-task-async-storage.external'\nimport { createAsyncLocalStorage } from './async-local-storage'\n\nexport const afterTaskAsyncStorageInstance: AfterTaskAsyncStorage =\n  createAsyncLocalStorage()\n"], "names": ["afterTaskAsyncStorageInstance", "createAsyncLocalStorage"], "mappings": ";;;+BAGaA,iCAAAA;;;eAAAA;;;mCAF2B;AAEjC,MAAMA,gCACXC,CAAAA,GAAAA,mBAAAA,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4341, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/server/app-render/after-task-async-storage.external.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\n\n// Share the instance module in the next-shared layer\nimport { afterTaskAsyncStorageInstance as afterTaskAsyncStorage } from './after-task-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\nimport type { WorkUnitStore } from './work-unit-async-storage.external'\n\nexport interface AfterTaskStore {\n  /** The phase in which the topmost `after` was called.\n   *\n   * NOTE: Can be undefined when running `generateStaticParams`,\n   * where we only have a `workStore`, no `workUnitStore`.\n   */\n  readonly rootTaskSpawnPhase: WorkUnitStore['phase'] | undefined\n}\n\nexport type AfterTaskAsyncStorage = AsyncLocalStorage<AfterTaskStore>\n\nexport { afterTaskAsyncStorage }\n"], "names": ["afterTaskAsyncStorage"], "mappings": ";;;+BAiBSA,yBAAAA;;;eAAAA,+BAAAA,6BAAqB;;;+CAdyC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4357, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/server/request/utils.ts"], "sourcesContent": ["import { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { afterTaskAsyncStorage } from '../app-render/after-task-async-storage.external'\nimport type { WorkStore } from '../app-render/work-async-storage.external'\n\nexport function throwWithStaticGenerationBailoutError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwWithStaticGenerationBailoutErrorWithDynamicError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwForSearchParamsAccessInUseCache(\n  workStore: WorkStore,\n  constructorOpt: Function\n): never {\n  const error = new Error(\n    `Route ${workStore.route} used \"searchParams\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"searchParams\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n  )\n\n  Error.captureStackTrace(error, constructorOpt)\n  workStore.invalidDynamicUsageError ??= error\n\n  throw error\n}\n\nexport function isRequestAPICallableInsideAfter() {\n  const afterTaskStore = afterTaskAsyncStorage.getStore()\n  return afterTaskStore?.rootTaskSpawnPhase === 'action'\n}\n"], "names": ["isRequestAPICallableInsideAfter", "throwForSearchParamsAccessInUseCache", "throwWithStaticGenerationBailoutError", "throwWithStaticGenerationBailoutErrorWithDynamicError", "route", "expression", "StaticGenBailoutError", "workStore", "constructorOpt", "error", "Error", "captureStackTrace", "invalidDynamicUsageError", "afterTaskStore", "afterTaskAsyncStorage", "getStore", "rootTaskSpawnPhase"], "mappings": ";;;;;;;;;;;;;;;;IAoCgBA,+BAA+B,EAAA;eAA/BA;;IAdAC,oCAAoC,EAAA;eAApCA;;IAlBAC,qCAAqC,EAAA;eAArCA;;IASAC,qDAAqD,EAAA;eAArDA;;;yCAbsB;+CACA;AAG/B,SAASD,sCACdE,KAAa,EACbC,UAAkB;IAElB,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,AAAC,MAAM,UAAEF,OAAM,iDAAiD,WAAEC,YAAW,0HAA0H,CAAC,KADpM,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASF,sDACdC,KAAa,EACbC,UAAkB;IAElB,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,AAAC,MAAM,UAAEF,OAAM,4EAA4E,UAAEC,YAAW,0HAA0H,CAAC,KAD/N,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASJ,qCACdM,SAAoB,EACpBC,cAAwB;QAOxBD;IALA,MAAME,QAAQ,OAAA,cAEb,CAFa,IAAIC,MAChB,AAAC,MAAM,UAAEH,UAAUH,KAAK,EAAC,oVAAoV,CAAC,KADlW,qBAAA;eAAA;oBAAA;sBAAA;IAEd;IAEAM,MAAMC,iBAAiB,CAACF,OAAOD;;8KACrBI,wBAAwB,GAAKH;IAEvC,MAAMA;AACR;AAEO,SAAST;IACd,MAAMa,iBAAiBC,+BAAAA,qBAAqB,CAACC,QAAQ;IACrD,OAAOF,CAAAA,kBAAAA,OAAAA,KAAAA,IAAAA,eAAgBG,kBAAkB,MAAK;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4424, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/server/request/search-params.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  trackDynamicDataInDynamicRender,\n  annotateDynamicAccess,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n  type PrerenderStoreLegacy,\n  type PrerenderStorePPR,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport {\n  describeStringPropertyAccess,\n  describeHasCheckingStringProperty,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport {\n  throwWithStaticGenerationBailoutErrorWithDynamicError,\n  throwForSearchParamsAccessInUseCache,\n} from './utils'\nimport { scheduleImmediate } from '../../lib/scheduler'\n\nexport type SearchParams = { [key: string]: string | string[] | undefined }\n\n/**\n * In this version of Next.js the `params` prop passed to Layouts, Pages, and other Segments is a Promise.\n * However to facilitate migration to this new Promise type you can currently still access params directly on the Promise instance passed to these Segments.\n * The `UnsafeUnwrappedSearchParams` type is available if you need to temporarily access the underlying params without first awaiting or `use`ing the Promise.\n *\n * In a future version of Next.js the `params` prop will be a plain Promise and this type will be removed.\n *\n * Typically instances of `params` can be updated automatically to be treated as a Promise by a codemod published alongside this Next.js version however if you\n * have not yet run the codemod of the codemod cannot detect certain instances of `params` usage you should first try to refactor your code to await `params`.\n *\n * If refactoring is not possible but you still want to be able to access params directly without typescript errors you can cast the params Promise to this type\n *\n * ```tsx\n * type Props = { searchParams: Promise<{ foo: string }> }\n *\n * export default async function Page(props: Props) {\n *  const { searchParams } = (props.searchParams as unknown as UnsafeUnwrappedSearchParams<typeof props.searchParams>)\n *  return ...\n * }\n * ```\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedSearchParams<P> =\n  P extends Promise<infer U> ? Omit<U, 'then' | 'status' | 'value'> : never\n\nexport function createSearchParamsFromClient(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderSearchParams(workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderSearchParams(underlyingSearchParams, workStore)\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport const createServerSearchParamsForMetadata =\n  createServerSearchParamsForServerPage\n\nexport function createServerSearchParamsForServerPage(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderSearchParams(workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderSearchParams(underlyingSearchParams, workStore)\n}\n\nexport function createPrerenderSearchParamsForClientPage(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (\n    prerenderStore &&\n    (prerenderStore.type === 'prerender' ||\n      prerenderStore.type === 'prerender-client')\n  ) {\n    // dynamicIO Prerender\n    // We're prerendering in a mode that aborts (dynamicIO) and should stall\n    // the promise to ensure the RSC side is considered dynamic\n    return makeHangingPromise(prerenderStore.renderSignal, '`searchParams`')\n  }\n  // We're prerendering in a mode that does not aborts. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve({})\n}\n\nfunction createPrerenderSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  switch (prerenderStore.type) {\n    case 'prerender':\n    case 'prerender-client':\n      // We are in a dynamicIO (PPR or otherwise) prerender\n      return makeHangingSearchParams(prerenderStore)\n    default:\n      // The remaining cases are prerender-ppr and prerender-legacy\n      // We are in a legacy static generation and need to interrupt the prerender\n      // when search params are accessed.\n      return makeErroringExoticSearchParams(workStore, prerenderStore)\n  }\n}\n\nfunction createRenderSearchParams(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  } else {\n    if (\n      process.env.NODE_ENV === 'development' &&\n      !workStore.isPrefetchRequest\n    ) {\n      if (process.env.__NEXT_DYNAMIC_IO) {\n        return makeUntrackedSearchParamsWithDevWarnings(\n          underlyingSearchParams,\n          workStore\n        )\n      }\n\n      return makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(\n        underlyingSearchParams,\n        workStore\n      )\n    } else {\n      if (process.env.__NEXT_DYNAMIC_IO) {\n        return makeUntrackedSearchParams(underlyingSearchParams)\n      }\n\n      return makeUntrackedExoticSearchParams(underlyingSearchParams, workStore)\n    }\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nconst CachedSearchParamsForUseCache = new WeakMap<\n  CacheLifetime,\n  Promise<SearchParams>\n>()\n\nfunction makeHangingSearchParams(\n  prerenderStore: PrerenderStoreModern\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(prerenderStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = makeHangingPromise<SearchParams>(\n    prerenderStore.renderSignal,\n    '`searchParams`'\n  )\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      switch (prop) {\n        case 'then': {\n          const expression =\n            '`await searchParams`, `searchParams.then`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n        case 'status': {\n          const expression =\n            '`use(searchParams)`, `searchParams.status`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        default: {\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n      }\n    },\n  })\n\n  CachedSearchParams.set(prerenderStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeErroringExoticSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreLegacy | PrerenderStorePPR\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const underlyingSearchParams = {}\n  // For search params we don't construct a ReactPromise because we want to interrupt\n  // rendering on any property access that was not set from outside and so we only want\n  // to have properties like value and status if React sets them.\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      switch (prop) {\n        case 'then': {\n          const expression =\n            '`await searchParams`, `searchParams.then`, or similar'\n          if (workStore.dynamicShouldError) {\n            throwWithStaticGenerationBailoutErrorWithDynamicError(\n              workStore.route,\n              expression\n            )\n          } else if (prerenderStore.type === 'prerender-ppr') {\n            // PPR Prerender (no dynamicIO)\n            postponeWithTracking(\n              workStore.route,\n              expression,\n              prerenderStore.dynamicTracking\n            )\n          } else {\n            // Legacy Prerender\n            throwToInterruptStaticGeneration(\n              expression,\n              workStore,\n              prerenderStore\n            )\n          }\n          return\n        }\n        case 'status': {\n          const expression =\n            '`use(searchParams)`, `searchParams.status`, or similar'\n          if (workStore.dynamicShouldError) {\n            throwWithStaticGenerationBailoutErrorWithDynamicError(\n              workStore.route,\n              expression\n            )\n          } else if (prerenderStore.type === 'prerender-ppr') {\n            // PPR Prerender (no dynamicIO)\n            postponeWithTracking(\n              workStore.route,\n              expression,\n              prerenderStore.dynamicTracking\n            )\n          } else {\n            // Legacy Prerender\n            throwToInterruptStaticGeneration(\n              expression,\n              workStore,\n              prerenderStore\n            )\n          }\n          return\n        }\n        default: {\n          if (typeof prop === 'string' && !wellKnownProperties.has(prop)) {\n            const expression = describeStringPropertyAccess(\n              'searchParams',\n              prop\n            )\n            if (workStore.dynamicShouldError) {\n              throwWithStaticGenerationBailoutErrorWithDynamicError(\n                workStore.route,\n                expression\n              )\n            } else if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          }\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n      }\n    },\n    has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests trigger dynamic. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (typeof prop === 'string') {\n        const expression = describeHasCheckingStringProperty(\n          'searchParams',\n          prop\n        )\n        if (workStore.dynamicShouldError) {\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            workStore.route,\n            expression\n          )\n        } else if (prerenderStore.type === 'prerender-ppr') {\n          // PPR Prerender (no dynamicIO)\n          postponeWithTracking(\n            workStore.route,\n            expression,\n            prerenderStore.dynamicTracking\n          )\n        } else {\n          // Legacy Prerender\n          throwToInterruptStaticGeneration(\n            expression,\n            workStore,\n            prerenderStore\n          )\n        }\n        return false\n      }\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys() {\n      const expression =\n        '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n      if (workStore.dynamicShouldError) {\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          workStore.route,\n          expression\n        )\n      } else if (prerenderStore.type === 'prerender-ppr') {\n        // PPR Prerender (no dynamicIO)\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          prerenderStore.dynamicTracking\n        )\n      } else {\n        // Legacy Prerender\n        throwToInterruptStaticGeneration(expression, workStore, prerenderStore)\n      }\n    },\n  })\n\n  CachedSearchParams.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\n/**\n * This is a variation of `makeErroringExoticSearchParams` that always throws an\n * error on access, because accessing searchParams inside of `\"use cache\"` is\n * not allowed.\n */\nexport function makeErroringExoticSearchParamsForUseCache(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParamsForUseCache.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = Promise.resolve({})\n\n  const proxiedPromise = new Proxy(promise, {\n    get: function get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it. We know it\n        // isn't a dynamic access because it can only be something that was\n        // previously written to the promise and thus not an underlying\n        // searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      if (\n        typeof prop === 'string' &&\n        (prop === 'then' || !wellKnownProperties.has(prop))\n      ) {\n        throwForSearchParamsAccessInUseCache(workStore, get)\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    has: function has(target, prop) {\n      // We don't expect key checking to be used except for testing the existence of\n      // searchParams so we make all has tests throw an error. this means that `promise.then`\n      // can resolve to the then function on the Promise prototype but 'then' in promise will assume\n      // you are testing whether the searchParams has a 'then' property.\n      if (\n        typeof prop === 'string' &&\n        (prop === 'then' || !wellKnownProperties.has(prop))\n      ) {\n        throwForSearchParamsAccessInUseCache(workStore, has)\n      }\n\n      return ReflectAdapter.has(target, prop)\n    },\n    ownKeys: function ownKeys() {\n      throwForSearchParamsAccessInUseCache(workStore, ownKeys)\n    },\n  })\n\n  CachedSearchParamsForUseCache.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeUntrackedExoticSearchParams(\n  underlyingSearchParams: SearchParams,\n  store: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingSearchParams)\n  CachedSearchParams.set(underlyingSearchParams, promise)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (!wellKnownProperties.has(prop)) {\n      Object.defineProperty(promise, prop, {\n        get() {\n          const workUnitStore = workUnitAsyncStorage.getStore()\n          trackDynamicDataInDynamicRender(store, workUnitStore)\n          return underlyingSearchParams[prop]\n        },\n        set(value) {\n          Object.defineProperty(promise, prop, {\n            value,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedSearchParams(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = Promise.resolve(underlyingSearchParams)\n  CachedSearchParams.set(underlyingSearchParams, promise)\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedExoticSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams,\n  store: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  // We have an unfortunate sequence of events that requires this initialization logic. We want to instrument the underlying\n  // searchParams object to detect if you are accessing values in dev. This is used for warnings and for things like the static prerender\n  // indicator. However when we pass this proxy to our Promise.resolve() below the VM checks if the resolved value is a promise by looking\n  // at the `.then` property. To our dynamic tracking logic this is indistinguishable from a `then` searchParam and so we would normally trigger\n  // dynamic tracking. However we know that this .then is not real dynamic access, it's just how thenables resolve in sequence. So we introduce\n  // this initialization concept so we omit the dynamic check until after we've constructed our resolved promise.\n  let promiseInitialized = false\n  const proxiedUnderlying = new Proxy(underlyingSearchParams, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string' && promiseInitialized) {\n        if (store.dynamicShouldError) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            store.route,\n            expression\n          )\n        }\n        const workUnitStore = workUnitAsyncStorage.getStore()\n        trackDynamicDataInDynamicRender(store, workUnitStore)\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (store.dynamicShouldError) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            store.route,\n            expression\n          )\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      if (store.dynamicShouldError) {\n        const expression =\n          '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          store.route,\n          expression\n        )\n      }\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  // We don't use makeResolvedReactPromise here because searchParams\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = new Promise<SearchParams>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingSearchParams))\n  )\n  promise.then(() => {\n    promiseInitialized = true\n  })\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      Object.defineProperty(promise, prop, {\n        get() {\n          return proxiedUnderlying[prop]\n        },\n        set(newValue) {\n          Object.defineProperty(promise, prop, {\n            value: newValue,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (prop === 'then' && store.dynamicShouldError) {\n        const expression = '`searchParams.then`'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          store.route,\n          expression\n        )\n      }\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          syncIODev(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          syncIODev(store.route, expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      const expression = '`Object.keys(searchParams)` or similar'\n      syncIODev(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\n// Similar to `makeDynamicallyTrackedExoticSearchParamsWithDevWarnings`, but\n// just logging the sync access without actually defining the search params on\n// the promise.\nfunction makeUntrackedSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams,\n  store: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          warnForSyncAccess(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          warnForSyncAccess(store.route, expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      const expression = '`Object.keys(searchParams)` or similar'\n      warnForIncompleteEnumeration(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction syncIODev(\n  route: string | undefined,\n  expression: string,\n  missingProperties?: Array<string>\n) {\n  // In all cases we warn normally\n  if (missingProperties && missingProperties.length > 0) {\n    warnForIncompleteEnumeration(route, expression, missingProperties)\n  } else {\n    warnForSyncAccess(route, expression)\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createSearchAccessError\n)\n\nconst warnForIncompleteEnumeration =\n  createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError)\n\nfunction createSearchAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`searchParams\\` should be awaited before using its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction createIncompleteEnumerationError(\n  route: string | undefined,\n  expression: string,\n  missingProperties: Array<string>\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`searchParams\\` should be awaited before using its properties. ` +\n      `The following properties were not available through enumeration ` +\n      `because they conflict with builtin or well-known property names: ` +\n      `${describeListOfPropertyNames(missingProperties)}. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n"], "names": ["createPrerenderSearchParamsForClientPage", "createSearchParamsFromClient", "createServerSearchParamsForMetadata", "createServerSearchParamsForServerPage", "makeErroringExoticSearchParamsForUseCache", "underlyingSearchParams", "workStore", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "createPrerenderSearchParams", "createRenderSearchParams", "forceStatic", "Promise", "resolve", "prerenderStore", "makeHangingPromise", "renderSignal", "makeHangingSearchParams", "makeErroringExoticSearchParams", "process", "env", "NODE_ENV", "isPrefetchRequest", "__NEXT_DYNAMIC_IO", "makeUntrackedSearchParamsWithDevWarnings", "makeDynamicallyTrackedExoticSearchParamsWithDevWarnings", "makeUntrackedSearchParams", "makeUntrackedExoticSearchParams", "CachedSearchParams", "WeakMap", "CachedSearchParamsForUseCache", "cachedSearchParams", "get", "promise", "proxiedPromise", "Proxy", "target", "prop", "receiver", "Object", "hasOwn", "ReflectAdapter", "expression", "annotateDynamicAccess", "set", "dynamicShouldError", "throwWithStaticGenerationBailoutErrorWithDynamicError", "route", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "wellKnownProperties", "has", "describeStringPropertyAccess", "describeHasCheckingStringProperty", "ownKeys", "throwForSearchParamsAccessInUseCache", "store", "keys", "for<PERSON>ach", "defineProperty", "trackDynamicDataInDynamicRender", "value", "writable", "enumerable", "configurable", "proxiedProperties", "Set", "unproxiedProperties", "promiseInitialized", "proxiedUnderlying", "Reflect", "scheduleImmediate", "then", "push", "add", "newValue", "syncIODev", "delete", "warnForSyncAccess", "warnForIncompleteEnumeration", "missingProperties", "length", "prerenderPhase", "requestStore", "trackSynchronousRequestDataAccessInDev", "createDedupedByCallsiteServerErrorLoggerDev", "createSearchAccessError", "createIncompleteEnumerationError", "prefix", "Error", "describeListOfPropertyNames", "properties", "InvariantError", "description", "i"], "mappings": "AAmKMqB,QAAQC,GAAG,CAACC,QAAQ;;;;;;;;;;;;;;;;;;;IA3DVvB,wCAAwC,EAAA;eAAxCA;;IA1CAC,4BAA4B,EAAA;eAA5BA;;IAoBHC,mCAAmC,EAAA;eAAnCA;;IAGGC,qCAAqC,EAAA;eAArCA;;IAwUAC,yCAAyC,EAAA;eAAzCA;;;yBA3Ze;kCAOxB;8CAQA;gCACwB;uCACI;0DACyB;8BAKrD;uBAIA;2BAC2B;AAgC3B,SAASH,6BACdI,sBAAoC,EACpCC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BAA4BL,WAAWC;YAChD;QAEF;IACF;IACA,OAAOK,yBAAyBP,wBAAwBC;AAC1D;AAGO,MAAMJ,sCACXC;AAEK,SAASA,sCACdE,sBAAoC,EACpCC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BAA4BL,WAAWC;YAChD;QAEF;IACF;IACA,OAAOK,yBAAyBP,wBAAwBC;AAC1D;AAEO,SAASN,yCACdM,SAAoB;IAEpB,IAAIA,UAAUO,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B;IAEA,MAAMC,iBAAiBR,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,IACEO,kBACCA,CAAAA,eAAeN,IAAI,KAAK,eACvBM,eAAeN,IAAI,KAAK,kBAAiB,GAC3C;QACA,sBAAsB;QACtB,wEAAwE;QACxE,2DAA2D;QAC3D,OAAOO,CAAAA,GAAAA,uBAAAA,kBAAkB,EAACD,eAAeE,YAAY,EAAE;IACzD;IACA,oFAAoF;IACpF,gGAAgG;IAChG,mBAAmB;IACnB,OAAOJ,QAAQC,OAAO,CAAC,CAAC;AAC1B;AAEA,SAASJ,4BACPL,SAAoB,EACpBU,cAA8B;IAE9B,IAAIV,UAAUO,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B;IAEA,OAAQC,eAAeN,IAAI;QACzB,KAAK;QACL,KAAK;YACH,qDAAqD;YACrD,OAAOS,wBAAwBH;QACjC;YACE,6DAA6D;YAC7D,2EAA2E;YAC3E,mCAAmC;YACnC,OAAOI,+BAA+Bd,WAAWU;IACrD;AACF;AAEA,SAASJ,yBACPP,sBAAoC,EACpCC,SAAoB;IAEpB,IAAIA,UAAUO,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B,OAAO;QACL,wDAC2B,iBACzB,CAACT,UAAUkB,iBAAiB,EAC5B;YACA,IAAIH,QAAQC,GAAG,CAACG,iBAAiB,EAAE;;YAOnC,OAAOE,wDACLtB,wBACAC;QAEJ,OAAO;YACL,IAAIe,QAAQC,GAAG,CAACG,iBAAiB,EAAE;;YAInC,OAAOI,gCAAgCxB,wBAAwBC;QACjE;IACF;AACF;AAGA,MAAMwB,qBAAqB,IAAIC;AAE/B,MAAMC,gCAAgC,IAAID;AAK1C,SAASZ,wBACPH,cAAoC;IAEpC,MAAMiB,qBAAqBH,mBAAmBI,GAAG,CAAClB;IAClD,IAAIiB,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUlB,CAAAA,GAAAA,uBAAAA,kBAAkB,EAChCD,eAAeE,YAAY,EAC3B;IAGF,MAAMkB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,6DAA6D;gBAC7D,qEAAqE;gBACrE,0FAA0F;gBAC1F,OAAOI,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,OAAQD;gBACN,KAAK;oBAAQ;wBACX,MAAMK,aACJ;wBACFC,CAAAA,GAAAA,kBAAAA,qBAAqB,EAACD,YAAY5B;wBAClC,OAAO2B,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;gBACA,KAAK;oBAAU;wBACb,MAAMI,aACJ;wBACFC,CAAAA,GAAAA,kBAAAA,qBAAqB,EAACD,YAAY5B;wBAClC,OAAO2B,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;gBAEA;oBAAS;wBACP,OAAOG,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;YACF;QACF;IACF;IAEAV,mBAAmBgB,GAAG,CAAC9B,gBAAgBoB;IACvC,OAAOA;AACT;AAEA,SAAShB,+BACPd,SAAoB,EACpBU,cAAwD;IAExD,MAAMiB,qBAAqBH,mBAAmBI,GAAG,CAAC5B;IAClD,IAAI2B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAM5B,yBAAyB,CAAC;IAChC,mFAAmF;IACnF,qFAAqF;IACrF,+DAA+D;IAC/D,MAAM8B,UAAUrB,QAAQC,OAAO,CAACV;IAEhC,MAAM+B,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,6DAA6D;gBAC7D,qEAAqE;gBACrE,0FAA0F;gBAC1F,OAAOI,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,OAAQD;gBACN,KAAK;oBAAQ;wBACX,MAAMK,aACJ;wBACF,IAAItC,UAAUyC,kBAAkB,EAAE;4BAChCC,CAAAA,GAAAA,OAAAA,qDAAqD,EACnD1C,UAAU2C,KAAK,EACfL;wBAEJ,OAAO,IAAI5B,eAAeN,IAAI,KAAK,iBAAiB;4BAClD,+BAA+B;4BAC/BwC,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClB5C,UAAU2C,KAAK,EACfL,YACA5B,eAAemC,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9BR,YACAtC,WACAU;wBAEJ;wBACA;oBACF;gBACA,KAAK;oBAAU;wBACb,MAAM4B,aACJ;wBACF,IAAItC,UAAUyC,kBAAkB,EAAE;4BAChCC,CAAAA,GAAAA,OAAAA,qDAAqD,EACnD1C,UAAU2C,KAAK,EACfL;wBAEJ,OAAO,IAAI5B,eAAeN,IAAI,KAAK,iBAAiB;4BAClD,+BAA+B;4BAC/BwC,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClB5C,UAAU2C,KAAK,EACfL,YACA5B,eAAemC,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9BR,YACAtC,WACAU;wBAEJ;wBACA;oBACF;gBACA;oBAAS;wBACP,IAAI,OAAOuB,SAAS,YAAY,CAACc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,OAAO;4BAC9D,MAAMK,aAAaW,CAAAA,GAAAA,cAAAA,4BAA4B,EAC7C,gBACAhB;4BAEF,IAAIjC,UAAUyC,kBAAkB,EAAE;gCAChCC,CAAAA,GAAAA,OAAAA,qDAAqD,EACnD1C,UAAU2C,KAAK,EACfL;4BAEJ,OAAO,IAAI5B,eAAeN,IAAI,KAAK,iBAAiB;gCAClD,+BAA+B;gCAC/BwC,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClB5C,UAAU2C,KAAK,EACfL,YACA5B,eAAemC,eAAe;4BAElC,OAAO;gCACL,mBAAmB;gCACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9BR,YACAtC,WACAU;4BAEJ;wBACF;wBACA,OAAO2B,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;YACF;QACF;QACAc,KAAIhB,MAAM,EAAEC,IAAI;YACd,8EAA8E;YAC9E,wFAAwF;YACxF,8FAA8F;YAC9F,kEAAkE;YAClE,IAAI,OAAOA,SAAS,UAAU;gBAC5B,MAAMK,aAAaY,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAjB;gBAEF,IAAIjC,UAAUyC,kBAAkB,EAAE;oBAChCC,CAAAA,GAAAA,OAAAA,qDAAqD,EACnD1C,UAAU2C,KAAK,EACfL;gBAEJ,OAAO,IAAI5B,eAAeN,IAAI,KAAK,iBAAiB;oBAClD,+BAA+B;oBAC/BwC,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClB5C,UAAU2C,KAAK,EACfL,YACA5B,eAAemC,eAAe;gBAElC,OAAO;oBACL,mBAAmB;oBACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9BR,YACAtC,WACAU;gBAEJ;gBACA,OAAO;YACT;YACA,OAAO2B,SAAAA,cAAc,CAACW,GAAG,CAAChB,QAAQC;QACpC;QACAkB;YACE,MAAMb,aACJ;YACF,IAAItC,UAAUyC,kBAAkB,EAAE;gBAChCC,CAAAA,GAAAA,OAAAA,qDAAqD,EACnD1C,UAAU2C,KAAK,EACfL;YAEJ,OAAO,IAAI5B,eAAeN,IAAI,KAAK,iBAAiB;gBAClD,+BAA+B;gBAC/BwC,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClB5C,UAAU2C,KAAK,EACfL,YACA5B,eAAemC,eAAe;YAElC,OAAO;gBACL,mBAAmB;gBACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAACR,YAAYtC,WAAWU;YAC1D;QACF;IACF;IAEAc,mBAAmBgB,GAAG,CAACxC,WAAW8B;IAClC,OAAOA;AACT;AAOO,SAAShC,0CACdE,SAAoB;IAEpB,MAAM2B,qBAAqBD,8BAA8BE,GAAG,CAAC5B;IAC7D,IAAI2B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUrB,QAAQC,OAAO,CAAC,CAAC;IAEjC,MAAMqB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAK,SAASA,IAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACtC,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,wEAAwE;gBACxE,mEAAmE;gBACnE,+DAA+D;gBAC/D,oBAAoB;gBACpB,OAAOI,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,IACE,OAAOD,SAAS,YACfA,CAAAA,SAAS,UAAU,CAACc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,KAAI,GACjD;gBACAmB,CAAAA,GAAAA,OAAAA,oCAAoC,EAACpD,WAAW4B;YAClD;YAEA,OAAOS,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAc,KAAK,SAASA,IAAIhB,MAAM,EAAEC,IAAI;YAC5B,8EAA8E;YAC9E,uFAAuF;YACvF,8FAA8F;YAC9F,kEAAkE;YAClE,IACE,OAAOA,SAAS,YACfA,CAAAA,SAAS,UAAU,CAACc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,KAAI,GACjD;gBACAmB,CAAAA,GAAAA,OAAAA,oCAAoC,EAACpD,WAAWgD;YAClD;YAEA,OAAOX,SAAAA,cAAc,CAACW,GAAG,CAAChB,QAAQC;QACpC;QACAkB,SAAS,SAASA;YAChBC,CAAAA,GAAAA,OAAAA,oCAAoC,EAACpD,WAAWmD;QAClD;IACF;IAEAzB,8BAA8Bc,GAAG,CAACxC,WAAW8B;IAC7C,OAAOA;AACT;AAEA,SAASP,gCACPxB,sBAAoC,EACpCsD,KAAgB;IAEhB,MAAM1B,qBAAqBH,mBAAmBI,GAAG,CAAC7B;IAClD,IAAI4B,oBAAoB;QACtB,OAAOA;IACT;IAEA,kEAAkE;IAClE,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUrB,QAAQC,OAAO,CAACV;IAChCyB,mBAAmBgB,GAAG,CAACzC,wBAAwB8B;IAE/CM,OAAOmB,IAAI,CAACvD,wBAAwBwD,OAAO,CAAC,CAACtB;QAC3C,IAAI,CAACc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,OAAO;YAClCE,OAAOqB,cAAc,CAAC3B,SAASI,MAAM;gBACnCL;oBACE,MAAM3B,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;oBACnDsD,CAAAA,GAAAA,kBAAAA,+BAA+B,EAACJ,OAAOpD;oBACvC,OAAOF,sBAAsB,CAACkC,KAAK;gBACrC;gBACAO,KAAIkB,KAAK;oBACPvB,OAAOqB,cAAc,CAAC3B,SAASI,MAAM;wBACnCyB;wBACAC,UAAU;wBACVC,YAAY;oBACd;gBACF;gBACAA,YAAY;gBACZC,cAAc;YAChB;QACF;IACF;IAEA,OAAOhC;AACT;AAEA,SAASP,0BACPvB,sBAAoC;IAEpC,MAAM4B,qBAAqBH,mBAAmBI,GAAG,CAAC7B;IAClD,IAAI4B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUrB,QAAQC,OAAO,CAACV;IAChCyB,mBAAmBgB,GAAG,CAACzC,wBAAwB8B;IAE/C,OAAOA;AACT;AAEA,SAASR,wDACPtB,sBAAoC,EACpCsD,KAAgB;IAEhB,MAAM1B,qBAAqBH,mBAAmBI,GAAG,CAAC7B;IAClD,IAAI4B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAMmC,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7C,0HAA0H;IAC1H,uIAAuI;IACvI,wIAAwI;IACxI,8IAA8I;IAC9I,6IAA6I;IAC7I,+GAA+G;IAC/G,IAAIC,qBAAqB;IACzB,MAAMC,oBAAoB,IAAInC,MAAMhC,wBAAwB;QAC1D6B,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,YAAYgC,oBAAoB;gBAClD,IAAIZ,MAAMZ,kBAAkB,EAAE;oBAC5B,MAAMH,aAAaW,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,gBAAgBhB;oBAChES,CAAAA,GAAAA,OAAAA,qDAAqD,EACnDW,MAAMV,KAAK,EACXL;gBAEJ;gBACA,MAAMrC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;gBACnDsD,CAAAA,GAAAA,kBAAAA,+BAA+B,EAACJ,OAAOpD;YACzC;YACA,OAAOoC,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAc,KAAIhB,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IAAIoB,MAAMZ,kBAAkB,EAAE;oBAC5B,MAAMH,aAAaY,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAjB;oBAEFS,CAAAA,GAAAA,OAAAA,qDAAqD,EACnDW,MAAMV,KAAK,EACXL;gBAEJ;YACF;YACA,OAAO6B,QAAQnB,GAAG,CAAChB,QAAQC;QAC7B;QACAkB,SAAQnB,MAAM;YACZ,IAAIqB,MAAMZ,kBAAkB,EAAE;gBAC5B,MAAMH,aACJ;gBACFI,CAAAA,GAAAA,OAAAA,qDAAqD,EACnDW,MAAMV,KAAK,EACXL;YAEJ;YACA,OAAO6B,QAAQhB,OAAO,CAACnB;QACzB;IACF;IAEA,kEAAkE;IAClE,kEAAkE;IAClE,qEAAqE;IACrE,MAAMH,UAAU,IAAIrB,QAAsB,CAACC,UACzC2D,CAAAA,GAAAA,WAAAA,iBAAiB,EAAC,IAAM3D,QAAQV;IAElC8B,QAAQwC,IAAI,CAAC;QACXJ,qBAAqB;IACvB;IAEA9B,OAAOmB,IAAI,CAACvD,wBAAwBwD,OAAO,CAAC,CAACtB;QAC3C,IAAIc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClE+B,oBAAoBM,IAAI,CAACrC;QAC3B,OAAO;YACL6B,kBAAkBS,GAAG,CAACtC;YACtBE,OAAOqB,cAAc,CAAC3B,SAASI,MAAM;gBACnCL;oBACE,OAAOsC,iBAAiB,CAACjC,KAAK;gBAChC;gBACAO,KAAIgC,QAAQ;oBACVrC,OAAOqB,cAAc,CAAC3B,SAASI,MAAM;wBACnCyB,OAAOc;wBACPb,UAAU;wBACVC,YAAY;oBACd;gBACF;gBACAA,YAAY;gBACZC,cAAc;YAChB;QACF;IACF;IAEA,MAAM/B,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAID,SAAS,UAAUoB,MAAMZ,kBAAkB,EAAE;gBAC/C,MAAMH,aAAa;gBACnBI,CAAAA,GAAAA,OAAAA,qDAAqD,EACnDW,MAAMV,KAAK,EACXL;YAEJ;YACA,IAAI,OAAOL,SAAS,UAAU;gBAC5B,IACE,CAACc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,SACxB6B,CAAAA,kBAAkBd,GAAG,CAACf,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BkC,QAAQnB,GAAG,CAAChB,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMK,aAAaW,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,gBAAgBhB;oBAChEwC,UAAUpB,MAAMV,KAAK,EAAEL;gBACzB;YACF;YACA,OAAOD,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAM,KAAIR,MAAM,EAAEC,IAAI,EAAEyB,KAAK,EAAExB,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5B6B,kBAAkBY,MAAM,CAACzC;YAC3B;YACA,OAAOkC,QAAQ3B,GAAG,CAACR,QAAQC,MAAMyB,OAAOxB;QAC1C;QACAc,KAAIhB,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,CAACc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,SACxB6B,CAAAA,kBAAkBd,GAAG,CAACf,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BkC,QAAQnB,GAAG,CAAChB,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMK,aAAaY,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAjB;oBAEFwC,UAAUpB,MAAMV,KAAK,EAAEL;gBACzB;YACF;YACA,OAAO6B,QAAQnB,GAAG,CAAChB,QAAQC;QAC7B;QACAkB,SAAQnB,MAAM;YACZ,MAAMM,aAAa;YACnBmC,UAAUpB,MAAMV,KAAK,EAAEL,YAAY0B;YACnC,OAAOG,QAAQhB,OAAO,CAACnB;QACzB;IACF;IAEAR,mBAAmBgB,GAAG,CAACzC,wBAAwB+B;IAC/C,OAAOA;AACT;AAEA,4EAA4E;AAC5E,8EAA8E;AAC9E,eAAe;AACf,SAASV,yCACPrB,sBAAoC,EACpCsD,KAAgB;IAEhB,MAAM1B,qBAAqBH,mBAAmBI,GAAG,CAAC7B;IAClD,IAAI4B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAMmC,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAC7C,MAAMnC,UAAUrB,QAAQC,OAAO,CAACV;IAEhCoC,OAAOmB,IAAI,CAACvD,wBAAwBwD,OAAO,CAAC,CAACtB;QAC3C,IAAIc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClE+B,oBAAoBM,IAAI,CAACrC;QAC3B,OAAO;YACL6B,kBAAkBS,GAAG,CAACtC;QACxB;IACF;IAEA,MAAMH,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,UAAU;gBAC5B,IACE,CAACc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,SACxB6B,CAAAA,kBAAkBd,GAAG,CAACf,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BkC,QAAQnB,GAAG,CAAChB,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMK,aAAaW,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,gBAAgBhB;oBAChE0C,kBAAkBtB,MAAMV,KAAK,EAAEL;gBACjC;YACF;YACA,OAAOD,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAM,KAAIR,MAAM,EAAEC,IAAI,EAAEyB,KAAK,EAAExB,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5B6B,kBAAkBY,MAAM,CAACzC;YAC3B;YACA,OAAOkC,QAAQ3B,GAAG,CAACR,QAAQC,MAAMyB,OAAOxB;QAC1C;QACAc,KAAIhB,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,CAACc,cAAAA,mBAAmB,CAACC,GAAG,CAACf,SACxB6B,CAAAA,kBAAkBd,GAAG,CAACf,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BkC,QAAQnB,GAAG,CAAChB,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMK,aAAaY,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAjB;oBAEF0C,kBAAkBtB,MAAMV,KAAK,EAAEL;gBACjC;YACF;YACA,OAAO6B,QAAQnB,GAAG,CAAChB,QAAQC;QAC7B;QACAkB,SAAQnB,MAAM;YACZ,MAAMM,aAAa;YACnBsC,6BAA6BvB,MAAMV,KAAK,EAAEL,YAAY0B;YACtD,OAAOG,QAAQhB,OAAO,CAACnB;QACzB;IACF;IAEAR,mBAAmBgB,GAAG,CAACzC,wBAAwB+B;IAC/C,OAAOA;AACT;AAEA,SAAS2C,UACP9B,KAAyB,EACzBL,UAAkB,EAClBuC,iBAAiC;IAEjC,gCAAgC;IAChC,IAAIA,qBAAqBA,kBAAkBC,MAAM,GAAG,GAAG;QACrDF,6BAA6BjC,OAAOL,YAAYuC;IAClD,OAAO;QACLF,kBAAkBhC,OAAOL;IAC3B;IAEA,MAAMrC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IACEF,iBACAA,cAAcG,IAAI,KAAK,aACvBH,cAAc8E,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAMC,eAAe/E;QACrBgF,CAAAA,GAAAA,kBAAAA,sCAAsC,EAACD;IACzC;AACF;AAEA,MAAML,oBAAoBO,CAAAA,GAAAA,0CAAAA,2CAA2C,EACnEC;AAGF,MAAMP,+BACJM,CAAAA,GAAAA,0CAAAA,2CAA2C,EAACE;AAE9C,SAASD,wBACPxC,KAAyB,EACzBL,UAAkB;IAElB,MAAM+C,SAAS1C,QAAQ,AAAC,OAAO,GAAQ,EAAE,CAAC,IAATA,eAAY;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAI2C,MACT,UAAGD,QAAO,KAAK,WAAE/C,YAAW,EAAE,CAAC,GAC7B,EAAC,gEAAgE,CAAC,EACjE,CAAD,6DAA+D,CAAC,IAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAAS8C,iCACPzC,KAAyB,EACzBL,UAAkB,EAClBuC,iBAAgC;IAEhC,MAAMQ,SAAS1C,QAAS,AAAD,OAAQ,UAAEA,OAAM,EAAE,CAAC,KAAG;IAC7C,OAAO,OAAA,cAON,CAPM,IAAI2C,MACT,UAAGD,QAAO,KAAK,WAAE/C,YAAW,EAAE,CAAC,GAC7B,EAAC,gEAAgE,CAAC,EACjE,CAAD,+DAAiE,CAAC,GAClE,CAAC,iEAAiE,CAAC,IACnE,UAAGiD,4BAA4BV,oBAAmB,EAAE,CAAC,GACrD,EAAC,8DAA8D,CAAC,IAN7D,qBAAA;eAAA;oBAAA;sBAAA;IAOP;AACF;AAEA,SAASU,4BAA4BC,UAAyB;IAC5D,OAAQA,WAAWV,MAAM;QACvB,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,IAAIW,gBAAAA,cAAc,CACtB,wFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YACH,OAAO,AAAC,EAAE,SAAED,UAAU,CAAC,EAAE,EAAC,EAAE,CAAC;QAC/B,KAAK;YACH,OAAQ,AAAD,EAAG,SAAEA,UAAU,CAAC,EAAE,EAAC,SAAS,SAAEA,UAAU,CAAC,EAAE,EAAC,EAAE,CAAC;QACxD;YAAS;gBACP,IAAIE,cAAc;gBAClB,IAAK,IAAIC,IAAI,GAAGA,IAAIH,WAAWV,MAAM,GAAG,GAAGa,IAAK;oBAC9CD,eAAe,AAAC,EAAE,SAAEF,UAAU,CAACG,EAAE,EAAC,IAAI,CAAC;gBACzC;gBACAD,eAAe,AAAC,QAAQ,SAAEF,UAAU,CAACA,WAAWV,MAAM,GAAG,EAAE,EAAC,EAAE,CAAC;gBAC/D,OAAOY;YACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5003, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/server/app-render/dynamic-access-async-storage-instance.ts"], "sourcesContent": ["import { createAsyncLocalStorage } from './async-local-storage'\nimport type { DynamicAccessStorage } from './dynamic-access-async-storage.external'\n\nexport const dynamicAccessAsyncStorageInstance: DynamicAccessStorage =\n  createAsyncLocalStorage()\n"], "names": ["dynamicAccessAsyncStorageInstance", "createAsyncLocalStorage"], "mappings": ";;;+BAGaA,qCAAAA;;;eAAAA;;;mCAH2B;AAGjC,MAAMA,oCACXC,CAAAA,GAAAA,mBAAAA,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5020, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/server/app-render/dynamic-access-async-storage.external.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\n\n// Share the instance module in the next-shared layer\nimport { dynamicAccessAsyncStorageInstance } from './dynamic-access-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\n\nexport interface DynamicAccessAsyncStore {\n  readonly abortController: AbortController\n}\n\nexport type DynamicAccessStorage = AsyncLocalStorage<DynamicAccessAsyncStore>\nexport { dynamicAccessAsyncStorageInstance as dynamicAccessAsyncStorage }\n"], "names": ["dynamicAccessAsyncStorage", "dynamicAccessAsyncStorageInstance"], "mappings": ";;;+BAU8CA,6BAAAA;;;eAArCC,mCAAAA,iCAAiC;;;mDAPQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5036, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/server/request/params.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\nimport type { FallbackRouteParams } from './fallback-params'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n  type PrerenderStorePPR,\n  type PrerenderStoreLegacy,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { scheduleImmediate } from '../../lib/scheduler'\nimport { dynamicAccessAsyncStorage } from '../app-render/dynamic-access-async-storage.external'\n\nexport type ParamValue = string | Array<string> | undefined\nexport type Params = Record<string, ParamValue>\n\n/**\n * In this version of Next.js the `params` prop passed to Layouts, Pages, and other Segments is a Promise.\n * However to facilitate migration to this new Promise type you can currently still access params directly on the Promise instance passed to these Segments.\n * The `UnsafeUnwrappedParams` type is available if you need to temporarily access the underlying params without first awaiting or `use`ing the Promise.\n *\n * In a future version of Next.js the `params` prop will be a plain Promise and this type will be removed.\n *\n * Typically instances of `params` can be updated automatically to be treated as a Promise by a codemod published alongside this Next.js version however if you\n * have not yet run the codemod of the codemod cannot detect certain instances of `params` usage you should first try to refactor your code to await `params`.\n *\n * If refactoring is not possible but you still want to be able to access params directly without typescript errors you can cast the params Promise to this type\n *\n * ```tsx\n * type Props = { params: Promise<{ id: string }>}\n *\n * export default async function Layout(props: Props) {\n *  const directParams = (props.params as unknown as UnsafeUnwrappedParams<typeof props.params>)\n *  return ...\n * }\n * ```\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedParams<P> =\n  P extends Promise<infer U> ? Omit<U, 'then' | 'status' | 'value'> : never\n\nexport function createParamsFromClient(\n  underlyingParams: Params,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport type CreateServerParamsForMetadata = typeof createServerParamsForMetadata\nexport const createServerParamsForMetadata = createServerParamsForServerSegment\n\n// routes always runs in RSC context so it is equivalent to a Server Page Component\nexport function createServerParamsForRoute(\n  underlyingParams: Params,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\nexport function createServerParamsForServerSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\nexport function createPrerenderParamsForClientSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (\n    prerenderStore &&\n    (prerenderStore.type === 'prerender' ||\n      prerenderStore.type === 'prerender-client')\n  ) {\n    const fallbackParams = workStore.fallbackRouteParams\n    if (fallbackParams) {\n      for (let key in underlyingParams) {\n        if (fallbackParams.has(key)) {\n          // This params object has one of more fallback params so we need to consider\n          // the awaiting of this params object \"dynamic\". Since we are in dynamicIO mode\n          // we encode this as a promise that never resolves\n          return makeHangingPromise(prerenderStore.renderSignal, '`params`')\n        }\n      }\n    }\n  }\n  // We're prerendering in a mode that does not abort. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve(underlyingParams)\n}\n\nfunction createPrerenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStore\n): Promise<Params> {\n  const fallbackParams = workStore.fallbackRouteParams\n  if (fallbackParams) {\n    let hasSomeFallbackParams = false\n    for (const key in underlyingParams) {\n      if (fallbackParams.has(key)) {\n        hasSomeFallbackParams = true\n        break\n      }\n    }\n\n    if (hasSomeFallbackParams) {\n      // params need to be treated as dynamic because we have at least one fallback param\n      switch (prerenderStore.type) {\n        case 'prerender':\n        case 'prerender-client':\n          // We are in a dynamicIO prerender\n          return makeHangingParams(underlyingParams, prerenderStore)\n        default:\n          return makeErroringExoticParams(\n            underlyingParams,\n            fallbackParams,\n            workStore,\n            prerenderStore\n          )\n      }\n    }\n  }\n\n  // We don't have any fallback params so we have an entirely static safe params object\n  return makeUntrackedExoticParams(underlyingParams)\n}\n\nfunction createRenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  if (process.env.NODE_ENV === 'development' && !workStore.isPrefetchRequest) {\n    if (process.env.__NEXT_DYNAMIC_IO) {\n      return makeDynamicallyTrackedParamsWithDevWarnings(\n        underlyingParams,\n        workStore\n      )\n    }\n\n    return makeDynamicallyTrackedExoticParamsWithDevWarnings(\n      underlyingParams,\n      workStore\n    )\n  } else {\n    if (process.env.__NEXT_DYNAMIC_IO) {\n      return makeUntrackedParams(underlyingParams)\n    }\n\n    return makeUntrackedExoticParams(underlyingParams)\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nconst fallbackParamsProxyHandler: ProxyHandler<Promise<Params>> = {\n  get: function get(target, prop, receiver) {\n    if (prop === 'then' || prop === 'catch' || prop === 'finally') {\n      const originalMethod = ReflectAdapter.get(target, prop, receiver)\n\n      return {\n        [prop]: (...args: unknown[]) => {\n          const store = dynamicAccessAsyncStorage.getStore()\n\n          if (store) {\n            store.abortController.abort(\n              new Error(`Accessed fallback \\`params\\` during prerendering.`)\n            )\n          }\n\n          return new Proxy(\n            originalMethod.apply(target, args),\n            fallbackParamsProxyHandler\n          )\n        },\n      }[prop]\n    }\n\n    return ReflectAdapter.get(target, prop, receiver)\n  },\n}\n\nfunction makeHangingParams(\n  underlyingParams: Params,\n  prerenderStore: PrerenderStoreModern\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = new Proxy(\n    makeHangingPromise<Params>(prerenderStore.renderSignal, '`params`'),\n    fallbackParamsProxyHandler\n  )\n\n  CachedParams.set(underlyingParams, promise)\n\n  return promise\n}\n\nfunction makeErroringExoticParams(\n  underlyingParams: Params,\n  fallbackParams: FallbackRouteParams,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStorePPR | PrerenderStoreLegacy\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const augmentedUnderlying = { ...underlyingParams }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(augmentedUnderlying)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      if (fallbackParams.has(prop)) {\n        Object.defineProperty(augmentedUnderlying, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when dynamicIO is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          enumerable: true,\n        })\n        Object.defineProperty(promise, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when dynamicIO is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          set(newValue) {\n            Object.defineProperty(promise, prop, {\n              value: newValue,\n              writable: true,\n              enumerable: true,\n            })\n          },\n          enumerable: true,\n          configurable: true,\n        })\n      } else {\n        ;(promise as any)[prop] = underlyingParams[prop]\n      }\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedExoticParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedExoticParamsWithDevWarnings(\n  underlyingParams: Params,\n  store: WorkStore\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = new Promise<Params>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingParams))\n  )\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          syncIODev(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      const expression = '`...params` or similar expression'\n      syncIODev(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\n// Similar to `makeDynamicallyTrackedExoticParamsWithDevWarnings`, but just\n// logging the sync access without actually defining the params on the promise.\nfunction makeDynamicallyTrackedParamsWithDevWarnings(\n  underlyingParams: Params,\n  store: WorkStore\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = new Promise<Params>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingParams))\n  )\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          warnForSyncAccess(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      const expression = '`...params` or similar expression'\n      warnForIncompleteEnumeration(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction syncIODev(\n  route: string | undefined,\n  expression: string,\n  missingProperties?: Array<string>\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  if (missingProperties && missingProperties.length > 0) {\n    warnForIncompleteEnumeration(route, expression, missingProperties)\n  } else {\n    warnForSyncAccess(route, expression)\n  }\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createParamsAccessError\n)\n\nconst warnForIncompleteEnumeration =\n  createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError)\n\nfunction createParamsAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction createIncompleteEnumerationError(\n  route: string | undefined,\n  expression: string,\n  missingProperties: Array<string>\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `The following properties were not available through enumeration ` +\n      `because they conflict with builtin property names: ` +\n      `${describeListOfPropertyNames(missingProperties)}. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n"], "names": ["createParamsFromClient", "createPrerenderParamsForClientSegment", "createServerParamsForMetadata", "createServerParamsForRoute", "createServerParamsForServerSegment", "underlyingParams", "workStore", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "createPrerenderParams", "createRenderParams", "prerenderStore", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "key", "has", "makeHangingPromise", "renderSignal", "Promise", "resolve", "hasSomeFallbackParams", "makeHangingParams", "makeErroringExoticParams", "makeUntrackedExoticParams", "process", "env", "NODE_ENV", "isPrefetchRequest", "__NEXT_DYNAMIC_IO", "makeDynamicallyTrackedParamsWithDevWarnings", "makeDynamicallyTrackedExoticParamsWithDevWarnings", "makeUntrackedParams", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "fallbackParamsProxyHandler", "get", "target", "prop", "receiver", "originalMethod", "ReflectAdapter", "args", "store", "dynamicAccessAsyncStorage", "abortController", "abort", "Error", "Proxy", "apply", "cachedParams", "promise", "set", "augmentedUnderlying", "Object", "keys", "for<PERSON>ach", "wellKnownProperties", "defineProperty", "expression", "describeStringPropertyAccess", "postponeWithTracking", "route", "dynamicTracking", "throwToInterruptStaticGeneration", "enumerable", "newValue", "value", "writable", "configurable", "scheduleImmediate", "proxiedProperties", "Set", "unproxiedProperties", "push", "add", "proxiedPromise", "syncIODev", "delete", "ownKeys", "Reflect", "warnForSyncAccess", "warnForIncompleteEnumeration", "missingProperties", "prerenderPhase", "requestStore", "trackSynchronousRequestDataAccessInDev", "length", "createDedupedByCallsiteServerErrorLoggerDev", "createParamsAccessError", "createIncompleteEnumerationError", "prefix", "describeListOfPropertyNames", "properties", "InvariantError", "description", "i"], "mappings": "AA6LM0B,QAAQC,GAAG,CAACC,QAAQ;;;;;;;;;;;;;;;;;;;IAnIV5B,sBAAsB,EAAA;eAAtBA;;IA8DAC,qCAAqC,EAAA;eAArCA;;IAzCHC,6BAA6B,EAAA;eAA7BA;;IAGGC,0BAA0B,EAAA;eAA1BA;;IAmBAC,kCAAkC,EAAA;eAAlCA;;;yBAlGe;kCAKxB;8CAQA;gCACwB;8BAIxB;uCAC4B;0DACyB;2BAC1B;mDACQ;AAiCnC,SAASJ,uBACdK,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,sBAAsBN,kBAAkBC,WAAWC;YAC5D;QAEF;IACF;IACA,OAAOK,mBAAmBP,kBAAkBC;AAC9C;AAIO,MAAMJ,gCAAgCE;AAGtC,SAASD,2BACdE,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,sBAAsBN,kBAAkBC,WAAWC;YAC5D;QAEF;IACF;IACA,OAAOK,mBAAmBP,kBAAkBC;AAC9C;AAEO,SAASF,mCACdC,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,sBAAsBN,kBAAkBC,WAAWC;YAC5D;QAEF;IACF;IACA,OAAOK,mBAAmBP,kBAAkBC;AAC9C;AAEO,SAASL,sCACdI,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMO,iBAAiBL,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,IACEI,kBACCA,CAAAA,eAAeH,IAAI,KAAK,eACvBG,eAAeH,IAAI,KAAK,kBAAiB,GAC3C;QACA,MAAMI,iBAAiBR,UAAUS,mBAAmB;QACpD,IAAID,gBAAgB;YAClB,IAAK,IAAIE,OAAOX,iBAAkB;gBAChC,IAAIS,eAAeG,GAAG,CAACD,MAAM;oBAC3B,4EAA4E;oBAC5E,+EAA+E;oBAC/E,kDAAkD;oBAClD,OAAOE,CAAAA,GAAAA,uBAAAA,kBAAkB,EAACL,eAAeM,YAAY,EAAE;gBACzD;YACF;QACF;IACF;IACA,mFAAmF;IACnF,gGAAgG;IAChG,mBAAmB;IACnB,OAAOC,QAAQC,OAAO,CAAChB;AACzB;AAEA,SAASM,sBACPN,gBAAwB,EACxBC,SAAoB,EACpBO,cAA8B;IAE9B,MAAMC,iBAAiBR,UAAUS,mBAAmB;IACpD,IAAID,gBAAgB;QAClB,IAAIQ,wBAAwB;QAC5B,IAAK,MAAMN,OAAOX,iBAAkB;YAClC,IAAIS,eAAeG,GAAG,CAACD,MAAM;gBAC3BM,wBAAwB;gBACxB;YACF;QACF;QAEA,IAAIA,uBAAuB;YACzB,mFAAmF;YACnF,OAAQT,eAAeH,IAAI;gBACzB,KAAK;gBACL,KAAK;oBACH,kCAAkC;oBAClC,OAAOa,kBAAkBlB,kBAAkBQ;gBAC7C;oBACE,OAAOW,yBACLnB,kBACAS,gBACAR,WACAO;YAEN;QACF;IACF;IAEA,qFAAqF;IACrF,OAAOY,0BAA0BpB;AACnC;AAEA,SAASO,mBACPP,gBAAwB,EACxBC,SAAoB;IAEpB,wDAA6B,iBAAiB,CAACA,UAAUuB,iBAAiB,EAAE;QAC1E,IAAIH,QAAQC,GAAG,CAACG,iBAAiB,EAAE;;QAOnC,OAAOE,kDACL3B,kBACAC;IAEJ,OAAO;QACL,IAAIoB,QAAQC,GAAG,CAACG,iBAAiB,EAAE;;QAInC,OAAOL,0BAA0BpB;IACnC;AACF;AAGA,MAAM6B,eAAe,IAAIC;AAEzB,MAAMC,6BAA4D;IAChEC,KAAK,SAASA,IAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;QACtC,IAAID,SAAS,UAAUA,SAAS,WAAWA,SAAS,WAAW;YAC7D,MAAME,iBAAiBC,SAAAA,cAAc,CAACL,GAAG,CAACC,QAAQC,MAAMC;YAExD,OAAO,CAAA;gBACL,CAACD,KAAK,EAAE,CAAC;;wBAAGI;;oBACV,MAAMC,QAAQC,mCAAAA,yBAAyB,CAACpC,QAAQ;oBAEhD,IAAImC,OAAO;wBACTA,MAAME,eAAe,CAACC,KAAK,CACzB,OAAA,cAA8D,CAA9D,IAAIC,MAAM,AAAC,iDAAiD,CAAC,EAA7D,qBAAA;mCAAA;wCAAA;0CAAA;wBAA6D;oBAEjE;oBAEA,OAAO,IAAIC,MACTR,eAAeS,KAAK,CAACZ,QAAQK,OAC7BP;gBAEJ;YACF,CAAA,CAAC,CAACG,KAAK;QACT;QAEA,OAAOG,SAAAA,cAAc,CAACL,GAAG,CAACC,QAAQC,MAAMC;IAC1C;AACF;AAEA,SAASjB,kBACPlB,gBAAwB,EACxBQ,cAAoC;IAEpC,MAAMsC,eAAejB,aAAaG,GAAG,CAAChC;IACtC,IAAI8C,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMC,UAAU,IAAIH,MAClB/B,CAAAA,GAAAA,uBAAAA,kBAAkB,EAASL,eAAeM,YAAY,EAAE,aACxDiB;IAGFF,aAAamB,GAAG,CAAChD,kBAAkB+C;IAEnC,OAAOA;AACT;AAEA,SAAS5B,yBACPnB,gBAAwB,EACxBS,cAAmC,EACnCR,SAAoB,EACpBO,cAAwD;IAExD,MAAMsC,eAAejB,aAAaG,GAAG,CAAChC;IACtC,IAAI8C,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMG,sBAAsB;QAAE,GAAGjD,gBAAgB;IAAC;IAElD,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAM+C,UAAUhC,QAAQC,OAAO,CAACiC;IAChCpB,aAAamB,GAAG,CAAChD,kBAAkB+C;IAEnCG,OAAOC,IAAI,CAACnD,kBAAkBoD,OAAO,CAAC,CAAClB;QACrC,IAAImB,cAAAA,mBAAmB,CAACzC,GAAG,CAACsB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACL,IAAIzB,eAAeG,GAAG,CAACsB,OAAO;gBAC5BgB,OAAOI,cAAc,CAACL,qBAAqBf,MAAM;oBAC/CF;wBACE,MAAMuB,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUtB;wBAC1D,oEAAoE;wBACpE,oEAAoE;wBACpE,wEAAwE;wBACxE,kBAAkB;wBAClB,qEAAqE;wBACrE,iCAAiC;wBACjC,IAAI1B,eAAeH,IAAI,KAAK,iBAAiB;4BAC3C,+BAA+B;4BAC/BoD,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClBxD,UAAUyD,KAAK,EACfH,YACA/C,eAAemD,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9BL,YACAtD,WACAO;wBAEJ;oBACF;oBACAqD,YAAY;gBACd;gBACAX,OAAOI,cAAc,CAACP,SAASb,MAAM;oBACnCF;wBACE,MAAMuB,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUtB;wBAC1D,oEAAoE;wBACpE,oEAAoE;wBACpE,wEAAwE;wBACxE,kBAAkB;wBAClB,qEAAqE;wBACrE,iCAAiC;wBACjC,IAAI1B,eAAeH,IAAI,KAAK,iBAAiB;4BAC3C,+BAA+B;4BAC/BoD,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClBxD,UAAUyD,KAAK,EACfH,YACA/C,eAAemD,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9BL,YACAtD,WACAO;wBAEJ;oBACF;oBACAwC,KAAIc,QAAQ;wBACVZ,OAAOI,cAAc,CAACP,SAASb,MAAM;4BACnC6B,OAAOD;4BACPE,UAAU;4BACVH,YAAY;wBACd;oBACF;oBACAA,YAAY;oBACZI,cAAc;gBAChB;YACF,OAAO;;gBACHlB,OAAe,CAACb,KAAK,GAAGlC,gBAAgB,CAACkC,KAAK;YAClD;QACF;IACF;IAEA,OAAOa;AACT;AAEA,SAAS3B,0BAA0BpB,gBAAwB;IACzD,MAAM8C,eAAejB,aAAaG,GAAG,CAAChC;IACtC,IAAI8C,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMC,UAAUhC,QAAQC,OAAO,CAAChB;IAChC6B,aAAamB,GAAG,CAAChD,kBAAkB+C;IAEnCG,OAAOC,IAAI,CAACnD,kBAAkBoD,OAAO,CAAC,CAAClB;QACrC,IAAImB,cAAAA,mBAAmB,CAACzC,GAAG,CAACsB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;;YACHa,OAAe,CAACb,KAAK,GAAGlC,gBAAgB,CAACkC,KAAK;QAClD;IACF;IAEA,OAAOa;AACT;AAEA,SAASnB,oBAAoB5B,gBAAwB;IACnD,MAAM8C,eAAejB,aAAaG,GAAG,CAAChC;IACtC,IAAI8C,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMC,UAAUhC,QAAQC,OAAO,CAAChB;IAChC6B,aAAamB,GAAG,CAAChD,kBAAkB+C;IAEnC,OAAOA;AACT;AAEA,SAASpB,kDACP3B,gBAAwB,EACxBuC,KAAgB;IAEhB,MAAMO,eAAejB,aAAaG,GAAG,CAAChC;IACtC,IAAI8C,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMC,UAAU,IAAIhC,QAAgB,CAACC,UACnCkD,CAAAA,GAAAA,WAAAA,iBAAiB,EAAC,IAAMlD,QAAQhB;IAGlC,MAAMmE,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7CnB,OAAOC,IAAI,CAACnD,kBAAkBoD,OAAO,CAAC,CAAClB;QACrC,IAAImB,cAAAA,mBAAmB,CAACzC,GAAG,CAACsB,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEmC,oBAAoBC,IAAI,CAACpC;QAC3B,OAAO;YACLiC,kBAAkBI,GAAG,CAACrC;YACpBa,OAAe,CAACb,KAAK,GAAGlC,gBAAgB,CAACkC,KAAK;QAClD;IACF;IAEA,MAAMsC,iBAAiB,IAAI5B,MAAMG,SAAS;QACxCf,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,UAAU;gBAC5B,IACE,AACAiC,kBAAkBvD,GAAG,CAACsB,OACtB,0CAFuE;oBAGvE,MAAMqB,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUtB;oBAC1DuC,UAAUlC,MAAMmB,KAAK,EAAEH;gBACzB;YACF;YACA,OAAOlB,SAAAA,cAAc,CAACL,GAAG,CAACC,QAAQC,MAAMC;QAC1C;QACAa,KAAIf,MAAM,EAAEC,IAAI,EAAE6B,KAAK,EAAE5B,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5BiC,kBAAkBO,MAAM,CAACxC;YAC3B;YACA,OAAOG,SAAAA,cAAc,CAACW,GAAG,CAACf,QAAQC,MAAM6B,OAAO5B;QACjD;QACAwC,SAAQ1C,MAAM;YACZ,MAAMsB,aAAa;YACnBkB,UAAUlC,MAAMmB,KAAK,EAAEH,YAAYc;YACnC,OAAOO,QAAQD,OAAO,CAAC1C;QACzB;IACF;IAEAJ,aAAamB,GAAG,CAAChD,kBAAkBwE;IACnC,OAAOA;AACT;AAEA,2EAA2E;AAC3E,+EAA+E;AAC/E,SAAS9C,4CACP1B,gBAAwB,EACxBuC,KAAgB;IAEhB,MAAMO,eAAejB,aAAaG,GAAG,CAAChC;IACtC,IAAI8C,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMC,UAAU,IAAIhC,QAAgB,CAACC,UACnCkD,CAAAA,GAAAA,WAAAA,iBAAiB,EAAC,IAAMlD,QAAQhB;IAGlC,MAAMmE,oBAAoB,IAAIC;IAC9B,MAAMC,sBAAqC,EAAE;IAE7CnB,OAAOC,IAAI,CAACnD,kBAAkBoD,OAAO,CAAC,CAAClB;QACrC,IAAImB,cAAAA,mBAAmB,CAACzC,GAAG,CAACsB,OAAO;YACjC,kEAAkE;YAClE,kEAAkE;YAClEmC,oBAAoBC,IAAI,CAACpC;QAC3B,OAAO;YACLiC,kBAAkBI,GAAG,CAACrC;QACxB;IACF;IAEA,MAAMsC,iBAAiB,IAAI5B,MAAMG,SAAS;QACxCf,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,UAAU;gBAC5B,IACE,AACAiC,kBAAkBvD,GAAG,CAACsB,OACtB,0CAFuE;oBAGvE,MAAMqB,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUtB;oBAC1D2C,kBAAkBtC,MAAMmB,KAAK,EAAEH;gBACjC;YACF;YACA,OAAOlB,SAAAA,cAAc,CAACL,GAAG,CAACC,QAAQC,MAAMC;QAC1C;QACAa,KAAIf,MAAM,EAAEC,IAAI,EAAE6B,KAAK,EAAE5B,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5BiC,kBAAkBO,MAAM,CAACxC;YAC3B;YACA,OAAOG,SAAAA,cAAc,CAACW,GAAG,CAACf,QAAQC,MAAM6B,OAAO5B;QACjD;QACAwC,SAAQ1C,MAAM;YACZ,MAAMsB,aAAa;YACnBuB,6BAA6BvC,MAAMmB,KAAK,EAAEH,YAAYc;YACtD,OAAOO,QAAQD,OAAO,CAAC1C;QACzB;IACF;IAEAJ,aAAamB,GAAG,CAAChD,kBAAkBwE;IACnC,OAAOA;AACT;AAEA,SAASC,UACPf,KAAyB,EACzBH,UAAkB,EAClBwB,iBAAiC;IAEjC,MAAM7E,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IACEF,iBACAA,cAAcG,IAAI,KAAK,aACvBH,cAAc8E,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAMC,eAAe/E;QACrBgF,CAAAA,GAAAA,kBAAAA,sCAAsC,EAACD;IACzC;IACA,gCAAgC;IAChC,IAAIF,qBAAqBA,kBAAkBI,MAAM,GAAG,GAAG;QACrDL,6BAA6BpB,OAAOH,YAAYwB;IAClD,OAAO;QACLF,kBAAkBnB,OAAOH;IAC3B;AACF;AAEA,MAAMsB,oBAAoBO,CAAAA,GAAAA,0CAAAA,2CAA2C,EACnEC;AAGF,MAAMP,+BACJM,CAAAA,GAAAA,0CAAAA,2CAA2C,EAACE;AAE9C,SAASD,wBACP3B,KAAyB,EACzBH,UAAkB;IAElB,MAAMgC,SAAS7B,QAAQ,AAAC,OAAO,GAAQ,EAAE,CAAC,IAATA,eAAY;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAIf,MACT,UAAG4C,QAAO,KAAK,WAAEhC,YAAW,EAAE,CAAC,GAC7B,EAAC,0DAA0D,CAAC,EAC3D,CAAD,6DAA+D,CAAC,IAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAAS+B,iCACP5B,KAAyB,EACzBH,UAAkB,EAClBwB,iBAAgC;IAEhC,MAAMQ,SAAS7B,QAAQ,AAAC,OAAO,UAAEA,OAAM,EAAE,CAAC,KAAG;IAC7C,OAAO,OAAA,cAON,CAPM,IAAIf,MACT,GAAiBY,OAAdgC,QAAO,KAAK,uBAAa,EAAE,CAAC,GAC7B,EAAC,0DAA0D,CAAC,EAC3D,CAAD,+DAAiE,CAAC,GAClE,CAAC,mDAAmD,CAAC,IACrD,UAAGC,4BAA4BT,oBAAmB,EAAE,CAAC,GACrD,EAAC,8DAA8D,CAAC,IAN7D,qBAAA;eAAA;oBAAA;sBAAA;IAOP;AACF;AAEA,SAASS,4BAA4BC,UAAyB;IAC5D,OAAQA,WAAWN,MAAM;QACvB,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,IAAIO,gBAAAA,cAAc,CACtB,wFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YACH,OAAO,AAAC,EAAE,EAAgB,EAAE,CAAC,IAAjBD,UAAU,CAAC,EAAE;QAC3B,KAAK;YACH,OAAO,AAAC,EAAE,SAAEA,UAAU,CAAC,EAAE,EAAC,SAAS,SAAEA,UAAU,CAAC,EAAE,EAAC,EAAE,CAAC;QACxD;YAAS;gBACP,IAAIE,cAAc;gBAClB,IAAK,IAAIC,IAAI,GAAGA,IAAIH,WAAWN,MAAM,GAAG,GAAGS,IAAK;oBAC9CD,eAAe,AAAC,EAAE,SAAEF,UAAU,CAACG,EAAE,EAAC,IAAI,CAAC;gBACzC;gBACAD,eAAe,AAAC,QAAQ,SAAEF,UAAU,CAACA,WAAWN,MAAM,GAAG,EAAE,EAAC,EAAE,CAAC;gBAC/D,OAAOQ;YACT;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5471, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/client/components/client-page.tsx"], "sourcesContent": ["'use client'\n\nimport type { ParsedUrlQuery } from 'querystring'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params and searchParams to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Page component.\n *\n * additionally we may send promises representing the params and searchParams. We don't ever use these passed\n * values but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations.\n * It is up to the caller to decide if the promises are needed.\n */\nexport function ClientPageRoot({\n  Component,\n  searchParams,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promises,\n}: {\n  Component: React.ComponentType<any>\n  searchParams: ParsedUrlQuery\n  params: Params\n  promises?: Array<Promise<any>>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientSearchParams: Promise<ParsedUrlQuery>\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling searchParams in a client Page.'\n      )\n    }\n\n    const { createSearchParamsFromClient } =\n      require('../../server/request/search-params') as typeof import('../../server/request/search-params')\n    clientSearchParams = createSearchParamsFromClient(searchParams, store)\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  } else {\n    const { createRenderSearchParamsFromClient } =\n      require('../request/search-params.browser') as typeof import('../request/search-params.browser')\n    const clientSearchParams = createRenderSearchParamsFromClient(searchParams)\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  }\n}\n"], "names": ["ClientPageRoot", "Component", "searchParams", "params", "promises", "window", "workAsyncStorage", "require", "clientSearchParams", "clientParams", "store", "getStore", "InvariantError", "createSearchParamsFromClient", "createParamsFromClient", "createRenderSearchParamsFromClient", "createRenderParamsFromClient"], "mappings": ";;;+BAeg<PERSON>,kBAAAA;;;eAAAA;;;;gCAZe;AAYxB,SAASA,eAAe,KAW9B;IAX8B,IAAA,EAC7BC,SAAS,EACTC,YAAY,EACZC,MAAM,EACN,AACAC,QAAQ,EAMT,GAX8B,gDAIgC;IAQ7D,IAAI,OAAOC,WAAW,aAAa;QACjC,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QAEV,IAAIC;QACJ,IAAIC;QACJ,yEAAyE;QACzE,wEAAwE;QACxE,MAAMC,QAAQJ,iBAAiBK,QAAQ;QACvC,IAAI,CAACD,OAAO;YACV,MAAM,OAAA,cAEL,CAFK,IAAIE,gBAAAA,cAAc,CACtB,6EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM,EAAEC,4BAA4B,EAAE,GACpCN,QAAQ;QACVC,qBAAqBK,6BAA6BX,cAAcQ;QAEhE,MAAM,EAAEI,sBAAsB,EAAE,GAC9BP,QAAQ;QACVE,eAAeK,uBAAuBX,QAAQO;QAE9C,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACT,WAAAA;YAAUE,QAAQM;YAAcP,cAAcM;;IACxD,OAAO;QACL,MAAM,EAAEO,kCAAkC,EAAE,GAC1CR,QAAQ;QACV,MAAMC,qBAAqBO,mCAAmCb;QAC9D,MAAM,EAAEc,4BAA4B,EAAE,GACpCT,QAAQ;QACV,MAAME,eAAeO,6BAA6Bb;QAElD,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACF,WAAAA;YAAUE,QAAQM;YAAcP,cAAcM;;IACxD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5530, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/client/components/client-segment.tsx"], "sourcesContent": ["'use client'\n\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\n\n/**\n * When the Page is a client component we send the params to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Segment component.\n *\n * additionally we may send a promise representing params. We don't ever use this passed\n * value but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations\n * such as when dynamicIO is enabled. It is up to the caller to decide if the promises are needed.\n */\nexport function ClientSegmentRoot({\n  Component,\n  slots,\n  params,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  promise,\n}: {\n  Component: React.ComponentType<any>\n  slots: { [key: string]: React.ReactNode }\n  params: Params\n  promise?: Promise<any>\n}) {\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling params in a client segment such as a Layout or Template.'\n      )\n    }\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component {...slots} params={clientParams} />\n  } else {\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n    return <Component {...slots} params={clientParams} />\n  }\n}\n"], "names": ["ClientSegmentRoot", "Component", "slots", "params", "promise", "window", "workAsyncStorage", "require", "clientParams", "store", "getStore", "InvariantError", "createParamsFromClient", "createRenderParamsFromClient"], "mappings": ";;;+BAcgBA,qBAAAA;;;eAAAA;;;;gCAZe;AAYxB,SAASA,kBAAkB,KAWjC;IAXiC,IAAA,EAChCC,SAAS,EACTC,KAAK,EACLC,MAAM,EAENC,AADA,OACO,EAMR,GAXiC,iDAI6B;IAQ7D,IAAI,OAAOC,WAAW,aAAa;QACjC,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QAEV,IAAIC;QACJ,yEAAyE;QACzE,wEAAwE;QACxE,MAAMC,QAAQH,iBAAiBI,QAAQ;QACvC,IAAI,CAACD,OAAO;YACV,MAAM,OAAA,cAEL,CAFK,IAAIE,gBAAAA,cAAc,CACtB,uGADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM,EAAEC,sBAAsB,EAAE,GAC9BL,QAAQ;QACVC,eAAeI,uBAAuBT,QAAQM;QAE9C,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACR,WAAAA;YAAW,GAAGC,KAAK;YAAEC,QAAQK;;IACvC,OAAO;QACL,MAAM,EAAEK,4BAA4B,EAAE,GACpCN,QAAQ;QACV,MAAMC,eAAeK,6BAA6BV;QAClD,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACF,WAAAA;YAAW,GAAGC,KAAK;YAAEC,QAAQK;;IACvC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5584, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/lib/metadata/generate/icon-mark.tsx"], "sourcesContent": ["'use client'\n\n// This is a client component that only renders during SSR,\n// but will be replaced during streaming with an icon insertion script tag.\n// We don't want it to be presented anywhere so it's only visible during streaming,\n// right after the icon meta tags so that browser can pick it up as soon as it's rendered.\n// Note: we don't just emit the script here because we only need the script if it's not in the head,\n// and we need it to be hoistable alongside the other metadata but sync scripts are not hoistable.\nexport const IconMark = () => {\n  if (typeof window !== 'undefined') {\n    return null\n  }\n  return <meta name=\"«nxt-icon»\" />\n}\n"], "names": ["IconMark", "window", "meta", "name"], "mappings": ";;;+BAQaA,YAAAA;;;eAAAA;;;;AAAN,MAAMA,WAAW;IACtB,IAAI,OAAOC,WAAW,aAAa;QACjC,OAAO;IACT;IACA,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACC,QAAAA;QAAKC,MAAK;;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5608, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/client/components/metadata/async-metadata.tsx"], "sourcesContent": ["'use client'\n\nimport { Suspense, use } from 'react'\nimport type { StreamingMetadataResolvedState } from './types'\n\nfunction MetadataOutlet({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  const { error, digest } = use(promise)\n  if (error) {\n    if (digest) {\n      // The error will lose its original digest after passing from server layer to client layer；\n      // We recover the digest property here to override the React created one if original digest exists.\n      ;(error as any).digest = digest\n    }\n    throw error\n  }\n  return null\n}\n\nexport function AsyncMetadataOutlet({\n  promise,\n}: {\n  promise: Promise<StreamingMetadataResolvedState>\n}) {\n  return (\n    <Suspense fallback={null}>\n      <MetadataOutlet promise={promise} />\n    </Suspense>\n  )\n}\n"], "names": ["AsyncMetadataOutlet", "MetadataOutlet", "promise", "error", "digest", "use", "Suspense", "fallback"], "mappings": ";;;+BAsBgBA,uBAAAA;;;eAAAA;;;;uBApBc;AAG9B,SAASC,eAAe,KAIvB;IAJuB,IAAA,EACtBC,OAAO,EAGR,GAJuB;IAKtB,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAE,GAAGC,CAAAA,GAAAA,OAAAA,GAAG,EAACH;IAC9B,IAAIC,OAAO;QACT,IAAIC,QAAQ;YACV,2FAA2F;YAC3F,mGAAmG;;YACjGD,MAAcC,MAAM,GAAGA;QAC3B;QACA,MAAMD;IACR;IACA,OAAO;AACT;AAEO,SAASH,oBAAoB,KAInC;IAJmC,IAAA,EAClCE,OAAO,EAGR,GAJmC;IAKlC,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACI,OAAAA,QAAQ,EAAA;QAACC,UAAU;kBAClB,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACN,gBAAAA;YAAeC,SAASA;;;AAG/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5655, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/node_modules/next/src/client/components/metadata/metadata-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../../lib/metadata/metadata-constants'\n\n// We use a namespace object to allow us to recover the name of the function\n// at runtime even when production bundling/minification is used.\nconst NameSpace = {\n  [METADATA_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n  [VIEWPORT_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n  [OUTLET_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: React.ReactNode\n  }) {\n    return children\n  },\n}\n\nexport const MetadataBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[METADATA_BOUNDARY_NAME.slice(0) as typeof METADATA_BOUNDARY_NAME]\n\nexport const ViewportBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[VIEWPORT_BOUNDARY_NAME.slice(0) as typeof VIEWPORT_BOUNDARY_NAME]\n\nexport const OutletBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[OUTLET_BOUNDARY_NAME.slice(0) as typeof OUTLET_BOUNDARY_NAME]\n"], "names": ["MetadataBoundary", "OutletBoundary", "ViewportBoundary", "NameSpace", "METADATA_BOUNDARY_NAME", "children", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "slice"], "mappings": ";;;;;;;;;;;;;;;IAkCaA,gBAAgB,EAAA;eAAhBA;;IAUAC,cAAc,EAAA;eAAdA;;IALAC,gBAAgB,EAAA;eAAhBA;;;mCAjCN;AAEP,4EAA4E;AAC5E,iEAAiE;AACjE,MAAMC,YAAY;IAChB,CAACC,mBAAAA,sBAAsB,CAAC,EAAE,SAAU,KAInC;QAJmC,IAAA,EAClCC,QAAQ,EAGT,GAJmC;QAKlC,OAAOA;IACT;IACA,CAACC,mBAAAA,sBAAsB,CAAC,EAAE,SAAU,KAInC;QAJmC,IAAA,EAClCD,QAAQ,EAGT,GAJmC;QAKlC,OAAOA;IACT;IACA,CAACE,mBAAAA,oBAAoB,CAAC,EAAE,SAAU,KAIjC;QAJiC,IAAA,EAChCF,QAAQ,EAGT,GAJiC;QAKhC,OAAOA;IACT;AACF;AAEO,MAAML,mBACX,AACA,4DAA4D,oBADoB;AAEhFG,SAAS,CAACC,mBAAAA,sBAAsB,CAACI,KAAK,CAAC,GAAoC;AAEtE,MAAMN,mBACX,AACA,4DAA4D,oBADoB;AAEhFC,SAAS,CAACG,mBAAAA,sBAAsB,CAACE,KAAK,CAAC,GAAoC;AAEtE,MAAMP,iBACX,AACA,4DAA4D,oBADoB;AAEhFE,SAAS,CAACI,mBAAAA,oBAAoB,CAACC,KAAK,CAAC,GAAkC", "ignoreList": [0], "debugId": null}}]}