"use client"

import { useState } from "react"
import { Employee, Department } from "@/lib/types"
import { useHRStore } from "@/lib/store/hr-store"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { formatDate } from "@/lib/utils"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Checkbox } from "@/components/ui/checkbox"
import { MoreHorizontal, Edit, Trash2, ArrowRight } from "lucide-react"

interface EmployeeTableProps {
  employees: Employee[]
  departments: Department[]
}

export function EmployeeTable({ employees, departments }: EmployeeTableProps) {
  const { 
    selectedEmployees, 
    toggleEmployeeSelection, 
    selectAllEmployees, 
    clearSelection 
  } = useHRStore()

  const isAllSelected = employees.length > 0 && selectedEmployees.length === employees.length
  const isPartiallySelected = selectedEmployees.length > 0 && selectedEmployees.length < employees.length

  const handleSelectAll = () => {
    if (isAllSelected) {
      clearSelection()
    } else {
      selectAllEmployees(employees.map(emp => emp.id))
    }
  }

  const getDepartmentName = (departmentId: string | null) => {
    if (!departmentId) return "Unassigned"
    const department = departments.find(dept => dept.id === departmentId)
    return department?.name || "Unknown"
  }

  const getStatusBadge = (status: Employee['status']) => {
    const variants = {
      ACTIVE: "default",
      TRANSFERRED: "secondary",
      PENDING_REMOVAL: "destructive",
      ARCHIVED: "outline"
    } as const

    return (
      <Badge variant={variants[status]}>
        {status.replace('_', ' ')}
      </Badge>
    )
  }

  const handleEdit = (employee: Employee) => {
    // TODO: Implement edit functionality
    console.log('Edit employee:', employee.id)
  }

  const handleDelete = (employee: Employee) => {
    // TODO: Implement delete functionality
    console.log('Delete employee:', employee.id)
  }

  const handleTransfer = (employee: Employee) => {
    // TODO: Implement transfer functionality
    console.log('Transfer employee:', employee.id)
  }

  if (employees.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No employees found.</p>
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={isAllSelected}
                indeterminate={isPartiallySelected}
                onCheckedChange={handleSelectAll}
                aria-label="Select all employees"
              />
            </TableHead>
            <TableHead>Employee ID</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Department</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Hire Date</TableHead>
            <TableHead className="w-12"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {employees.map((employee) => (
            <TableRow key={employee.id}>
              <TableCell>
                <Checkbox
                  checked={selectedEmployees.includes(employee.id)}
                  onCheckedChange={() => toggleEmployeeSelection(employee.id)}
                  aria-label={`Select ${employee.name}`}
                />
              </TableCell>
              <TableCell className="font-mono text-sm">
                {employee.id}
              </TableCell>
              <TableCell className="font-medium">
                {employee.name}
              </TableCell>
              <TableCell className="text-muted-foreground">
                {employee.email}
              </TableCell>
              <TableCell>
                {getDepartmentName(employee.departmentId)}
              </TableCell>
              <TableCell>
                {getStatusBadge(employee.status)}
              </TableCell>
              <TableCell className="text-muted-foreground">
                {formatDate(employee.hireDate)}
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuItem onClick={() => handleEdit(employee)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit Employee
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleTransfer(employee)}>
                      <ArrowRight className="mr-2 h-4 w-4" />
                      Transfer
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => handleDelete(employee)}
                      className="text-destructive"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
