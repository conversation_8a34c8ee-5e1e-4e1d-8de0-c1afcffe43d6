import { Department, Employee } from './types'

export const sampleDepartments: Department[] = [
  {
    id: 'dept-001',
    name: 'Engineering',
    code: 'ENG',
    capacity: 25,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
  },
  {
    id: 'dept-002',
    name: 'Marketing',
    code: 'MKT',
    capacity: 15,
    createdAt: new Date('2024-01-16'),
    updatedAt: new Date('2024-01-16'),
  },
  {
    id: 'dept-003',
    name: 'Sales',
    code: 'SAL',
    capacity: 20,
    createdAt: new Date('2024-01-17'),
    updatedAt: new Date('2024-01-17'),
  },
  {
    id: 'dept-004',
    name: 'Human Resources',
    code: 'HR',
    capacity: 8,
    createdAt: new Date('2024-01-18'),
    updatedAt: new Date('2024-01-18'),
  },
  {
    id: 'dept-005',
    name: 'Finance',
    code: 'FIN',
    capacity: 12,
    createdAt: new Date('2024-01-19'),
    updatedAt: new Date('2024-01-19'),
  },
  {
    id: 'dept-006',
    name: 'Operations',
    code: 'OPS',
    capacity: 18,
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20'),
  },
]

export const sampleEmployees: Employee[] = [
  // Engineering Department
  {
    id: 'ENG-001',
    name: 'John Smith',
    email: '<EMAIL>',
    departmentId: 'dept-001',
    status: 'ACTIVE',
    hireDate: new Date('2023-03-15'),
    transferHistory: [],
    createdAt: new Date('2023-03-15'),
    updatedAt: new Date('2023-03-15'),
  },
  {
    id: 'ENG-002',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    departmentId: 'dept-001',
    status: 'ACTIVE',
    hireDate: new Date('2023-05-20'),
    transferHistory: [],
    createdAt: new Date('2023-05-20'),
    updatedAt: new Date('2023-05-20'),
  },
  {
    id: 'ENG-003',
    name: 'Michael Chen',
    email: '<EMAIL>',
    departmentId: 'dept-001',
    status: 'ACTIVE',
    hireDate: new Date('2023-07-10'),
    transferHistory: [],
    createdAt: new Date('2023-07-10'),
    updatedAt: new Date('2023-07-10'),
  },
  {
    id: 'ENG-004',
    name: 'Emily Davis',
    email: '<EMAIL>',
    departmentId: 'dept-001',
    status: 'ACTIVE',
    hireDate: new Date('2023-09-05'),
    transferHistory: [],
    createdAt: new Date('2023-09-05'),
    updatedAt: new Date('2023-09-05'),
  },
  {
    id: 'ENG-005',
    name: 'David Wilson',
    email: '<EMAIL>',
    departmentId: 'dept-001',
    status: 'ACTIVE',
    hireDate: new Date('2023-11-12'),
    transferHistory: [],
    createdAt: new Date('2023-11-12'),
    updatedAt: new Date('2023-11-12'),
  },
  {
    id: 'ENG-006',
    name: 'Lisa Anderson',
    email: '<EMAIL>',
    departmentId: 'dept-001',
    status: 'ACTIVE',
    hireDate: new Date('2024-01-08'),
    transferHistory: [],
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-08'),
  },
  {
    id: 'ENG-007',
    name: 'Robert Taylor',
    email: '<EMAIL>',
    departmentId: 'dept-001',
    status: 'ACTIVE',
    hireDate: new Date('2024-02-14'),
    transferHistory: [],
    createdAt: new Date('2024-02-14'),
    updatedAt: new Date('2024-02-14'),
  },
  {
    id: 'ENG-008',
    name: 'Jennifer Brown',
    email: '<EMAIL>',
    departmentId: 'dept-001',
    status: 'ACTIVE',
    hireDate: new Date('2024-03-22'),
    transferHistory: [],
    createdAt: new Date('2024-03-22'),
    updatedAt: new Date('2024-03-22'),
  },
  {
    id: 'ENG-009',
    name: 'Christopher Lee',
    email: '<EMAIL>',
    departmentId: 'dept-001',
    status: 'ACTIVE',
    hireDate: new Date('2024-04-18'),
    transferHistory: [],
    createdAt: new Date('2024-04-18'),
    updatedAt: new Date('2024-04-18'),
  },
  {
    id: 'ENG-010',
    name: 'Amanda White',
    email: '<EMAIL>',
    departmentId: 'dept-001',
    status: 'ACTIVE',
    hireDate: new Date('2024-05-25'),
    transferHistory: [],
    createdAt: new Date('2024-05-25'),
    updatedAt: new Date('2024-05-25'),
  },

  // Marketing Department
  {
    id: 'MKT-001',
    name: 'Jessica Garcia',
    email: '<EMAIL>',
    departmentId: 'dept-002',
    status: 'ACTIVE',
    hireDate: new Date('2023-04-10'),
    transferHistory: [],
    createdAt: new Date('2023-04-10'),
    updatedAt: new Date('2023-04-10'),
  },
  {
    id: 'MKT-002',
    name: 'Daniel Martinez',
    email: '<EMAIL>',
    departmentId: 'dept-002',
    status: 'ACTIVE',
    hireDate: new Date('2023-06-15'),
    transferHistory: [],
    createdAt: new Date('2023-06-15'),
    updatedAt: new Date('2023-06-15'),
  },
  {
    id: 'MKT-003',
    name: 'Ashley Rodriguez',
    email: '<EMAIL>',
    departmentId: 'dept-002',
    status: 'ACTIVE',
    hireDate: new Date('2023-08-20'),
    transferHistory: [],
    createdAt: new Date('2023-08-20'),
    updatedAt: new Date('2023-08-20'),
  },
  {
    id: 'MKT-004',
    name: 'Matthew Thompson',
    email: '<EMAIL>',
    departmentId: 'dept-002',
    status: 'ACTIVE',
    hireDate: new Date('2023-10-12'),
    transferHistory: [],
    createdAt: new Date('2023-10-12'),
    updatedAt: new Date('2023-10-12'),
  },
  {
    id: 'MKT-005',
    name: 'Stephanie Clark',
    email: '<EMAIL>',
    departmentId: 'dept-002',
    status: 'ACTIVE',
    hireDate: new Date('2024-01-30'),
    transferHistory: [],
    createdAt: new Date('2024-01-30'),
    updatedAt: new Date('2024-01-30'),
  },
  {
    id: 'MKT-006',
    name: 'Kevin Lewis',
    email: '<EMAIL>',
    departmentId: 'dept-002',
    status: 'ACTIVE',
    hireDate: new Date('2024-03-15'),
    transferHistory: [],
    createdAt: new Date('2024-03-15'),
    updatedAt: new Date('2024-03-15'),
  },

  // Sales Department
  {
    id: 'SAL-001',
    name: 'Ryan Walker',
    email: '<EMAIL>',
    departmentId: 'dept-003',
    status: 'ACTIVE',
    hireDate: new Date('2023-02-28'),
    transferHistory: [],
    createdAt: new Date('2023-02-28'),
    updatedAt: new Date('2023-02-28'),
  },
  {
    id: 'SAL-002',
    name: 'Nicole Hall',
    email: '<EMAIL>',
    departmentId: 'dept-003',
    status: 'ACTIVE',
    hireDate: new Date('2023-05-10'),
    transferHistory: [],
    createdAt: new Date('2023-05-10'),
    updatedAt: new Date('2023-05-10'),
  },
  {
    id: 'SAL-003',
    name: 'Brandon Allen',
    email: '<EMAIL>',
    departmentId: 'dept-003',
    status: 'ACTIVE',
    hireDate: new Date('2023-07-25'),
    transferHistory: [],
    createdAt: new Date('2023-07-25'),
    updatedAt: new Date('2023-07-25'),
  },
  {
    id: 'SAL-004',
    name: 'Megan Young',
    email: '<EMAIL>',
    departmentId: 'dept-003',
    status: 'ACTIVE',
    hireDate: new Date('2023-09-18'),
    transferHistory: [],
    createdAt: new Date('2023-09-18'),
    updatedAt: new Date('2023-09-18'),
  },
  {
    id: 'SAL-005',
    name: 'Tyler King',
    email: '<EMAIL>',
    departmentId: 'dept-003',
    status: 'ACTIVE',
    hireDate: new Date('2023-11-30'),
    transferHistory: [],
    createdAt: new Date('2023-11-30'),
    updatedAt: new Date('2023-11-30'),
  },
  {
    id: 'SAL-006',
    name: 'Rachel Wright',
    email: '<EMAIL>',
    departmentId: 'dept-003',
    status: 'ACTIVE',
    hireDate: new Date('2024-02-05'),
    transferHistory: [],
    createdAt: new Date('2024-02-05'),
    updatedAt: new Date('2024-02-05'),
  },
  {
    id: 'SAL-007',
    name: 'Justin Lopez',
    email: '<EMAIL>',
    departmentId: 'dept-003',
    status: 'ACTIVE',
    hireDate: new Date('2024-04-12'),
    transferHistory: [],
    createdAt: new Date('2024-04-12'),
    updatedAt: new Date('2024-04-12'),
  },

  // HR Department
  {
    id: 'HR-001',
    name: 'Laura Hill',
    email: '<EMAIL>',
    departmentId: 'dept-004',
    status: 'ACTIVE',
    hireDate: new Date('2023-01-20'),
    transferHistory: [],
    createdAt: new Date('2023-01-20'),
    updatedAt: new Date('2023-01-20'),
  },
  {
    id: 'HR-002',
    name: 'Steven Green',
    email: '<EMAIL>',
    departmentId: 'dept-004',
    status: 'ACTIVE',
    hireDate: new Date('2023-06-08'),
    transferHistory: [],
    createdAt: new Date('2023-06-08'),
    updatedAt: new Date('2023-06-08'),
  },
  {
    id: 'HR-003',
    name: 'Kimberly Adams',
    email: '<EMAIL>',
    departmentId: 'dept-004',
    status: 'ACTIVE',
    hireDate: new Date('2023-10-25'),
    transferHistory: [],
    createdAt: new Date('2023-10-25'),
    updatedAt: new Date('2023-10-25'),
  },

  // Finance Department
  {
    id: 'FIN-001',
    name: 'Andrew Baker',
    email: '<EMAIL>',
    departmentId: 'dept-005',
    status: 'ACTIVE',
    hireDate: new Date('2023-03-08'),
    transferHistory: [],
    createdAt: new Date('2023-03-08'),
    updatedAt: new Date('2023-03-08'),
  },
  {
    id: 'FIN-002',
    name: 'Michelle Nelson',
    email: '<EMAIL>',
    departmentId: 'dept-005',
    status: 'ACTIVE',
    hireDate: new Date('2023-07-15'),
    transferHistory: [],
    createdAt: new Date('2023-07-15'),
    updatedAt: new Date('2023-07-15'),
  },
  {
    id: 'FIN-003',
    name: 'Joshua Carter',
    email: '<EMAIL>',
    departmentId: 'dept-005',
    status: 'ACTIVE',
    hireDate: new Date('2023-11-20'),
    transferHistory: [],
    createdAt: new Date('2023-11-20'),
    updatedAt: new Date('2023-11-20'),
  },
  {
    id: 'FIN-004',
    name: 'Samantha Mitchell',
    email: '<EMAIL>',
    departmentId: 'dept-005',
    status: 'ACTIVE',
    hireDate: new Date('2024-02-28'),
    transferHistory: [],
    createdAt: new Date('2024-02-28'),
    updatedAt: new Date('2024-02-28'),
  },

  // Operations Department
  {
    id: 'OPS-001',
    name: 'Gregory Perez',
    email: '<EMAIL>',
    departmentId: 'dept-006',
    status: 'ACTIVE',
    hireDate: new Date('2023-04-05'),
    transferHistory: [],
    createdAt: new Date('2023-04-05'),
    updatedAt: new Date('2023-04-05'),
  },
  {
    id: 'OPS-002',
    name: 'Heather Roberts',
    email: '<EMAIL>',
    departmentId: 'dept-006',
    status: 'ACTIVE',
    hireDate: new Date('2023-08-12'),
    transferHistory: [],
    createdAt: new Date('2023-08-12'),
    updatedAt: new Date('2023-08-12'),
  },
  {
    id: 'OPS-003',
    name: 'Nathan Turner',
    email: '<EMAIL>',
    departmentId: 'dept-006',
    status: 'ACTIVE',
    hireDate: new Date('2023-12-01'),
    transferHistory: [],
    createdAt: new Date('2023-12-01'),
    updatedAt: new Date('2023-12-01'),
  },
  {
    id: 'OPS-004',
    name: 'Brittany Phillips',
    email: '<EMAIL>',
    departmentId: 'dept-006',
    status: 'ACTIVE',
    hireDate: new Date('2024-03-10'),
    transferHistory: [],
    createdAt: new Date('2024-03-10'),
    updatedAt: new Date('2024-03-10'),
  },

  // Free Bucket (Unassigned) Employees
  {
    id: 'FB-001',
    name: 'Alex Campbell',
    email: '<EMAIL>',
    departmentId: null,
    status: 'ACTIVE',
    hireDate: new Date('2024-06-01'),
    transferHistory: [],
    createdAt: new Date('2024-06-01'),
    updatedAt: new Date('2024-06-01'),
  },
  {
    id: 'FB-002',
    name: 'Morgan Parker',
    email: '<EMAIL>',
    departmentId: null,
    status: 'ACTIVE',
    hireDate: new Date('2024-06-15'),
    transferHistory: [],
    createdAt: new Date('2024-06-15'),
    updatedAt: new Date('2024-06-15'),
  },
]

// Helper function to populate departments with their employees
export const getDepartmentsWithEmployees = (): Department[] => {
  return sampleDepartments.map(dept => ({
    ...dept,
    employees: sampleEmployees.filter(emp => emp.departmentId === dept.id)
  }))
}

// Helper function to get free bucket employees
export const getFreeBucketEmployees = (): Employee[] => {
  return sampleEmployees.filter(emp => emp.departmentId === null)
}
