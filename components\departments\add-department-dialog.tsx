"use client"

import { useState } from "react"
import { Department } from "@/lib/types"
import { useHRStore } from "@/lib/store/hr-store"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

interface AddDepartmentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AddDepartmentDialog({ 
  open, 
  onOpenChange 
}: AddDepartmentDialogProps) {
  const { departments, setDepartments } = useHRStore()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    capacity: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Department name is required'
    }

    if (!formData.code.trim()) {
      newErrors.code = 'Department code is required'
    } else if (formData.code.length > 5) {
      newErrors.code = 'Department code must be 5 characters or less'
    } else if (departments.some(dept => dept.code.toLowerCase() === formData.code.toLowerCase())) {
      newErrors.code = 'Department code already exists'
    }

    if (!formData.capacity.trim()) {
      newErrors.capacity = 'Capacity is required'
    } else {
      const capacity = parseInt(formData.capacity)
      if (isNaN(capacity) || capacity <= 0) {
        newErrors.capacity = 'Capacity must be a positive number'
      } else if (capacity > 1000) {
        newErrors.capacity = 'Capacity cannot exceed 1000'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsLoading(true)

    try {
      // Generate department ID
      const existingIds = departments.map(dept => 
        parseInt(dept.id.replace('dept-', '')) || 0
      )
      const nextId = Math.max(0, ...existingIds) + 1
      const departmentId = `dept-${nextId.toString().padStart(3, '0')}`

      const newDepartment: Department = {
        id: departmentId,
        name: formData.name.trim(),
        code: formData.code.trim().toUpperCase(),
        capacity: parseInt(formData.capacity),
        employees: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      // Add to departments list
      setDepartments([...departments, newDepartment])
      
      // Reset form
      setFormData({
        name: '',
        code: '',
        capacity: ''
      })
      setErrors({})
      
      onOpenChange(false)
    } catch (error) {
      console.error('Failed to add department:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const handleClose = () => {
    setFormData({
      name: '',
      code: '',
      capacity: ''
    })
    setErrors({})
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add New Department</DialogTitle>
          <DialogDescription>
            Create a new department with a specific capacity for employees.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Department Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter department name"
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name}</p>
              )}
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="code">Department Code</Label>
              <Input
                id="code"
                value={formData.code}
                onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}
                placeholder="Enter department code (e.g., ENG)"
                maxLength={5}
                className={errors.code ? 'border-red-500' : ''}
              />
              {errors.code && (
                <p className="text-sm text-red-500">{errors.code}</p>
              )}
              <p className="text-xs text-muted-foreground">
                Maximum 5 characters, will be converted to uppercase
              </p>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="capacity">Capacity</Label>
              <Input
                id="capacity"
                type="number"
                value={formData.capacity}
                onChange={(e) => handleInputChange('capacity', e.target.value)}
                placeholder="Enter maximum number of employees"
                min="1"
                max="1000"
                className={errors.capacity ? 'border-red-500' : ''}
              />
              {errors.capacity && (
                <p className="text-sm text-red-500">{errors.capacity}</p>
              )}
              <p className="text-xs text-muted-foreground">
                Maximum number of employees this department can hold
              </p>
            </div>
          </div>
          
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Creating...' : 'Create Department'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
