"use client"

import { useState, useRef } from "react"
import { Department, Employee, CSVImportResult } from "@/lib/types"
import { useHRStore } from "@/lib/store/hr-store"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { generateEmployeeId } from "@/lib/utils"
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertCircle,
  Download
} from "lucide-react"

interface CSVImportSectionProps {
  departments: Department[]
}

export function CSVImportSection({ departments }: CSVImportSectionProps) {
  const { employees, addEmployee } = useHRStore()
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [importResult, setImportResult] = useState<CSVImportResult | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.name.endsWith('.csv')) {
      alert('Please select a CSV file')
      return
    }

    setIsUploading(true)
    setUploadProgress(0)
    setImportResult(null)

    try {
      const text = await file.text()
      const result = await processCSV(text)
      setImportResult(result)
    } catch (error) {
      console.error('CSV import failed:', error)
      alert('Failed to import CSV file')
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const processCSV = async (csvText: string): Promise<CSVImportResult> => {
    const lines = csvText.trim().split('\n')
    const headers = lines[0].split(',').map(h => h.trim().toLowerCase())
    
    // Validate required headers
    const requiredHeaders = ['name', 'email']
    const missingHeaders = requiredHeaders.filter(h => !headers.includes(h))
    
    if (missingHeaders.length > 0) {
      throw new Error(`Missing required headers: ${missingHeaders.join(', ')}`)
    }

    const result: CSVImportResult = {
      success: [],
      errors: []
    }

    // Process each row
    for (let i = 1; i < lines.length; i++) {
      setUploadProgress((i / (lines.length - 1)) * 100)
      
      const values = lines[i].split(',').map(v => v.trim())
      const rowData: Record<string, string> = {}
      
      headers.forEach((header, index) => {
        rowData[header] = values[index] || ''
      })

      try {
        // Validate required fields
        if (!rowData.name || !rowData.email) {
          throw new Error('Name and email are required')
        }

        // Check if email already exists
        if (employees.some(emp => emp.email.toLowerCase() === rowData.email.toLowerCase())) {
          throw new Error('Email already exists')
        }

        // Find department if specified
        let departmentId: string | null = null
        if (rowData.departmentcode) {
          const department = departments.find(d => 
            d.code.toLowerCase() === rowData.departmentcode.toLowerCase()
          )
          if (!department) {
            throw new Error(`Department with code '${rowData.departmentcode}' not found`)
          }
          departmentId = department.id
        }

        // Generate employee ID
        const department = departments.find(d => d.id === departmentId)
        const departmentCode = department?.code || 'GEN'
        const existingIds = employees
          .filter(emp => emp.id.startsWith(departmentCode))
          .map(emp => parseInt(emp.id.split('-')[1]) || 0)
        const nextSequence = Math.max(0, ...existingIds) + result.success.length + 1
        const employeeId = generateEmployeeId(departmentCode, nextSequence)

        // Validate status if provided
        const validStatuses = ['ACTIVE', 'TRANSFERRED', 'PENDING_REMOVAL', 'ARCHIVED']
        const status = rowData.status?.toUpperCase() || 'ACTIVE'
        if (!validStatuses.includes(status)) {
          throw new Error(`Invalid status '${rowData.status}'. Must be one of: ${validStatuses.join(', ')}`)
        }

        const newEmployee: Employee = {
          id: employeeId,
          name: rowData.name,
          email: rowData.email,
          departmentId,
          status: status as Employee['status'],
          hireDate: new Date(),
          transferHistory: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        }

        result.success.push(newEmployee)
      } catch (error) {
        result.errors.push({
          row: i + 1,
          data: rowData,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }

      // Small delay to show progress
      await new Promise(resolve => setTimeout(resolve, 10))
    }

    return result
  }

  const handleConfirmImport = () => {
    if (!importResult) return

    // Add all successful employees
    importResult.success.forEach(employee => {
      addEmployee(employee)
    })

    alert(`Successfully imported ${importResult.success.length} employees`)
    setImportResult(null)
  }

  const downloadTemplate = () => {
    const template = [
      'name,email,departmentcode,status',
      'John Doe,<EMAIL>,ENG,ACTIVE',
      'Jane Smith,<EMAIL>,MKT,ACTIVE',
      'Bob Johnson,<EMAIL>,,ACTIVE'
    ].join('\n')

    const blob = new Blob([template], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'employee-import-template.csv'
    a.click()
    URL.revokeObjectURL(url)
  }

  return (
    <div className="space-y-6">
      {/* Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload CSV File
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <Button onClick={handleFileSelect} disabled={isUploading}>
                <FileText className="h-4 w-4 mr-2" />
                Select CSV File
              </Button>
              
              <Button variant="outline" onClick={downloadTemplate}>
                <Download className="h-4 w-4 mr-2" />
                Download Template
              </Button>
            </div>

            {isUploading && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Processing CSV...</span>
                  <span>{Math.round(uploadProgress)}%</span>
                </div>
                <Progress value={uploadProgress} />
              </div>
            )}

            <input
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleFileChange}
              className="hidden"
            />
          </div>
        </CardContent>
      </Card>

      {/* Import Results */}
      {importResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Import Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex gap-4">
                <Badge variant="default" className="bg-green-100 text-green-800">
                  {importResult.success.length} Successful
                </Badge>
                {importResult.errors.length > 0 && (
                  <Badge variant="destructive">
                    {importResult.errors.length} Errors
                  </Badge>
                )}
              </div>

              {importResult.errors.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-red-800">Errors:</h4>
                  <div className="max-h-40 overflow-y-auto space-y-1">
                    {importResult.errors.map((error, index) => (
                      <div key={index} className="text-sm p-2 bg-red-50 rounded border-l-4 border-red-400">
                        <div className="font-medium">Row {error.row}:</div>
                        <div className="text-red-700">{error.error}</div>
                        <div className="text-xs text-red-600 mt-1">
                          Data: {JSON.stringify(error.data)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {importResult.success.length > 0 && (
                <div className="flex gap-2">
                  <Button onClick={handleConfirmImport}>
                    Import {importResult.success.length} Employees
                  </Button>
                  <Button variant="outline" onClick={() => setImportResult(null)}>
                    Cancel
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="text-blue-800">CSV Format Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-blue-700">
            <p><strong>Required columns:</strong> name, email</p>
            <p><strong>Optional columns:</strong> departmentcode, status</p>
            <p><strong>Department codes:</strong> {departments.map(d => d.code).join(', ')}</p>
            <p><strong>Valid statuses:</strong> ACTIVE, TRANSFERRED, PENDING_REMOVAL, ARCHIVED</p>
            <p><strong>Note:</strong> If departmentcode is empty, employee will be added to the free bucket</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
