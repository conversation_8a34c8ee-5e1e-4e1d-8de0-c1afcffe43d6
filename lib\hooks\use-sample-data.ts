import { useEffect } from 'react'
import { useHRStore } from '@/lib/store/hr-store'
import { 
  sampleEmployees, 
  getDepartmentsWithEmployees, 
  getFreeBucketEmployees 
} from '@/lib/sample-data'

export function useSampleData() {
  const { setEmployees, setDepartments } = useHRStore()

  useEffect(() => {
    // Initialize with sample data
    const departmentsWithEmployees = getDepartmentsWithEmployees()
    const freeBucketEmployees = getFreeBucketEmployees()
    
    setEmployees(sampleEmployees)
    setDepartments(departmentsWithEmployees)
    
    // Note: Free bucket employees are automatically calculated in the store
    // based on employees with departmentId === null
    
    console.log('Sample data loaded:', {
      employees: sampleEmployees.length,
      departments: departmentsWithEmployees.length,
      freeBucket: freeBucketEmployees.length
    })
  }, [setEmployees, setDepartments])
}
