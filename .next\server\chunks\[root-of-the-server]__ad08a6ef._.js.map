{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\"\nimport Credential<PERSON><PERSON>rovider from \"next-auth/providers/credentials\"\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: \"credentials\",\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        // For demo purposes, use hardcoded credentials\n        // In production, this would check against a database\n        if (credentials.email === \"<EMAIL>\" && credentials.password === \"admin123\") {\n          return {\n            id: \"1\",\n            email: \"<EMAIL>\",\n            name: \"Admin User\",\n            role: \"ADMIN\",\n          }\n        }\n\n        if (credentials.email === \"<EMAIL>\" && credentials.password === \"hr123\") {\n          return {\n            id: \"2\",\n            email: \"<EMAIL>\",\n            name: \"HR Manager\",\n            role: \"HR_MANAGER\",\n          }\n        }\n\n        return null\n      }\n    })\n  ],\n  session: {\n    strategy: \"jwt\"\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: \"/auth/signin\",\n    error: \"/auth/error\"\n  }\n}"], "names": [], "mappings": ";;;AACA;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,+CAA+C;gBAC/C,qDAAqD;gBACrD,IAAI,YAAY,KAAK,KAAK,uBAAuB,YAAY,QAAQ,KAAK,YAAY;oBACpF,OAAO;wBACL,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,MAAM;oBACR;gBACF;gBAEA,IAAI,YAAY,KAAK,KAAK,oBAAoB,YAAY,QAAQ,KAAK,SAAS;oBAC9E,OAAO;wBACL,IAAI;wBACJ,OAAO;wBACP,MAAM;wBACN,MAAM;oBACR;gBACF;gBAEA,OAAO;YACT;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from \"next-auth\"\nimport { authOptions } from \"@/lib/auth\"\n\nconst handler = NextAuth(authOptions)\n\nexport { handler as GET, handler as POST }"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,6GAAA,CAAA,cAAW", "debugId": null}}]}