"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[816],{198:(e,t,a)=>{a.d(t,{Vy:()=>m,Yq:()=>i,cn:()=>d,pp:()=>s});var r=a(5403),n=a(9055);function d(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.QP)((0,r.$)(t))}function s(e,t){return"".concat(e,"-").concat(t.toString().padStart(3,"0"))}function i(e){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(e)}function m(e,t){return Math.round(e/t*100)}},425:(e,t,a)=>{a.d(t,{$:()=>o});var r=a(5155),n=a(2115),d=a(5441),s=a(1335),i=a(198);let m=(0,s.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=n.forwardRef((e,t)=>{let{className:a,variant:n,size:s,asChild:o=!1,...p}=e,l=o?d.DX:"button";return(0,r.jsx)(l,{className:(0,i.cn)(m({variant:n,size:s,className:a})),ref:t,...p})});o.displayName="Button"},538:(e,t,a)=>{a.d(t,{s:()=>m});var r=a(9749),n=a(2115),d=a(4373);let s=[{id:"dept-001",name:"Engineering",code:"ENG",capacity:25,createdAt:new Date("2024-01-15"),updatedAt:new Date("2024-01-15")},{id:"dept-002",name:"Marketing",code:"MKT",capacity:15,createdAt:new Date("2024-01-16"),updatedAt:new Date("2024-01-16")},{id:"dept-003",name:"Sales",code:"SAL",capacity:20,createdAt:new Date("2024-01-17"),updatedAt:new Date("2024-01-17")},{id:"dept-004",name:"Human Resources",code:"HR",capacity:8,createdAt:new Date("2024-01-18"),updatedAt:new Date("2024-01-18")},{id:"dept-005",name:"Finance",code:"FIN",capacity:12,createdAt:new Date("2024-01-19"),updatedAt:new Date("2024-01-19")},{id:"dept-006",name:"Operations",code:"OPS",capacity:18,createdAt:new Date("2024-01-20"),updatedAt:new Date("2024-01-20")}],i=[{id:"ENG-001",name:"John Smith",email:"<EMAIL>",departmentId:"dept-001",status:"ACTIVE",hireDate:new Date("2023-03-15"),transferHistory:[],createdAt:new Date("2023-03-15"),updatedAt:new Date("2023-03-15")},{id:"ENG-002",name:"Sarah Johnson",email:"<EMAIL>",departmentId:"dept-001",status:"ACTIVE",hireDate:new Date("2023-05-20"),transferHistory:[],createdAt:new Date("2023-05-20"),updatedAt:new Date("2023-05-20")},{id:"ENG-003",name:"Michael Chen",email:"<EMAIL>",departmentId:"dept-001",status:"ACTIVE",hireDate:new Date("2023-07-10"),transferHistory:[],createdAt:new Date("2023-07-10"),updatedAt:new Date("2023-07-10")},{id:"ENG-004",name:"Emily Davis",email:"<EMAIL>",departmentId:"dept-001",status:"ACTIVE",hireDate:new Date("2023-09-05"),transferHistory:[],createdAt:new Date("2023-09-05"),updatedAt:new Date("2023-09-05")},{id:"ENG-005",name:"David Wilson",email:"<EMAIL>",departmentId:"dept-001",status:"ACTIVE",hireDate:new Date("2023-11-12"),transferHistory:[],createdAt:new Date("2023-11-12"),updatedAt:new Date("2023-11-12")},{id:"ENG-006",name:"Lisa Anderson",email:"<EMAIL>",departmentId:"dept-001",status:"ACTIVE",hireDate:new Date("2024-01-08"),transferHistory:[],createdAt:new Date("2024-01-08"),updatedAt:new Date("2024-01-08")},{id:"ENG-007",name:"Robert Taylor",email:"<EMAIL>",departmentId:"dept-001",status:"ACTIVE",hireDate:new Date("2024-02-14"),transferHistory:[],createdAt:new Date("2024-02-14"),updatedAt:new Date("2024-02-14")},{id:"ENG-008",name:"Jennifer Brown",email:"<EMAIL>",departmentId:"dept-001",status:"ACTIVE",hireDate:new Date("2024-03-22"),transferHistory:[],createdAt:new Date("2024-03-22"),updatedAt:new Date("2024-03-22")},{id:"ENG-009",name:"Christopher Lee",email:"<EMAIL>",departmentId:"dept-001",status:"ACTIVE",hireDate:new Date("2024-04-18"),transferHistory:[],createdAt:new Date("2024-04-18"),updatedAt:new Date("2024-04-18")},{id:"ENG-010",name:"Amanda White",email:"<EMAIL>",departmentId:"dept-001",status:"ACTIVE",hireDate:new Date("2024-05-25"),transferHistory:[],createdAt:new Date("2024-05-25"),updatedAt:new Date("2024-05-25")},{id:"MKT-001",name:"Jessica Garcia",email:"<EMAIL>",departmentId:"dept-002",status:"ACTIVE",hireDate:new Date("2023-04-10"),transferHistory:[],createdAt:new Date("2023-04-10"),updatedAt:new Date("2023-04-10")},{id:"MKT-002",name:"Daniel Martinez",email:"<EMAIL>",departmentId:"dept-002",status:"ACTIVE",hireDate:new Date("2023-06-15"),transferHistory:[],createdAt:new Date("2023-06-15"),updatedAt:new Date("2023-06-15")},{id:"MKT-003",name:"Ashley Rodriguez",email:"<EMAIL>",departmentId:"dept-002",status:"ACTIVE",hireDate:new Date("2023-08-20"),transferHistory:[],createdAt:new Date("2023-08-20"),updatedAt:new Date("2023-08-20")},{id:"MKT-004",name:"Matthew Thompson",email:"<EMAIL>",departmentId:"dept-002",status:"ACTIVE",hireDate:new Date("2023-10-12"),transferHistory:[],createdAt:new Date("2023-10-12"),updatedAt:new Date("2023-10-12")},{id:"MKT-005",name:"Stephanie Clark",email:"<EMAIL>",departmentId:"dept-002",status:"ACTIVE",hireDate:new Date("2024-01-30"),transferHistory:[],createdAt:new Date("2024-01-30"),updatedAt:new Date("2024-01-30")},{id:"MKT-006",name:"Kevin Lewis",email:"<EMAIL>",departmentId:"dept-002",status:"ACTIVE",hireDate:new Date("2024-03-15"),transferHistory:[],createdAt:new Date("2024-03-15"),updatedAt:new Date("2024-03-15")},{id:"SAL-001",name:"Ryan Walker",email:"<EMAIL>",departmentId:"dept-003",status:"ACTIVE",hireDate:new Date("2023-02-28"),transferHistory:[],createdAt:new Date("2023-02-28"),updatedAt:new Date("2023-02-28")},{id:"SAL-002",name:"Nicole Hall",email:"<EMAIL>",departmentId:"dept-003",status:"ACTIVE",hireDate:new Date("2023-05-10"),transferHistory:[],createdAt:new Date("2023-05-10"),updatedAt:new Date("2023-05-10")},{id:"SAL-003",name:"Brandon Allen",email:"<EMAIL>",departmentId:"dept-003",status:"ACTIVE",hireDate:new Date("2023-07-25"),transferHistory:[],createdAt:new Date("2023-07-25"),updatedAt:new Date("2023-07-25")},{id:"SAL-004",name:"Megan Young",email:"<EMAIL>",departmentId:"dept-003",status:"ACTIVE",hireDate:new Date("2023-09-18"),transferHistory:[],createdAt:new Date("2023-09-18"),updatedAt:new Date("2023-09-18")},{id:"SAL-005",name:"Tyler King",email:"<EMAIL>",departmentId:"dept-003",status:"ACTIVE",hireDate:new Date("2023-11-30"),transferHistory:[],createdAt:new Date("2023-11-30"),updatedAt:new Date("2023-11-30")},{id:"SAL-006",name:"Rachel Wright",email:"<EMAIL>",departmentId:"dept-003",status:"ACTIVE",hireDate:new Date("2024-02-05"),transferHistory:[],createdAt:new Date("2024-02-05"),updatedAt:new Date("2024-02-05")},{id:"SAL-007",name:"Justin Lopez",email:"<EMAIL>",departmentId:"dept-003",status:"ACTIVE",hireDate:new Date("2024-04-12"),transferHistory:[],createdAt:new Date("2024-04-12"),updatedAt:new Date("2024-04-12")},{id:"HR-001",name:"Laura Hill",email:"<EMAIL>",departmentId:"dept-004",status:"ACTIVE",hireDate:new Date("2023-01-20"),transferHistory:[],createdAt:new Date("2023-01-20"),updatedAt:new Date("2023-01-20")},{id:"HR-002",name:"Steven Green",email:"<EMAIL>",departmentId:"dept-004",status:"ACTIVE",hireDate:new Date("2023-06-08"),transferHistory:[],createdAt:new Date("2023-06-08"),updatedAt:new Date("2023-06-08")},{id:"HR-003",name:"Kimberly Adams",email:"<EMAIL>",departmentId:"dept-004",status:"ACTIVE",hireDate:new Date("2023-10-25"),transferHistory:[],createdAt:new Date("2023-10-25"),updatedAt:new Date("2023-10-25")},{id:"FIN-001",name:"Andrew Baker",email:"<EMAIL>",departmentId:"dept-005",status:"ACTIVE",hireDate:new Date("2023-03-08"),transferHistory:[],createdAt:new Date("2023-03-08"),updatedAt:new Date("2023-03-08")},{id:"FIN-002",name:"Michelle Nelson",email:"<EMAIL>",departmentId:"dept-005",status:"ACTIVE",hireDate:new Date("2023-07-15"),transferHistory:[],createdAt:new Date("2023-07-15"),updatedAt:new Date("2023-07-15")},{id:"FIN-003",name:"Joshua Carter",email:"<EMAIL>",departmentId:"dept-005",status:"ACTIVE",hireDate:new Date("2023-11-20"),transferHistory:[],createdAt:new Date("2023-11-20"),updatedAt:new Date("2023-11-20")},{id:"FIN-004",name:"Samantha Mitchell",email:"<EMAIL>",departmentId:"dept-005",status:"ACTIVE",hireDate:new Date("2024-02-28"),transferHistory:[],createdAt:new Date("2024-02-28"),updatedAt:new Date("2024-02-28")},{id:"OPS-001",name:"Gregory Perez",email:"<EMAIL>",departmentId:"dept-006",status:"ACTIVE",hireDate:new Date("2023-04-05"),transferHistory:[],createdAt:new Date("2023-04-05"),updatedAt:new Date("2023-04-05")},{id:"OPS-002",name:"Heather Roberts",email:"<EMAIL>",departmentId:"dept-006",status:"ACTIVE",hireDate:new Date("2023-08-12"),transferHistory:[],createdAt:new Date("2023-08-12"),updatedAt:new Date("2023-08-12")},{id:"OPS-003",name:"Nathan Turner",email:"<EMAIL>",departmentId:"dept-006",status:"ACTIVE",hireDate:new Date("2023-12-01"),transferHistory:[],createdAt:new Date("2023-12-01"),updatedAt:new Date("2023-12-01")},{id:"OPS-004",name:"Brittany Phillips",email:"<EMAIL>",departmentId:"dept-006",status:"ACTIVE",hireDate:new Date("2024-03-10"),transferHistory:[],createdAt:new Date("2024-03-10"),updatedAt:new Date("2024-03-10")},{id:"FB-001",name:"Alex Campbell",email:"<EMAIL>",departmentId:null,status:"ACTIVE",hireDate:new Date("2024-06-01"),transferHistory:[],createdAt:new Date("2024-06-01"),updatedAt:new Date("2024-06-01")},{id:"FB-002",name:"Morgan Parker",email:"<EMAIL>",departmentId:null,status:"ACTIVE",hireDate:new Date("2024-06-15"),transferHistory:[],createdAt:new Date("2024-06-15"),updatedAt:new Date("2024-06-15")}];function m(){let e,t,a=(0,r.c)(4),{setEmployees:m,setDepartments:o}=(0,d.d)();a[0]!==o||a[1]!==m?(e=()=>{let e=s.map(e=>({...e,employees:i.filter(t=>t.departmentId===e.id)})),t=i.filter(e=>null===e.departmentId);m(i),o(e),console.log("Sample data loaded:",{employees:i.length,departments:e.length,freeBucket:t.length})},t=[m,o],a[0]=o,a[1]=m,a[2]=e,a[3]=t):(e=a[2],t=a[3]),(0,n.useEffect)(e,t)}},4373:(e,t,a)=>{a.d(t,{d:()=>d});var r=a(1468),n=a(4961);let d=(0,r.vt)()((0,n.eh)((e,t)=>({employees:[],departments:[],freeBucket:[],selectedEmployees:[],isLoading:!1,searchQuery:"",setEmployees:t=>e(e=>({employees:t,freeBucket:t.filter(e=>null===e.departmentId)})),setDepartments:t=>e({departments:t}),addEmployee:t=>e(e=>({employees:[...e.employees,t]})),updateEmployee:(t,a)=>e(e=>({employees:e.employees.map(e=>e.id===t?{...e,...a}:e)})),removeEmployee:t=>e(e=>({employees:e.employees.filter(e=>e.id!==t),selectedEmployees:e.selectedEmployees.filter(e=>e!==t)})),toggleEmployeeSelection:t=>e(e=>({selectedEmployees:e.selectedEmployees.includes(t)?e.selectedEmployees.filter(e=>e!==t):[...e.selectedEmployees,t]})),selectAllEmployees:t=>e({selectedEmployees:t}),clearSelection:()=>e({selectedEmployees:[]}),executeBulkOperation:async a=>{let{employees:r,selectedEmployees:n}=t();switch(a.type){case"transfer":e(e=>({employees:e.employees.map(e=>a.employeeIds.includes(e.id)?{...e,departmentId:a.targetDepartmentId||null}:e),freeBucket:e.employees.map(e=>a.employeeIds.includes(e.id)?{...e,departmentId:a.targetDepartmentId||null}:e).filter(e=>null===e.departmentId)}));break;case"status_update":a.newStatus&&e(e=>({employees:e.employees.map(e=>a.employeeIds.includes(e.id)?{...e,status:a.newStatus}:e)}));break;case"delete":e(e=>({employees:e.employees.filter(e=>!a.employeeIds.includes(e.id))}))}e({selectedEmployees:[]})},setSearchQuery:t=>e({searchQuery:t}),getFilteredEmployees:()=>{let{employees:e,searchQuery:a}=t();return a?e.filter(e=>e.name.toLowerCase().includes(a.toLowerCase())||e.email.toLowerCase().includes(a.toLowerCase())||e.id.toLowerCase().includes(a.toLowerCase())):e},moveToFreeBucket:t=>e(e=>{let a=e.employees.filter(e=>t.includes(e.id)).map(e=>({...e,departmentId:null}));return{employees:e.employees.map(e=>t.includes(e.id)?{...e,departmentId:null}:e),freeBucket:[...e.freeBucket,...a]}}),removeFromFreeBucket:t=>e(e=>({freeBucket:e.freeBucket.filter(e=>!t.includes(e.id)),employees:e.employees.filter(e=>!t.includes(e.id))}))})))},9683:(e,t,a)=>{a.d(t,{BT:()=>o,Wu:()=>p,ZB:()=>m,Zp:()=>s,aR:()=>i});var r=a(5155),n=a(2115),d=a(198);let s=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,d.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...n})});s.displayName="Card";let i=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",a),...n})});i.displayName="CardHeader";let m=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("h3",{ref:t,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",a),...n})});m.displayName="CardTitle";let o=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("p",{ref:t,className:(0,d.cn)("text-sm text-muted-foreground",a),...n})});o.displayName="CardDescription";let p=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,d.cn)("p-6 pt-0",a),...n})});p.displayName="CardContent",n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,d.cn)("flex items-center p-6 pt-0",a),...n})}).displayName="CardFooter"}}]);