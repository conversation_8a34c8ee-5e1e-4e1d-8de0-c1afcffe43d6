import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { Employee, Department, BulkOperation } from '@/lib/types'

interface HRState {
  // Data
  employees: Employee[]
  departments: Department[]
  freeBucket: Employee[]
  
  // UI State
  selectedEmployees: string[]
  isLoading: boolean
  searchQuery: string
  
  // Actions
  setEmployees: (employees: Employee[]) => void
  setDepartments: (departments: Department[]) => void
  addEmployee: (employee: Employee) => void
  updateEmployee: (id: string, updates: Partial<Employee>) => void
  removeEmployee: (id: string) => void
  
  // Selection
  toggleEmployeeSelection: (id: string) => void
  selectAllEmployees: (ids: string[]) => void
  clearSelection: () => void
  
  // Bulk Operations
  executeBulkOperation: (operation: BulkOperation) => Promise<void>
  
  // Search & Filter
  setSearchQuery: (query: string) => void
  getFilteredEmployees: () => Employee[]
  
  // Free Bucket
  moveToFreeBucket: (employeeIds: string[]) => void
  removeFromFreeBucket: (employeeIds: string[]) => void
}

export const useHRStore = create<HRState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    employees: [],
    departments: [],
    freeBucket: [],
    selectedEmployees: [],
    isLoading: false,
    searchQuery: '',

    // Data actions
    setEmployees: (employees) => set((state) => ({
      employees,
      freeBucket: employees.filter(emp => emp.departmentId === null)
    })),
    setDepartments: (departments) => set({ departments }),
    
    addEmployee: (employee) => set((state) => ({
      employees: [...state.employees, employee]
    })),
    
    updateEmployee: (id, updates) => set((state) => ({
      employees: state.employees.map(emp => 
        emp.id === id ? { ...emp, ...updates } : emp
      )
    })),
    
    removeEmployee: (id) => set((state) => ({
      employees: state.employees.filter(emp => emp.id !== id),
      selectedEmployees: state.selectedEmployees.filter(empId => empId !== id)
    })),

    // Selection actions
    toggleEmployeeSelection: (id) => set((state) => ({
      selectedEmployees: state.selectedEmployees.includes(id)
        ? state.selectedEmployees.filter(empId => empId !== id)
        : [...state.selectedEmployees, id]
    })),
    
    selectAllEmployees: (ids) => set({ selectedEmployees: ids }),
    clearSelection: () => set({ selectedEmployees: [] }),

    // Bulk operations
    executeBulkOperation: async (operation) => {
      const { employees, selectedEmployees } = get()
      
      switch (operation.type) {
        case 'transfer':
          if (operation.targetDepartmentId) {
            set((state) => ({
              employees: state.employees.map(emp =>
                operation.employeeIds.includes(emp.id)
                  ? { ...emp, departmentId: operation.targetDepartmentId! }
                  : emp
              )
            }))
          }
          break
          
        case 'status_update':
          if (operation.newStatus) {
            set((state) => ({
              employees: state.employees.map(emp =>
                operation.employeeIds.includes(emp.id)
                  ? { ...emp, status: operation.newStatus! }
                  : emp
              )
            }))
          }
          break
          
        case 'delete':
          set((state) => ({
            employees: state.employees.filter(emp => 
              !operation.employeeIds.includes(emp.id)
            )
          }))
          break
      }
      
      set({ selectedEmployees: [] })
    },

    // Search & filter
    setSearchQuery: (query) => set({ searchQuery: query }),
    
    getFilteredEmployees: () => {
      const { employees, searchQuery } = get()
      if (!searchQuery) return employees
      
      return employees.filter(emp =>
        emp.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        emp.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        emp.id.toLowerCase().includes(searchQuery.toLowerCase())
      )
    },

    // Free bucket operations
    moveToFreeBucket: (employeeIds) => set((state) => {
      const movedEmployees = state.employees
        .filter(emp => employeeIds.includes(emp.id))
        .map(emp => ({ ...emp, departmentId: null }))
      
      return {
        employees: state.employees.map(emp =>
          employeeIds.includes(emp.id) ? { ...emp, departmentId: null } : emp
        ),
        freeBucket: [...state.freeBucket, ...movedEmployees]
      }
    }),
    
    removeFromFreeBucket: (employeeIds) => set((state) => ({
      freeBucket: state.freeBucket.filter(emp => !employeeIds.includes(emp.id)),
      employees: state.employees.filter(emp => !employeeIds.includes(emp.id))
    }))
  }))
)