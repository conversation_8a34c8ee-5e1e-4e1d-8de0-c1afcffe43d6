(()=>{var a={};a.id=82,a.ids=[82],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13181:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["free-bucket",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,39297)),"G:\\Augment code\\app\\(dashboard)\\free-bucket\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,71934)),"G:\\Augment code\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"G:\\Augment code\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["G:\\Augment code\\app\\(dashboard)\\free-bucket\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/free-bucket/page",pathname:"/free-bucket",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/free-bucket/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19947:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(18962).A)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},21774:(a,b,c)=>{Promise.resolve().then(c.bind(c,70245))},23689:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(18962).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},39297:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\free-bucket\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Augment code\\app\\(dashboard)\\free-bucket\\page.tsx","default")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},52038:(a,b,c)=>{Promise.resolve().then(c.bind(c,39297))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(a,b,c)=>{"use strict";c.d(b,{bq:()=>m,eb:()=>q,gC:()=>p,l6:()=>k,yv:()=>l});var d=c(60687),e=c(43210),f=c(97822),g=c(78272),h=c(3589),i=c(13964),j=c(96241);let k=f.bL;f.YJ;let l=f.WT,m=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.l9,{ref:e,className:(0,j.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...c,children:[b,(0,d.jsx)(f.In,{asChild:!0,children:(0,d.jsx)(g.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=f.l9.displayName;let n=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.PP,{ref:c,className:(0,j.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(h.A,{className:"h-4 w-4"})}));n.displayName=f.PP.displayName;let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wn,{ref:c,className:(0,j.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(g.A,{className:"h-4 w-4"})}));o.displayName=f.wn.displayName;let p=e.forwardRef(({className:a,children:b,position:c="popper",...e},g)=>(0,d.jsx)(f.ZL,{children:(0,d.jsxs)(f.UC,{ref:g,className:(0,j.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...e,children:[(0,d.jsx)(n,{}),(0,d.jsx)(f.LM,{className:(0,j.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:b}),(0,d.jsx)(o,{})]})}));p.displayName=f.UC.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.JU,{ref:c,className:(0,j.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...b})).displayName=f.JU.displayName;let q=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.q7,{ref:e,className:(0,j.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(f.VF,{children:(0,d.jsx)(i.A,{className:"h-4 w-4"})})}),(0,d.jsx)(f.p4,{children:b})]}));q.displayName=f.q7.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wv,{ref:c,className:(0,j.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=f.wv.displayName},70245:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>I});var d=c(60687),e=c(43210),f=c(82136),g=c(55192),h=c(24934),i=c(68988),j=c(42850),k=c(59821),l=c(96752),m=c(55629),n=c(63974),o=c(93437),p=c(70334),q=c(91356),r=c(9923),s=c(88233);function t({employees:a,departments:b}){let{selectedEmployees:c,toggleEmployeeSelection:f,selectAllEmployees:g,clearSelection:i,executeBulkOperation:t}=(0,j.d)(),[u,v]=(0,e.useState)({}),w=a.length>0&&a.every(a=>c.includes(a.id)),x=a.some(a=>c.includes(a.id))&&!w,y=async(a,b)=>{try{await t({type:"transfer",employeeIds:[a],targetDepartmentId:b}),v(b=>{let c={...b};return delete c[a],c})}catch(a){console.error("Quick assignment failed:",a),alert("Assignment failed. Please try again.")}};return 0===a.length?(0,d.jsx)("div",{className:"text-center py-8",children:(0,d.jsx)("p",{className:"text-muted-foreground",children:"No unassigned employees found."})}):(0,d.jsx)("div",{className:"rounded-md border",children:(0,d.jsxs)(l.XI,{children:[(0,d.jsx)(l.A0,{children:(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nd,{className:"w-12",children:(0,d.jsx)(o.S,{checked:w,indeterminate:x,onCheckedChange:()=>{w?i():g(a.map(a=>a.id))},"aria-label":"Select all employees"})}),(0,d.jsx)(l.nd,{children:"Employee ID"}),(0,d.jsx)(l.nd,{children:"Name"}),(0,d.jsx)(l.nd,{children:"Email"}),(0,d.jsx)(l.nd,{children:"Status"}),(0,d.jsx)(l.nd,{children:"Days Unassigned"}),(0,d.jsx)(l.nd,{children:"Quick Assign"}),(0,d.jsx)(l.nd,{className:"w-12"})]})}),(0,d.jsx)(l.BF,{children:a.map(a=>{var e;let g,i=(g=a.createdAt,Math.floor((Date.now()-g.getTime())/864e5));return(0,d.jsxs)(l.Hj,{children:[(0,d.jsx)(l.nA,{children:(0,d.jsx)(o.S,{checked:c.includes(a.id),onCheckedChange:()=>f(a.id),"aria-label":`Select ${a.name}`})}),(0,d.jsx)(l.nA,{className:"font-mono text-sm",children:a.id}),(0,d.jsx)(l.nA,{className:"font-medium",children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[a.name,i<=7&&(0,d.jsx)(k.E,{variant:"secondary",className:"text-xs",children:"New"})]})}),(0,d.jsx)(l.nA,{className:"text-muted-foreground",children:a.email}),(0,d.jsx)(l.nA,{children:(e=a.status,(0,d.jsx)(k.E,{variant:{ACTIVE:"default",TRANSFERRED:"secondary",PENDING_REMOVAL:"destructive",ARCHIVED:"outline"}[e],children:e.replace("_"," ")}))}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("span",{className:`${i>30?"text-red-600 font-medium":i>14?"text-orange-600":"text-muted-foreground"}`,children:[i," days"]})}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)(n.l6,{value:u[a.id]||"",onValueChange:b=>v(c=>({...c,[a.id]:b})),children:[(0,d.jsx)(n.bq,{className:"w-40",children:(0,d.jsx)(n.yv,{placeholder:"Select dept..."})}),(0,d.jsx)(n.gC,{children:b.map(a=>(0,d.jsx)(n.eb,{value:a.id,children:a.name},a.id))})]}),u[a.id]&&(0,d.jsx)(h.$,{size:"sm",onClick:()=>y(a.id,u[a.id]),children:(0,d.jsx)(p.A,{className:"h-4 w-4"})})]})}),(0,d.jsx)(l.nA,{children:(0,d.jsxs)(m.rI,{children:[(0,d.jsx)(m.ty,{asChild:!0,children:(0,d.jsxs)(h.$,{variant:"ghost",className:"h-8 w-8 p-0",children:[(0,d.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,d.jsx)(q.A,{className:"h-4 w-4"})]})}),(0,d.jsxs)(m.SQ,{align:"end",children:[(0,d.jsx)(m.lp,{children:"Actions"}),(0,d.jsxs)(m._2,{onClick:()=>{console.log("Edit employee:",a.id)},children:[(0,d.jsx)(r.A,{className:"mr-2 h-4 w-4"}),"Edit Employee"]}),(0,d.jsx)(m.mB,{}),(0,d.jsxs)(m._2,{onClick:()=>{console.log("Delete employee:",a.id)},className:"text-destructive",children:[(0,d.jsx)(s.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})})]},a.id)})})]})})}var u=c(39390),v=c(27253),w=c(96241),x=c(17313),y=c(19947),z=c(23689);function A({open:a,onOpenChange:b,selectedEmployees:c,departments:f}){let{employees:i,executeBulkOperation:l,clearSelection:m}=(0,j.d)(),[o,p]=(0,e.useState)(""),[q,r]=(0,e.useState)(!1),s=i.filter(a=>c.includes(a.id)),t=async()=>{if(o&&0!==c.length){r(!0);try{await l({type:"transfer",employeeIds:c,targetDepartmentId:o}),m(),p(""),b(!1),alert(`Successfully assigned ${c.length} employees`)}catch(a){console.error("Assignment failed:",a),alert("Assignment failed. Please try again.")}finally{r(!1)}}},A=()=>{p(""),b(!1)},B=(()=>{let a=f.find(a=>a.id===o)||null;if(!a)return null;let b=i.filter(b=>b.departmentId===a.id),d=b.length+c.length,e=(0,w.Vy)(d,a.capacity);return{current:b.length,afterAssignment:d,capacity:a.capacity,utilization:e,isOverCapacity:d>a.capacity,isNearCapacity:e>=80&&e<100}})();return(0,d.jsx)(v.lG,{open:a,onOpenChange:A,children:(0,d.jsxs)(v.Cf,{className:"sm:max-w-[600px]",children:[(0,d.jsxs)(v.c7,{children:[(0,d.jsxs)(v.L3,{className:"flex items-center gap-2",children:[(0,d.jsx)(x.A,{className:"h-5 w-5"}),"Assign Employees to Department"]}),(0,d.jsxs)(v.rr,{children:["Assign ",c.length," selected employees to a department."]})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)(u.J,{className:"text-base font-medium",children:["Selected Employees (",c.length,")"]}),(0,d.jsx)(g.Zp,{children:(0,d.jsx)(g.Wu,{className:"pt-4",children:(0,d.jsx)("div",{className:"grid gap-2 max-h-32 overflow-y-auto",children:s.map(a=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted rounded",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.name}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:a.email})]}),(0,d.jsx)(k.E,{variant:"outline",className:"text-xs",children:a.id})]},a.id))})})})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(u.J,{htmlFor:"department",children:"Target Department"}),(0,d.jsxs)(n.l6,{value:o,onValueChange:p,children:[(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Select a department"})}),(0,d.jsx)(n.gC,{children:f.map(a=>{let b=i.filter(b=>b.departmentId===a.id),c=(0,w.Vy)(b.length,a.capacity);return(0,d.jsx)(n.eb,{value:a.id,children:(0,d.jsx)("div",{className:"flex items-center justify-between w-full",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.name}),(0,d.jsxs)("div",{className:"text-xs text-muted-foreground",children:[b.length,"/",a.capacity," (",c,"%)"]})]})})},a.id)})})]})]}),B&&(0,d.jsx)(g.Zp,{className:`border-2 ${B.isOverCapacity?"border-red-200 bg-red-50":B.isNearCapacity?"border-orange-200 bg-orange-50":"border-green-200 bg-green-50"}`,children:(0,d.jsxs)(g.Wu,{className:"pt-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[B.isOverCapacity?(0,d.jsx)(y.A,{className:"h-5 w-5 text-red-600"}):(0,d.jsx)(z.A,{className:"h-5 w-5 text-green-600"}),(0,d.jsx)("span",{className:"font-medium",children:"Capacity Analysis"})]}),(0,d.jsxs)("div",{className:"grid gap-2 text-sm",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"Current employees:"}),(0,d.jsx)("span",{className:"font-medium",children:B.current})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"After assignment:"}),(0,d.jsx)("span",{className:"font-medium",children:B.afterAssignment})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"Department capacity:"}),(0,d.jsx)("span",{className:"font-medium",children:B.capacity})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"Utilization:"}),(0,d.jsxs)("span",{className:`font-medium ${B.isOverCapacity?"text-red-600":B.isNearCapacity?"text-orange-600":"text-green-600"}`,children:[B.utilization,"%"]})]})]}),B.isOverCapacity&&(0,d.jsxs)("div",{className:"mt-3 p-2 bg-red-100 rounded text-sm text-red-700",children:["⚠️ This assignment will exceed department capacity by ",B.afterAssignment-B.capacity," employees."]}),B.isNearCapacity&&!B.isOverCapacity&&(0,d.jsxs)("div",{className:"mt-3 p-2 bg-orange-100 rounded text-sm text-orange-700",children:["⚠️ This assignment will bring the department near capacity (",B.utilization,"%)."]})]})})]}),(0,d.jsxs)(v.Es,{children:[(0,d.jsx)(h.$,{type:"button",variant:"outline",onClick:A,disabled:q,children:"Cancel"}),(0,d.jsx)(h.$,{onClick:t,disabled:!o||q,children:q?"Assigning...":`Assign ${c.length} Employees`})]})]})})}var B=c(41312),C=c(11860);function D({selectedCount:a,onAssign:b,onClearSelection:c}){return(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(B.A,{className:"h-4 w-4 text-blue-600"}),(0,d.jsxs)("span",{className:"text-sm font-medium text-blue-800",children:[a," employee",1!==a?"s":""," selected for assignment"]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)(h.$,{size:"sm",onClick:b,className:"bg-blue-600 hover:bg-blue-700",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Assign to Department"]}),(0,d.jsx)(h.$,{variant:"ghost",size:"sm",onClick:c,className:"text-blue-600 hover:text-blue-700 hover:bg-blue-100",children:(0,d.jsx)(C.A,{className:"h-4 w-4"})})]})]})}var E=c(18516),F=c(77026),G=c(23026),H=c(99270);function I(){let{data:a}=(0,f.useSession)(),{employees:b,departments:c,selectedEmployees:k,searchQuery:l,setSearchQuery:m,clearSelection:n}=(0,j.d)(),[o,q]=(0,e.useState)(!1),[r,s]=(0,e.useState)("");(0,E.s)();let u=b.filter(a=>null===a.departmentId),v=u.filter(a=>a.name.toLowerCase().includes(r.toLowerCase())||a.email.toLowerCase().includes(r.toLowerCase())||a.id.toLowerCase().includes(r.toLowerCase())),w=k.filter(a=>u.some(b=>b.id===a)),x=w.length>0,z=u.length,C=u.filter(a=>"ACTIVE"===a.status).length,I=u.filter(a=>7>=Math.floor((Date.now()-a.createdAt.getTime())/864e5)).length,J=()=>{w.length>0&&q(!0)};return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Free Bucket"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Manage unassigned employees and assign them to departments."})]}),(0,d.jsx)("div",{className:"flex gap-2",children:(0,d.jsxs)(h.$,{variant:"outline",onClick:J,disabled:!x,children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Assign Selected (",w.length,")"]})})]}),(0,d.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"Total Unassigned"}),(0,d.jsx)(F.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:z}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Employees in free bucket"})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"Active"}),(0,d.jsx)(B.A,{className:"h-4 w-4 text-green-600"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:C}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active unassigned"})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"Recently Added"}),(0,d.jsx)(G.A,{className:"h-4 w-4 text-blue-600"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:I}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Added this week"})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"Departments"}),(0,d.jsx)(p.A,{className:"h-4 w-4 text-orange-600"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:c.length}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Available for assignment"})]})]})]}),z>10&&(0,d.jsxs)(g.Zp,{className:"border-orange-200 bg-orange-50",children:[(0,d.jsx)(g.aR,{children:(0,d.jsxs)(g.ZB,{className:"text-orange-800 flex items-center gap-2",children:[(0,d.jsx)(y.A,{className:"h-5 w-5"}),"High Number of Unassigned Employees"]})}),(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("p",{className:"text-sm text-orange-700",children:["You have ",z," unassigned employees. Consider assigning them to departments to improve organization and capacity utilization."]})})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)(g.ZB,{children:"Unassigned Employees"}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(H.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(i.p,{placeholder:"Search unassigned employees...",value:r,onChange:a=>{s(a.target.value)},className:"pl-8 w-[300px]"})]}),r&&(0,d.jsx)(h.$,{variant:"ghost",size:"sm",onClick:()=>{s("")},children:"Clear"})]})]})}),(0,d.jsxs)(g.Wu,{children:[x&&(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)(D,{selectedCount:w.length,onAssign:J,onClearSelection:n})}),0===z?(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)(F.A,{className:"h-16 w-16 text-muted-foreground mx-auto mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No Unassigned Employees"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"All employees are currently assigned to departments. Great job!"})]}):0===v.length?(0,d.jsx)("div",{className:"text-center py-8",children:(0,d.jsx)("p",{className:"text-muted-foreground",children:"No employees match your search criteria."})}):(0,d.jsx)(t,{employees:v,departments:c})]})]}),(0,d.jsx)(A,{open:o,onOpenChange:q,selectedEmployees:w,departments:c})]})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},93437:(a,b,c)=>{"use strict";c.d(b,{S:()=>i});var d=c(60687),e=c(43210),f=c(40211),g=c(13964),h=c(96241);let i=e.forwardRef(({className:a,indeterminate:b,...c},e)=>(0,d.jsx)(f.bL,{ref:e,className:(0,h.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...c,children:(0,d.jsx)(f.C1,{className:(0,h.cn)("flex items-center justify-center text-current"),children:b?(0,d.jsx)("div",{className:"h-2 w-2 bg-current rounded-sm"}):(0,d.jsx)(g.A,{className:"h-4 w-4"})})}));i.displayName=f.bL.displayName},96752:(a,b,c)=>{"use strict";c.d(b,{A0:()=>h,BF:()=>i,Hj:()=>j,XI:()=>g,nA:()=>l,nd:()=>k});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{className:"relative w-full overflow-auto",children:(0,d.jsx)("table",{ref:c,className:(0,f.cn)("w-full caption-bottom text-sm",a),...b})}));g.displayName="Table";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("thead",{ref:c,className:(0,f.cn)("[&_tr]:border-b",a),...b}));h.displayName="TableHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("tbody",{ref:c,className:(0,f.cn)("[&_tr:last-child]:border-0",a),...b}));i.displayName="TableBody",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("tfoot",{ref:c,className:(0,f.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...b})).displayName="TableFooter";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("tr",{ref:c,className:(0,f.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...b}));j.displayName="TableRow";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("th",{ref:c,className:(0,f.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...b}));k.displayName="TableHead";let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("td",{ref:c,className:(0,f.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...b}));l.displayName="TableCell",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("caption",{ref:c,className:(0,f.cn)("mt-4 text-sm text-muted-foreground",a),...b})).displayName="TableCaption"}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[602,440,579,175,362,682,786],()=>b(b.s=13181));module.exports=c})();