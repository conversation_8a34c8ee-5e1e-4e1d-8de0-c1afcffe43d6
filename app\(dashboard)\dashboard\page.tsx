"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { DepartmentCard } from "@/components/dashboard/department-card"
import { Department, Employee } from "@/lib/types"
import { useHRStore } from "@/lib/store/hr-store"
import { useSampleData } from "@/lib/hooks/use-sample-data"
import {
  Users,
  Building2,
  UserPlus,
  Archive,
  TrendingUp,
  Download
} from "lucide-react"

export default function DashboardPage() {
  const { data: session } = useSession()
  const { departments, employees, freeBucket } = useHRStore()
  const [isLoading, setIsLoading] = useState(true)

  // Load sample data
  useSampleData()

  useEffect(() => {
    // Simulate loading data
    const loadData = async () => {
      try {
        // Sample data is loaded via useSampleData hook
        await new Promise(resolve => setTimeout(resolve, 500))
        setIsLoading(false)
      } catch (error) {
        console.error("Failed to load dashboard data:", error)
        setIsLoading(false)
      }
    }

    loadData()
  }, [])

  const totalEmployees = employees.length
  const totalDepartments = departments.length
  const freeBucketCount = freeBucket.length
  const averageUtilization = departments.length > 0 
    ? Math.round(
        departments.reduce((sum, dept) => {
          const employeeCount = dept.employees?.length || 0
          return sum + (employeeCount / dept.capacity) * 100
        }, 0) / departments.length
      )
    : 0

  const recentActivity = [
    { action: "Employee transferred", details: "John Doe moved to Engineering", time: "2 hours ago" },
    { action: "New employee added", details: "Jane Smith joined Marketing", time: "4 hours ago" },
    { action: "Department created", details: "Research & Development", time: "1 day ago" },
  ]

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-muted rounded w-20"></div>
                <div className="h-4 w-4 bg-muted rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-muted rounded w-16 mb-2"></div>
                <div className="h-3 bg-muted rounded w-24"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back, {session?.user?.name}. Here's what's happening with your organization.
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <Button>
            <UserPlus className="h-4 w-4 mr-2" />
            Add Employee
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalEmployees}</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Departments</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalDepartments}</div>
            <p className="text-xs text-muted-foreground">
              Active departments
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Free Bucket</CardTitle>
            <Archive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{freeBucketCount}</div>
            <p className="text-xs text-muted-foreground">
              Unassigned employees
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Utilization</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageUtilization}%</div>
            <p className="text-xs text-muted-foreground">
              Department capacity
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Department Overview */}
      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Department Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {departments.slice(0, 6).map((department) => (
                  <DepartmentCard
                    key={department.id}
                    department={department}
                    onAddEmployee={() => {
                      // TODO: Implement add employee to department
                      console.log("Add employee to", department.name)
                    }}
                    onViewDetails={() => {
                      // TODO: Navigate to department details
                      console.log("View details for", department.name)
                    }}
                  />
                ))}
              </div>
              
              {departments.length > 6 && (
                <div className="mt-4 text-center">
                  <Button variant="outline">
                    View All Departments ({departments.length})
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex flex-col space-y-1">
                    <div className="text-sm font-medium">{activity.action}</div>
                    <div className="text-sm text-muted-foreground">
                      {activity.details}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {activity.time}
                    </div>
                    {index < recentActivity.length - 1 && (
                      <div className="border-b my-2"></div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}