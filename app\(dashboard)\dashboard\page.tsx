"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { DepartmentCard } from "@/components/dashboard/department-card"
import { Department, Employee } from "@/lib/types"
import { useHRStore } from "@/lib/store/hr-store"
import { useSampleData } from "@/lib/hooks/use-sample-data"
import {
  Users,
  Building2,
  UserPlus,
  Archive,
  TrendingUp,
  Download,
  Activity
} from "lucide-react"

export default function DashboardPage() {
  const { data: session } = useSession()
  const { departments, employees, freeBucket } = useHRStore()
  const [isLoading, setIsLoading] = useState(true)

  // Load sample data
  useSampleData()

  useEffect(() => {
    // Simulate loading data
    const loadData = async () => {
      try {
        // Sample data is loaded via useSampleData hook
        await new Promise(resolve => setTimeout(resolve, 500))
        setIsLoading(false)
      } catch (error) {
        console.error("Failed to load dashboard data:", error)
        setIsLoading(false)
      }
    }

    loadData()
  }, [])

  const totalEmployees = employees.length
  const totalDepartments = departments.length
  const freeBucketCount = freeBucket.length
  const averageUtilization = departments.length > 0 
    ? Math.round(
        departments.reduce((sum, dept) => {
          const employeeCount = dept.employees?.length || 0
          return sum + (employeeCount / dept.capacity) * 100
        }, 0) / departments.length
      )
    : 0

  const recentActivity = [
    { action: "Employee transferred", details: "John Doe moved to Engineering", time: "2 hours ago" },
    { action: "New employee added", details: "Jane Smith joined Marketing", time: "4 hours ago" },
    { action: "Department created", details: "Research & Development", time: "1 day ago" },
  ]

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 bg-muted rounded w-20"></div>
                <div className="h-4 w-4 bg-muted rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-muted rounded w-16 mb-2"></div>
                <div className="h-3 bg-muted rounded w-24"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8 p-8 bg-gradient-to-br from-slate-50/50 to-white min-h-screen">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Dashboard
          </h1>
          <p className="text-lg text-muted-foreground">
            Welcome back, {session?.user?.name}. Here's what's happening with your organization.
          </p>
        </div>

        <div className="flex gap-3">
          <Button variant="outline" className="shadow-soft hover-lift border-2 border-border/50">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <Button className="bg-gradient-primary hover:opacity-90 shadow-medium hover-lift">
            <UserPlus className="h-4 w-4 mr-2" />
            Add Employee
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="shadow-soft hover-lift border-0 bg-gradient-to-br from-blue-50 to-blue-100/50 border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-blue-700">Total Employees</CardTitle>
            <div className="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center shadow-medium">
              <Users className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-900">{totalEmployees}</div>
            <p className="text-sm text-blue-600 font-medium mt-1">
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-soft hover-lift border-0 bg-gradient-to-br from-purple-50 to-purple-100/50 border-l-4 border-l-purple-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-purple-700">Departments</CardTitle>
            <div className="w-10 h-10 bg-purple-500 rounded-xl flex items-center justify-center shadow-medium">
              <Building2 className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-900">{totalDepartments}</div>
            <p className="text-sm text-purple-600 font-medium mt-1">
              Active departments
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-soft hover-lift border-0 bg-gradient-to-br from-orange-50 to-orange-100/50 border-l-4 border-l-orange-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-orange-700">Free Bucket</CardTitle>
            <div className="w-10 h-10 bg-orange-500 rounded-xl flex items-center justify-center shadow-medium">
              <Archive className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-orange-900">{freeBucketCount}</div>
            <p className="text-sm text-orange-600 font-medium mt-1">
              Unassigned employees
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-soft hover-lift border-0 bg-gradient-to-br from-green-50 to-green-100/50 border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-green-700">Avg. Utilization</CardTitle>
            <div className="w-10 h-10 bg-green-500 rounded-xl flex items-center justify-center shadow-medium">
              <TrendingUp className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-900">{averageUtilization}%</div>
            <p className="text-sm text-green-600 font-medium mt-1">
              Department capacity
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Department Overview */}
      <div className="grid gap-8 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <Card className="shadow-medium border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-6">
              <CardTitle className="text-2xl font-bold text-foreground flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                  <Building2 className="h-4 w-4 text-white" />
                </div>
                Department Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                {departments.slice(0, 6).map((department) => (
                  <DepartmentCard
                    key={department.id}
                    department={department}
                    onAddEmployee={() => {
                      // TODO: Implement add employee to department
                      console.log("Add employee to", department.name)
                    }}
                    onViewDetails={() => {
                      // TODO: Navigate to department details
                      console.log("View details for", department.name)
                    }}
                  />
                ))}
              </div>
              
              {departments.length > 6 && (
                <div className="mt-6 text-center">
                  <Button variant="outline" className="shadow-soft hover-lift border-2 border-border/50">
                    View All Departments ({departments.length})
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <div>
          <Card className="shadow-medium border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-6">
              <CardTitle className="text-xl font-bold text-foreground flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-accent rounded-lg flex items-center justify-center">
                  <Activity className="h-4 w-4 text-white" />
                </div>
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-start space-x-4 p-4 rounded-xl bg-gradient-to-r from-slate-50 to-white border border-border/50 hover-lift">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-medium flex-shrink-0">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                    <div className="flex-1 space-y-2">
                      <div className="text-sm font-semibold text-foreground">{activity.action}</div>
                      <div className="text-sm text-muted-foreground">
                        {activity.details}
                      </div>
                      <div className="text-xs text-muted-foreground font-medium">
                        {activity.time}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}