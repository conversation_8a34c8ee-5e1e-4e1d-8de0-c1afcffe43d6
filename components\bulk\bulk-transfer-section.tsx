"use client"

import { useState } from "react"
import { Employee, Department } from "@/lib/types"
import { useHRStore } from "@/lib/store/hr-store"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  ArrowRight, 
  Users,
  AlertCircle,
  CheckCircle
} from "lucide-react"

interface BulkTransferSectionProps {
  employees: Employee[]
  departments: Department[]
  selectedEmployees: string[]
}

export function BulkTransferSection({ 
  employees, 
  departments, 
  selectedEmployees 
}: BulkTransferSectionProps) {
  const { executeBulkOperation, clearSelection } = useHRStore()
  const [targetDepartment, setTargetDepartment] = useState<string>("")
  const [isTransferring, setIsTransferring] = useState(false)

  const selectedEmployeeData = employees.filter(emp => 
    selectedEmployees.includes(emp.id)
  )

  const getDepartmentName = (departmentId: string | null) => {
    if (!departmentId) return "Unassigned"
    const department = departments.find(dept => dept.id === departmentId)
    return department?.name || "Unknown"
  }

  const getTargetDepartment = () => {
    if (targetDepartment === "unassigned") return null
    return departments.find(dept => dept.id === targetDepartment) || null
  }

  const getCapacityInfo = () => {
    const target = getTargetDepartment()
    if (!target) return null

    const currentEmployees = employees.filter(emp => emp.departmentId === target.id)
    const afterTransfer = currentEmployees.length + selectedEmployees.length
    const utilization = Math.round((afterTransfer / target.capacity) * 100)

    return {
      current: currentEmployees.length,
      afterTransfer,
      capacity: target.capacity,
      utilization,
      isOverCapacity: afterTransfer > target.capacity
    }
  }

  const handleTransfer = async () => {
    if (!targetDepartment || selectedEmployees.length === 0) return

    const targetDeptId = targetDepartment === "unassigned" ? null : targetDepartment

    setIsTransferring(true)
    try {
      await executeBulkOperation({
        type: 'transfer',
        employeeIds: selectedEmployees,
        targetDepartmentId: targetDeptId
      })

      clearSelection()
      setTargetDepartment("")
      alert(`Successfully transferred ${selectedEmployees.length} employees`)
    } catch (error) {
      console.error('Bulk transfer failed:', error)
      alert('Transfer failed. Please try again.')
    } finally {
      setIsTransferring(false)
    }
  }

  const capacityInfo = getCapacityInfo()

  return (
    <div className="space-y-6">
      {/* Selection Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Selected Employees ({selectedEmployees.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {selectedEmployees.length === 0 ? (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                No employees selected. Go to the Employees page to select employees for transfer.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="grid gap-2 max-h-40 overflow-y-auto">
                {selectedEmployeeData.map((employee) => (
                  <div key={employee.id} className="flex items-center justify-between p-2 bg-muted rounded">
                    <div>
                      <div className="font-medium">{employee.name}</div>
                      <div className="text-sm text-muted-foreground">{employee.email}</div>
                    </div>
                    <Badge variant="outline">
                      {getDepartmentName(employee.departmentId)}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Transfer Configuration */}
      {selectedEmployees.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ArrowRight className="h-5 w-5" />
              Transfer Configuration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Target Department</Label>
                <Select
                  value={targetDepartment}
                  onValueChange={setTargetDepartment}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select target department" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="unassigned">Unassigned (Free Bucket)</SelectItem>
                    {departments.map((dept) => (
                      <SelectItem key={dept.id} value={dept.id}>
                        {dept.name} ({dept.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Capacity Warning */}
              {capacityInfo && (
                <Card className={`border-2 ${
                  capacityInfo.isOverCapacity 
                    ? 'border-red-200 bg-red-50' 
                    : capacityInfo.utilization >= 80 
                    ? 'border-orange-200 bg-orange-50'
                    : 'border-green-200 bg-green-50'
                }`}>
                  <CardContent className="pt-4">
                    <div className="flex items-center gap-2 mb-2">
                      {capacityInfo.isOverCapacity ? (
                        <AlertCircle className="h-5 w-5 text-red-600" />
                      ) : (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      )}
                      <span className="font-medium">
                        Capacity Check
                      </span>
                    </div>
                    
                    <div className="space-y-1 text-sm">
                      <div>Current: {capacityInfo.current} / {capacityInfo.capacity}</div>
                      <div>After transfer: {capacityInfo.afterTransfer} / {capacityInfo.capacity}</div>
                      <div>Utilization: {capacityInfo.utilization}%</div>
                    </div>

                    {capacityInfo.isOverCapacity && (
                      <div className="mt-2 text-sm text-red-700">
                        ⚠️ This transfer will exceed department capacity by {capacityInfo.afterTransfer - capacityInfo.capacity} employees.
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              <Button 
                onClick={handleTransfer}
                disabled={!targetDepartment || isTransferring}
                className="w-full"
              >
                {isTransferring ? 'Transferring...' : `Transfer ${selectedEmployees.length} Employees`}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="text-blue-800">Transfer Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-blue-700">
            <p>1. Go to the <strong>Employees</strong> page and select the employees you want to transfer</p>
            <p>2. Return to this page and select the target department</p>
            <p>3. Review the capacity information and confirm the transfer</p>
            <p>4. The system will update all selected employees' department assignments</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
