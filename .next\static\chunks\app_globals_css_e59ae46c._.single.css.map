{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/globals.css"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n@layer base {\n  :root {\n    /* Modern Enterprise Color Palette */\n    --background: 240 10% 98%;\n    --foreground: 240 10% 3.9%;\n    --card: 0 0% 100%;\n    --card-foreground: 240 10% 3.9%;\n    --popover: 0 0% 100%;\n    --popover-foreground: 240 10% 3.9%;\n\n    /* Primary: Professional Blue */\n    --primary: 217 91% 60%;\n    --primary-foreground: 0 0% 98%;\n    --primary-hover: 217 91% 55%;\n\n    /* Secondary: Sophisticated Purple */\n    --secondary: 262 83% 58%;\n    --secondary-foreground: 0 0% 98%;\n\n    /* Accent: Modern Teal */\n    --accent: 173 80% 40%;\n    --accent-foreground: 0 0% 98%;\n\n    /* Neutral Grays */\n    --muted: 240 4.8% 95.9%;\n    --muted-foreground: 240 3.8% 46.1%;\n    --border: 240 5.9% 90%;\n    --input: 240 5.9% 90%;\n    --ring: 217 91% 60%;\n\n    /* Status Colors */\n    --destructive: 0 84.2% 60.2%;\n    --destructive-foreground: 0 0% 98%;\n    --success: 142 76% 36%;\n    --success-foreground: 0 0% 98%;\n    --warning: 38 92% 50%;\n    --warning-foreground: 0 0% 98%;\n\n    /* Enhanced Radius */\n    --radius: 0.75rem;\n\n    /* Chart Colors - Modern Palette */\n    --chart-1: 217 91% 60%;\n    --chart-2: 262 83% 58%;\n    --chart-3: 173 80% 40%;\n    --chart-4: 38 92% 50%;\n    --chart-5: 142 76% 36%;\n  }\n\n  .dark {\n    --background: 240 10% 3.9%;\n    --foreground: 0 0% 98%;\n    --card: 240 10% 3.9%;\n    --card-foreground: 0 0% 98%;\n    --popover: 240 10% 3.9%;\n    --popover-foreground: 0 0% 98%;\n\n    --primary: 217 91% 60%;\n    --primary-foreground: 240 10% 3.9%;\n    --primary-hover: 217 91% 65%;\n\n    --secondary: 262 83% 58%;\n    --secondary-foreground: 240 10% 3.9%;\n\n    --accent: 173 80% 40%;\n    --accent-foreground: 240 10% 3.9%;\n\n    --muted: 240 3.7% 15.9%;\n    --muted-foreground: 240 5% 64.9%;\n    --border: 240 3.7% 15.9%;\n    --input: 240 3.7% 15.9%;\n    --ring: 217 91% 60%;\n\n    --destructive: 0 62.8% 30.6%;\n    --destructive-foreground: 0 0% 98%;\n    --success: 142 76% 36%;\n    --success-foreground: 0 0% 98%;\n    --warning: 38 92% 50%;\n    --warning-foreground: 240 10% 3.9%;\n\n    --chart-1: 217 91% 60%;\n    --chart-2: 262 83% 58%;\n    --chart-3: 173 80% 40%;\n    --chart-4: 38 92% 50%;\n    --chart-5: 142 76% 36%;\n  }\n}\n\n@layer base {\n  * {\n    @apply border-border;\n  }\n\n  body {\n    @apply bg-background text-foreground font-sans antialiased;\n    font-feature-settings: \"rlig\" 1, \"calt\" 1;\n  }\n\n  /* Enhanced Typography */\n  h1, h2, h3, h4, h5, h6 {\n    @apply font-semibold tracking-tight;\n  }\n\n  h1 {\n    @apply text-4xl lg:text-5xl;\n  }\n\n  h2 {\n    @apply text-3xl lg:text-4xl;\n  }\n\n  h3 {\n    @apply text-2xl lg:text-3xl;\n  }\n\n  h4 {\n    @apply text-xl lg:text-2xl;\n  }\n}\n\n@layer components {\n  /* Modern Gradient Backgrounds */\n  .gradient-primary {\n    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--secondary)) 100%);\n  }\n\n  .gradient-card {\n    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);\n  }\n\n  .gradient-accent {\n    background: linear-gradient(135deg, hsl(var(--accent)) 0%, hsl(var(--primary)) 100%);\n  }\n\n  /* Glass Morphism Effect */\n  .glass {\n    background: rgba(255, 255, 255, 0.1);\n    backdrop-filter: blur(10px);\n    border: 1px solid rgba(255, 255, 255, 0.2);\n  }\n\n  /* Enhanced Shadows */\n  .shadow-soft {\n    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);\n  }\n\n  .shadow-medium {\n    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  }\n\n  .shadow-strong {\n    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 2px 10px -2px rgba(0, 0, 0, 0.05);\n  }\n\n  /* Hover Animations */\n  .hover-lift {\n    transition: all 0.2s ease-in-out;\n  }\n\n  .hover-lift:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 30px -5px rgba(0, 0, 0, 0.15);\n  }\n\n  /* Status Indicators */\n  .status-success {\n    @apply bg-green-50 text-green-700 border-green-200;\n  }\n\n  .status-warning {\n    @apply bg-yellow-50 text-yellow-700 border-yellow-200;\n  }\n\n  .status-error {\n    @apply bg-red-50 text-red-700 border-red-200;\n  }\n\n  .status-info {\n    @apply bg-blue-50 text-blue-700 border-blue-200;\n  }\n\n  /* Animations */\n  @keyframes fadeIn {\n    from { opacity: 0; transform: translateY(10px); }\n    to { opacity: 1; transform: translateY(0); }\n  }\n\n  @keyframes slideIn {\n    from { opacity: 0; transform: translateX(-20px); }\n    to { opacity: 1; transform: translateX(0); }\n  }\n\n  @keyframes pulse-soft {\n    0%, 100% { opacity: 1; }\n    50% { opacity: 0.8; }\n  }\n\n  .animate-fade-in {\n    animation: fadeIn 0.5s ease-out;\n  }\n\n  .animate-slide-in {\n    animation: slideIn 0.3s ease-out;\n  }\n\n  .animate-pulse-soft {\n    animation: pulse-soft 2s infinite;\n  }\n\n  /* Responsive Grid Background */\n  .bg-grid-slate-100 {\n    background-image:\n      linear-gradient(to right, rgb(241 245 249 / 0.5) 1px, transparent 1px),\n      linear-gradient(to bottom, rgb(241 245 249 / 0.5) 1px, transparent 1px);\n    background-size: 20px 20px;\n  }\n\n  /* Smooth Scrolling */\n  html {\n    scroll-behavior: smooth;\n  }\n\n  /* Focus Styles */\n  .focus-ring {\n    @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background;\n  }\n}"], "names": [], "mappings": "AAAA;;AACA;;AACA;;AAEA;EACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAgDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAyCE;;EAIA;;EACsB;;;;EAKtB;;EAIA;;EAIA;;EAIA;;EAIA;;;AAIJ;EAEE;;;;EAIA;;;;EAIA;;;;EAKA;;;;;;;EAOA;;;;EAIA;;;;EAIA;;;;EAKA;;;;EAIA;;;;;EAOE;;EAIA;;EAIA;;EAIA;;EAIF;;;;;;;;;;;;EAKA;;;;;;;;;;;;EAKA;;;;;;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAKA;;;;;EAQA;;;;EAME"}}]}