import { Employee, Department, DistributionConfig } from '@/lib/types'
import { generateEmployeeId } from '@/lib/utils'

export class EmployeeDistributor {
  private departments: Department[]
  private config: DistributionConfig

  constructor(departments: Department[], config: DistributionConfig) {
    this.departments = departments
    this.config = config
  }

  distributeEmployees(employees: Employee[]): Map<string, Employee[]> {
    const distribution = new Map<string, Employee[]>()
    
    // Initialize distribution map
    this.departments.forEach(dept => {
      distribution.set(dept.id, [])
    })

    // Sort departments by current load for initial distribution
    const sortedDepartments = this.getSortedDepartments()
    
    employees.forEach((employee, index) => {
      const targetDepartment = this.selectTargetDepartment(
        sortedDepartments,
        distribution,
        index
      )
      
      if (targetDepartment) {
        const currentList = distribution.get(targetDepartment.id) || []
        distribution.set(targetDepartment.id, [...currentList, employee])
      }
    })

    return distribution
  }

  private selectTargetDepartment(
    departments: Department[],
    currentDistribution: Map<string, Employee[]>,
    employeeIndex: number
  ): Department | null {
    switch (this.config.strategy) {
      case 'round-robin':
        return this.roundRobinSelection(departments, employeeIndex)
      
      case 'least-loaded':
        return this.leastLoadedSelection(departments, currentDistribution)
      
      case 'capacity-weighted':
        return this.capacityWeightedSelection(departments, currentDistribution)
      
      default:
        return departments[0] || null
    }
  }

  private roundRobinSelection(departments: Department[], index: number): Department {
    return departments[index % departments.length]
  }

  private leastLoadedSelection(
    departments: Department[],
    currentDistribution: Map<string, Employee[]>
  ): Department | null {
    let minLoad = Infinity
    let selectedDept: Department | null = null

    for (const dept of departments) {
      const currentLoad = (currentDistribution.get(dept.id) || []).length
      const existingEmployees = dept.employees?.length || 0
      const totalLoad = currentLoad + existingEmployees

      if (this.config.respectCapacity && totalLoad >= dept.capacity) {
        continue
      }

      if (totalLoad < minLoad) {
        minLoad = totalLoad
        selectedDept = dept
      }
    }

    return selectedDept
  }

  private capacityWeightedSelection(
    departments: Department[],
    currentDistribution: Map<string, Employee[]>
  ): Department | null {
    const availableDepts = departments.filter(dept => {
      if (!this.config.respectCapacity) return true
      
      const currentLoad = (currentDistribution.get(dept.id) || []).length
      const existingEmployees = dept.employees?.length || 0
      return (currentLoad + existingEmployees) < dept.capacity
    })

    if (availableDepts.length === 0) {
      return this.config.allowOverflow ? departments[0] : null
    }

    // Weight by remaining capacity
    const weights = availableDepts.map(dept => {
      const currentLoad = (currentDistribution.get(dept.id) || []).length
      const existingEmployees = dept.employees?.length || 0
      return dept.capacity - (currentLoad + existingEmployees)
    })

    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0)
    const random = Math.random() * totalWeight
    
    let cumulativeWeight = 0
    for (let i = 0; i < availableDepts.length; i++) {
      cumulativeWeight += weights[i]
      if (random <= cumulativeWeight) {
        return availableDepts[i]
      }
    }

    return availableDepts[0]
  }

  private getSortedDepartments(): Department[] {
    return [...this.departments].sort((a, b) => {
      const aLoad = a.employees?.length || 0
      const bLoad = b.employees?.length || 0
      return aLoad - bLoad
    })
  }

  generateNextEmployeeId(departmentCode: string): string {
    const existingIds = this.departments
      .flatMap(dept => dept.employees || [])
      .map(emp => emp.id)
      .filter(id => id.startsWith(departmentCode))

    const sequences = existingIds
      .map(id => parseInt(id.split('-')[1]))
      .filter(seq => !isNaN(seq))

    const nextSequence = sequences.length > 0 ? Math.max(...sequences) + 1 : 1
    return generateEmployeeId(departmentCode, nextSequence)
  }
}

export function createDistributor(
  departments: Department[],
  config: DistributionConfig = {
    strategy: 'least-loaded',
    respectCapacity: true,
    allowOverflow: false
  }
): EmployeeDistributor {
  return new EmployeeDistributor(departments, config)
}