(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[337],{64:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var r=s(5155),a=s(9749),l=s(2115),n=s(235),o=s(9683),i=s(425),c=s(9159),d=s(198),m=s(130),u=s(8379),h=s(8685);function x(e){var t;let s,l,n,x,f,p,b,v,y,g,j,N,w,_,k,A,S,E,M,R=(0,a.c)(41),{department:C,onAddEmployee:D,onViewDetails:Z}=e,$=(null==(t=C.employees)?void 0:t.length)||0,P=(0,d.Vy)($,C.capacity);R[0]!==C.name?(s=(0,r.jsx)(o.ZB,{className:"text-lg font-semibold text-foreground",children:C.name}),R[0]=C.name,R[1]=s):s=R[1],R[2]===Symbol.for("react.memo_cache_sentinel")?(l=(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center shadow-medium",children:(0,r.jsx)(m.A,{className:"h-5 w-5 text-white"})}),R[2]=l):l=R[2],R[3]!==s?(n=(0,r.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:[s,l]}),R[3]=s,R[4]=n):n=R[4],R[5]===Symbol.for("react.memo_cache_sentinel")?(x=(0,r.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Department Code:"}),R[5]=x):x=R[5],R[6]!==C.code?(f=(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gradient-to-r from-slate-50 to-white rounded-lg border border-border/50",children:[x,(0,r.jsx)("span",{className:"font-mono text-sm font-semibold bg-primary/10 text-primary px-2 py-1 rounded",children:C.code})]}),R[6]=C.code,R[7]=f):f=R[7],R[8]===Symbol.for("react.memo_cache_sentinel")?(p=(0,r.jsxs)("span",{className:"flex items-center gap-2 text-sm font-medium text-muted-foreground",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),"Employee Count"]}),R[8]=p):p=R[8],R[9]!==C.capacity||R[10]!==$?(b=(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[p,(0,r.jsxs)("span",{className:"font-semibold text-lg",children:[$," / ",C.capacity]})]}),R[9]=C.capacity,R[10]=$,R[11]=b):b=R[11],R[12]!==P?(v=(0,r.jsx)(c.k,{value:P,className:"h-3 bg-slate-200"}),R[12]=P,R[13]=v):v=R[13],R[14]===Symbol.for("react.memo_cache_sentinel")?(y=(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-full"}),R[14]=y):y=R[14],R[15]!==v?(g=(0,r.jsxs)("div",{className:"relative",children:[v,y]}),R[15]=v,R[16]=g):g=R[16],R[17]===Symbol.for("react.memo_cache_sentinel")?(j=(0,r.jsx)("span",{className:"font-medium text-muted-foreground",children:"Capacity Utilization"}),R[17]=j):j=R[17];let O="font-bold ".concat(P>=90?"text-red-600":P>=75?"text-orange-600":"text-green-600");return R[18]!==O||R[19]!==P?(N=(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[j,(0,r.jsxs)("span",{className:O,children:[P,"%"]})]}),R[18]=O,R[19]=P,R[20]=N):N=R[20],R[21]!==g||R[22]!==N?(w=(0,r.jsxs)("div",{className:"space-y-2",children:[g,N]}),R[21]=g,R[22]=N,R[23]=w):w=R[23],R[24]!==w||R[25]!==b?(_=(0,r.jsxs)("div",{className:"space-y-3",children:[b,w]}),R[24]=w,R[25]=b,R[26]=_):_=R[26],R[27]!==D?(k=D&&(0,r.jsxs)(i.$,{size:"sm",variant:"outline",onClick:D,className:"flex-1 h-9 border-2 border-border/50 hover:border-primary hover:bg-primary/5 transition-all",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Add Employee"]}),R[27]=D,R[28]=k):k=R[28],R[29]!==Z?(A=Z&&(0,r.jsx)(i.$,{size:"sm",variant:"secondary",onClick:Z,className:"flex-1 h-9 bg-gradient-to-r from-slate-100 to-slate-200 hover:from-slate-200 hover:to-slate-300 transition-all",children:"View Details"}),R[29]=Z,R[30]=A):A=R[30],R[31]!==k||R[32]!==A?(S=(0,r.jsxs)("div",{className:"flex gap-3 pt-3",children:[k,A]}),R[31]=k,R[32]=A,R[33]=S):S=R[33],R[34]!==_||R[35]!==S||R[36]!==f?(E=(0,r.jsx)(o.Wu,{children:(0,r.jsxs)("div",{className:"space-y-5",children:[f,_,S]})}),R[34]=_,R[35]=S,R[36]=f,R[37]=E):E=R[37],R[38]!==E||R[39]!==n?(M=(0,r.jsxs)(o.Zp,{className:"shadow-soft hover-lift border-0 bg-gradient-to-br from-white to-slate-50/50 transition-all duration-200",children:[n,E]}),R[38]=E,R[39]=n,R[40]=M):M=R[40],M}var f=s(4373),p=s(538),b=s(9309),v=s(4825),y=s(5369),g=s(5970);let j=(0,s(8776).A)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]);function N(){var e;let t,s,c,d,h,x,N,E,M,R,C,D,Z,$,P,O,W,B,T,U,H,V,z,L,q,I,G,F,J,X,Y,K,Q,ee,et,es,er,ea,el,en=(0,a.c)(64),{data:eo}=(0,n.useSession)(),{departments:ei,employees:ec,freeBucket:ed}=(0,f.d)(),[em,eu]=(0,l.useState)(!0);(0,p.s)(),en[0]===Symbol.for("react.memo_cache_sentinel")?(t=()=>{(async()=>{try{await new Promise(S),eu(!1)}catch(e){console.error("Failed to load dashboard data:",e),eu(!1)}})()},s=[],en[0]=t,en[1]=s):(t=en[0],s=en[1]),(0,l.useEffect)(t,s);let eh=ec.length,ex=ei.length,ef=ed.length;en[2]!==ei?(c=ei.length>0?Math.round(ei.reduce(A,0)/ei.length):0,en[2]=ei,en[3]=c):c=en[3];let ep=c;en[4]===Symbol.for("react.memo_cache_sentinel")?(d=[{action:"Employee transferred",details:"John Doe moved to Engineering",time:"2 hours ago"},{action:"New employee added",details:"Jane Smith joined Marketing",time:"4 hours ago"},{action:"Department created",details:"Research & Development",time:"1 day ago"}],en[4]=d):d=en[4];let eb=d;if(em){let e;return en[5]===Symbol.for("react.memo_cache_sentinel")?(e=(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[void 0,void 0,void 0,void 0].map(k)})}),en[5]=e):e=en[5],e}en[6]===Symbol.for("react.memo_cache_sentinel")?(h=(0,r.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Dashboard"}),en[6]=h):h=en[6];let ev=null==eo||null==(e=eo.user)?void 0:e.name;return en[7]!==ev?(x=(0,r.jsxs)("div",{className:"space-y-2",children:[h,(0,r.jsxs)("p",{className:"text-lg text-muted-foreground",children:["Welcome back, ",ev,". Here's what's happening with your organization."]})]}),en[7]=ev,en[8]=x):x=en[8],en[9]===Symbol.for("react.memo_cache_sentinel")?(N=(0,r.jsxs)(i.$,{variant:"outline",className:"shadow-soft hover-lift border-2 border-border/50",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Export Report"]}),en[9]=N):N=en[9],en[10]===Symbol.for("react.memo_cache_sentinel")?(E=(0,r.jsxs)("div",{className:"flex gap-3",children:[N,(0,r.jsxs)(i.$,{className:"bg-gradient-primary hover:opacity-90 shadow-medium hover-lift",children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Add Employee"]})]}),en[10]=E):E=en[10],en[11]!==x?(M=(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[x,E]}),en[11]=x,en[12]=M):M=en[12],en[13]===Symbol.for("react.memo_cache_sentinel")?(R=(0,r.jsx)(o.ZB,{className:"text-sm font-semibold text-blue-700",children:"Total Employees"}),en[13]=R):R=en[13],en[14]===Symbol.for("react.memo_cache_sentinel")?(C=(0,r.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3",children:[R,(0,r.jsx)("div",{className:"w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center shadow-medium",children:(0,r.jsx)(u.A,{className:"h-5 w-5 text-white"})})]}),en[14]=C):C=en[14],en[15]!==eh?(D=(0,r.jsx)("div",{className:"text-3xl font-bold text-blue-900",children:eh}),en[15]=eh,en[16]=D):D=en[16],en[17]===Symbol.for("react.memo_cache_sentinel")?(Z=(0,r.jsx)("p",{className:"text-sm text-blue-600 font-medium mt-1",children:"+12% from last month"}),en[17]=Z):Z=en[17],en[18]!==D?($=(0,r.jsxs)(o.Zp,{className:"shadow-soft hover-lift border-0 bg-gradient-to-br from-blue-50 to-blue-100/50 border-l-4 border-l-blue-500",children:[C,(0,r.jsxs)(o.Wu,{children:[D,Z]})]}),en[18]=D,en[19]=$):$=en[19],en[20]===Symbol.for("react.memo_cache_sentinel")?(P=(0,r.jsx)(o.ZB,{className:"text-sm font-semibold text-purple-700",children:"Departments"}),en[20]=P):P=en[20],en[21]===Symbol.for("react.memo_cache_sentinel")?(O=(0,r.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3",children:[P,(0,r.jsx)("div",{className:"w-10 h-10 bg-purple-500 rounded-xl flex items-center justify-center shadow-medium",children:(0,r.jsx)(m.A,{className:"h-5 w-5 text-white"})})]}),en[21]=O):O=en[21],en[22]!==ex?(W=(0,r.jsx)("div",{className:"text-3xl font-bold text-purple-900",children:ex}),en[22]=ex,en[23]=W):W=en[23],en[24]===Symbol.for("react.memo_cache_sentinel")?(B=(0,r.jsx)("p",{className:"text-sm text-purple-600 font-medium mt-1",children:"Active departments"}),en[24]=B):B=en[24],en[25]!==W?(T=(0,r.jsxs)(o.Zp,{className:"shadow-soft hover-lift border-0 bg-gradient-to-br from-purple-50 to-purple-100/50 border-l-4 border-l-purple-500",children:[O,(0,r.jsxs)(o.Wu,{children:[W,B]})]}),en[25]=W,en[26]=T):T=en[26],en[27]===Symbol.for("react.memo_cache_sentinel")?(U=(0,r.jsx)(o.ZB,{className:"text-sm font-semibold text-orange-700",children:"Free Bucket"}),en[27]=U):U=en[27],en[28]===Symbol.for("react.memo_cache_sentinel")?(H=(0,r.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3",children:[U,(0,r.jsx)("div",{className:"w-10 h-10 bg-orange-500 rounded-xl flex items-center justify-center shadow-medium",children:(0,r.jsx)(y.A,{className:"h-5 w-5 text-white"})})]}),en[28]=H):H=en[28],en[29]!==ef?(V=(0,r.jsx)("div",{className:"text-3xl font-bold text-orange-900",children:ef}),en[29]=ef,en[30]=V):V=en[30],en[31]===Symbol.for("react.memo_cache_sentinel")?(z=(0,r.jsx)("p",{className:"text-sm text-orange-600 font-medium mt-1",children:"Unassigned employees"}),en[31]=z):z=en[31],en[32]!==V?(L=(0,r.jsxs)(o.Zp,{className:"shadow-soft hover-lift border-0 bg-gradient-to-br from-orange-50 to-orange-100/50 border-l-4 border-l-orange-500",children:[H,(0,r.jsxs)(o.Wu,{children:[V,z]})]}),en[32]=V,en[33]=L):L=en[33],en[34]===Symbol.for("react.memo_cache_sentinel")?(q=(0,r.jsx)(o.ZB,{className:"text-sm font-semibold text-green-700",children:"Avg. Utilization"}),en[34]=q):q=en[34],en[35]===Symbol.for("react.memo_cache_sentinel")?(I=(0,r.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-3",children:[q,(0,r.jsx)("div",{className:"w-10 h-10 bg-green-500 rounded-xl flex items-center justify-center shadow-medium",children:(0,r.jsx)(g.A,{className:"h-5 w-5 text-white"})})]}),en[35]=I):I=en[35],en[36]!==ep?(G=(0,r.jsxs)("div",{className:"text-3xl font-bold text-green-900",children:[ep,"%"]}),en[36]=ep,en[37]=G):G=en[37],en[38]===Symbol.for("react.memo_cache_sentinel")?(F=(0,r.jsx)("p",{className:"text-sm text-green-600 font-medium mt-1",children:"Department capacity"}),en[38]=F):F=en[38],en[39]!==G?(J=(0,r.jsxs)(o.Zp,{className:"shadow-soft hover-lift border-0 bg-gradient-to-br from-green-50 to-green-100/50 border-l-4 border-l-green-500",children:[I,(0,r.jsxs)(o.Wu,{children:[G,F]})]}),en[39]=G,en[40]=J):J=en[40],en[41]!==$||en[42]!==T||en[43]!==L||en[44]!==J?(X=(0,r.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-4",children:[$,T,L,J]}),en[41]=$,en[42]=T,en[43]=L,en[44]=J,en[45]=X):X=en[45],en[46]===Symbol.for("react.memo_cache_sentinel")?(Y=(0,r.jsx)(o.aR,{className:"pb-6",children:(0,r.jsxs)(o.ZB,{className:"text-2xl font-bold text-foreground flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center",children:(0,r.jsx)(m.A,{className:"h-4 w-4 text-white"})}),"Department Overview"]})}),en[46]=Y):Y=en[46],en[47]!==ei?(K=ei.slice(0,6).map(_),en[47]=ei,en[48]=K):K=en[48],en[49]!==K?(Q=(0,r.jsx)("div",{className:"grid gap-6 md:grid-cols-2",children:K}),en[49]=K,en[50]=Q):Q=en[50],en[51]!==ei.length?(ee=ei.length>6&&(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsxs)(i.$,{variant:"outline",className:"shadow-soft hover-lift border-2 border-border/50",children:["View All Departments (",ei.length,")"]})}),en[51]=ei.length,en[52]=ee):ee=en[52],en[53]!==Q||en[54]!==ee?(et=(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)(o.Zp,{className:"shadow-medium border-0 bg-white/80 backdrop-blur-sm",children:[Y,(0,r.jsxs)(o.Wu,{children:[Q,ee]})]})}),en[53]=Q,en[54]=ee,en[55]=et):et=en[55],en[56]===Symbol.for("react.memo_cache_sentinel")?(es=(0,r.jsx)(o.aR,{className:"pb-6",children:(0,r.jsxs)(o.ZB,{className:"text-xl font-bold text-foreground flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-accent rounded-lg flex items-center justify-center",children:(0,r.jsx)(j,{className:"h-4 w-4 text-white"})}),"Recent Activity"]})}),en[56]=es):es=en[56],en[57]===Symbol.for("react.memo_cache_sentinel")?(er=(0,r.jsx)("div",{children:(0,r.jsxs)(o.Zp,{className:"shadow-medium border-0 bg-white/80 backdrop-blur-sm",children:[es,(0,r.jsx)(o.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:eb.map(w)})})]})}),en[57]=er):er=en[57],en[58]!==et?(ea=(0,r.jsxs)("div",{className:"grid gap-8 lg:grid-cols-3",children:[et,er]}),en[58]=et,en[59]=ea):ea=en[59],en[60]!==X||en[61]!==ea||en[62]!==M?(el=(0,r.jsxs)("div",{className:"space-y-8 p-8 bg-gradient-to-br from-slate-50/50 to-white min-h-screen",children:[M,X,ea]}),en[60]=X,en[61]=ea,en[62]=M,en[63]=el):el=en[63],el}function w(e,t){return(0,r.jsxs)("div",{className:"flex items-start space-x-4 p-4 rounded-xl bg-gradient-to-r from-slate-50 to-white border border-border/50 hover-lift",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-medium flex-shrink-0",children:(0,r.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"})}),(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,r.jsx)("div",{className:"text-sm font-semibold text-foreground",children:e.action}),(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:e.details}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground font-medium",children:e.time})]})]},t)}function _(e){return(0,r.jsx)(x,{department:e,onAddEmployee:()=>{console.log("Add employee to",e.name)},onViewDetails:()=>{console.log("View details for",e.name)}},e.id)}function k(e,t){return(0,r.jsxs)(o.Zp,{className:"animate-pulse",children:[(0,r.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)("div",{className:"h-4 bg-muted rounded w-20"}),(0,r.jsx)("div",{className:"h-4 w-4 bg-muted rounded"})]}),(0,r.jsxs)(o.Wu,{children:[(0,r.jsx)("div",{className:"h-8 bg-muted rounded w-16 mb-2"}),(0,r.jsx)("div",{className:"h-3 bg-muted rounded w-24"})]})]},t)}function A(e,t){var s;return e+((null==(s=t.employees)?void 0:s.length)||0)/t.capacity*100}function S(e){return setTimeout(e,500)}},130:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(8776).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},1129:(e,t,s)=>{Promise.resolve().then(s.bind(s,64))},1632:(e,t,s)=>{"use strict";e.exports=s(3319)},3319:(e,t,s)=>{"use strict";var r=s(2115),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},l=r.useState,n=r.useEffect,o=r.useLayoutEffect,i=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var s=t();return!a(e,s)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var s=t(),r=l({inst:{value:s,getSnapshot:t}}),a=r[0].inst,d=r[1];return o(function(){a.value=s,a.getSnapshot=t,c(a)&&d({inst:a})},[e,s,t]),n(function(){return c(a)&&d({inst:a}),e(function(){c(a)&&d({inst:a})})},[e]),i(s),s};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:d},4825:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(8776).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},4873:(e,t,s)=>{"use strict";var r=s(2115).__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;t.c=function(e){return r.H.useMemoCache(e)}},5369:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(8776).A)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},5970:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(8776).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},8379:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(8776).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},8685:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(8776).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},8776:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(2115),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(e,t)=>{let s=(0,r.forwardRef)((s,l)=>{let{color:n="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:c,className:d="",children:m,...u}=s;return(0,r.createElement)("svg",{ref:l,...a,width:o,height:o,stroke:n,strokeWidth:c?24*Number(i)/Number(o):i,className:["lucide","lucide-".concat(e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim()),d].join(" "),...u},[...t.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(m)?m:[m]])});return s.displayName="".concat(e),s}},9159:(e,t,s)=>{"use strict";s.d(t,{k:()=>j});var r=s(5155),a=s(2115),l=s(9602),n=s(4372),o="Progress",[i,c]=(0,l.A)(o),[d,m]=i(o),u=a.forwardRef((e,t)=>{var s,a,l,o;let{__scopeProgress:i,value:c=null,max:m,getValueLabel:u=f,...h}=e;(m||0===m)&&!v(m)&&console.error((s="".concat(m),a="Progress","Invalid prop `max` of value `".concat(s,"` supplied to `").concat(a,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let x=v(m)?m:100;null===c||y(c,x)||console.error((l="".concat(c),o="Progress","Invalid prop `value` of value `".concat(l,"` supplied to `").concat(o,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let g=y(c,x)?c:null,j=b(g)?u(g,x):void 0;return(0,r.jsx)(d,{scope:i,value:g,max:x,children:(0,r.jsx)(n.sG.div,{"aria-valuemax":x,"aria-valuemin":0,"aria-valuenow":b(g)?g:void 0,"aria-valuetext":j,role:"progressbar","data-state":p(g,x),"data-value":null!=g?g:void 0,"data-max":x,...h,ref:t})})});u.displayName=o;var h="ProgressIndicator",x=a.forwardRef((e,t)=>{var s;let{__scopeProgress:a,...l}=e,o=m(h,a);return(0,r.jsx)(n.sG.div,{"data-state":p(o.value,o.max),"data-value":null!=(s=o.value)?s:void 0,"data-max":o.max,...l,ref:t})});function f(e,t){return"".concat(Math.round(e/t*100),"%")}function p(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function b(e){return"number"==typeof e}function v(e){return b(e)&&!isNaN(e)&&e>0}function y(e,t){return b(e)&&!isNaN(e)&&e<=t&&e>=0}x.displayName=h;var g=s(198);let j=a.forwardRef((e,t)=>{let{className:s,value:a,...l}=e;return(0,r.jsx)(u,{ref:t,className:(0,g.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",s),...l,children:(0,r.jsx)(x,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(a||0),"%)")}})})});j.displayName=u.displayName},9309:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(8776).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},9602:(e,t,s)=>{"use strict";s.d(t,{A:()=>n,q:()=>l});var r=s(2115),a=s(5155);function l(e,t){let s=r.createContext(t),l=e=>{let{children:t,...l}=e,n=r.useMemo(()=>l,Object.values(l));return(0,a.jsx)(s.Provider,{value:n,children:t})};return l.displayName=e+"Provider",[l,function(a){let l=r.useContext(s);if(l)return l;if(void 0!==t)return t;throw Error(`\`${a}\` must be used within \`${e}\``)}]}function n(e,t=[]){let s=[],l=()=>{let t=s.map(e=>r.createContext(e));return function(s){let a=s?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...s,[e]:a}}),[s,a])}};return l.scopeName=e,[function(t,l){let n=r.createContext(l),o=s.length;s=[...s,l];let i=t=>{let{scope:s,children:l,...i}=t,c=s?.[e]?.[o]||n,d=r.useMemo(()=>i,Object.values(i));return(0,a.jsx)(c.Provider,{value:d,children:l})};return i.displayName=t+"Provider",[i,function(s,a){let i=a?.[e]?.[o]||n,c=r.useContext(i);if(c)return c;if(void 0!==l)return l;throw Error(`\`${s}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let s=()=>{let s=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=s.reduce((t,{useScope:s,scopeName:r})=>{let a=s(e)[`__scope${r}`];return{...t,...a}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return s.scopeName=t.scopeName,s}(l,...t)]}},9749:(e,t,s)=>{"use strict";e.exports=s(4873)}},e=>{e.O(0,[235,666,18,816,441,326,358],()=>e(e.s=1129)),_N_E=e.O()}]);