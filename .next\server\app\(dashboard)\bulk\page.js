(()=>{var a={};a.id=147,a.ids=[147],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},743:(a,b,c)=>{Promise.resolve().then(c.bind(c,55606))},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6476:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\bulk\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Augment code\\app\\(dashboard)\\bulk\\page.tsx","default")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23689:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(18962).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(18962).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},33873:a=>{"use strict";a.exports=require("path")},39390:(a,b,c)=>{"use strict";c.d(b,{J:()=>j});var d=c(60687),e=c(43210),f=c(78148),g=c(24224),h=c(96241);let i=(0,g.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.b,{ref:c,className:(0,h.cn)(i(),a),...b}));j.displayName=f.b.displayName},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55606:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>E});var d=c(60687),e=c(43210),f=c(82136),g=c(55192),h=c(24934),i=c(42850),j=c(59821),k=c(57769),l=c(96241),m=c(16023),n=c(18962);let o=(0,n.A)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]);var p=c(31158),q=c(23689);function r({departments:a}){let{employees:b,addEmployee:c}=(0,i.d)(),[f,n]=(0,e.useState)(!1),[r,s]=(0,e.useState)(0),[t,u]=(0,e.useState)(null),v=(0,e.useRef)(null),w=async a=>{let b=a.target.files?.[0];if(b){if(!b.name.endsWith(".csv"))return void alert("Please select a CSV file");n(!0),s(0),u(null);try{let a=await b.text(),c=await x(a);u(c)}catch(a){console.error("CSV import failed:",a),alert("Failed to import CSV file")}finally{n(!1),s(0),v.current&&(v.current.value="")}}},x=async c=>{let d=c.trim().split("\n"),e=d[0].split(",").map(a=>a.trim().toLowerCase()),f=["name","email"].filter(a=>!e.includes(a));if(f.length>0)throw Error(`Missing required headers: ${f.join(", ")}`);let g={success:[],errors:[]};for(let c=1;c<d.length;c++){s(c/(d.length-1)*100);let f=d[c].split(",").map(a=>a.trim()),h={};e.forEach((a,b)=>{h[a]=f[b]||""});try{if(!h.name||!h.email)throw Error("Name and email are required");if(b.some(a=>a.email.toLowerCase()===h.email.toLowerCase()))throw Error("Email already exists");let c=null;if(h.departmentcode){let b=a.find(a=>a.code.toLowerCase()===h.departmentcode.toLowerCase());if(!b)throw Error(`Department with code '${h.departmentcode}' not found`);c=b.id}let d=a.find(a=>a.id===c),e=d?.code||"GEN",f=b.filter(a=>a.id.startsWith(e)).map(a=>parseInt(a.id.split("-")[1])||0),i=Math.max(0,...f)+g.success.length+1,j=(0,l.pp)(e,i),k=["ACTIVE","TRANSFERRED","PENDING_REMOVAL","ARCHIVED"],m=h.status?.toUpperCase()||"ACTIVE";if(!k.includes(m))throw Error(`Invalid status '${h.status}'. Must be one of: ${k.join(", ")}`);let n={id:j,name:h.name,email:h.email,departmentId:c,status:m,hireDate:new Date,transferHistory:[],createdAt:new Date,updatedAt:new Date};g.success.push(n)}catch(a){g.errors.push({row:c+1,data:h,error:a instanceof Error?a.message:"Unknown error"})}await new Promise(a=>setTimeout(a,10))}return g};return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(m.A,{className:"h-5 w-5"}),"Upload CSV File"]})}),(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsxs)(h.$,{onClick:()=>{v.current?.click()},disabled:f,children:[(0,d.jsx)(o,{className:"h-4 w-4 mr-2"}),"Select CSV File"]}),(0,d.jsxs)(h.$,{variant:"outline",onClick:()=>{let a=new Blob(["name,email,departmentcode,status\nJohn Doe,<EMAIL>,ENG,ACTIVE\nJane Smith,<EMAIL>,MKT,ACTIVE\nBob Johnson,<EMAIL>,,ACTIVE"],{type:"text/csv"}),b=URL.createObjectURL(a),c=document.createElement("a");c.href=b,c.download="employee-import-template.csv",c.click(),URL.revokeObjectURL(b)},children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Download Template"]})]}),f&&(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,d.jsx)("span",{children:"Processing CSV..."}),(0,d.jsxs)("span",{children:[Math.round(r),"%"]})]}),(0,d.jsx)(k.k,{value:r})]}),(0,d.jsx)("input",{ref:v,type:"file",accept:".csv",onChange:w,className:"hidden"})]})})]}),t&&(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(q.A,{className:"h-5 w-5 text-green-600"}),"Import Results"]})}),(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex gap-4",children:[(0,d.jsxs)(j.E,{variant:"default",className:"bg-green-100 text-green-800",children:[t.success.length," Successful"]}),t.errors.length>0&&(0,d.jsxs)(j.E,{variant:"destructive",children:[t.errors.length," Errors"]})]}),t.errors.length>0&&(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("h4",{className:"font-medium text-red-800",children:"Errors:"}),(0,d.jsx)("div",{className:"max-h-40 overflow-y-auto space-y-1",children:t.errors.map((a,b)=>(0,d.jsxs)("div",{className:"text-sm p-2 bg-red-50 rounded border-l-4 border-red-400",children:[(0,d.jsxs)("div",{className:"font-medium",children:["Row ",a.row,":"]}),(0,d.jsx)("div",{className:"text-red-700",children:a.error}),(0,d.jsxs)("div",{className:"text-xs text-red-600 mt-1",children:["Data: ",JSON.stringify(a.data)]})]},b))})]}),t.success.length>0&&(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsxs)(h.$,{onClick:()=>{t&&(t.success.forEach(a=>{c(a)}),alert(`Successfully imported ${t.success.length} employees`),u(null))},children:["Import ",t.success.length," Employees"]}),(0,d.jsx)(h.$,{variant:"outline",onClick:()=>u(null),children:"Cancel"})]})]})})]}),(0,d.jsxs)(g.Zp,{className:"border-blue-200 bg-blue-50",children:[(0,d.jsx)(g.aR,{children:(0,d.jsx)(g.ZB,{className:"text-blue-800",children:"CSV Format Instructions"})}),(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("div",{className:"space-y-2 text-sm text-blue-700",children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Required columns:"})," name, email"]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Optional columns:"})," departmentcode, status"]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Department codes:"})," ",a.map(a=>a.code).join(", ")]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Valid statuses:"})," ACTIVE, TRANSFERRED, PENDING_REMOVAL, ARCHIVED"]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Note:"})," If departmentcode is empty, employee will be added to the free bucket"]})]})})]})]})}var s=c(39390),t=c(93437),u=c(63974),v=c(98492);function w({employees:a,departments:b}){let[c,f]=(0,e.useState)({department:"all",status:"all",includeTransferHistory:!1}),[i,j]=(0,e.useState)({id:!0,name:!0,email:!0,department:!0,status:!0,hireDate:!0,createdAt:!1,updatedAt:!1}),k=()=>a.filter(a=>{if("all"!==c.department){if("unassigned"===c.department){if(null!==a.departmentId)return!1}else if(a.departmentId!==c.department)return!1}return"all"===c.status||a.status===c.status}),m=k();return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(v.A,{className:"h-5 w-5"}),"Export Filters"]})}),(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(s.J,{children:"Department"}),(0,d.jsxs)(u.l6,{value:c.department,onValueChange:a=>f(b=>({...b,department:a})),children:[(0,d.jsx)(u.bq,{children:(0,d.jsx)(u.yv,{})}),(0,d.jsxs)(u.gC,{children:[(0,d.jsx)(u.eb,{value:"all",children:"All Departments"}),(0,d.jsx)(u.eb,{value:"unassigned",children:"Unassigned"}),b.map(a=>(0,d.jsx)(u.eb,{value:a.id,children:a.name},a.id))]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(s.J,{children:"Status"}),(0,d.jsxs)(u.l6,{value:c.status,onValueChange:a=>f(b=>({...b,status:a})),children:[(0,d.jsx)(u.bq,{children:(0,d.jsx)(u.yv,{})}),(0,d.jsxs)(u.gC,{children:[(0,d.jsx)(u.eb,{value:"all",children:"All Statuses"}),(0,d.jsx)(u.eb,{value:"ACTIVE",children:"Active"}),(0,d.jsx)(u.eb,{value:"TRANSFERRED",children:"Transferred"}),(0,d.jsx)(u.eb,{value:"PENDING_REMOVAL",children:"Pending Removal"}),(0,d.jsx)(u.eb,{value:"ARCHIVED",children:"Archived"})]})]})]})]})})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsx)(g.ZB,{children:"Select Columns to Export"})}),(0,d.jsx)(g.Wu,{children:(0,d.jsx)("div",{className:"grid gap-3 md:grid-cols-2",children:[{key:"id",label:"Employee ID",required:!0},{key:"name",label:"Name",required:!0},{key:"email",label:"Email",required:!0},{key:"department",label:"Department",required:!1},{key:"status",label:"Status",required:!1},{key:"hireDate",label:"Hire Date",required:!1},{key:"createdAt",label:"Created Date",required:!1},{key:"updatedAt",label:"Updated Date",required:!1}].map(a=>(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(t.S,{id:a.key,checked:i[a.key],onCheckedChange:b=>{var c;return c=a.key,void j(a=>({...a,[c]:b}))},disabled:a.required}),(0,d.jsxs)(s.J,{htmlFor:a.key,className:a.required?"text-muted-foreground":"",children:[a.label,a.required&&" (Required)"]})]},a.key))})})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(o,{className:"h-5 w-5"}),"Export Summary"]})}),(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid gap-2 md:grid-cols-3",children:[(0,d.jsxs)("div",{className:"text-center p-4 bg-muted rounded-lg",children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:m.length}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:"Employees to export"})]}),(0,d.jsxs)("div",{className:"text-center p-4 bg-muted rounded-lg",children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:Object.values(i).filter(Boolean).length}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:"Columns selected"})]}),(0,d.jsxs)("div",{className:"text-center p-4 bg-muted rounded-lg",children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:"CSV"}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:"Export format"})]})]}),(0,d.jsxs)(h.$,{onClick:()=>{let a=k();if(0===a.length)return void alert("No employees match the current filters");let c=[];i.id&&c.push("Employee ID"),i.name&&c.push("Name"),i.email&&c.push("Email"),i.department&&c.push("Department"),i.status&&c.push("Status"),i.hireDate&&c.push("Hire Date"),i.createdAt&&c.push("Created Date"),i.updatedAt&&c.push("Updated Date");let d=a.map(a=>{let c=[];return i.id&&c.push(a.id),i.name&&c.push(a.name),i.email&&c.push(a.email),i.department&&c.push((a=>{if(!a)return"Unassigned";let c=b.find(b=>b.id===a);return c?.name||"Unknown"})(a.departmentId)),i.status&&c.push(a.status),i.hireDate&&c.push((0,l.Yq)(a.hireDate)),i.createdAt&&c.push((0,l.Yq)(a.createdAt)),i.updatedAt&&c.push((0,l.Yq)(a.updatedAt)),c}),e=new Blob([[c.join(","),...d.map(a=>a.map(a=>`"${a}"`).join(","))].join("\n")],{type:"text/csv"}),f=URL.createObjectURL(e),g=document.createElement("a");g.href=f,g.download=`employees-export-${new Date().toISOString().split("T")[0]}.csv`,g.click(),URL.revokeObjectURL(f)},disabled:0===m.length,className:"w-full",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Export ",m.length," Employees to CSV"]})]})})]})]})}var x=c(41312);let y=(0,n.A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var z=c(70334);function A({employees:a,departments:b,selectedEmployees:c}){let{executeBulkOperation:f,clearSelection:k}=(0,i.d)(),[l,m]=(0,e.useState)(""),[n,o]=(0,e.useState)(!1),p=a.filter(a=>c.includes(a.id)),r=async()=>{if(l&&0!==c.length){o(!0);try{await f({type:"transfer",employeeIds:c,targetDepartmentId:"unassigned"===l?null:l}),k(),m(""),alert(`Successfully transferred ${c.length} employees`)}catch(a){console.error("Bulk transfer failed:",a),alert("Transfer failed. Please try again.")}finally{o(!1)}}},t=(()=>{let d="unassigned"===l?null:b.find(a=>a.id===l)||null;if(!d)return null;let e=a.filter(a=>a.departmentId===d.id),f=e.length+c.length,g=Math.round(f/d.capacity*100);return{current:e.length,afterTransfer:f,capacity:d.capacity,utilization:g,isOverCapacity:f>d.capacity}})();return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(x.A,{className:"h-5 w-5"}),"Selected Employees (",c.length,")"]})}),(0,d.jsx)(g.Wu,{children:0===c.length?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(y,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"No employees selected. Go to the Employees page to select employees for transfer."})]}):(0,d.jsx)("div",{className:"space-y-4",children:(0,d.jsx)("div",{className:"grid gap-2 max-h-40 overflow-y-auto",children:p.map(a=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted rounded",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.name}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:a.email})]}),(0,d.jsx)(j.E,{variant:"outline",children:(a=>{if(!a)return"Unassigned";let c=b.find(b=>b.id===a);return c?.name||"Unknown"})(a.departmentId)})]},a.id))})})})]}),c.length>0&&(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(z.A,{className:"h-5 w-5"}),"Transfer Configuration"]})}),(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(s.J,{children:"Target Department"}),(0,d.jsxs)(u.l6,{value:l,onValueChange:m,children:[(0,d.jsx)(u.bq,{children:(0,d.jsx)(u.yv,{placeholder:"Select target department"})}),(0,d.jsxs)(u.gC,{children:[(0,d.jsx)(u.eb,{value:"unassigned",children:"Unassigned (Free Bucket)"}),b.map(a=>(0,d.jsxs)(u.eb,{value:a.id,children:[a.name," (",a.code,")"]},a.id))]})]})]}),t&&(0,d.jsx)(g.Zp,{className:`border-2 ${t.isOverCapacity?"border-red-200 bg-red-50":t.utilization>=80?"border-orange-200 bg-orange-50":"border-green-200 bg-green-50"}`,children:(0,d.jsxs)(g.Wu,{className:"pt-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[t.isOverCapacity?(0,d.jsx)(y,{className:"h-5 w-5 text-red-600"}):(0,d.jsx)(q.A,{className:"h-5 w-5 text-green-600"}),(0,d.jsx)("span",{className:"font-medium",children:"Capacity Check"})]}),(0,d.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,d.jsxs)("div",{children:["Current: ",t.current," / ",t.capacity]}),(0,d.jsxs)("div",{children:["After transfer: ",t.afterTransfer," / ",t.capacity]}),(0,d.jsxs)("div",{children:["Utilization: ",t.utilization,"%"]})]}),t.isOverCapacity&&(0,d.jsxs)("div",{className:"mt-2 text-sm text-red-700",children:["⚠️ This transfer will exceed department capacity by ",t.afterTransfer-t.capacity," employees."]})]})}),(0,d.jsx)(h.$,{onClick:r,disabled:!l||n,className:"w-full",children:n?"Transferring...":`Transfer ${c.length} Employees`})]})})]}),(0,d.jsxs)(g.Zp,{className:"border-blue-200 bg-blue-50",children:[(0,d.jsx)(g.aR,{children:(0,d.jsx)(g.ZB,{className:"text-blue-800",children:"Transfer Instructions"})}),(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("div",{className:"space-y-2 text-sm text-blue-700",children:[(0,d.jsxs)("p",{children:["1. Go to the ",(0,d.jsx)("strong",{children:"Employees"})," page and select the employees you want to transfer"]}),(0,d.jsx)("p",{children:"2. Return to this page and select the target department"}),(0,d.jsx)("p",{children:"3. Review the capacity information and confirm the transfer"}),(0,d.jsx)("p",{children:"4. The system will update all selected employees' department assignments"})]})})]})]})}let B=(0,n.A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);function C({employees:a,selectedEmployees:b}){let{executeBulkOperation:c,clearSelection:f}=(0,i.d)(),[k,l]=(0,e.useState)(""),[m,n]=(0,e.useState)(!1),o=a.filter(a=>b.includes(a.id)),p=[{value:"ACTIVE",label:"Active",description:"Employee is currently active"},{value:"TRANSFERRED",label:"Transferred",description:"Employee has been transferred"},{value:"PENDING_REMOVAL",label:"Pending Removal",description:"Employee is scheduled for removal"},{value:"ARCHIVED",label:"Archived",description:"Employee record is archived"}],q=a=>(0,d.jsx)(j.E,{variant:{ACTIVE:"default",TRANSFERRED:"secondary",PENDING_REMOVAL:"destructive",ARCHIVED:"outline"}[a],children:a.replace("_"," ")}),r=async()=>{if(k&&0!==b.length){n(!0);try{await c({type:"status_update",employeeIds:b,newStatus:k}),f(),l(""),alert(`Successfully updated status for ${b.length} employees`)}catch(a){console.error("Bulk status update failed:",a),alert("Status update failed. Please try again.")}finally{n(!1)}}},t=o.reduce((a,b)=>(a[b.status]=(a[b.status]||0)+1,a),{});return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(x.A,{className:"h-5 w-5"}),"Selected Employees (",b.length,")"]})}),(0,d.jsx)(g.Wu,{children:0===b.length?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)(y,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"No employees selected. Go to the Employees page to select employees for status update."})]}):(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium mb-2",children:"Current Status Distribution:"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:Object.entries(t).map(([a,b])=>(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[q(a),(0,d.jsxs)("span",{className:"text-sm text-muted-foreground",children:["(",b,")"]})]},a))})]}),(0,d.jsx)("div",{className:"grid gap-2 max-h-40 overflow-y-auto",children:o.map(a=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted rounded",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.name}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:a.email})]}),q(a.status)]},a.id))})]})})]}),b.length>0&&(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(B,{className:"h-5 w-5"}),"Status Update Configuration"]})}),(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(s.J,{children:"New Status"}),(0,d.jsxs)(u.l6,{value:k,onValueChange:a=>l(a),children:[(0,d.jsx)(u.bq,{children:(0,d.jsx)(u.yv,{placeholder:"Select new status"})}),(0,d.jsx)(u.gC,{children:p.map(a=>(0,d.jsx)(u.eb,{value:a.value,children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.label}),(0,d.jsx)("div",{className:"text-xs text-muted-foreground",children:a.description})]})},a.value))})]})]}),k&&(0,d.jsx)(g.Zp,{className:"border-blue-200 bg-blue-50",children:(0,d.jsxs)(g.Wu,{className:"pt-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,d.jsx)(B,{className:"h-4 w-4 text-blue-600"}),(0,d.jsx)("span",{className:"font-medium text-blue-800",children:"Preview Changes"})]}),(0,d.jsxs)("div",{className:"text-sm text-blue-700",children:[b.length," employees will be updated to: ",q(k)]})]})}),(0,d.jsx)(h.$,{onClick:r,disabled:!k||m,className:"w-full",children:m?"Updating...":`Update Status for ${b.length} Employees`})]})})]}),(0,d.jsxs)(g.Zp,{className:"border-gray-200 bg-gray-50",children:[(0,d.jsx)(g.aR,{children:(0,d.jsx)(g.ZB,{className:"text-gray-800",children:"Status Descriptions"})}),(0,d.jsx)(g.Wu,{children:(0,d.jsx)("div",{className:"space-y-2 text-sm text-gray-700",children:p.map(a=>(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[q(a.value),(0,d.jsx)("span",{children:a.description})]},a.value))})})]}),(0,d.jsxs)(g.Zp,{className:"border-blue-200 bg-blue-50",children:[(0,d.jsx)(g.aR,{children:(0,d.jsx)(g.ZB,{className:"text-blue-800",children:"Status Update Instructions"})}),(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("div",{className:"space-y-2 text-sm text-blue-700",children:[(0,d.jsxs)("p",{children:["1. Go to the ",(0,d.jsx)("strong",{children:"Employees"})," page and select the employees whose status you want to update"]}),(0,d.jsx)("p",{children:"2. Return to this page and select the new status"}),(0,d.jsx)("p",{children:"3. Review the preview and confirm the update"}),(0,d.jsx)("p",{children:"4. All selected employees will have their status updated simultaneously"})]})})]})]})}var D=c(18516);function E(){let{data:a}=(0,f.useSession)(),{employees:b,departments:c,selectedEmployees:j}=(0,i.d)(),[k,l]=(0,e.useState)("import");(0,D.s)();let n=[{id:"import",label:"Import CSV",icon:m.A,description:"Import employees from CSV file"},{id:"export",label:"Export CSV",icon:p.A,description:"Export employees to CSV file"},{id:"transfer",label:"Bulk Transfer",icon:z.A,description:"Transfer multiple employees between departments"},{id:"status",label:"Status Update",icon:x.A,description:"Update status for multiple employees"}];return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"flex items-center justify-between",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Bulk Operations"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Perform bulk operations on employee data including CSV import/export and mass updates."})]})}),(0,d.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"Total Employees"}),(0,d.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:b.length}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Available for operations"})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"Selected"}),(0,d.jsx)(x.A,{className:"h-4 w-4 text-blue-600"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:j.length}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Currently selected"})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"Departments"}),(0,d.jsx)(o,{className:"h-4 w-4 text-muted-foreground"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:c.length}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Available departments"})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"Unassigned"}),(0,d.jsx)(y,{className:"h-4 w-4 text-orange-600"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:b.filter(a=>null===a.departmentId).length}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"In free bucket"})]})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsx)("div",{className:"flex space-x-1 rounded-lg bg-muted p-1",children:n.map(a=>{let b=a.icon;return(0,d.jsxs)(h.$,{variant:k===a.id?"default":"ghost",size:"sm",onClick:()=>l(a.id),className:"flex-1",children:[(0,d.jsx)(b,{className:"h-4 w-4 mr-2"}),a.label]},a.id)})})}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)("h3",{className:"text-lg font-medium",children:n.find(a=>a.id===k)?.label}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:n.find(a=>a.id===k)?.description})]}),"import"===k&&(0,d.jsx)(r,{departments:c}),"export"===k&&(0,d.jsx)(w,{employees:b,departments:c}),"transfer"===k&&(0,d.jsx)(A,{employees:b,departments:c,selectedEmployees:j}),"status"===k&&(0,d.jsx)(C,{employees:b,selectedEmployees:j})]})]}),(0,d.jsxs)(g.Zp,{className:"border-blue-200 bg-blue-50",children:[(0,d.jsx)(g.aR,{children:(0,d.jsxs)(g.ZB,{className:"text-blue-800 flex items-center gap-2",children:[(0,d.jsx)(y,{className:"h-5 w-5"}),"Bulk Operations Guidelines"]})}),(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("div",{className:"space-y-2 text-sm text-blue-700",children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"CSV Import:"})," Upload a CSV file with employee data. Required columns: name, email. Optional: departmentCode, status."]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"CSV Export:"})," Download employee data in CSV format with filtering options."]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Bulk Transfer:"})," Move multiple employees between departments. Select employees from the Employees page first."]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Status Update:"})," Change the status of multiple employees at once (Active, Transferred, Archived, etc.)."]})]})})]})]})}},57769:(a,b,c)=>{"use strict";c.d(b,{k:()=>v});var d=c(60687),e=c(43210),f=c(11273),g=c(14163),h="Progress",[i,j]=(0,f.A)(h),[k,l]=i(h),m=e.forwardRef((a,b)=>{var c,e;let{__scopeProgress:f,value:h=null,max:i,getValueLabel:j=p,...l}=a;(i||0===i)&&!s(i)&&console.error((c=`${i}`,`Invalid prop \`max\` of value \`${c}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=s(i)?i:100;null===h||t(h,m)||console.error((e=`${h}`,`Invalid prop \`value\` of value \`${e}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let n=t(h,m)?h:null,o=r(n)?j(n,m):void 0;return(0,d.jsx)(k,{scope:f,value:n,max:m,children:(0,d.jsx)(g.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":r(n)?n:void 0,"aria-valuetext":o,role:"progressbar","data-state":q(n,m),"data-value":n??void 0,"data-max":m,...l,ref:b})})});m.displayName=h;var n="ProgressIndicator",o=e.forwardRef((a,b)=>{let{__scopeProgress:c,...e}=a,f=l(n,c);return(0,d.jsx)(g.sG.div,{"data-state":q(f.value,f.max),"data-value":f.value??void 0,"data-max":f.max,...e,ref:b})});function p(a,b){return`${Math.round(a/b*100)}%`}function q(a,b){return null==a?"indeterminate":a===b?"complete":"loading"}function r(a){return"number"==typeof a}function s(a){return r(a)&&!isNaN(a)&&a>0}function t(a,b){return r(a)&&!isNaN(a)&&a<=b&&a>=0}o.displayName=n;var u=c(96241);let v=e.forwardRef(({className:a,value:b,...c},e)=>(0,d.jsx)(m,{ref:e,className:(0,u.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",a),...c,children:(0,d.jsx)(o,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(b||0)}%)`}})}));v.displayName=m.displayName},59821:(a,b,c)=>{"use strict";c.d(b,{E:()=>h});var d=c(60687);c(43210);var e=c(24224),f=c(96241);let g=(0,e.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a),...c})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(a,b,c)=>{"use strict";c.d(b,{bq:()=>m,eb:()=>q,gC:()=>p,l6:()=>k,yv:()=>l});var d=c(60687),e=c(43210),f=c(97822),g=c(78272),h=c(3589),i=c(13964),j=c(96241);let k=f.bL;f.YJ;let l=f.WT,m=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.l9,{ref:e,className:(0,j.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...c,children:[b,(0,d.jsx)(f.In,{asChild:!0,children:(0,d.jsx)(g.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=f.l9.displayName;let n=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.PP,{ref:c,className:(0,j.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(h.A,{className:"h-4 w-4"})}));n.displayName=f.PP.displayName;let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wn,{ref:c,className:(0,j.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(g.A,{className:"h-4 w-4"})}));o.displayName=f.wn.displayName;let p=e.forwardRef(({className:a,children:b,position:c="popper",...e},g)=>(0,d.jsx)(f.ZL,{children:(0,d.jsxs)(f.UC,{ref:g,className:(0,j.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...e,children:[(0,d.jsx)(n,{}),(0,d.jsx)(f.LM,{className:(0,j.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:b}),(0,d.jsx)(o,{})]})}));p.displayName=f.UC.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.JU,{ref:c,className:(0,j.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...b})).displayName=f.JU.displayName;let q=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.q7,{ref:e,className:(0,j.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(f.VF,{children:(0,d.jsx)(i.A,{className:"h-4 w-4"})})}),(0,d.jsx)(f.p4,{children:b})]}));q.displayName=f.q7.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wv,{ref:c,className:(0,j.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=f.wv.displayName},64535:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["bulk",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,6476)),"G:\\Augment code\\app\\(dashboard)\\bulk\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,71934)),"G:\\Augment code\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"G:\\Augment code\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["G:\\Augment code\\app\\(dashboard)\\bulk\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/bulk/page",pathname:"/bulk",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/bulk/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},92495:(a,b,c)=>{Promise.resolve().then(c.bind(c,6476))},93437:(a,b,c)=>{"use strict";c.d(b,{S:()=>i});var d=c(60687),e=c(43210),f=c(40211),g=c(13964),h=c(96241);let i=e.forwardRef(({className:a,indeterminate:b,...c},e)=>(0,d.jsx)(f.bL,{ref:e,className:(0,h.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...c,children:(0,d.jsx)(f.C1,{className:(0,h.cn)("flex items-center justify-center text-current"),children:b?(0,d.jsx)("div",{className:"h-2 w-2 bg-current rounded-sm"}):(0,d.jsx)(g.A,{className:"h-4 w-4"})})}));i.displayName=f.bL.displayName},98492:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(18962).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[602,440,579,175,362,682],()=>b(b.s=64535));module.exports=c})();