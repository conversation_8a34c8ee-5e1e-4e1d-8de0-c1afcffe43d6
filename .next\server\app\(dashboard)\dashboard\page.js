(()=>{var a={};a.id=337,a.ids=[337],a.modules={183:(a,b,c)=>{Promise.resolve().then(c.bind(c,67062))},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},2818:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>u});var d=c(60687),e=c(43210),f=c(82136),g=c(55192),h=c(24934),i=c(57769),j=c(96241),k=c(17313),l=c(41312),m=c(96474);function n({department:a,onAddEmployee:b,onViewDetails:c}){let e=a.employees?.length||0,f=(0,j.Vy)(e,a.capacity);return(0,d.jsxs)(g.<PERSON><PERSON>,{className:"hover:shadow-md transition-shadow",children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-lg font-medium",children:a.name}),(0,d.jsx)(k.A,{className:"h-5 w-5 text-muted-foreground"})]}),(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,d.jsx)("span",{className:"text-muted-foreground",children:"Code:"}),(0,d.jsx)("span",{className:"font-mono",children:a.code})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,d.jsxs)("span",{className:"flex items-center gap-1",children:[(0,d.jsx)(l.A,{className:"h-4 w-4"}),"Employees"]}),(0,d.jsxs)("span",{className:"font-medium",children:[e," / ",a.capacity]})]}),(0,d.jsx)(i.k,{value:f,className:"h-2"}),(0,d.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground",children:[(0,d.jsx)("span",{children:"Capacity Utilization"}),(0,d.jsxs)("span",{className:`font-medium ${f>=90?"text-red-600":f>=75?"text-yellow-600":"text-green-600"}`,children:[f,"%"]})]})]}),(0,d.jsxs)("div",{className:"flex gap-2 pt-2",children:[b&&(0,d.jsxs)(h.$,{size:"sm",variant:"outline",onClick:b,className:"flex-1",children:[(0,d.jsx)(m.A,{className:"h-4 w-4 mr-1"}),"Add Employee"]}),c&&(0,d.jsx)(h.$,{size:"sm",variant:"secondary",onClick:c,className:"flex-1",children:"View Details"})]})]})})]})}var o=c(42850),p=c(18516),q=c(31158),r=c(23026),s=c(77026),t=c(25541);function u(){let{data:a}=(0,f.useSession)(),{departments:b,employees:c,freeBucket:i}=(0,o.d)(),[j,m]=(0,e.useState)(!0);(0,p.s)();let u=c.length,v=b.length,w=i.length,x=b.length>0?Math.round(b.reduce((a,b)=>a+(b.employees?.length||0)/b.capacity*100,0)/b.length):0,y=[{action:"Employee transferred",details:"John Doe moved to Engineering",time:"2 hours ago"},{action:"New employee added",details:"Jane Smith joined Marketing",time:"4 hours ago"},{action:"Department created",details:"Research & Development",time:"1 day ago"}];return j?(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)(g.Zp,{className:"animate-pulse",children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)("div",{className:"h-4 bg-muted rounded w-20"}),(0,d.jsx)("div",{className:"h-4 w-4 bg-muted rounded"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"h-8 bg-muted rounded w-16 mb-2"}),(0,d.jsx)("div",{className:"h-3 bg-muted rounded w-24"})]})]},b))})}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Dashboard"}),(0,d.jsxs)("p",{className:"text-muted-foreground",children:["Welcome back, ",a?.user?.name,". Here's what's happening with your organization."]})]}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsxs)(h.$,{variant:"outline",children:[(0,d.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"Export Report"]}),(0,d.jsxs)(h.$,{children:[(0,d.jsx)(r.A,{className:"h-4 w-4 mr-2"}),"Add Employee"]})]})]}),(0,d.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"Total Employees"}),(0,d.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:u}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"+12% from last month"})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"Departments"}),(0,d.jsx)(k.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:v}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active departments"})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"Free Bucket"}),(0,d.jsx)(s.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"text-2xl font-bold",children:w}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Unassigned employees"})]})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,d.jsx)(g.ZB,{className:"text-sm font-medium",children:"Avg. Utilization"}),(0,d.jsx)(t.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsxs)("div",{className:"text-2xl font-bold",children:[x,"%"]}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground",children:"Department capacity"})]})]})]}),(0,d.jsxs)("div",{className:"grid gap-6 lg:grid-cols-3",children:[(0,d.jsx)("div",{className:"lg:col-span-2",children:(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsx)(g.ZB,{children:"Department Overview"})}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("div",{className:"grid gap-4 md:grid-cols-2",children:b.slice(0,6).map(a=>(0,d.jsx)(n,{department:a,onAddEmployee:()=>{console.log("Add employee to",a.name)},onViewDetails:()=>{console.log("View details for",a.name)}},a.id))}),b.length>6&&(0,d.jsx)("div",{className:"mt-4 text-center",children:(0,d.jsxs)(h.$,{variant:"outline",children:["View All Departments (",b.length,")"]})})]})]})}),(0,d.jsx)("div",{children:(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsx)(g.ZB,{children:"Recent Activity"})}),(0,d.jsx)(g.Wu,{children:(0,d.jsx)("div",{className:"space-y-4",children:y.map((a,b)=>(0,d.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,d.jsx)("div",{className:"text-sm font-medium",children:a.action}),(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:a.details}),(0,d.jsx)("div",{className:"text-xs text-muted-foreground",children:a.time}),b<y.length-1&&(0,d.jsx)("div",{className:"border-b my-2"})]},b))})})]})})]})]})}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23026:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(18962).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},25541:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(18962).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(18962).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},49309:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,67062)),"G:\\Augment code\\app\\(dashboard)\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,71934)),"G:\\Augment code\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"G:\\Augment code\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["G:\\Augment code\\app\\(dashboard)\\dashboard\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/dashboard/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},57769:(a,b,c)=>{"use strict";c.d(b,{k:()=>v});var d=c(60687),e=c(43210),f=c(11273),g=c(14163),h="Progress",[i,j]=(0,f.A)(h),[k,l]=i(h),m=e.forwardRef((a,b)=>{var c,e;let{__scopeProgress:f,value:h=null,max:i,getValueLabel:j=p,...l}=a;(i||0===i)&&!s(i)&&console.error((c=`${i}`,`Invalid prop \`max\` of value \`${c}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=s(i)?i:100;null===h||t(h,m)||console.error((e=`${h}`,`Invalid prop \`value\` of value \`${e}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let n=t(h,m)?h:null,o=r(n)?j(n,m):void 0;return(0,d.jsx)(k,{scope:f,value:n,max:m,children:(0,d.jsx)(g.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":r(n)?n:void 0,"aria-valuetext":o,role:"progressbar","data-state":q(n,m),"data-value":n??void 0,"data-max":m,...l,ref:b})})});m.displayName=h;var n="ProgressIndicator",o=e.forwardRef((a,b)=>{let{__scopeProgress:c,...e}=a,f=l(n,c);return(0,d.jsx)(g.sG.div,{"data-state":q(f.value,f.max),"data-value":f.value??void 0,"data-max":f.max,...e,ref:b})});function p(a,b){return`${Math.round(a/b*100)}%`}function q(a,b){return null==a?"indeterminate":a===b?"complete":"loading"}function r(a){return"number"==typeof a}function s(a){return r(a)&&!isNaN(a)&&a>0}function t(a,b){return r(a)&&!isNaN(a)&&a<=b&&a>=0}o.displayName=n;var u=c(96241);let v=e.forwardRef(({className:a,value:b,...c},e)=>(0,d.jsx)(m,{ref:e,className:(0,u.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",a),...c,children:(0,d.jsx)(o,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(b||0)}%)`}})}));v.displayName=m.displayName},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67062:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Augment code\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Augment code\\app\\(dashboard)\\dashboard\\page.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88559:(a,b,c)=>{Promise.resolve().then(c.bind(c,2818))},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(18962).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[602,440,579,175,682],()=>b(b.s=49309));module.exports=c})();