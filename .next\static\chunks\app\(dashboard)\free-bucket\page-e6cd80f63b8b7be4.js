(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[82],{4071:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>T});var n=t(5155),a=t(9749),l=t(2115),c=t(235),i=t(9683),r=t(425),d=t(7071),m=t(4373),o=t(6226),x=t(6203),h=t(650),j=t(245),u=t(9138),p=t(3689),g=t(1895),f=t(5076),y=t(8106);function N(e){let s,t,c,i,d,N,w,A,_,C,S,E,k,D,I,Z,O,R=(0,a.c)(47),{employees:z,departments:B}=e,{selectedEmployees:T,toggleEmployeeSelection:V,selectAllEmployees:L,clearSelection:W,executeBulkOperation:$}=(0,m.d)();R[0]===Symbol.for("react.memo_cache_sentinel")?(s={},R[0]=s):s=R[0];let[M,U]=(0,l.useState)(s);R[1]!==z||R[2]!==T?(t=z.length>0&&z.every(e=>T.includes(e.id)),R[1]=z,R[2]=T,R[3]=t):t=R[3];let F=t;R[4]!==z||R[5]!==F||R[6]!==T?(c=z.some(e=>T.includes(e.id))&&!F,R[4]=z,R[5]=F,R[6]=T,R[7]=c):c=R[7];let H=c;R[8]!==W||R[9]!==z||R[10]!==F||R[11]!==L?(i=()=>{F?W():L(z.map(v))},R[8]=W,R[9]=z,R[10]=F,R[11]=L,R[12]=i):i=R[12];let P=i;R[13]!==$?(d=async(e,s)=>{try{await $({type:"transfer",employeeIds:[e],targetDepartmentId:s}),U(s=>{let t={...s};return delete t[e],t})}catch(e){console.error("Quick assignment failed:",e),alert("Assignment failed. Please try again.")}},R[13]=$,R[14]=d):d=R[14];let G=d;if(0===z.length){let e;return R[15]===Symbol.for("react.memo_cache_sentinel")?(e=(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-muted-foreground",children:"No unassigned employees found."})}),R[15]=e):e=R[15],e}if(R[16]!==P||R[17]!==F||R[18]!==H?(N=(0,n.jsx)(x.nd,{className:"w-12",children:(0,n.jsx)(u.S,{checked:F,indeterminate:H,onCheckedChange:P,"aria-label":"Select all employees"})}),R[16]=P,R[17]=F,R[18]=H,R[19]=N):N=R[19],R[20]===Symbol.for("react.memo_cache_sentinel")?(S=(0,n.jsx)(x.nd,{children:"Employee ID"}),E=(0,n.jsx)(x.nd,{children:"Name"}),k=(0,n.jsx)(x.nd,{children:"Email"}),w=(0,n.jsx)(x.nd,{children:"Status"}),A=(0,n.jsx)(x.nd,{children:"Days Unassigned"}),_=(0,n.jsx)(x.nd,{children:"Quick Assign"}),C=(0,n.jsx)(x.nd,{className:"w-12"}),R[20]=w,R[21]=A,R[22]=_,R[23]=C,R[24]=S,R[25]=E,R[26]=k):(w=R[20],A=R[21],_=R[22],C=R[23],S=R[24],E=R[25],k=R[26]),R[27]!==N?(D=(0,n.jsx)(x.A0,{children:(0,n.jsxs)(x.Hj,{children:[N,S,E,k,w,A,_,C]})}),R[27]=N,R[28]=D):D=R[28],R[29]!==B||R[30]!==z||R[31]!==G||R[32]!==M||R[33]!==T||R[34]!==V){let e;R[36]!==B||R[37]!==G||R[38]!==M||R[39]!==T||R[40]!==V?(e=e=>{let s=function(e){return Math.floor((Date.now()-e.getTime())/864e5)}(e.createdAt);return(0,n.jsxs)(x.Hj,{children:[(0,n.jsx)(x.nA,{children:(0,n.jsx)(u.S,{checked:T.includes(e.id),onCheckedChange:()=>V(e.id),"aria-label":"Select ".concat(e.name)})}),(0,n.jsx)(x.nA,{className:"font-mono text-sm",children:e.id}),(0,n.jsx)(x.nA,{className:"font-medium",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[e.name,s<=7&&(0,n.jsx)(o.E,{variant:"secondary",className:"text-xs",children:"New"})]})}),(0,n.jsx)(x.nA,{className:"text-muted-foreground",children:e.email}),(0,n.jsx)(x.nA,{children:function(e){return(0,n.jsx)(o.E,{variant:{ACTIVE:"default",TRANSFERRED:"secondary",PENDING_REMOVAL:"destructive",ARCHIVED:"outline"}[e],children:e.replace("_"," ")})}(e.status)}),(0,n.jsx)(x.nA,{children:(0,n.jsxs)("span",{className:"".concat(s>30?"text-red-600 font-medium":s>14?"text-orange-600":"text-muted-foreground"),children:[s," days"]})}),(0,n.jsx)(x.nA,{children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)(j.l6,{value:M[e.id]||"",onValueChange:s=>U(t=>({...t,[e.id]:s})),children:[(0,n.jsx)(j.bq,{className:"w-40",children:(0,n.jsx)(j.yv,{placeholder:"Select dept..."})}),(0,n.jsx)(j.gC,{children:B.map(b)})]}),M[e.id]&&(0,n.jsx)(r.$,{size:"sm",onClick:()=>G(e.id,M[e.id]),children:(0,n.jsx)(p.A,{className:"h-4 w-4"})})]})}),(0,n.jsx)(x.nA,{children:(0,n.jsxs)(h.rI,{children:[(0,n.jsx)(h.ty,{asChild:!0,children:(0,n.jsxs)(r.$,{variant:"ghost",className:"h-8 w-8 p-0",children:[(0,n.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,n.jsx)(g.A,{className:"h-4 w-4"})]})}),(0,n.jsxs)(h.SQ,{align:"end",children:[(0,n.jsx)(h.lp,{children:"Actions"}),(0,n.jsxs)(h._2,{onClick:()=>(function(e){console.log("Edit employee:",e.id)})(e),children:[(0,n.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Edit Employee"]}),(0,n.jsx)(h.mB,{}),(0,n.jsxs)(h._2,{onClick:()=>(function(e){console.log("Delete employee:",e.id)})(e),className:"text-destructive",children:[(0,n.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})})]},e.id)},R[36]=B,R[37]=G,R[38]=M,R[39]=T,R[40]=V,R[41]=e):e=R[41],I=z.map(e),R[29]=B,R[30]=z,R[31]=G,R[32]=M,R[33]=T,R[34]=V,R[35]=I}else I=R[35];return R[42]!==I?(Z=(0,n.jsx)(x.BF,{children:I}),R[42]=I,R[43]=Z):Z=R[43],R[44]!==D||R[45]!==Z?(O=(0,n.jsx)("div",{className:"rounded-md border",children:(0,n.jsxs)(x.XI,{children:[D,Z]})}),R[44]=D,R[45]=Z,R[46]=O):O=R[46],O}function b(e){return(0,n.jsx)(j.eb,{value:e.id,children:e.name},e.id)}function v(e){return e.id}var w=t(7013),A=t(7273),_=t(198),C=t(130),S=t(208),E=t(8778);function k(e){let{open:s,onOpenChange:t,selectedEmployees:a,departments:c}=e,{employees:d,executeBulkOperation:x,clearSelection:h}=(0,m.d)(),[u,p]=(0,l.useState)(""),[g,f]=(0,l.useState)(!1),y=d.filter(e=>a.includes(e.id)),N=async()=>{if(u&&0!==a.length){f(!0);try{await x({type:"transfer",employeeIds:a,targetDepartmentId:u}),h(),p(""),t(!1),alert("Successfully assigned ".concat(a.length," employees"))}catch(e){console.error("Assignment failed:",e),alert("Assignment failed. Please try again.")}finally{f(!1)}}},b=()=>{p(""),t(!1)},v=(()=>{let e=c.find(e=>e.id===u)||null;if(!e)return null;let s=d.filter(s=>s.departmentId===e.id),t=s.length+a.length,n=(0,_.Vy)(t,e.capacity);return{current:s.length,afterAssignment:t,capacity:e.capacity,utilization:n,isOverCapacity:t>e.capacity,isNearCapacity:n>=80&&n<100}})();return(0,n.jsx)(A.lG,{open:s,onOpenChange:b,children:(0,n.jsxs)(A.Cf,{className:"sm:max-w-[600px]",children:[(0,n.jsxs)(A.c7,{children:[(0,n.jsxs)(A.L3,{className:"flex items-center gap-2",children:[(0,n.jsx)(C.A,{className:"h-5 w-5"}),"Assign Employees to Department"]}),(0,n.jsxs)(A.rr,{children:["Assign ",a.length," selected employees to a department."]})]}),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)(w.J,{className:"text-base font-medium",children:["Selected Employees (",a.length,")"]}),(0,n.jsx)(i.Zp,{children:(0,n.jsx)(i.Wu,{className:"pt-4",children:(0,n.jsx)("div",{className:"grid gap-2 max-h-32 overflow-y-auto",children:y.map(e=>(0,n.jsxs)("div",{className:"flex items-center justify-between p-2 bg-muted rounded",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"font-medium",children:e.name}),(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:e.email})]}),(0,n.jsx)(o.E,{variant:"outline",className:"text-xs",children:e.id})]},e.id))})})})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(w.J,{htmlFor:"department",children:"Target Department"}),(0,n.jsxs)(j.l6,{value:u,onValueChange:p,children:[(0,n.jsx)(j.bq,{children:(0,n.jsx)(j.yv,{placeholder:"Select a department"})}),(0,n.jsx)(j.gC,{children:c.map(e=>{let s=d.filter(s=>s.departmentId===e.id),t=(0,_.Vy)(s.length,e.capacity);return(0,n.jsx)(j.eb,{value:e.id,children:(0,n.jsx)("div",{className:"flex items-center justify-between w-full",children:(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"font-medium",children:e.name}),(0,n.jsxs)("div",{className:"text-xs text-muted-foreground",children:[s.length,"/",e.capacity," (",t,"%)"]})]})})},e.id)})})]})]}),v&&(0,n.jsx)(i.Zp,{className:"border-2 ".concat(v.isOverCapacity?"border-red-200 bg-red-50":v.isNearCapacity?"border-orange-200 bg-orange-50":"border-green-200 bg-green-50"),children:(0,n.jsxs)(i.Wu,{className:"pt-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[v.isOverCapacity?(0,n.jsx)(S.A,{className:"h-5 w-5 text-red-600"}):(0,n.jsx)(E.A,{className:"h-5 w-5 text-green-600"}),(0,n.jsx)("span",{className:"font-medium",children:"Capacity Analysis"})]}),(0,n.jsxs)("div",{className:"grid gap-2 text-sm",children:[(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{children:"Current employees:"}),(0,n.jsx)("span",{className:"font-medium",children:v.current})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{children:"After assignment:"}),(0,n.jsx)("span",{className:"font-medium",children:v.afterAssignment})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{children:"Department capacity:"}),(0,n.jsx)("span",{className:"font-medium",children:v.capacity})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{children:"Utilization:"}),(0,n.jsxs)("span",{className:"font-medium ".concat(v.isOverCapacity?"text-red-600":v.isNearCapacity?"text-orange-600":"text-green-600"),children:[v.utilization,"%"]})]})]}),v.isOverCapacity&&(0,n.jsxs)("div",{className:"mt-3 p-2 bg-red-100 rounded text-sm text-red-700",children:["⚠️ This assignment will exceed department capacity by ",v.afterAssignment-v.capacity," employees."]}),v.isNearCapacity&&!v.isOverCapacity&&(0,n.jsxs)("div",{className:"mt-3 p-2 bg-orange-100 rounded text-sm text-orange-700",children:["⚠️ This assignment will bring the department near capacity (",v.utilization,"%)."]})]})})]}),(0,n.jsxs)(A.Es,{children:[(0,n.jsx)(r.$,{type:"button",variant:"outline",onClick:b,disabled:g,children:"Cancel"}),(0,n.jsx)(r.$,{onClick:N,disabled:!u||g,children:g?"Assigning...":"Assign ".concat(a.length," Employees")})]})]})})}var D=t(8379),I=t(7747);function Z(e){let s,t,l,c,i,d,m,o,x=(0,a.c)(16),{selectedCount:h,onAssign:j,onClearSelection:u}=e;x[0]===Symbol.for("react.memo_cache_sentinel")?(s=(0,n.jsx)(D.A,{className:"h-4 w-4 text-blue-600"}),x[0]=s):s=x[0];let g=1!==h?"s":"";return x[1]!==h||x[2]!==g?(t=(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[s,(0,n.jsxs)("span",{className:"text-sm font-medium text-blue-800",children:[h," employee",g," selected for assignment"]})]}),x[1]=h,x[2]=g,x[3]=t):t=x[3],x[4]===Symbol.for("react.memo_cache_sentinel")?(l=(0,n.jsx)(p.A,{className:"h-4 w-4 mr-2"}),x[4]=l):l=x[4],x[5]!==j?(c=(0,n.jsxs)(r.$,{size:"sm",onClick:j,className:"bg-blue-600 hover:bg-blue-700",children:[l,"Assign to Department"]}),x[5]=j,x[6]=c):c=x[6],x[7]===Symbol.for("react.memo_cache_sentinel")?(i=(0,n.jsx)(I.A,{className:"h-4 w-4"}),x[7]=i):i=x[7],x[8]!==u?(d=(0,n.jsx)(r.$,{variant:"ghost",size:"sm",onClick:u,className:"text-blue-600 hover:text-blue-700 hover:bg-blue-100",children:i}),x[8]=u,x[9]=d):d=x[9],x[10]!==c||x[11]!==d?(m=(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[c,d]}),x[10]=c,x[11]=d,x[12]=m):m=x[12],x[13]!==t||x[14]!==m?(o=(0,n.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200",children:[t,m]}),x[13]=t,x[14]=m,x[15]=o):o=x[15],o}var O=t(538),R=t(5369),z=t(4825),B=t(553);function T(){let e,s,t,o,x,h,j,u,g,f,y,b,v,w,A,_,C,E,I,T,$,M,U,F,H,P,G,Q,q,J,X,Y,K,ee,es,et,en,ea,el,ec,ei,er,ed=(0,a.c)(86);(0,c.useSession)();let{employees:em,departments:eo,selectedEmployees:ex,clearSelection:eh}=(0,m.d)(),[ej,eu]=(0,l.useState)(!1),[ep,eg]=(0,l.useState)("");if((0,O.s)(),ed[0]!==em||ed[1]!==ep||ed[2]!==ex){let n,a=em.filter(W);ed[9]!==ep?(n=e=>e.name.toLowerCase().includes(ep.toLowerCase())||e.email.toLowerCase().includes(ep.toLowerCase())||e.id.toLowerCase().includes(ep.toLowerCase()),ed[9]=ep,ed[10]=n):n=ed[10],s=a.filter(n),t=(o=ex.filter(e=>a.some(s=>s.id===e))).length>0,h=a.length,e=a.filter(L).length,x=a.filter(V),ed[0]=em,ed[1]=ep,ed[2]=ex,ed[3]=e,ed[4]=s,ed[5]=t,ed[6]=o,ed[7]=x,ed[8]=h}else e=ed[3],s=ed[4],t=ed[5],o=ed[6],x=ed[7],h=ed[8];let ef=x.length;ed[11]===Symbol.for("react.memo_cache_sentinel")?(j=e=>{eg(e)},ed[11]=j):j=ed[11];let ey=j;ed[12]!==o.length?(u=()=>{o.length>0&&eu(!0)},ed[12]=o.length,ed[13]=u):u=ed[13];let eN=u;ed[14]===Symbol.for("react.memo_cache_sentinel")?(g=()=>{eg("")},ed[14]=g):g=ed[14];let eb=g;ed[15]===Symbol.for("react.memo_cache_sentinel")?(f=(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Free Bucket"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Manage unassigned employees and assign them to departments."})]}),ed[15]=f):f=ed[15];let ev=!t;return ed[16]===Symbol.for("react.memo_cache_sentinel")?(y=(0,n.jsx)(p.A,{className:"h-4 w-4 mr-2"}),ed[16]=y):y=ed[16],ed[17]!==eN||ed[18]!==o.length||ed[19]!==ev?(b=(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[f,(0,n.jsx)("div",{className:"flex gap-2",children:(0,n.jsxs)(r.$,{variant:"outline",onClick:eN,disabled:ev,children:[y,"Assign Selected (",o.length,")"]})})]}),ed[17]=eN,ed[18]=o.length,ed[19]=ev,ed[20]=b):b=ed[20],ed[21]===Symbol.for("react.memo_cache_sentinel")?(v=(0,n.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,n.jsx)(i.ZB,{className:"text-sm font-medium",children:"Total Unassigned"}),(0,n.jsx)(R.A,{className:"h-4 w-4 text-muted-foreground"})]}),ed[21]=v):v=ed[21],ed[22]!==h?(w=(0,n.jsx)("div",{className:"text-2xl font-bold",children:h}),ed[22]=h,ed[23]=w):w=ed[23],ed[24]===Symbol.for("react.memo_cache_sentinel")?(A=(0,n.jsx)("p",{className:"text-xs text-muted-foreground",children:"Employees in free bucket"}),ed[24]=A):A=ed[24],ed[25]!==w?(_=(0,n.jsxs)(i.Zp,{children:[v,(0,n.jsxs)(i.Wu,{children:[w,A]})]}),ed[25]=w,ed[26]=_):_=ed[26],ed[27]===Symbol.for("react.memo_cache_sentinel")?(C=(0,n.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,n.jsx)(i.ZB,{className:"text-sm font-medium",children:"Active"}),(0,n.jsx)(D.A,{className:"h-4 w-4 text-green-600"})]}),ed[27]=C):C=ed[27],ed[28]!==e?(E=(0,n.jsx)("div",{className:"text-2xl font-bold",children:e}),ed[28]=e,ed[29]=E):E=ed[29],ed[30]===Symbol.for("react.memo_cache_sentinel")?(I=(0,n.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active unassigned"}),ed[30]=I):I=ed[30],ed[31]!==E?(T=(0,n.jsxs)(i.Zp,{children:[C,(0,n.jsxs)(i.Wu,{children:[E,I]})]}),ed[31]=E,ed[32]=T):T=ed[32],ed[33]===Symbol.for("react.memo_cache_sentinel")?($=(0,n.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,n.jsx)(i.ZB,{className:"text-sm font-medium",children:"Recently Added"}),(0,n.jsx)(z.A,{className:"h-4 w-4 text-blue-600"})]}),ed[33]=$):$=ed[33],ed[34]!==ef?(M=(0,n.jsx)("div",{className:"text-2xl font-bold",children:ef}),ed[34]=ef,ed[35]=M):M=ed[35],ed[36]===Symbol.for("react.memo_cache_sentinel")?(U=(0,n.jsx)("p",{className:"text-xs text-muted-foreground",children:"Added this week"}),ed[36]=U):U=ed[36],ed[37]!==M?(F=(0,n.jsxs)(i.Zp,{children:[$,(0,n.jsxs)(i.Wu,{children:[M,U]})]}),ed[37]=M,ed[38]=F):F=ed[38],ed[39]===Symbol.for("react.memo_cache_sentinel")?(H=(0,n.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,n.jsx)(i.ZB,{className:"text-sm font-medium",children:"Departments"}),(0,n.jsx)(p.A,{className:"h-4 w-4 text-orange-600"})]}),ed[39]=H):H=ed[39],ed[40]!==eo.length?(P=(0,n.jsx)("div",{className:"text-2xl font-bold",children:eo.length}),ed[40]=eo.length,ed[41]=P):P=ed[41],ed[42]===Symbol.for("react.memo_cache_sentinel")?(G=(0,n.jsx)("p",{className:"text-xs text-muted-foreground",children:"Available for assignment"}),ed[42]=G):G=ed[42],ed[43]!==P?(Q=(0,n.jsxs)(i.Zp,{children:[H,(0,n.jsxs)(i.Wu,{children:[P,G]})]}),ed[43]=P,ed[44]=Q):Q=ed[44],ed[45]!==_||ed[46]!==T||ed[47]!==F||ed[48]!==Q?(q=(0,n.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[_,T,F,Q]}),ed[45]=_,ed[46]=T,ed[47]=F,ed[48]=Q,ed[49]=q):q=ed[49],ed[50]!==h?(J=h>10&&(0,n.jsxs)(i.Zp,{className:"border-orange-200 bg-orange-50",children:[(0,n.jsx)(i.aR,{children:(0,n.jsxs)(i.ZB,{className:"text-orange-800 flex items-center gap-2",children:[(0,n.jsx)(S.A,{className:"h-5 w-5"}),"High Number of Unassigned Employees"]})}),(0,n.jsx)(i.Wu,{children:(0,n.jsxs)("p",{className:"text-sm text-orange-700",children:["You have ",h," unassigned employees. Consider assigning them to departments to improve organization and capacity utilization."]})})]}),ed[50]=h,ed[51]=J):J=ed[51],ed[52]===Symbol.for("react.memo_cache_sentinel")?(X=(0,n.jsx)(i.ZB,{children:"Unassigned Employees"}),ed[52]=X):X=ed[52],ed[53]===Symbol.for("react.memo_cache_sentinel")?(Y=(0,n.jsx)(B.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),ed[53]=Y):Y=ed[53],ed[54]===Symbol.for("react.memo_cache_sentinel")?(K=e=>ey(e.target.value),ed[54]=K):K=ed[54],ed[55]!==ep?(ee=(0,n.jsxs)("div",{className:"relative",children:[Y,(0,n.jsx)(d.p,{placeholder:"Search unassigned employees...",value:ep,onChange:K,className:"pl-8 w-[300px]"})]}),es=ep&&(0,n.jsx)(r.$,{variant:"ghost",size:"sm",onClick:eb,children:"Clear"}),ed[55]=ep,ed[56]=ee,ed[57]=es):(ee=ed[56],es=ed[57]),ed[58]!==ee||ed[59]!==es?(et=(0,n.jsx)(i.aR,{children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[X,(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[ee,es]})]})}),ed[58]=ee,ed[59]=es,ed[60]=et):et=ed[60],ed[61]!==eh||ed[62]!==eN||ed[63]!==t||ed[64]!==o.length?(en=t&&(0,n.jsx)("div",{className:"mb-4",children:(0,n.jsx)(Z,{selectedCount:o.length,onAssign:eN,onClearSelection:eh})}),ed[61]=eh,ed[62]=eN,ed[63]=t,ed[64]=o.length,ed[65]=en):en=ed[65],ed[66]!==eo||ed[67]!==s||ed[68]!==h?(ea=0===h?(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)(R.A,{className:"h-16 w-16 text-muted-foreground mx-auto mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No Unassigned Employees"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"All employees are currently assigned to departments. Great job!"})]}):0===s.length?(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-muted-foreground",children:"No employees match your search criteria."})}):(0,n.jsx)(N,{employees:s,departments:eo}),ed[66]=eo,ed[67]=s,ed[68]=h,ed[69]=ea):ea=ed[69],ed[70]!==en||ed[71]!==ea?(el=(0,n.jsxs)(i.Wu,{children:[en,ea]}),ed[70]=en,ed[71]=ea,ed[72]=el):el=ed[72],ed[73]!==et||ed[74]!==el?(ec=(0,n.jsxs)(i.Zp,{children:[et,el]}),ed[73]=et,ed[74]=el,ed[75]=ec):ec=ed[75],ed[76]!==eo||ed[77]!==o||ed[78]!==ej?(ei=(0,n.jsx)(k,{open:ej,onOpenChange:eu,selectedEmployees:o,departments:eo}),ed[76]=eo,ed[77]=o,ed[78]=ej,ed[79]=ei):ei=ed[79],ed[80]!==q||ed[81]!==J||ed[82]!==ec||ed[83]!==ei||ed[84]!==b?(er=(0,n.jsxs)("div",{className:"space-y-6",children:[b,q,J,ec,ei]}),ed[80]=q,ed[81]=J,ed[82]=ec,ed[83]=ei,ed[84]=b,ed[85]=er):er=ed[85],er}function V(e){return 7>=Math.floor((Date.now()-e.createdAt.getTime())/864e5)}function L(e){return"ACTIVE"===e.status}function W(e){return null===e.departmentId}},5369:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});let n=(0,t(8776).A)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},8842:(e,s,t)=>{Promise.resolve().then(t.bind(t,4071))}},e=>{e.O(0,[235,666,368,18,445,3,814,816,210,441,326,358],()=>e(e.s=8842)),_N_E=e.O()}]);