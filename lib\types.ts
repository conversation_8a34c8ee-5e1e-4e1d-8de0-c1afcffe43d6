export interface Employee {
  id: string;
  name: string;
  email: string;
  departmentId: string | null;
  status: 'ACTIVE' | 'TRANSFERRED' | 'PENDING_REMOVAL' | 'ARCHIVED';
  hireDate: Date;
  transferHistory: TransferRecord[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Department {
  id: string;
  name: string;
  code: string;
  capacity: number;
  employees?: Employee[];
  createdAt: Date;
  updatedAt: Date;
}

export interface TransferRecord {
  fromDepartmentId: string | null;
  toDepartmentId: string | null;
  timestamp: Date;
  reason?: string;
  userId: string;
}

export interface DistributionConfig {
  strategy: 'round-robin' | 'least-loaded' | 'capacity-weighted';
  respectCapacity: boolean;
  allowOverflow: boolean;
}

export interface BulkOperation {
  type: 'transfer' | 'status_update' | 'delete';
  employeeIds: string[];
  targetDepartmentId?: string;
  newStatus?: Employee['status'];
}

export interface CSVImportResult {
  success: Employee[];
  errors: Array<{
    row: number;
    data: any;
    error: string;
  }>;
}

export type UserRole = 'ADMIN' | 'HR_MANAGER' | 'VIEWER';

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  image?: string;
}