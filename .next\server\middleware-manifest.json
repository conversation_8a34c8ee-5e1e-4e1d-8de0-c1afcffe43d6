{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_a57d5dab._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_c1cf8224.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "UajYjgneHB5uLB/knA9x9rWrkAcqUKV94zpzH0CadVU=", "__NEXT_PREVIEW_MODE_ID": "22c6ee813190f8b3a0f04221a2fb3d10", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8c74618f9ddee254eb5d32ba2b416ac8d6d1362d2331dd3099eb135de69afdcf", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "05faddf99fb82c99824845ecc8cb44ee2b95aade010be9e20ca476df8445acfb"}}}, "sortedMiddleware": ["/"], "functions": {}}