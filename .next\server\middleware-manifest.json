{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_a57d5dab._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_c1cf8224.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "UajYjgneHB5uLB/knA9x9rWrkAcqUKV94zpzH0CadVU=", "__NEXT_PREVIEW_MODE_ID": "f983f39e3eb813dede7e8c21410dbde2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "cec46319971b570fd63d1c08c7d846e6094500ffbcaf7b78246dd05fc5dd0df4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3e5bd8f37c3c4975b8db725f0c3b421044d66b89edf82b1ce7baadfd864610e5"}}}, "sortedMiddleware": ["/"], "functions": {}}