generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  role          Role      @default(VIEWER)
  password      String?
  image         String?
  emailVerified DateTime?
  accounts      Account[]
  sessions      Session[]
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model Department {
  id        String     @id @default(uuid())
  name      String     @unique
  code      String     @unique
  capacity  Int
  employees Employee[]
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  @@map("departments")
}

model Employee {
  id              String      @id
  name            String
  email           String      @unique
  departmentId    String?
  department      Department? @relation(fields: [departmentId], references: [id])
  status          Status      @default(ACTIVE)
  hireDate        DateTime
  transferHistory Json[]      @default([])
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  @@map("employees")
}

model AuditLog {
  id         String   @id @default(uuid())
  action     String
  entityType String
  entityId   String
  userId     String
  changes    Json
  timestamp  DateTime @default(now())

  @@map("audit_logs")
}

model BackupRecord {
  id          String       @id @default(uuid())
  filename    String
  size        Int
  location    String
  type        BackupType
  status      BackupStatus @default(PENDING)
  createdAt   DateTime     @default(now())
  completedAt DateTime?

  @@map("backup_records")
}

enum Role {
  ADMIN
  HR_MANAGER
  VIEWER
}

enum Status {
  ACTIVE
  TRANSFERRED
  PENDING_REMOVAL
  ARCHIVED
}

enum BackupType {
  MANUAL
  AUTOMATED
}

enum BackupStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
}
