{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function generateEmployeeId(departmentCode: string, sequence: number): string {\n  return `${departmentCode}-${sequence.toString().padStart(3, '0')}`\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric'\n  }).format(date)\n}\n\nexport function calculateCapacityUtilization(current: number, capacity: number): number {\n  return Math.round((current / capacity) * 100)\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,mBAAmB,cAAsB,EAAE,QAAgB;IACzE,OAAO,GAAG,eAAe,CAAC,EAAE,SAAS,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACpE;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,6BAA6B,OAAe,EAAE,QAAgB;IAC5E,OAAO,KAAK,KAAK,CAAC,AAAC,UAAU,WAAY;AAC3C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { useSession } from \"next-auth/react\"\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\nimport {\n  LayoutDashboard,\n  Users,\n  Building2,\n  Upload,\n  Settings,\n  Archive,\n  UserCheck,\n  User\n} from \"lucide-react\"\n\nconst navigation = [\n  {\n    name: \"Dashboard\",\n    href: \"/dashboard\",\n    icon: LayoutDashboard,\n    roles: [\"ADMIN\", \"HR_MANAGER\", \"VIEWER\"]\n  },\n  {\n    name: \"Employees\",\n    href: \"/employees\",\n    icon: Users,\n    roles: [\"ADMIN\", \"HR_MANAGER\", \"VIEWER\"]\n  },\n  {\n    name: \"Departments\",\n    href: \"/departments\",\n    icon: Building2,\n    roles: [\"ADMIN\", \"HR_MANAGER\", \"VIEWER\"]\n  },\n  {\n    name: \"Bulk Operations\",\n    href: \"/bulk\",\n    icon: Upload,\n    roles: [\"ADMIN\", \"HR_MANAGER\"]\n  },\n  {\n    name: \"Free Bucket\",\n    href: \"/free-bucket\",\n    icon: Archive,\n    roles: [\"ADMIN\", \"HR_MANAGER\"]\n  },\n  {\n    name: \"User Management\",\n    href: \"/admin/users\",\n    icon: UserCheck,\n    roles: [\"ADMIN\"]\n  },\n  {\n    name: \"Settings\",\n    href: \"/settings\",\n    icon: Settings,\n    roles: [\"ADMIN\", \"HR_MANAGER\"]\n  }\n]\n\nexport function Sidebar() {\n  const pathname = usePathname()\n  const { data: session } = useSession()\n  const userRole = session?.user?.role\n\n  const filteredNavigation = navigation.filter(item =>\n    item.roles.includes(userRole as string)\n  )\n\n  return (\n    <div className=\"flex h-full w-72 flex-col bg-gradient-to-b from-slate-50 to-white border-r border-border/50 shadow-soft\">\n      <div className=\"flex h-20 items-center px-8 border-b border-border/50\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center shadow-medium\">\n            <div className=\"text-white text-lg font-bold\">HR</div>\n          </div>\n          <div>\n            <h1 className=\"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n              HR Synergy\n            </h1>\n            <p className=\"text-xs text-muted-foreground font-medium\">\n              Employee Management\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <nav className=\"flex-1 space-y-2 p-6\">\n        <div className=\"mb-6\">\n          <p className=\"text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3\">\n            Navigation\n          </p>\n        </div>\n\n        {filteredNavigation.map((item) => {\n          const isActive = pathname === item.href || pathname.startsWith(item.href + \"/\")\n\n          return (\n            <Link key={item.name} href={item.href}>\n              <Button\n                variant=\"ghost\"\n                className={cn(\n                  \"w-full justify-start h-12 px-4 rounded-xl font-medium transition-all duration-200 group\",\n                  isActive\n                    ? \"bg-gradient-primary text-white shadow-medium hover:opacity-90\"\n                    : \"hover:bg-muted/50 hover:translate-x-1 text-muted-foreground hover:text-foreground\"\n                )}\n              >\n                <item.icon className={cn(\n                  \"mr-3 h-5 w-5 transition-colors\",\n                  isActive ? \"text-white\" : \"text-muted-foreground group-hover:text-foreground\"\n                )} />\n                <span className=\"text-sm\">{item.name}</span>\n                {isActive && (\n                  <div className=\"ml-auto w-2 h-2 bg-white rounded-full opacity-80\" />\n                )}\n              </Button>\n            </Link>\n          )\n        })}\n      </nav>\n\n      <div className=\"p-6 border-t border-border/50\">\n        <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-200/50\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-gradient-accent rounded-lg flex items-center justify-center\">\n              <User className=\"h-4 w-4 text-white\" />\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm font-medium text-foreground truncate\">\n                {session?.user?.name}\n              </p>\n              <p className=\"text-xs text-muted-foreground\">\n                {session?.user?.role?.replace('_', ' ')}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAkBA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,4NAAA,CAAA,kBAAe;QACrB,OAAO;YAAC;YAAS;YAAc;SAAS;IAC1C;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;YAAC;YAAS;YAAc;SAAS;IAC1C;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,gNAAA,CAAA,YAAS;QACf,OAAO;YAAC;YAAS;YAAc;SAAS;IAC1C;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;YAAC;YAAS;SAAa;IAChC;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;YAAC;YAAS;SAAa;IAChC;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,gNAAA,CAAA,YAAS;QACf,OAAO;YAAC;SAAQ;IAClB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;YAAC;YAAS;SAAa;IAChC;CACD;AAEM,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,WAAW,SAAS,MAAM;IAEhC,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,OAC3C,KAAK,KAAK,CAAC,QAAQ,CAAC;IAGtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAA+B;;;;;;;;;;;sCAEhD,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAgG;;;;;;8CAG9G,8OAAC;oCAAE,WAAU;8CAA4C;;;;;;;;;;;;;;;;;;;;;;;0BAO/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAA4E;;;;;;;;;;;oBAK1F,mBAAmB,GAAG,CAAC,CAAC;wBACvB,MAAM,WAAW,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;wBAE3E,qBACE,8OAAC,4JAAA,CAAA,UAAI;4BAAiB,MAAM,KAAK,IAAI;sCACnC,cAAA,8OAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2FACA,WACI,kEACA;;kDAGN,8OAAC,KAAK,IAAI;wCAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACrB,kCACA,WAAW,eAAe;;;;;;kDAE5B,8OAAC;wCAAK,WAAU;kDAAW,KAAK,IAAI;;;;;;oCACnC,0BACC,8OAAC;wCAAI,WAAU;;;;;;;;;;;;2BAhBV,KAAK,IAAI;;;;;oBAqBxB;;;;;;;0BAGF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDACV,SAAS,MAAM;;;;;;kDAElB,8OAAC;wCAAE,WAAU;kDACV,SAAS,MAAM,MAAM,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/dropdown-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,eAAe,4KAAA,CAAA,OAA0B;AAC/C,MAAM,sBAAsB,4KAAA,CAAA,UAA6B;AACzD,MAAM,oBAAoB,4KAAA,CAAA,QAA2B;AACrD,MAAM,qBAAqB,4KAAA,CAAA,SAA4B;AACvD,MAAM,kBAAkB,4KAAA,CAAA,MAAyB;AACjD,MAAM,yBAAyB,4KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,qMAAA,CAAA,aAAgB,CAK7C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAAG,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAEjF,MAAM,uCAAyB,qMAAA,CAAA,aAAgB,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAAG,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAEjF,MAAM,oCAAsB,qMAAA,CAAA,aAAgB,CAG1C,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,4KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,qMAAA,CAAA,aAAgB,CAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,4KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,kCAAoB,qMAAA,CAAA,aAAgB,CAKxC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,4KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,qMAAA,CAAA,aAAgB,CAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,qMAAA,CAAA,aAAgB,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,kKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/components/layout/header.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useSession, signOut } from \"next-auth/react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { LogOut, Settings, User } from \"lucide-react\"\n\nexport function Header() {\n  const { data: session } = useSession()\n\n  const handleSignOut = () => {\n    signOut({ callbackUrl: \"/auth/signin\" })\n  }\n\n  return (\n    <header className=\"flex h-20 items-center justify-between border-b border-border/50 bg-white/80 backdrop-blur-md px-8 shadow-soft\">\n      <div className=\"flex items-center space-x-6\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center shadow-medium\">\n            <div className=\"text-white text-sm font-bold\">HR</div>\n          </div>\n          <div>\n            <h2 className=\"text-xl font-semibold text-foreground\">\n              Welcome back, {session?.user?.name?.split(' ')[0]}\n            </h2>\n            <p className=\"text-sm text-muted-foreground\">\n              {new Date().toLocaleDateString('en-US', {\n                weekday: 'long',\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric'\n              })}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"flex items-center space-x-6\">\n        <div className=\"hidden md:flex items-center space-x-3\">\n          <div className=\"px-3 py-1.5 bg-gradient-to-r from-blue-50 to-purple-50 rounded-full border border-blue-200/50\">\n            <span className=\"text-sm font-medium text-blue-700\">\n              {session?.user?.role?.replace('_', ' ')}\n            </span>\n          </div>\n        </div>\n\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"ghost\" className=\"relative h-10 w-10 rounded-full hover:bg-muted/50 transition-colors\">\n              <Avatar className=\"h-10 w-10 border-2 border-white shadow-medium\">\n                <AvatarImage src={session?.user?.image || \"\"} alt={session?.user?.name || \"\"} />\n                <AvatarFallback className=\"bg-gradient-primary text-white font-semibold\">\n                  {session?.user?.name?.charAt(0).toUpperCase()}\n                </AvatarFallback>\n              </Avatar>\n            </Button>\n          </DropdownMenuTrigger>\n\n          <DropdownMenuContent className=\"w-64 shadow-strong border-0 bg-white/95 backdrop-blur-md\" align=\"end\" forceMount>\n            <DropdownMenuLabel className=\"font-normal p-4\">\n              <div className=\"flex flex-col space-y-2\">\n                <p className=\"text-base font-semibold leading-none text-foreground\">\n                  {session?.user?.name}\n                </p>\n                <p className=\"text-sm leading-none text-muted-foreground\">\n                  {session?.user?.email}\n                </p>\n                <div className=\"mt-2 px-2 py-1 bg-muted/50 rounded-md\">\n                  <p className=\"text-xs font-medium text-muted-foreground\">\n                    Role: {session?.user?.role?.replace('_', ' ')}\n                  </p>\n                </div>\n              </div>\n            </DropdownMenuLabel>\n\n            <DropdownMenuSeparator className=\"bg-border/50\" />\n\n            <DropdownMenuItem className=\"p-3 cursor-pointer hover:bg-muted/50 transition-colors\">\n              <User className=\"mr-3 h-4 w-4 text-muted-foreground\" />\n              <span className=\"font-medium\">Profile Settings</span>\n            </DropdownMenuItem>\n\n            <DropdownMenuItem className=\"p-3 cursor-pointer hover:bg-muted/50 transition-colors\">\n              <Settings className=\"mr-3 h-4 w-4 text-muted-foreground\" />\n              <span className=\"font-medium\">Preferences</span>\n            </DropdownMenuItem>\n\n            <DropdownMenuSeparator className=\"bg-border/50\" />\n\n            <DropdownMenuItem\n              onClick={handleSignOut}\n              className=\"p-3 cursor-pointer hover:bg-red-50 hover:text-red-600 transition-colors\"\n            >\n              <LogOut className=\"mr-3 h-4 w-4\" />\n              <span className=\"font-medium\">Sign Out</span>\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n      </div>\n    </header>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAQA;AACA;AAAA;AAAA;AAbA;;;;;;;AAeO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAEnC,MAAM,gBAAgB;QACpB,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;YAAE,aAAa;QAAe;IACxC;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAA+B;;;;;;;;;;;sCAEhD,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;;wCAAwC;wCACrC,SAAS,MAAM,MAAM,MAAM,IAAI,CAAC,EAAE;;;;;;;8CAEnD,8OAAC;oCAAE,WAAU;8CACV,IAAI,OAAO,kBAAkB,CAAC,SAAS;wCACtC,SAAS;wCACT,MAAM;wCACN,OAAO;wCACP,KAAK;oCACP;;;;;;;;;;;;;;;;;;;;;;;0BAMR,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CACb,SAAS,MAAM,MAAM,QAAQ,KAAK;;;;;;;;;;;;;;;;kCAKzC,8OAAC,qIAAA,CAAA,eAAY;;0CACX,8OAAC,qIAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,WAAU;8CAChC,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,8OAAC,2HAAA,CAAA,cAAW;gDAAC,KAAK,SAAS,MAAM,SAAS;gDAAI,KAAK,SAAS,MAAM,QAAQ;;;;;;0DAC1E,8OAAC,2HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,SAAS,MAAM,MAAM,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;0CAMxC,8OAAC,qIAAA,CAAA,sBAAmB;gCAAC,WAAU;gCAA2D,OAAM;gCAAM,UAAU;;kDAC9G,8OAAC,qIAAA,CAAA,oBAAiB;wCAAC,WAAU;kDAC3B,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,SAAS,MAAM;;;;;;8DAElB,8OAAC;oDAAE,WAAU;8DACV,SAAS,MAAM;;;;;;8DAElB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;4DAA4C;4DAChD,SAAS,MAAM,MAAM,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;kDAMjD,8OAAC,qIAAA,CAAA,wBAAqB;wCAAC,WAAU;;;;;;kDAEjC,8OAAC,qIAAA,CAAA,mBAAgB;wCAAC,WAAU;;0DAC1B,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAGhC,8OAAC,qIAAA,CAAA,mBAAgB;wCAAC,WAAU;;0DAC1B,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAGhC,8OAAC,qIAAA,CAAA,wBAAqB;wCAAC,WAAU;;;;;;kDAEjC,8OAAC,qIAAA,CAAA,mBAAgB;wCACf,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C", "debugId": null}}, {"offset": {"line": 884, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/Augment%20code/app/%28dashboard%29/layout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useSession } from \"next-auth/react\"\nimport { useRouter } from \"next/navigation\"\nimport { useEffect } from \"react\"\nimport { Sidebar } from \"@/components/layout/sidebar\"\nimport { Header } from \"@/components/layout/header\"\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (status !== \"loading\" && !session) {\n      router.push(\"/auth/signin\")\n    }\n  }, [session, status, router])\n\n  if (status === \"loading\") {\n    return (\n      <div className=\"flex h-screen items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"></div>\n      </div>\n    )\n  }\n\n  if (!session) {\n    return null\n  }\n\n  return (\n    <div className=\"flex h-screen bg-gradient-to-br from-slate-50/30 to-white\">\n      <Sidebar />\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        <Header />\n        <main className=\"flex-1 overflow-auto\">\n          <div className=\"animate-fade-in\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,aAAa,CAAC,SAAS;YACpC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAS;QAAQ;KAAO;IAE5B,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,UAAO;;;;;0BACR,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,+HAAA,CAAA,SAAM;;;;;kCACP,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}]}