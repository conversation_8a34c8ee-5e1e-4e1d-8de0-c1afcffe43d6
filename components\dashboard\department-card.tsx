"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { Department } from "@/lib/types"
import { calculateCapacityUtilization } from "@/lib/utils"
import { Building2, Users, Plus } from "lucide-react"

interface DepartmentCardProps {
  department: Department
  onAddEmployee?: () => void
  onViewDetails?: () => void
}

export function DepartmentCard({ 
  department, 
  onAddEmployee, 
  onViewDetails 
}: DepartmentCardProps) {
  const employeeCount = department.employees?.length || 0
  const utilization = calculateCapacityUtilization(employeeCount, department.capacity)
  
  const getUtilizationColor = (percentage: number) => {
    if (percentage >= 90) return "bg-red-500"
    if (percentage >= 75) return "bg-yellow-500"
    return "bg-green-500"
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-medium">
          {department.name}
        </CardTitle>
        <Building2 className="h-5 w-5 text-muted-foreground" />
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Code:</span>
            <span className="font-mono">{department.code}</span>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                Employees
              </span>
              <span className="font-medium">
                {employeeCount} / {department.capacity}
              </span>
            </div>
            
            <Progress 
              value={utilization} 
              className="h-2"
            />
            
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Capacity Utilization</span>
              <span className={`font-medium ${
                utilization >= 90 ? 'text-red-600' : 
                utilization >= 75 ? 'text-yellow-600' : 
                'text-green-600'
              }`}>
                {utilization}%
              </span>
            </div>
          </div>
          
          <div className="flex gap-2 pt-2">
            {onAddEmployee && (
              <Button 
                size="sm" 
                variant="outline" 
                onClick={onAddEmployee}
                className="flex-1"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Employee
              </Button>
            )}
            
            {onViewDetails && (
              <Button 
                size="sm" 
                variant="secondary" 
                onClick={onViewDetails}
                className="flex-1"
              >
                View Details
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}