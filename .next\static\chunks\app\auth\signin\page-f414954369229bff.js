(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[680],{198:(e,r,t)=>{"use strict";t.d(r,{Vy:()=>o,Yq:()=>l,cn:()=>s,pp:()=>i});var a=t(5403),n=t(9055);function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.QP)((0,a.$)(r))}function i(e,r){return"".concat(e,"-").concat(r.toString().padStart(3,"0"))}function l(e){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(e)}function o(e,r){return Math.round(e/r*100)}},360:(e,r,t)=>{Promise.resolve().then(t.bind(t,8142))},425:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var a=t(5155),n=t(2115),s=t(5441),i=t(1335),l=t(198);let o=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,r)=>{let{className:t,variant:n,size:i,asChild:d=!1,...c}=e,u=d?s.DX:"button";return(0,a.jsx)(u,{className:(0,l.cn)(o({variant:n,size:i,className:t})),ref:r,...c})});d.displayName="Button"},6031:(e,r,t)=>{"use strict";t.d(r,{b:()=>l});var a=t(2115),n=t(4372),s=t(5155),i=a.forwardRef((e,r)=>(0,s.jsx)(n.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var l=i},7013:(e,r,t)=>{"use strict";t.d(r,{J:()=>d});var a=t(5155),n=t(2115),s=t(6031),i=t(1335),l=t(198);let o=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)(s.b,{ref:r,className:(0,l.cn)(o(),t),...n})});d.displayName=s.b.displayName},7071:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var a=t(5155),n=t(2115),s=t(198);let i=n.forwardRef((e,r)=>{let{className:t,type:n,...i}=e;return(0,a.jsx)("input",{type:n,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...i})});i.displayName="Input"},8142:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var a=t(5155),n=t(2115),s=t(235),i=t(425),l=t(7071),o=t(9683),d=t(7013),c=t(9554);function u(){let[e,r]=(0,n.useState)(""),[t,u]=(0,n.useState)(""),[f,m]=(0,n.useState)(!1),p=async r=>{r.preventDefault(),m(!0);try{let r=await (0,s.signIn)("credentials",{email:e,password:t,redirect:!1});(null==r?void 0:r.error)?c.oR.error("Invalid credentials"):(c.oR.success("Signed in successfully"),window.location.href="/dashboard")}catch(e){c.oR.error("An error occurred during sign in")}finally{m(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,a.jsxs)(o.Zp,{className:"w-full max-w-md",children:[(0,a.jsxs)(o.aR,{className:"space-y-1",children:[(0,a.jsx)(o.ZB,{className:"text-2xl text-center",children:"HR Synergy"}),(0,a.jsx)(o.BT,{className:"text-center",children:"Sign in to your account to continue"})]}),(0,a.jsx)(o.Wu,{children:(0,a.jsxs)("form",{onSubmit:p,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"email",children:"Email"}),(0,a.jsx)(l.p,{id:"email",type:"email",placeholder:"Enter your email",value:e,onChange:e=>r(e.target.value),required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"password",children:"Password"}),(0,a.jsx)(l.p,{id:"password",type:"password",placeholder:"Enter your password",value:t,onChange:e=>u(e.target.value),required:!0})]}),(0,a.jsx)(i.$,{type:"submit",className:"w-full",disabled:f,children:f?"Signing in...":"Sign In"})]})})]})})}},9683:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>l});var a=t(5155),n=t(2115),s=t(198);let i=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...n})});i.displayName="Card";let l=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",t),...n})});l.displayName="CardHeader";let o=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("h3",{ref:r,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",t),...n})});o.displayName="CardTitle";let d=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("p",{ref:r,className:(0,s.cn)("text-sm text-muted-foreground",t),...n})});d.displayName="CardDescription";let c=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)("p-6 pt-0",t),...n})});c.displayName="CardContent",n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("div",{ref:r,className:(0,s.cn)("flex items-center p-6 pt-0",t),...n})}).displayName="CardFooter"}},e=>{e.O(0,[235,666,554,441,326,358],()=>e(e.s=360)),_N_E=e.O()}]);